namespace PaySpace.Venuta.Excel.Employees.Test.Validators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation.TestHelper;

    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
    using Microsoft.Extensions.Localization;

    using Moq;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Employees.Validators;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;

    using Xunit;

    public class EmployeeBankingDetailsValidatorTests
    {
        private const string TestAccountNumber = "*********";

        private readonly Mock<IEmployeeBankDetailService> employeeBankDetailService;
        private readonly MutatorContext context;

        private readonly EmployeeBankDetailValidator validator;

        public EmployeeBankingDetailsValidatorTests()
        {
            this.employeeBankDetailService = new Mock<IEmployeeBankDetailService>();

            var localizer = new Mock<IStringLocalizer<EmployeeBankDetailDto>>();
            var applicationContext = new Mock<ApplicationContext>();
            var authorisationService = new Mock<IAuthorizationService>();
            var modelMetaDataProvider = new Mock<IModelMetadataProvider>();
            var objectModelValidator = new Mock<IObjectModelValidator>();
            var scopedCache = new Mock<IScopedCache>();
            var serviceProvider = new Mock<IServiceProvider>();
            var profile = new Mock<ISecurityProfile>();

            localizer.Setup(_ => _[It.IsAny<string>()]).Returns<string>(label => new LocalizedString(label, label));
            authorisationService.Setup(_ => _.AuthorizeAsync(null, It.IsAny<EmployeeBankDetailDto>(), It.IsAny<IList<IAuthorizationRequirement>>()))
                .ReturnsAsync(AuthorizationResult.Success());
            profile.Setup(_ => _.IsFullAccess(SystemAreas.Leave.Adjustment, SystemAreas.Leave.Adjustment)).Returns(true);

            this.context = new MutatorContext(profile.Object, 1, null);

            this.validator = new EmployeeBankDetailValidator(
                localizer.Object,
                applicationContext.Object,
                authorisationService.Object,
                this.employeeBankDetailService.Object,
                modelMetaDataProvider.Object,
                objectModelValidator.Object,
                scopedCache.Object,
                serviceProvider.Object);
        }

        [Fact]
        public async Task PercentageSplit_PercentWithRelatedDetails_Success()
        {
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = TestAccountNumber,
                Amount = 30,
                SplitType = BankDetailSplitType.Percentage,
                PaymentMethod = PaymentMethod.EFT.ToString()
            };

            var relatedDetails = new List<(long detailId, decimal? percentage)> { new(1, 70) };
            var percentage = model.Amount + relatedDetails.Select(_ => _.percentage).Sum();
            var relatedIds = relatedDetails.Where(_ => _.detailId > 0).Select(_ => _.detailId).ToList();

            this.employeeBankDetailService.Setup(_ => _.HasValidSplitSumAsync(1, 0, percentage!.Value, relatedIds))
                .ReturnsAsync(true);

            this.context.TryAdd("BankHeaderId", 1L);
            this.context.TryAdd("RelatedDetails", relatedDetails);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.True(result.IsValid);
            result.Errors.WithoutErrorMessage(ErrorCodes.BankDetails.InvalidSplit);
        }

        [Fact]
        public async Task PercentageSplit_NotPercentSplitType_Success()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = TestAccountNumber,
                Amount = 50,
                SplitType = BankDetailSplitType.Amount,
                PaymentMethod = PaymentMethod.EFT.ToString()
            };

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.True(result.IsValid);
            result.Errors.WithoutErrorMessage(ErrorCodes.BankDetails.InvalidSplit);
        }

        [Fact]
        public async Task PercentageSplit_NoBankHeaderId_Success()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = TestAccountNumber,
                Amount = 50,
                SplitType = BankDetailSplitType.Percentage,
                PaymentMethod = PaymentMethod.EFT.ToString()
            };

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.True(result.IsValid);
            result.Errors.WithoutErrorMessage(ErrorCodes.BankDetails.InvalidSplit);
        }

        [Fact]
        public async Task PercentageSplit_PercentageMoreThan100_Error()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = TestAccountNumber,
                Amount = 101,
                SplitType = BankDetailSplitType.Percentage,
                PaymentMethod = PaymentMethod.EFT.ToString(),
                PaymentMethodId = PaymentMethod.EFT
            };

            this.employeeBankDetailService.Setup(_ => _.HasValidSplitSumAsync(0, 0, model.Amount.Value, new List<long>()))
                .ReturnsAsync(true);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.False(result.IsValid);
            result.Errors.WithErrorMessage(ErrorCodes.BankDetails.InvalidSplit);
        }

        [Fact]
        public async Task PercentageSplit_PercentageLessThan0_Error()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = TestAccountNumber,
                Amount = -1,
                SplitType = BankDetailSplitType.Percentage,
                PaymentMethod = PaymentMethod.EFT.ToString(),
                PaymentMethodId = PaymentMethod.EFT
            };

            this.employeeBankDetailService.Setup(_ => _.HasValidSplitSumAsync(0, 0, model.Amount.Value, new List<long>()))
                .ReturnsAsync(true);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.False(result.IsValid);
            result.Errors.WithErrorMessage(ErrorCodes.BankDetails.InvalidSplit);
        }

        [Fact]
        public async Task PercentageSplit_PercentTooHigh_WithRelatedDetails_Error()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = TestAccountNumber,
                Amount = 70,
                SplitType = BankDetailSplitType.Percentage,
                PaymentMethod = PaymentMethod.EFT.ToString(),
                PaymentMethodId = PaymentMethod.EFT
            };

            var relatedDetails = new List<(long detailId, decimal? percentage)> { new (1, 70) };
            var percentage = model.Amount + relatedDetails.Select(_ => _.percentage).Sum();
            var relatedIds = relatedDetails.Where(_ => _.detailId > 0).Select(_ => _.detailId).ToList();

            this.employeeBankDetailService.Setup(_ => _.HasValidSplitSumAsync(1, 0, percentage!.Value, relatedIds))
                .ReturnsAsync(false);

            this.context.TryAdd("BankHeaderId", 1L);
            this.context.TryAdd("RelatedDetails", relatedDetails);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.False(result.IsValid);
            result.Errors.WithErrorMessage(ErrorCodes.BankDetails.InvalidSplit);
        }

        [Fact]
        public async Task BankAccountNo_ValidCharacters_Success()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                // No spaces, ?, or !
                BankAccountNo = TestAccountNumber,
                PaymentMethod = PaymentMethod.EFT.ToString()
            };

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.True(result.IsValid);
            result.Errors.WithoutErrorMessage(ErrorCodes.BankDetails.InvalidAccountNumber);
        }

        [Fact]
        public async Task BankAccountNo_WithExclamationMark_Error()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = "*********!",
                PaymentMethod = PaymentMethod.EFT.ToString(),
                PaymentMethodId = PaymentMethod.EFT
            };

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.False(result.IsValid);
            result.Errors.WithErrorMessage(ErrorCodes.BankDetails.InvalidAccountNumber);
        }

        [Fact]
        public async Task BankAccountNo_WithQuestionMark_Error()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = "*********?",
                PaymentMethod = PaymentMethod.EFT.ToString(),
                PaymentMethodId = PaymentMethod.EFT
            };

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.False(result.IsValid);
            result.Errors.WithErrorMessage(ErrorCodes.BankDetails.InvalidAccountNumber);
        }

        [Fact]
        public async Task BankAccountNo_InvalidCharacters_ButNonEFTPaymentMethod_Success()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = "**********!",
                PaymentMethod = PaymentMethod.Cheque.ToString()
            };

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.True(result.IsValid);
            result.Errors.WithoutErrorMessage(ErrorCodes.BankDetails.InvalidAccountNumber);
        }

        [Fact]
        public async Task BankAccountNo_WithUnicodeCharacter_Error()
        {
            // Given
            var model = new EmployeeBankDetailDto
            {
                BankAccountNo = "*********\u202C", // Includes unicode character
                PaymentMethod = PaymentMethod.EFT.ToString(),
                PaymentMethodId = PaymentMethod.EFT
            };

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.False(result.IsValid); // Validation should fail
            result.Errors.WithErrorMessage(ErrorCodes.BankDetails.InvalidAccountNumber);
        }
    }
}