namespace PaySpace.Venuta.Data.Models.Validation.Tests.Employees.UK
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.Extensions.Localization;

    using MockQueryable;

    using Moq;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Validation.Employees.UK;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Services.Organization;

    using Xunit;

    using CustomFieldHelper = Helpers.CustomFieldHelper;

    public class UkEmployeePayRateValidatorTests
    {
        private readonly UkEmployeePayRateValidator payRateValidator;
        private readonly Mock<ITenantProvider> tenantProvider;

        public UkEmployeePayRateValidatorTests()
        {
            var stringLocalizer = new Mock<IStringLocalizer>();
            var stringLocalizerFactory = new Mock<IStringLocalizerFactory>();
            this.tenantProvider = new Mock<ITenantProvider>();
            var applicationContext = new Mock<ApplicationContext>();
            var countryServiceFactory = new Mock<ICountryServiceFactory>();
            var customFieldService = new Mock<ICustomFieldService>();
            var employmentStatus = new Mock<IEmploymentStatusService>();
            var payRateService = new Mock<IPayRateService>();
            var employeeService = new Mock<IEmployeeService>();
            var companyFrequencyService = new Mock<ICompanyFrequencyService>();
            var customFieldListValidator = new CustomFieldListValidator<EmployeePayRateCustomFieldValue>(customFieldService.Object, CustomFieldHelper.CreateMock<EmployeePayRateCustomFieldValue>().Object);

            this.tenantProvider.Setup(_ => _.GetCompanyId()).Returns(158);
            this.tenantProvider.Setup(_ => _.GetUserId()).Returns(1);
            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns("ZA");
            this.tenantProvider.Setup(_ => _.GetFrequencyId()).Returns(80);

            stringLocalizer.Setup(_ => _[It.IsAny<string>()]).Returns<string>(label => new LocalizedString(label, label));
            stringLocalizerFactory.Setup(_ => _.Create(It.IsAny<string>(), It.IsAny<string>())).Returns(stringLocalizer.Object);
            stringLocalizerFactory.Setup(_ => _.Create(It.IsAny<Type>())).Returns(stringLocalizer.Object);

            var payRates = new List<EmployeePayRate>
            {
                new()
                {
                    EmployeeId = 126,
                    PayRateId = 1,
                    EffectiveDate = DateTime.Today.AddMonths(-3)
                }
            }.AsQueryable().BuildMock();
            var employmentStatusQuery = new List<EmployeeEmploymentStatus> { new() { EmployeeId = 126, } }
                .AsQueryable()
                .BuildMock();

            applicationContext.Setup(c => c.IsFieldModified(It.IsAny<EmployeePayRate>(), It.IsAny<string>())).Returns(true);
            companyFrequencyService.Setup(_ => _.GetRunFrequencyAsync(It.IsAny<long>())).ReturnsAsync(Enums.PayslipFrequency.Monthly);
            companyFrequencyService.Setup(_ => _.IsCompanyFrequencyActiveAsync(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<DateTime>())).ReturnsAsync(true);
            employeeService.Setup(_ => _.GetEmployeeFrequencyIdAsync(It.IsAny<long>())).ReturnsAsync(80);
            payRateService.Setup(_ => _.GetPayRatesByEmployeeId(It.IsAny<long>())).Returns(payRates);
            employmentStatus.Setup(_ => _.GetEmploymentStatusesByEmployeeId(It.IsAny<long>())).Returns(employmentStatusQuery);

            countryServiceFactory.Setup(_ => _.Create<ICustomFieldListValidator<EmployeePayRateCustomFieldValue>>(It.IsAny<string?>())).Returns(customFieldListValidator);

            this.payRateValidator = new UkEmployeePayRateValidator(
                applicationContext.Object,
                stringLocalizerFactory.Object,
                Mock.Of<ICompanySettingService>(),
                payRateService.Object,
                Mock.Of<ICompanyService>(),
                Mock.Of<ICompanyGroupExchangeRateService>(),
                Mock.Of<ICompanyRunService>(),
                Mock.Of<ICompanyRunEntityService>(),
                employmentStatus.Object,
                employeeService.Object,
                customFieldService.Object,
                this.tenantProvider.Object,
                companyFrequencyService.Object,
                Mock.Of<IIncreaseReasonService>(),
                Mock.Of<IOrganizationCategoryService>(),
                Mock.Of<IScopedCache>(),
                countryServiceFactory.Object);
        }

        [Fact]
        public async Task ValidateDaysPerPeriod_Not_Required_For_UK()
        {
            // Given
            var payRate = new EmployeePayRate()
            {
                EffectiveDate = DateTime.Now,
                EmployeeId = 126,
                DaysPerPeriod = 0,
                PayFrequency = Enums.PayFrequency.Monthly,
                HoursPerDay = 8
            };

            // When
            var validationResults = await this.payRateValidator.ValidateAsync(payRate, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            Assert.True(validationResults.IsValid);
        }
    }
}
