namespace PaySpace.Venuta.Data
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Reflection;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.ChangeTracking;
    using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
    using Microsoft.EntityFrameworkCore.ValueGeneration;

    using PaySpace.Venuta.Data.Extensions;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Agency;
    using PaySpace.Venuta.Data.Models.Attachments;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Data.Models.BulkUploads;
    using PaySpace.Venuta.Data.Models.Bureau;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Company.Result;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Components.Results;
    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Country.Brazil;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Results;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Dashboard;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Employees.Results;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Financial;
    using PaySpace.Venuta.Data.Models.Formula;
    using PaySpace.Venuta.Data.Models.Integrations;
    using PaySpace.Venuta.Data.Models.Logging;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.Data.Models.Popup;
    using PaySpace.Venuta.Data.Models.Reports;
    using PaySpace.Venuta.Data.Models.Security;
    using PaySpace.Venuta.Data.Models.Tax;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Data.Models.Workflow;
    using PaySpace.Venuta.Data.ValueConverters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Security.Abstractions.Models;

    public static class ModelBuilderHelper
    {
        public static void BuildModel(ModelBuilder modelBuilder)
        {
            modelBuilder.UsePropertyAccessMode(PropertyAccessMode.PreferProperty);

            // Logging.
            modelBuilder.Entity<AuditTrail>();
            modelBuilder.Entity<AuditTrailHeader>();
            modelBuilder.Entity<CompanySmsHistory>();

            // Workflow.
            modelBuilder.Entity<WorkflowHeader>();
            modelBuilder.Entity<WorkflowLineItem>();
            modelBuilder.Entity<WorkflowStepRequired>();
            modelBuilder.Entity<CompanyWorkflowHeader>()
                .HasDiscriminator(_ => _.WorkflowItem)
                .HasValue<CompanyWorkflowHeader>(WorkflowItem.Claims)
                .HasValue<LeaveWorkflowHeader>(WorkflowItem.AdvancedLeave)
                .HasValue<ChangeRequestWorkflowHeader>(WorkflowItem.ChangeRequest);
            modelBuilder.Entity<CompanyWorkflowLineItem>();
            modelBuilder.Entity<UserWorkflowStep>()
                .Property(_ => _.ActionDate).HasConversion(_ => _.LocalDateTime, _ => new DateTimeOffset(_, CultureData.DefaultTimezone.GetUtcOffset(_)));
            modelBuilder.Entity<UserWorkflowHeaderStep>()
                .Property(_ => _.ActionDate).HasConversion(_ => _.LocalDateTime, _ => new DateTimeOffset(_, CultureData.DefaultTimezone.GetUtcOffset(_)));
            modelBuilder.Entity<WorkflowCountryItem>()
                .HasKey(_ => new { _.WorkflowLineItemId, _.CountryId });

            // Users.
            modelBuilder.Entity<User>()
                .Property(_ => _.FullName).HasField("fullName").ValueGeneratedOnAddOrUpdate();
            modelBuilder.Entity<UserTheme>();

            modelBuilder.Entity<UserAgencyLink>();
            modelBuilder.Entity<UserCompanyLink>();
            modelBuilder.Entity<UserContactType>();

            // Security.
            modelBuilder.Entity<BureauUserCountryPermission>();
            modelBuilder.Entity<SecurityGroup>();
            modelBuilder.Entity<SecurityGroupUser>()
                .HasOne(_ => _.SecurityGroup)
                .WithMany(_ => _.SecurityGroupUsers)
                .HasForeignKey(_ => _.SecurityGroupId)
                .OnDelete(DeleteBehavior.Cascade);

            var navigationLink = modelBuilder.Entity<NavigationLink>();
            navigationLink.Property<string>("Target").HasValueGenerator((_, _) => new ConstantValueGenerator<string>("_self"));
            navigationLink.Property<bool>("IncludeEmployeeSelfService").HasValueGenerator((_, _) => new ConstantValueGenerator<bool>(false));
            navigationLink.Property<int>("OrderNumber").HasValueGenerator((_, _) => new ConstantValueGenerator<int>(0));
            navigationLink.Property<bool>("hideFromMenuStructure").HasValueGenerator((_, _) => new ConstantValueGenerator<bool>(true));

            modelBuilder.Entity<EmployeeWorkflowSetting>();
            modelBuilder.Entity<EmployeeWorkflowLink>();

            modelBuilder.Entity<EmployeeDependant>()
                .HasEnumMinusOneConverter(_ => _.AdultRelationship)
                .HasIntMinusOneConverter(_ => _.MaritalStatusId);

            modelBuilder.Entity<EmployeeDependantHistory>()
                .HasEnumMinusOneConverter(_ => _.AdultRelationship)
                .HasIntMinusOneConverter(_ => _.MaritalStatusId);

            modelBuilder.Entity<CompanyReviewProcess>();
            modelBuilder.Entity<CompanyProcessType>();
            modelBuilder.Entity<EmployeeReviewKpa>();
            modelBuilder.Entity<EmployeeReviewKpaDetail>();
            modelBuilder.Entity<CompanyCustomExplanationLink>();
            modelBuilder.Entity<CompanyTemplateSectionHeader>();
            modelBuilder.Entity<CompanyTemplateSection>();
            modelBuilder.Entity<EmployeeReviewDefaults>();
            modelBuilder.Entity<EmployeeReviewDefRaters>();
            modelBuilder.Entity<EmployeeReviewTemplate>();

            modelBuilder.Entity<Agency>()
                .HasIntMinusOneConverter(_ => _.BillingDay)
                .HasIntMinusOneConverter(_ => _.MfaOptionId);

            modelBuilder.Entity<AgencyBankDetail>();
            modelBuilder.Entity<CompanyBankDetail>();

            modelBuilder.Entity<CountryBankName>();
            modelBuilder.Entity<CompanyBankName>();

            modelBuilder.Entity<GeneralAttachment>();

            modelBuilder.Entity<BureauCountryReportPath>();
            modelBuilder.Entity<BureauCustomFormCategory>();
            modelBuilder.Entity<BureauCountryCustomFormAttribute>();
            modelBuilder.Entity<BureauMedicalAid>();
            modelBuilder.Entity<BureauMedicalAidLine>()
                .HasKey(t => new { t.MedicalAidId, t.EffectiveDate });
            modelBuilder.Entity<BureauOrgGroupChangeTracking>();
            modelBuilder.Entity<BureauInsightUser>();
            modelBuilder.Entity<BureauConfigSetting>();
            modelBuilder.Entity<TableBuilderCategory>();

            var bureauReleaseNote = modelBuilder.Entity<BureauReleaseNote>();
            bureauReleaseNote.HasMany(_ => _.BureauReleaseNoteCountries).WithOne(_ => _.ReleaseNote);
            bureauReleaseNote.HasMany(_ => _.BureauReleaseNoteAudiences).WithOne(_ => _.ReleaseNote);

            modelBuilder.Entity<BureauReleaseNoteCountry>()
                .HasOne(_ => _.ReleaseNote)
                .WithMany(_ => _.BureauReleaseNoteCountries);

            modelBuilder.Entity<BureauReleaseNoteAudience>()
                .HasOne(_ => _.ReleaseNote)
                .WithMany(_ => _.BureauReleaseNoteAudiences);

            modelBuilder.Entity<BureauBillingCountry>();
            modelBuilder.Entity<BureauSuspension>();
            modelBuilder.Entity<BureauSuspensionHistory>();

            modelBuilder.Entity<Company>()
                .HasMany(_ => _.CompanyGroupExchangeRates).WithOne(_ => _.Company);

            modelBuilder.Entity<RegionCompanyExclusion>();

            modelBuilder.Entity<CompanyProfileCustomFieldValue>();

            modelBuilder.Entity<CompanyBillingSetting>();
            modelBuilder.Entity<CompanyBudgetPeriodProcess>();
            modelBuilder.Entity<CompanyBudgetSnapshotArchiveHeader>();

            modelBuilder.Entity<CompanyBudgetSnapshotArchive>()
                .HasOne(_ => _.CompanyJobManagement)
                .WithMany(_ => _.CompanyBudgetSnapshots)
                .HasForeignKey(_ => _.JobId);

            modelBuilder.Entity<CompanyEmployeeNumber>();
            modelBuilder.Entity<CompanyAttachment>();
            modelBuilder.Entity<CompanyAttachmentClassification>();

            // Company Menu Override
            modelBuilder.Entity<CountryMenuOverride>();
            modelBuilder.Entity<CompanyMenuOverride>();

            modelBuilder.Entity<CompanyProcessBaseChecklist>();
            modelBuilder.Entity<CompanyPeriodProcess>();

            // Company Employment Equity
            modelBuilder.Entity<EquityPlan>();
            modelBuilder.Entity<EquityPlanPeriod>()
                .HasOne(_ => _.EquityPlan)
                .WithMany(_ => _.EquityPlanPeriods)
                .HasForeignKey(_ => _.EquityPlanId);
            modelBuilder.Entity<EquityValue>()
                .HasKey(_ => new { _.EquityPlanPeriodId, _.OccupationalLevelId, _.GenderId, _.RaceId });
            modelBuilder.Entity<EquityValue>()
                .HasOne(_ => _.EquityPlanPeriod)
                .WithMany(_ => _.EquityPlanValues)
                .HasForeignKey(_ => _.EquityPlanPeriodId);

            modelBuilder.Entity<CompanyEmploymentSubCategory>();
            modelBuilder.Entity<CompanyEmployeeNumber>();
            modelBuilder.Entity<CompanyGroupExchangeRateResult>();
            modelBuilder.Entity<CompanyGroup>();
            modelBuilder.Entity<CompanyGroupLink>();
            modelBuilder.Entity<CompanyGroupIPAddressRange>();
            modelBuilder.Entity<CompanyIntegrationValue>();

            modelBuilder.Entity<CompanyLeaveBucketMapping>();
            modelBuilder.Entity<CompanyLeaveBucketMappingDetail>();
            modelBuilder.Entity<CompanyLeaveReason>();
            modelBuilder.Entity<CompanyLeaveScheme>();
            modelBuilder.Entity<CompanyLeaveServiceLength>();
            modelBuilder.Entity<CompanyLeaveSetup>();

            modelBuilder.Entity<CompanyManagerRequestType>();
            modelBuilder.Entity<CompanyManagerRequestTypeAttachment>();
            modelBuilder.Entity<CompanyPaymentModule>();
            modelBuilder.Entity<CompanyRoster>();
            modelBuilder.Entity<CompanyTAShiftType>();
            modelBuilder.Entity<CompanyRosterHistory>();
            modelBuilder.Entity<CompanyRosterSchedule>();
            modelBuilder.Entity<CompanyRun>();
            modelBuilder.Entity<CompanyRunForPayslipResult>();
            modelBuilder.Entity<CompanyRunFrequency>();
            modelBuilder.Entity<CompanySetting>();
            modelBuilder.Entity<CompanyTheme>();
            modelBuilder.Entity<CompanyGroupExchangeRates>();
            modelBuilder.Entity<CompanyExternalQuickLink>();
            modelBuilder.Entity<CompanyCustomFormCategory>();
            modelBuilder.Entity<CompanyIncreaseReason>();
            modelBuilder.Entity<CompanyEFTPublishOutbox>();
            modelBuilder.Entity<CompanyCustomForm>()
                .HasOne(_ => _.CostingProjectActivity)
                .WithOne()
                .HasForeignKey<CompanyCustomForm>(_ => _.RelatedPrimaryKey);

            modelBuilder.Entity<CompanyForceRecalculation>()
                .HasKey(_ => new { _.CompanyId, _.DateInserted });

            modelBuilder.Entity<CompanyBudgetGroup>();
            modelBuilder.Entity<CompanyComponentTemplate>();
            modelBuilder.Entity<CompanyMedicalAid>()
                .HasIntToLongConverter(_ => _.MedicalAidId);
            modelBuilder.Entity<CompanyMedicalAidDetail>();
            modelBuilder.Entity<CompanyGroupLife>();
            modelBuilder.Entity<CompanyGroupLifeLink>();
            modelBuilder.Entity<CompanyGroupLifeDetail>();
            modelBuilder.Entity<CompanyDisability>();
            modelBuilder.Entity<CompanyDisabilityLink>();
            modelBuilder.Entity<CompanyDisabilityDetail>();
            modelBuilder.Entity<CompanyScaleDefinition>();
            modelBuilder.Entity<CompanyReportOption>();
            modelBuilder.Entity<TableBuilder>();

            modelBuilder.Entity<CustomFieldForm>();
            modelBuilder.Entity<CompanyCustomFieldGroup>();
            modelBuilder.Entity<CountryCustomFieldGroup>();
            modelBuilder.Entity<CompanyCustomField>()
                .HasOne(_ => _.CompanyCustomFormCategory)
                .WithMany(_ => _.CustomFields)
                .HasForeignKey(_ => _.CompanyCustomFormCategoryId)
                .OnDelete(DeleteBehavior.Cascade);
            modelBuilder.Entity<BureauCustomField>()
                .HasOne(_ => _.BureauCustomFormCategory)
                .WithMany(_ => _.CustomFields)
                .HasForeignKey(_ => _.BureauCustomFormCategoryId)
                .OnDelete(DeleteBehavior.Cascade);
            modelBuilder.Entity<CustomFieldOption>();
            modelBuilder.Entity<CustomFieldValue>();
            modelBuilder.Entity<CompanyTemplateDefinition>();
            modelBuilder.Entity<CompanyScaleOption>();
            modelBuilder.Entity<CompanyReviewProcess>();
            modelBuilder.Entity<CompanyTrainingCourse>();
            modelBuilder.Entity<CompanyJobManagement>();
            modelBuilder.Entity<CompanyJobManagementMultiDate>();
            modelBuilder.Entity<CompanyJobManagementComponent>();
            modelBuilder.Entity<CompanyJobCostingGradeComponent>();
            modelBuilder.Entity<CompanyJobCostingGradeHeader>();
            modelBuilder.Entity<CompanyGl>();
            modelBuilder.Entity<CompanyGLDetail>();
            modelBuilder.Entity<CompanyEmploymentCategory>();
            modelBuilder.Entity<CompanyEmploymentSubCategory>();
            modelBuilder.Entity<CboCode>();

            modelBuilder.Entity<OnboardingNotificationHeader>();
            modelBuilder.Entity<OnboardingNotificationDetail>();

            modelBuilder.Entity<CostingProjectActivity>()
                .Property(_ => _.CategoryCode)
                .HasConversion(new ValueConverter<string, string>(valToDb => valToDb, valFromDb => valFromDb == "-1" ? null : valFromDb));

            modelBuilder.Entity<AccumulationLine>();
            modelBuilder.Entity<BureauAccumulationLine>();

            modelBuilder.Entity<ComponentBureau>();
            modelBuilder.Entity<ComponentBureauLinkingTable>().HasNoKey();

            var cc = modelBuilder.Entity<ComponentCompany>();

            cc.HasOne(_ => _.ComponentCompanyParent)
                .WithMany()
                .HasForeignKey(_ => _.ComponentCompanyParentId);

            modelBuilder.Entity<ComponentEmployee>();
            modelBuilder.Entity<CompanyPayrollComponentResult>().HasNoKey();

            modelBuilder.Entity<ComponentValues>();
            modelBuilder.Entity<BulkCaptureCode>();
            modelBuilder.Entity<BulkCaptureCodeResult>();
            modelBuilder.Entity<EmployeePayslipTakeOn>();

            modelBuilder.Entity<FormulaHeader>();
            modelBuilder.Entity<FormulaLinkingTable>();
            modelBuilder.Entity<FormulaLine>();
            modelBuilder.Entity<FormulaTable>();
            modelBuilder.Entity<FormulaTableLine>();

            modelBuilder.Entity<CompanyWebhookConfig>();
            modelBuilder.Entity<CompanyWebhookSecret>();

            // Components for component screens
            modelBuilder.Entity<EmployeePensionFund>();
            modelBuilder.Entity<EmployeeUnion>()
                .HasIntToLongConverter(_ => _.UnionId);

            modelBuilder.Entity<EmployeeHousePaymentDetail>();
            modelBuilder.Entity<EmployeeCompanyCarDetail>()
                .HasIntMinusOneConverter(cc => cc.KenyaPurchasedCarOptionId);
            modelBuilder.Entity<EmployeeMedical>();

            modelBuilder.Entity<EmployeeIncomeProtection>();
            modelBuilder.Entity<EmployeeGroupLife>();
            modelBuilder.Entity<EmployeeDisability>();
            modelBuilder.Entity<EmployeeRetirementAnnuity>();
            modelBuilder.Entity<EmployeeLoan>();
            modelBuilder.Entity<EmployeeMultiContractWork>();
            modelBuilder.Entity<EmployeeGarnishee>();
            modelBuilder.Entity<EmployeeAlimony>();
            modelBuilder.Entity<EmployeeTravelBusinessUsage>();
            modelBuilder.Entity<EmployeeBonusProvision>();
            modelBuilder.Entity<EmployeeSaving>();
            modelBuilder.Entity<CompanyBonusProvision>();
            modelBuilder.Entity<TaxCalculationError>();
            modelBuilder.Entity<EmployeeTaxCalcBreakDown>()
                .HasKey(_ => new { _.EmployeeId, _.RunId });
            modelBuilder.Entity<EmployeeFinancialHousePayment>();

            modelBuilder.Entity<BureauPensionFund>();
            modelBuilder.Entity<CompanyPensionFund>();
            modelBuilder.Entity<CompanyPensionFundLink>();
            modelBuilder.Entity<CompanyPensionFundDetails>();
            modelBuilder.Entity<CompanyIncomeProtection>();
            modelBuilder.Entity<CompanyIncomeProtectionLink>();
            modelBuilder.Entity<CompanyIncomeProtectionDetail>();

            var address = modelBuilder.Entity<Address>();
            address.HasIntMinusOneConverter(_ => _.ProvinceId);

            modelBuilder.Entity<EmailAddress>();

            var employee = modelBuilder.Entity<Employee>();
            employee.HasMany(_ => _.EmploymentStatuses).WithOne(_ => _.Employee);
            employee.HasMany(_ => _.EmployeePositions).WithOne(_ => _.Employee);
            employee.HasOne(_ => _.EmployeeWithPosition).WithOne(_ => _.Employee)
                .HasForeignKey<EmployeeWithPosition>(_ => _.EmployeeId);
            employee.HasIntMinusOneConverter(_ => _.MaritalStatusId);
            employee.HasMany(_ => _.CustomFields).WithOne().HasForeignKey(_ => _.EmployeeId);
            employee.HasMany(_ => _.EmployeeHistory).WithOne(_ => _.Employee).HasForeignKey(_ => _.EmployeeId);

            modelBuilder.Entity<SubordinateEmployeeIdResult>()
                .HasNoKey();
            modelBuilder.Entity<SubordinateEmployeeResult>()
                .HasNoKey();

            modelBuilder.Entity<EmployeeTaxCertificateResult>();

            var employeeWithHierachy = modelBuilder.Entity<EmployeeWithHierachy>();
            employeeWithHierachy.Property(_ => _.FullName).HasField("fullName").ValueGeneratedOnAddOrUpdate();
            employeeWithHierachy.Property(_ => _.FirstName).HasField("firstName");
            employeeWithHierachy.Property(_ => _.LastName).HasField("lastName");
            employeeWithHierachy.Property(_ => _.PreferredName).HasField("preferredName");
            employeeWithHierachy.Property(_ => _.Position).HasField("position");
            employeeWithHierachy.Property(_ => _.DirectlyReportsEmployeeId).HasField("directlyReportsEmployeeId");
            employeeWithHierachy.Property(_ => _.SubordinateCount).HasField("subordinateCount");
            employeeWithHierachy.Property(_ => _.PositionLevel).HasField("positionLevel");
            employeeWithHierachy.Property(_ => _.Root).HasField("root");

            var employeeWithPosition = modelBuilder.Entity<EmployeeWithPosition>();
            employeeWithPosition.Property(_ => _.FullName).HasField("fullName").ValueGeneratedOnAddOrUpdate();
            employeeWithPosition.Property(_ => _.FirstName).HasField("firstName");
            employeeWithPosition.Property(_ => _.LastName).HasField("lastName");
            employeeWithPosition.Property(_ => _.PreferredName).HasField("preferredName");
            employeeWithPosition.Property(_ => _.EmployeePositionId).HasField("employeePositionId");
            employeeWithPosition.Property(_ => _.OrganizationPositionId).HasField("organizationPositionId");
            employeeWithPosition.Property(_ => _.DirectlyReportsPositionId).HasField("directlyReportsPositionId");
            employeeWithPosition.Property(_ => _.DirectlyReportsPositionOverrideId).HasField("directlyReportsPositionOverrideId");
            employeeWithPosition.Property(_ => _.DirectlyReportsEmployeeId).HasField("directlyReportsEmployeeId");
            employeeWithPosition.Property(_ => _.SubordinateCount).HasField("subordinateCount");
            employeeWithPosition.Property(_ => _.OrganizationGroupId).HasField("organizationGroupId");
            employeeWithPosition.Property(_ => _.OrganizationRegionId).HasField("organizationRegionId");
            employeeWithPosition.Property(_ => _.WorkflowRoleId).HasField("workflowRoleId");

            var employeePositionOrgUnitStructure = modelBuilder.Entity<EmployeePositionOrgStructure>();
            employeePositionOrgUnitStructure.Property(_ => _.Description).HasField("description");
            employeePositionOrgUnitStructure.Property(_ => _.CompanyId).HasField("companyId");
            employeePositionOrgUnitStructure.Property(_ => _.ParentOrganizationGroupId).HasField("parentOrganizationGroupId");
            employeePositionOrgUnitStructure.Property(_ => _.CostCentre).HasField("costCentre");
            employeePositionOrgUnitStructure.Property(_ => _.HasChildren).HasField("hasChildren");
            employeePositionOrgUnitStructure.Property(_ => _.OrganizationLevel).HasField("organizationLevel");
            employeePositionOrgUnitStructure.Property(_ => _.OrganizationLevelName).HasField("organizationLevelName");
            employeePositionOrgUnitStructure.Property(_ => _.Root).HasField("root");

            modelBuilder.Entity<EmployeeAttachment>()
                .Ignore(_ => _.Attachment);
            modelBuilder.Entity<EmployeeChangeRequest>();

            modelBuilder.Entity<BirthdayResult>().HasNoKey();

            modelBuilder.Entity<EmployeeLeaveAdjustment>();
            modelBuilder.Entity<EmployeeHistoricConcession>();
            modelBuilder.Entity<EmployeeLeaveBucketResult>().HasNoKey();
            modelBuilder.Entity<UpcomingLeaveResult>().HasNoKey();
            modelBuilder.Entity<EmployeeLeaveAmountBreakdown>();
            modelBuilder.Entity<EmployeeLeaveSetup>();
            modelBuilder.Entity<EmployeeLeaveDetail>();
            modelBuilder.Entity<EmployeeLeaveBalanceResult>();
            modelBuilder.Entity<EmployeeLeaveSetupEntitlement>();

            var es = modelBuilder.Entity<EmployeeEmploymentStatus>();
            es.HasMany(_ => _.CustomFields).WithOne().HasForeignKey(_ => _.EmploymentStatusId);
            es.HasOne(_ => _.NatureOfPerson).WithMany().IsRequired(false);

            modelBuilder.Entity<EmployeeEmploymentStatusResult>()
                .HasMany(_ => _.CustomFields).WithOne().HasForeignKey(_ => _.EmploymentStatusId);

            var employeePosition = modelBuilder.Entity<EmployeePosition>()
                .HasIntMinusOneConverter(_ => _.PositionTypeId);
            employeePosition.HasMany(_ => _.CustomFields).WithOne().HasForeignKey(_ => _.EmployeePositionId);
            employeePosition.HasMany(_ => _.OrganizationGroups).WithOne(_ => _.EmployeePosition).HasForeignKey(_ => _.EmployeePositionId);

            var employeePositionResult = modelBuilder.Entity<EmployeePositionResult>();
            employeePositionResult.HasMany(_ => _.CustomFields).WithOne().HasForeignKey(_ => _.EmployeePositionId);
            employeePositionResult.HasMany(_ => _.OrganizationGroups).WithOne().HasForeignKey(_ => _.EmployeePositionId);

            modelBuilder.Entity<EmployeePositionGroupResult>();

            modelBuilder.Entity<EmployeeProject>();
            modelBuilder.Entity<EmployeePositionLevel>();
            modelBuilder.Entity<EmployeeSuspension>();

            modelBuilder.Entity<EmployeeTransferHistory>();

            modelBuilder.Entity<EmployeeTotalHeader>();
            modelBuilder.Entity<EmployeeTotalHeaderTaxability>().HasNoKey();
            modelBuilder.Entity<EmployeeTotalsComponent>();
            modelBuilder.Entity<EmployeePeriodLastRun>().HasKey(_ => new { _.EmployeeId, _.PeriodCode });

            modelBuilder.Entity<EmployeeLumpSum>();
            modelBuilder.Entity<EmployeePeriodWorked>();
            modelBuilder.Entity<EmployeeCustomForm>();
            modelBuilder.Entity<EmployeeJournal>();
            modelBuilder.Entity<EmployeeAsset>();
            modelBuilder.Entity<EmployeeComponentProjectedTotals>()
                .HasKey(_ => new { _.ComponentEmployeeId, _.RunId, _.EmployeeId });
            modelBuilder.Entity<EmployeeReviewHeader>();
            modelBuilder.Entity<EmployeeReviewTemplate>();
            modelBuilder.Entity<EmployeeReviewSection>();
            modelBuilder.Entity<EmployeeReviewRating>();
            modelBuilder.Entity<EmployeeTraining>();

            modelBuilder.Entity<EmployeeTakeOn>()
                .HasKey(_ => new { _.ComponentEmployeeId, _.CompanyRunId });
            modelBuilder.Entity<EmployeeEtiTakeOn>();
            modelBuilder.Entity<EmployeeEtiTakeOnDateRange>();

            modelBuilder.Entity<EmployeeBankHeader>();
            modelBuilder.Entity<EmployeeBankDetail>();
            modelBuilder.Entity<CompanyEbdIndicator>();
            modelBuilder.Entity<EmployeeNote>()
                .HasMany(_ => _.EmployeeNoteRecurring);
            modelBuilder.Entity<EmployeeNoteRecurring>();

            modelBuilder.Entity<EmployeeRecurringCostingSplitHeader>()
                .HasMany(_ => _.RecurringCostingSplitDetails).WithOne(_ => _.RecurringCostingSplitHeader);
            modelBuilder.Entity<EmployeeRecurringCostingSplitDetail>();

            modelBuilder.Entity<Irp5>();
            modelBuilder.Entity<ManualIrp5Number>();
            modelBuilder.Entity<Irp5Resubmit>();

            // Claims.
            modelBuilder.Entity<ClaimBatch>();
            modelBuilder.Entity<ClaimComponent>()
                .HasLongMinusOneConverter(_ => _.ComponentCompanyId)
                .HasLongMinusOneConverter(_ => _.ProjectId)
                .HasLongMinusOneConverter(_ => _.ActivityId)
                .HasLongMinusOneConverter(_ => _.OrganizationId)
                .HasMany(_ => _.CustomFields).WithOne().HasForeignKey(_ => _.ClaimComponentId);
            modelBuilder.Entity<ClaimComponentSetting>();
            modelBuilder.Entity<ClaimDetail>();

            // Payslips.
            modelBuilder.Entity<PayslipHeader>(config =>
            {
                config.HasAlternateKey(_ => new { _.EmployeeId, _.CompanyRunId });
            });
            modelBuilder.Entity<PayslipLine>()
                .HasIntToLongConverter(_ => _.TaxCodeId);
            modelBuilder.Entity<PayslipComment>();
            modelBuilder.Entity<EmployeeRecoveryFigure>();

            modelBuilder.Entity<BureauUnion>();
            modelBuilder.Entity<BureauUnionLine>();

            modelBuilder.Entity<CountryTaxYear>();
            modelBuilder.Entity<CountryTaxYearRateDetail>();
            modelBuilder.Entity<CountryTaxYearBracketDetail>();

            modelBuilder.Entity<TaxCode>();
            modelBuilder.Entity<TaxCodeFunction>();
            modelBuilder.Entity<TaxCodeIRP5Action>();
            modelBuilder.Entity<TaxCodeTaxabilityOverride>();

            modelBuilder.Entity<EmployeeRetroTrigger>();

            var employeePayRate = modelBuilder.Entity<EmployeePayRate>();
            employeePayRate.HasMany(_ => _.CustomFields).WithOne().HasForeignKey(_ => _.PayRateId);
            employeePayRate.Property(_ => _.CategoryCode)
                .HasConversion(new ValueConverter<string, string>(valToDb => valToDb, valFromDb => valFromDb == "-1" ? null : valFromDb));

            modelBuilder.Entity<EmployeePayRateResult>()
                .HasMany(_ => _.CustomFields).WithOne().HasForeignKey(_ => _.PayRateId);

            modelBuilder.Entity<WeekTemplate>();
            modelBuilder.Entity<WeekTemplateRosterLink>();
            modelBuilder.Entity<OrganizationGroup>();
            modelBuilder.Entity<OrganizationGroupByCompanyGroup>();
            modelBuilder.Entity<OrganizationLevel>();
            modelBuilder.Entity<OrganizationPayPoint>();
            modelBuilder.Entity<OrganizationPosition>()
                .HasMany(_ => _.OrganizationPositionDetails).WithOne(_ => _.OrganizationPosition);
            modelBuilder.Entity<OrganizationPositionDetail>()
                .HasIntMinusOneConverter(_ => _.OccupationalLevelId)
                .HasIntMinusOneConverter(_ => _.OccupationCategoryId)
                .HasLongMinusOneConverter(_ => _.SstLevelId)
                .HasLongMinusOneConverter(_ => _.OfoCodeId)
                .HasLongMinusOneConverter(_ => _.GradeId)
                .HasIntMinusOneConverter(_ => _.PositionTypeId)
                .HasEnumMinusOneConverter(_ => _.BeeLevelId)
                .HasEnumMinusOneConverter(_ => _.EeFunctionId);

            modelBuilder.Entity<OrganizationRegion>();
            modelBuilder.Entity<OrganizationRegionHistory>();
            modelBuilder.Entity<OrganizationRegionCustomFieldValue>();
            modelBuilder.Entity<OrganizationCategory>()
                .HasIndex(_ => new { _.CompanyId, _.Code }).IsUnique();
            modelBuilder.Entity<OrganizationGrade>();
            modelBuilder.Entity<OrganizationPayPoint>();
            modelBuilder.Entity<CompanyGradeField>();

            modelBuilder.Entity<CompanyCategoryField>();
            modelBuilder.Entity<CompanyCategoryFieldValue>()
                .HasOne(_ => _.CompanyCategoryField);

            modelBuilder.Entity<CompanyBulkUpload>()
                .Property(_ => _.Status).HasConversion<string>();

            modelBuilder.Entity<EmployeeLeaveBalanceBreakdownResult>()
                .HasNoKey();

            modelBuilder.Entity<EmployeeTakeOnResult>();

            modelBuilder.Entity<ComponentValueResult>()
                .HasNoKey();
            modelBuilder.Entity<CompanyDisabilityLookupResult>()
                .HasNoKey();
            modelBuilder.Entity<CompanyGroupLifeLookupResult>()
                .HasNoKey();

            modelBuilder.Entity<ComponentCompanyInfoResult>()
                .HasNoKey();

            modelBuilder.Entity<CompanyLengthOfServiceResult>()
                .HasNoKey();
            modelBuilder.Entity<CompanyMovementResult>()
                .HasNoKey();
            modelBuilder.Entity<CompanyStatsResult>()
                .HasNoKey();
            modelBuilder.Entity<CompanyTerminationReasonResult>()
                .HasNoKey();
            modelBuilder.Entity<CompanyComponentVarianceResult>()
                .HasNoKey();
            modelBuilder.Entity<CompanyComponentEmployeeVarianceResult>()
                .HasNoKey();
            modelBuilder.Entity<FormulaLineResult>()
                .HasNoKey();
            modelBuilder.Entity<GeneralLedgerComponentsBalance>()
                .HasNoKey();
            modelBuilder.Entity<GeneralLedgerStandardResult>()
                .HasNoKey();
            modelBuilder.Entity<GeneralLedgerGroupedByPayrollResult>()
                .HasNoKey();
            modelBuilder.Entity<GeneralLedgerPerComponentResult>()
                .HasNoKey();
            modelBuilder.Entity<CompanyGeneralLedgerPerComponentResult>()
                .HasNoKey();
            modelBuilder.Entity<CustomFieldDetailResult>()
                .HasNoKey()
                .Property(_ => _.AdvancedSettings)
                .HasConversion(JsonValueConverter<AdvancedSettings>.Default);
            modelBuilder.Entity<DashboardWidgetResult>()
                .HasNoKey();
            modelBuilder.Entity<EmployeePayslipLineResult>();
            modelBuilder.Entity<EmployeePayslipResult>();
            modelBuilder.Entity<EmployeeInboxResult>();
            modelBuilder.Entity<ComponentCompanyResult>();
            modelBuilder.Entity<ActiveComponentCompanyResult>();
            modelBuilder.Entity<OrganizationPositionSummaryResult>();

            // Reports
            modelBuilder.Entity<ReportHeader>();
            modelBuilder.Entity<ReportHeaderParamLink>().HasKey(_ => new { _.ReportHeaderId, _.ReportParamId });
            modelBuilder.Entity<ReportHeaderParamLink>().HasOne(_ => _.ReportParameterDetail);
            modelBuilder.Entity<ReportParameterDetail>();
            modelBuilder.Entity<UserReportAccessTracker>();
            modelBuilder.Entity<DynamicColumnResult>();

            // Qualifications
            modelBuilder.Entity<EmployeeQualification>();
            modelBuilder.Entity<CompanyQualification>();

            // Incidents
            modelBuilder.Entity<EmployeeIncident>();
            modelBuilder.Entity<CompanyIncidentType>();
            modelBuilder.Entity<CompanyLegalBody>();
            modelBuilder.Entity<CompanyOffence>();

            // Skills
            modelBuilder.Entity<CompanySkillCategory>();
            modelBuilder.Entity<CompanySkill>()
                .HasOne(_ => _.CompanySkillCategory)
                .WithMany(_ => _.CompanySkills)
                .HasForeignKey(_ => new { _.CompanySkillCategoryCode, _.CompanyId })
                .HasPrincipalKey(_ => new { _.CompanySkillCategoryCode, _.CompanyId });
            modelBuilder.Entity<EmployeeSkill>()
                .Property(_ => _.CompetencyId)
                .HasConversion<int?>();

            // Financial Documents
            modelBuilder.Entity<FinancialDocumentsHeader>();
            modelBuilder.Entity<FinancialDocumentsLineItem>();

            // Popup
            modelBuilder.Entity<PopupHeader>();
            modelBuilder.Entity<PopupDetail>();

            // CustomFields
            modelBuilder.Entity<CompanyJobManagementCustomFieldValue>();
            modelBuilder.Entity<CompanyQualificationCustomFieldValue>();
            modelBuilder.Entity<CompanySkillCategoryCustomFieldValue>();
            modelBuilder.Entity<CompanySkillCustomFieldValue>();
            modelBuilder.Entity<CostingProjectActivityCustomFieldValue>();
            modelBuilder.Entity<EmployeeAssetCustomFieldValue>();
            modelBuilder.Entity<EmployeeAttachmentCustomFieldValue>();
            modelBuilder.Entity<EmployeeClaimItemsCustomFieldValue>();
            modelBuilder.Entity<EmployeeCustomFieldValue>();
            modelBuilder.Entity<EmployeeDependantCustomFieldValue>();
            modelBuilder.Entity<EmployeeDependantHistoryCustomFieldValue>();
            modelBuilder.Entity<EmployeeEmploymentStatusCustomFieldValue>();
            modelBuilder.Entity<EmployeePositionCustomFieldValue>();
            modelBuilder.Entity<EmployeeSuspensionCustomFieldValue>();
            modelBuilder.Entity<EmployeeTrainingCustomFieldValue>();
            modelBuilder.Entity<EmployeePayRateCustomFieldValue>();
            modelBuilder.Entity<EmployeeProjectCustomFieldValue>();
            modelBuilder.Entity<EmployeeLeaveAdjustmentCustomFieldValue>();
            modelBuilder.Entity<EmployeeCustomFormFieldValue>();
            modelBuilder.Entity<EmployeeSkillCustomFieldValue>();
            modelBuilder.Entity<EmployeeQualificationCustomFieldValue>();
            modelBuilder.Entity<EmployeeIncidentCustomFieldValue>();

            modelBuilder.Entity<CustomFieldFormField>()
                .HasKey(_ => new { _.CustomFieldId, _.CustomFieldType });
            modelBuilder.Entity<CustomFieldValueResult>()
                .HasNoKey();
            modelBuilder.Entity<CustomFieldOptionResult>()
                .HasNoKey();

            modelBuilder.Entity<CompanyCustomFormCategoryResult>();

            // Integrations
            modelBuilder.Entity<XeroTenant>()
                .HasKey(_ => new { _.UserId, _.CompanyId });

            // Company Profile
            modelBuilder.Entity<CommissionEarner>();

            // API Claims
            modelBuilder.Entity<ComponentHeader>()
                .HasNoKey();

            // API Components
            modelBuilder.Entity<EmployeeComponentValueResult>();

            var incomeProtectionComponent = modelBuilder.Entity<EmployeeIncomeProtectionResult>();
            incomeProtectionComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            incomeProtectionComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var medicalComponent = modelBuilder.Entity<EmployeeMedicalResult>();
            medicalComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            medicalComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var retirementAnnuityComponent = modelBuilder.Entity<EmployeeRetirementAnnuityResult>();
            retirementAnnuityComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            retirementAnnuityComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var loanComponent = modelBuilder.Entity<EmployeeLoanResult>();
            loanComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            loanComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            // We need to manually ignore this, We can't use the NotMappedAttribute. OData then ignores the property.
            loanComponent.Ignore(_ => _.OverrideOutstandingBalance);

            var pensionFundComponent = modelBuilder.Entity<EmployeePensionFundResult>();
            pensionFundComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            pensionFundComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var bonusProvisionComponent = modelBuilder.Entity<EmployeeBonusProvisionResult>();
            bonusProvisionComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            bonusProvisionComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var multiContractWork = modelBuilder.Entity<EmployeeMultiContractWorkResult>();
            multiContractWork.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            multiContractWork.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var alimony = modelBuilder.Entity<EmployeeAlimonyResult>();
            alimony.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            alimony.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var financialHousePayment = modelBuilder.Entity<EmployeeHousePaymentResult>();
            financialHousePayment.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            financialHousePayment.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var garnisheeComponent = modelBuilder.Entity<EmployeeGarnisheeResult>();
            garnisheeComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            garnisheeComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var unionComponent = modelBuilder.Entity<EmployeeUnionResult>();
            unionComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            unionComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });
            unionComponent.HasIntToLongConverter(_ => _.UnionId);

            var disabilityComponent = modelBuilder.Entity<EmployeeDisabilityResult>();
            disabilityComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            disabilityComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var employeeComponent = modelBuilder.Entity<EmployeeComponentResult>();
            employeeComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            employeeComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var companyCarComponent = modelBuilder.Entity<EmployeeCompanyCarDetailResult>();
            companyCarComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            companyCarComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var groupLifeComponent = modelBuilder.Entity<EmployeeGroupLifeResult>();
            groupLifeComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            groupLifeComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var tableBuilderComponent = modelBuilder.Entity<EmployeeTableBuilderResult>();
            tableBuilderComponent.HasMany(x => x.CustomFields)
                .WithOne()
                .HasForeignKey(_ => _.EmployeeTableBuilderId);

            var housePaymentDetailComponent = modelBuilder.Entity<EmployeeHousePaymentDetailResult>();
            housePaymentDetailComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            housePaymentDetailComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            var employeeTableBuilder = modelBuilder.Entity<EmployeeTableBuilder>();
            employeeTableBuilder.HasMany(x => x.CustomFields)
                .WithOne()
                .HasForeignKey(_ => _.EmployeeTableBuilderId);

            var savingComponent = modelBuilder.Entity<EmployeeSavingResult>();
            savingComponent.HasKey(_ => new { _.ComponentEmployeeId, _.RunId });
            savingComponent.HasMany(_ => _.Values)
                .WithOne()
                .HasForeignKey(_ => new { _.ComponentEmployeeId, _.RunId });

            var travelBusinessUsageComponent = modelBuilder.Entity<EmployeeTravelBusinessUsageResult>();
            travelBusinessUsageComponent.HasKey(x => new { x.ComponentEmployeeId, x.RunId });
            travelBusinessUsageComponent.HasMany(x => x.Values)
                .WithOne()
                .HasForeignKey(x => new { x.ComponentEmployeeId, x.RunId });

            modelBuilder.Entity<EmployeeTableBuilderBalance>();

            var models = Assembly.Load("PaySpace.Venuta.Data.Models");
            var enums = models.GetTypes().Where(type => !type.IsInterface && !type.IsAbstract && type.Name.StartsWith("Enum"));

            modelBuilder.Entity<CompanyCarBodyType>();

            foreach (var type in enums)
            {
                // Specific configuration for EnumCompanySettingType
                if (type == typeof(EnumCompanySettingType))
                {
                    BuildCompanySettingType(modelBuilder);
                }
                else
                {
                    modelBuilder.Entity(type);
                }
            }

            modelBuilder.Entity<EmployeeNumberHistory>();
            modelBuilder.Entity<PayCalendar>();

            modelBuilder.Entity<CompanyRunsPayCalendarsLink>().HasKey(_ => new { _.RunId, _.PayCalendarId });

            modelBuilder.Entity<CompanyRunResult>();

            modelBuilder.Entity<OrganizationPositionDetailResult>();

            ApplyConfigurations(modelBuilder);

            // TODO: Use efcore6 convention: https://stackoverflow.com/questions/43277154/entity-framework-core-setting-the-decimal-precision-and-scale-to-all-decimal-p
            foreach (var property in modelBuilder.Model.GetEntityTypes()
                .SelectMany(t => t.GetProperties())
                .Where(p => (p.ClrType == typeof(decimal) || p.ClrType == typeof(decimal?))
                            && p.GetColumnType() == null)) // Check if the column type is not already set
            {
                property.SetColumnType("decimal(18,6)");
            }
        }

        public static void BuildEmployeeInboxModel(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<EmployeeInbox>()
                .ToTable("EmployeeInbox", "dbo")
                .Property(_ => _.EffectiveDate).HasConversion(_ => _.LocalDateTime, _ => new DateTimeOffset(_, CultureData.DefaultTimezone.GetUtcOffset(_)));
        }

        public static void BuildCompanySettingType(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<EnumCompanySettingType>()
                .Property(_ => _.Options)
                .HasConversion(JsonValueConverter<List<CompanySettingOption>>.Default);
        }

        private sealed class ConstantValueGenerator<T> : ValueGenerator<T>
        {
            private readonly T value;

            public ConstantValueGenerator(T value)
            {
                this.value = value;
            }

            public override T Next(EntityEntry _) => this.value;

            public override bool GeneratesTemporaryValues => false;
        }

        private static void ApplyConfigurations(ModelBuilder modelBuilder)
        {
            var assemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(_ => _.FullName.StartsWith("PaySpace.Venuta"))
                .Where(_ => _.GetTypes().Any(_ => _.GetInterfaces().Any(_ => _.IsGenericType && _.GetGenericTypeDefinition() == typeof(IEntityTypeConfiguration<>))))
                .ToList();

            foreach (var assembly in assemblies)
            {
                modelBuilder.ApplyConfigurationsFromAssembly(assembly);
            }
        }
    }
}