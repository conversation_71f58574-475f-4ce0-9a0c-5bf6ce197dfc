namespace PaySpace.Venuta.Modules.Leave.Abstractions
{
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Employees;

    public interface IBrazilEmployeeLeaveBalanceService : IEmployeeLeaveBalanceService
    {
        Task<double?> GetDaysToSellAsync(
            long companyId,
            long employeeId,
            long frequencyId,
            long? companyLeaveSetupId,
            long runId,
            EmployeeHistoricConcession historicalConcession);

        Task<bool> AllowVacationDaysSellAsync(
            long companyId,
            long employeeId,
            long runId,
            long? companyLeaveSetupId,
            EmployeeHistoricConcession historicalConcession);
    }
}
