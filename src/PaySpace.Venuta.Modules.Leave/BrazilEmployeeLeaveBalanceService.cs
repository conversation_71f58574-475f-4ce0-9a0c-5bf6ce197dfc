namespace PaySpace.Venuta.Modules.Leave
{
    using System;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;

    public class BrazilEmployeeLeaveBalanceService : EmployeeLeaveBalanceService, IBrazilEmployeeLeaveBalanceService
    {
        private readonly ApplicationContext context;
        private readonly IBureauConfigService bureauConfigService;
        private readonly ICompanyService companyService;
        private readonly ICompanyRunService companyRunService;
        private readonly ICompanyLeaveService companyLeaveService;
        private readonly IEmployeeService employeeService;
        private readonly IEmployeeLeaveService employeeLeaveService;
        private readonly IEmploymentStatusService employmentStatusService;
        private readonly IEmployeePositionService employeePositionService;

        public BrazilEmployeeLeaveBalanceService(
            IStringLocalizer<EmployeeLeaveBalanceService> localizer,
            ApplicationContext context,
            ReadOnlyContext readOnlyContext,
            IBureauConfigService bureauConfigService,
            ICompanyService companyService,
            ICompanyRunService companyRunService,
            ICompanySettingService companySettingService,
            ICompanyLeaveService companyLeaveService,
            IEmployeeLeaveConcessionService employeeLeaveConcessionService,
            ICompanyFrequencyService companyFrequencyService,
            IDistributedCache distributedCache,
            IEmployeeService employeeService,
            IEmployeeLeaveService employeeLeaveService,
            IEmployeeLeaveSettingService employeeLeaveSettingService,
            IEmploymentStatusService employmentStatusService,
            IEmployeePositionService employeePositionService)
            : base(
                localizer,
                context,
                readOnlyContext,
                companySettingService,
                companyLeaveService,
                companyFrequencyService,
                distributedCache,
                employeeLeaveService,
                employeeLeaveConcessionService,
                employeeLeaveSettingService)
        {
            this.context = context;
            this.bureauConfigService = bureauConfigService;
            this.companyService = companyService;
            this.companyRunService = companyRunService;
            this.companyLeaveService = companyLeaveService;
            this.employeeService = employeeService;
            this.employeeLeaveService = employeeLeaveService;
            this.employmentStatusService = employmentStatusService;
            this.employeePositionService = employeePositionService;
        }

        public async Task<double?> GetDaysToSellAsync(
            long companyId,
            long employeeId,
            long frequencyId,
            long? companyLeaveSetupId,
            long runId,
            EmployeeHistoricConcession historicalConcession)
        {
            var companySchemeId = await this.employeeLeaveService.GetCompanySchemeIdAsync(employeeId, companyLeaveSetupId, DateTime.Today);
            if (!companySchemeId.HasValue)
            {
                return null;
            }

            var currentRun = await this.companyRunService.GetCompanyRuns(frequencyId).FirstOrDefaultAsync(_ => _.RunId == runId);

            var companyLeaveDetail = await this.companyLeaveService.GetLeaveSetupByDescriptionAsync(companySchemeId.Value, BrazilConstants.AcquisitionLeaveBucket, currentRun.PeriodCodeEndDate);
            if (companyLeaveDetail == null)
            {
                return null;
            }

            var (_, currentCycleStartDate) = await this.CalculateCurrentCycleDatesAsync(companyId, employeeId, currentRun, historicalConcession, companyLeaveDetail);
            var previousCycleEndDate = currentCycleStartDate.AddMonths(-1);

            double carryOverBalance;
            if (historicalConcession is null)
            {
                var lastRunInPreviousCycle = await this.companyRunService.GetMainRunByEffectiveDateAsync(frequencyId, previousCycleEndDate);
                if (lastRunInPreviousCycle == null)
                {
                    // Return current balance when in first year of employment
                    carryOverBalance = await this.GetLeaveBalanceForRunAsync(employeeId, currentRun.RunId, companyLeaveDetail.CompanyLeaveSetupId);
                }
                else
                {
                    carryOverBalance = await this.GetLeaveBalanceForRunAsync(employeeId, lastRunInPreviousCycle.RunId, companyLeaveDetail.CompanyLeaveSetupId);
                }
            }
            else
            {
                var employmentDate = await this.employmentStatusService.GetEmploymentDateAsync(employeeId);
                if (employmentDate == historicalConcession.ConcessionYearStartDate)
                {
                    carryOverBalance = (double)historicalConcession.RunningLeaveBalance;
                }
                else
                {
                    carryOverBalance = (double)historicalConcession.CarryOverLeaveBalance;
                }
            }

            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var rateString = await this.bureauConfigService.GetSettingValueAsync(taxCountryId, BrazilConstants.BureauConfigCodes.ThirdVac);
            if (double.TryParse(rateString, NumberStyles.Any, CultureInfo.InvariantCulture, out var rateValue))
            {
                return Math.Round(carryOverBalance * rateValue, 0, MidpointRounding.AwayFromZero);
            }

            return 0;
        }

        public async Task<bool> AllowVacationDaysSellAsync(long companyId, long employeeId, long runId, long? companyLeaveSetupId, EmployeeHistoricConcession historicalConcession)
        {
            var companySchemeId = await this.employeeLeaveService.GetCompanySchemeIdAsync(employeeId, companyLeaveSetupId, DateTime.Today);
            if (!companySchemeId.HasValue)
            {
                return false;
            }

            var frequencyId = await this.employeeService.GetEmployeeFrequencyIdAsync(employeeId);
            if (!frequencyId.HasValue)
            {
                return false;
            }

            var currentRun = await this.companyRunService.GetCompanyRuns(frequencyId.Value).FirstOrDefaultAsync(_ => _.RunId == runId);

            var companyLeaveDetail = await this.companyLeaveService.GetLeaveSetupByDescriptionAsync(companySchemeId.Value, BrazilConstants.AcquisitionLeaveBucket, currentRun.PeriodCodeEndDate);
            if (companyLeaveDetail == null)
            {
                return false;
            }

            var useCustomVacationSellOption = await this.GetVacationSellSettingAsync(employeeId);
            var (currentCycleEndDate, currentCycleStartDate) = await this.CalculateCurrentCycleDatesAsync(companyId, employeeId, currentRun, historicalConcession, companyLeaveDetail);

            return await this.CanSellVacationDaysAsync(
                employeeId,
                historicalConcession?.HistoricalConcessionId,
                currentCycleStartDate,
                currentCycleEndDate,
                useCustomVacationSellOption);
        }

        private async Task<(DateTime currentCycleEndDate, DateTime currentCycleStartDate)> CalculateCurrentCycleDatesAsync(
            long companyId,
            long employeeId,
            CompanyRun currentRun,
            EmployeeHistoricConcession historicalConcession,
            CompanyLeaveDetail companyLeaveDetail)
        {
            var currentCycleEndDate = historicalConcession?.ConcessionYearEndDate ?? await this.GetEffectiveDateAsync(companyId, employeeId, companyLeaveDetail, currentRun);
            var currentCycleStartDate = historicalConcession?.ConcessionYearStartDate ?? currentCycleEndDate.AddMonths(-11);
            return (currentCycleEndDate, currentCycleStartDate);
        }

        private async Task<bool> GetVacationSellSettingAsync(long employeeId)
        {
            var unionId = await this.employeePositionService.GetTableBuilderUnionIdAsync(employeeId, DateTime.Today);
            var useCustomVacationSellOption = false;
            if (unionId.HasValue)
            {
                useCustomVacationSellOption = await this.GetVacationSellOptionAsync(unionId.Value);
            }

            return useCustomVacationSellOption;
        }

        private async Task<bool> CanSellVacationDaysAsync(
            long employeeId,
            long? historicalConcessionId,
            DateTime currentCycleStartDate,
            DateTime currentCycleEndDate,
            bool useCustomVacationSellOption)
        {
            var applications = await this.context.Set<EmployeeLeaveAdjustment>()
                .Where(_ => _.EmployeeId == employeeId
                            && _.LeaveType == LeaveType.Annual
                            && _.LeaveEntryType == LeaveEntryType.LeaveApplication
                            && _.Status == LeaveStatus.Approved)
                .Where(_ => (historicalConcessionId != null && _.HistoricalConcessionId == historicalConcessionId)
                            || (_.HistoricalConcessionId == null
                                 && _.CompanyRun.PeriodStartDate >= currentCycleStartDate
                                 && _.CompanyRun.PeriodStartDate <= currentCycleEndDate
                                 && _.CompanyRun.PeriodEndDate >= currentCycleStartDate
                                 && _.CompanyRun.PeriodEndDate <= currentCycleEndDate))
                .Where(_ => !useCustomVacationSellOption || _.TotalDaysSell > 0)
                .Select(_ => _.LeaveAdjustmentId)
                .ToListAsync();

            if (applications.Count == 0)
            {
                return true;
            }

            return await this.context.Set<EmployeeLeaveAdjustment>()
                .Where(_ => _.EmployeeId == employeeId && _.LeaveEntryType == LeaveEntryType.Cancellation)
                .AnyAsync(_ => !applications.Contains(_.CancellationId ?? 0));
        }

        private Task<double> GetLeaveBalanceForRunAsync(long employeeId, long runId, long companyLeaveSetupId)
        {
            return this.context.Set<EmployeeLeaveDetail>()
                .TagWithSource()
                .Where(_ => _.CompanyRunId == runId && _.EmployeeId == employeeId && _.CompanyLeaveSetupId == companyLeaveSetupId)
                .Select(_ => _.LeaveBalance)
                .FirstOrDefaultAsync();
        }

        private async Task<bool> GetVacationSellOptionAsync(long unionId)
        {
            var vacationSetting = await this.context.Set<TableBuilder>()
                .Where(_ => _.TableBuilderId == unionId)
                .SelectMany(_ => _.CustomFields)
                .Where(_ => _.BureauCustomField.FieldCode == BrazilConstants.CustomFieldCodes.VacationSell)
                .Select(_ => _.Code)
                .FirstOrDefaultAsync();

            return vacationSetting?.Equals("02", StringComparison.OrdinalIgnoreCase) ?? false;
        }
    }
}
