namespace PaySpace.Venuta.Modules.Leave
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.Data.SqlClient;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions.Models;
    using PaySpace.Venuta.Services.Abstractions.Company;

    [DisplayName(SystemAreas.Leave.Balances)]
    public class EmployeeLeaveBalanceService : IEmployeeLeaveBalanceService
    {
        private readonly IStringLocalizer localizer;
        private readonly ApplicationContext context;
        private readonly ReadOnlyContext readOnlyContext;
        private readonly ICompanySettingService companySettingService;
        private readonly ICompanyLeaveService companyLeaveService;
        private readonly ICompanyFrequencyService companyFrequencyService;
        private readonly IDistributedCache distributedCache;
        private readonly IEmployeeLeaveService employeeLeaveService;
        private readonly IEmployeeLeaveConcessionService employeeLeaveConcessionService;
        private readonly IEmployeeLeaveSettingService employeeLeaveSettingService;

        public EmployeeLeaveBalanceService(
            IStringLocalizer<EmployeeLeaveBalanceService> localizer,
            ApplicationContext context,
            ReadOnlyContext readOnlyContext,
            ICompanySettingService companySettingService,
            ICompanyLeaveService companyLeaveService,
            ICompanyFrequencyService companyFrequencyService,
            IDistributedCache distributedCache,
            IEmployeeLeaveService employeeLeaveService,
            IEmployeeLeaveConcessionService employeeLeaveConcessionService,
            IEmployeeLeaveSettingService employeeLeaveSettingService)
        {
            this.localizer = localizer;
            this.context = context;
            this.readOnlyContext = readOnlyContext;
            this.companySettingService = companySettingService;
            this.companyLeaveService = companyLeaveService;
            this.companyFrequencyService = companyFrequencyService;
            this.distributedCache = distributedCache;
            this.employeeLeaveService = employeeLeaveService;
            this.employeeLeaveConcessionService = employeeLeaveConcessionService;
            this.employeeLeaveSettingService = employeeLeaveSettingService;
        }

        public IQueryable<EmployeeLeaveBalanceResult> GetLeaveBalances(long employeeId, long runId)
        {
            return this.context.Set<EmployeeLeaveDetail>()
                .Where(w => w.EmployeeId == employeeId && w.CompanyRunId == runId)
                .Select(s => new EmployeeLeaveBalanceResult
                {
                    CompanyLeaveSetupId = s.CompanyLeaveSetupId,
                    LeaveType = s.CompanyLeaveSetup.LeaveType,
                    LeaveTypeDescription = EnumResourceManager.GetEnumDescription<EnumLeaveType>(CultureInfo.CurrentCulture, (int)s.CompanyLeaveSetup.LeaveType, null),
                    LeaveDescription = s.CompanyLeaveSetup.LeaveDescription,
                    LeaveBalance = s.LeaveBalance,
                    Accrual = s.Accrual,
                    MaxBalance = s.MaxBalance,
                    YearsOfServiceApplicable = s.YearsOfServiceApplicable,
                    YearsOfService = s.YearsOfService,
                    ServiceLengthDescription = s.ServiceLengthDescription,
                    DaysDueToForfeit = s.DaysDueToForfeit,
                    DaysDueToCarryOver = s.DaysDueToCarryOver,
                    ExpiryOrCarryOverDate = s.ExpiryOrCarryOverDate,
                    ApplyGradeBands = s.CompanyLeaveSetup.LeaveDetails.Any(_ => _.ApplyGradeBands == true)
                })
                .OrderByDescending(o => o.CompanyLeaveSetupId);
        }

        public async Task<IList<EmployeeLeaveBalanceResult>> GetLeaveBalancesForUserAsync(
            long companyId,
            long employeeId,
            CompanyRun companyRun,
            bool employeeSelfService,
            bool hasLeaveValueAccess,
            bool hasLeaveValueCalculationAccess,
            bool hasDisplayEssLeaveBalanceAccess)
        {
            var balances = await this.readOnlyContext.Set<EmployeeLeaveDetail>()
                .TagWith("EmployeeLeaveBalanceService:LeaveBalancesForUser")
                .Where(_ => _.EmployeeId == employeeId && _.CompanyRunId == companyRun.RunId)
                .Select(_ => new EmployeeLeaveBalanceResult
                {
                    CompanyLeaveSetupId = _.CompanyLeaveSetupId,
                    LeaveType = _.CompanyLeaveSetup.LeaveType,
                    LeaveTypeDescription = EnumResourceManager.GetEnumDescription<EnumLeaveType>(CultureInfo.CurrentCulture, (int)_.CompanyLeaveSetup.LeaveType, null),
                    LeaveDescription = _.CompanyLeaveSetup.LeaveDescription,
                    BCOEAmount = _.BCOELeaveAmount,
                    DaysInPeriod = _.DaysInPeriod,
                    LeaveBalance = _.LeaveBalance,
                    Accrual = _.Accrual,
                    MaxBalance = _.MaxBalance,
                    YearsOfServiceApplicable = _.YearsOfServiceApplicable,
                    YearsOfService = _.YearsOfService,
                    ServiceLengthDescription = _.ServiceLengthDescription,
                    DaysDueToForfeit = _.DaysDueToForfeit,
                    DaysDueToCarryOver = _.DaysDueToCarryOver,
                    ExpiryOrCarryOverDate = _.ExpiryOrCarryOverDate,
                    ApplyGradeBands = _.CompanyLeaveSetup.LeaveDetails.Any(x => x.ApplyGradeBands == true),
                    LeaveCycleStartDate = _.LeaveCycleStartDate.HasValue ? _.LeaveCycleStartDate.Value.ToString("MMMM yyyy") : string.Empty,
                    OrderNumber = _.CompanyLeaveSetup.OrderNumber
                })
                .ToListAsync();

            balances.AddRange(await this.employeeLeaveConcessionService.GetEmployeeHistoricalConcessions(employeeId)
                .Where(_ => companyRun.PeriodCodeEndDate.AddYears(-2) >= _.ConcessionYearStartDate)
                .Select(_ => new EmployeeLeaveBalanceResult
                {
                    CompanyLeaveSetupId = _.CompanyLeaveSetupId,
                    LeaveType = LeaveType.Annual,
                    LeaveTypeDescription = this.localizer.GetString("lblConcessionHeader"),
                    LeaveDescription = this.localizer.GetString("lblConcession") + ": " + _.ConcessionYearStartDate.ToShortDateString() + " - " + _.ConcessionYearEndDate.ToShortDateString(),
                    LeaveBalance = (double)_.RunningLeaveBalance,
                    IsConcession = true,
                    LeaveCycleStartDate = _.ConcessionYearStartDate.ToString("MMMM yyyy")
                })
                .OrderByDescending(_ => _.CompanyLeaveSetupId)
                .ToListAsync());

            foreach (var balance in balances.ToList())
            {
                var leaveDetail = await this.companyLeaveService.GetLeaveSetupAsync(balance.CompanyLeaveSetupId, companyRun.PeriodCodeEndDate);
                if (leaveDetail == null)
                {
                    balances.Remove(balance);
                    continue;
                }

                balance.BucketRules = leaveDetail.BucketRules;

                if (balance.LeaveType != LeaveType.Annual && employeeSelfService && !hasDisplayEssLeaveBalanceAccess)
                {
                    if (leaveDetail.DisplayBalanceESS is null or false)
                    {
                        balances.Remove(balance);
                        continue;
                    }
                }

                // Only calculate and display leaveValue if the user has access and the leaveType is annual
                if (!balance.IsConcession && hasLeaveValueAccess && balance.LeaveType == LeaveType.Annual)
                {
                    balance.DisplayLeaveValue = true;
                    balance.DisplayLeaveValueCalculation = hasLeaveValueCalculationAccess;

                    balance.LeaveValue = leaveDetail.DoNotCalculateBceaValue == true
                        ? 0
                        : (balance.BCOEAmount / balance.DaysInPeriod) * Convert.ToDecimal(balance.LeaveBalance);

                    if (leaveDetail.LiabilityComponentId.HasValue)
                    {
                        balance.CalculationType = LeaveCalculationType.Custom;
                    }
                    else
                    {
                        balance.CalculationType = leaveDetail.DoNotCalculateBceaValue == true
                            ? LeaveCalculationType.DoNotCalculateBcoe
                            : LeaveCalculationType.Standard;
                    }
                }
                else
                {
                    // If the user does not have permission for leaveValue, clear these values to prevent hack vulnerability
                    // NB: leaveValue is already null since we haven't done the calculation
                    balance.BCOEAmount = null;
                    balance.DaysInPeriod = null;
                }

                // This needs to be at the end so that the LeaveValue calculation does not use the balance in hours for the equation.
                await this.ConvertHourlyBuckets(employeeId, companyRun, balance, leaveDetail);

                if (!balance.IsConcession && leaveDetail.CompanyLeaveSetupType == CompanyLeaveSetupType.NonAccumulative)
                {
                    var due = await this.GetDueToExpireAsync(companyId, employeeId, balance, leaveDetail, companyRun);
                    if (due.Type != ExpireType.None && due.Value >= 0)
                    {
                        balance.DueToExpire = due.Description;
                    }
                }
            }

            return balances;
        }

        public IQueryable<EmployeeLeaveAmountBreakdownResult> GetLeaveAmountBreakdowns(long employeeId, long runId)
        {
            return this.readOnlyContext.Set<EmployeeLeaveAmountBreakdown>()
                .TagWith("EmployeeLeaveBalanceService:LeaveAmountBreakdowns")
                .Where(w => w.EmployeeId == employeeId && w.RunId == runId)
                .OrderBy(o => o.ComponentEmployeeId)
                .Select(s => new EmployeeLeaveAmountBreakdownResult
                {
                    EmployeeLeaveAmountBreakdownId = s.EmployeeLeaveAmountBreakdownId,
                    Description = s.ComponentEmployeeId == SpecialComponentCodes.PayRateMinusCurrentInPackageContributionsComponentId
                        ? this.localizer.GetString("lblPayRateMinusInPackageContributions")
                        : s.ComponentEmployee.ComponentCompany.AliasDescription,
                    PeriodsAndAmounts = s.PeriodsAndAmounts,
                    PeriodsToAverageBy = s.DivideByCount,
                    Total = s.TotalAmount
                });
        }

        public async Task<IReadOnlyList<EmployeeLeaveBalanceBreakdownResult>> GetLeaveBalanceBreakdownsAsync(long employeeId, long frequencyId, long runId, LeaveType leaveType, long? bucketId, bool reflectInHours)
        {
            var companyRunResult = await this.readOnlyContext.Set<CompanyRun>()
                .TagWith("EmployeeLeaveBalanceService:LeaveBalanceBreakdowns")
                .Where(w => w.RunId == runId)
                .Select(s => new
                {
                    StartDate = s.PeriodStartDate,
                    s.PeriodCodeEndDate,
                    s.PeriodEndDate,
                    s.Status
                }).SingleAsync();

            var runFrequency = await this.companyFrequencyService.GetRunFrequencyAsync(frequencyId);

            var parameters = new[]
            {
                new SqlParameter("@EmployeeId", employeeId),
                new SqlParameter("@FrequencyId", frequencyId),
                new SqlParameter("@StartDate", companyRunResult.StartDate),
                new SqlParameter("@EndDate", companyRunResult.PeriodCodeEndDate),
                new SqlParameter("@BucketId", (object)bucketId ?? DBNull.Value),
                new SqlParameter("@LeaveType", leaveType),
                new SqlParameter("@RunId", runId),
                new SqlParameter("@IsMonthly", runFrequency == PayslipFrequency.Monthly)
            };

            // TODO: implement stored proc logic here
            var breakDowns = await this.readOnlyContext.Set<EmployeeLeaveBalanceBreakdownResult>()
                .FromSqlRaw("EXEC nextgen_sp_employee_leave_recon @EmployeeId, @FrequencyId, @StartDate, @EndDate, @BucketId, @LeaveType, @RunId, @IsMonthly", parameters)
                .TagWith("EmployeeLeaveBalanceService:LeaveBalanceBreakdowns")
                .ToListAsync();

            if (reflectInHours)
            {
                var effectiveDate = companyRunResult.Status == RunStatus.Open ? DateTime.Now : companyRunResult.PeriodEndDate;
                var payrateHours = await this.employeeLeaveSettingService.GetDefaultHoursPerDayAsync(employeeId, effectiveDate);
                payrateHours = payrateHours == 0 ? 1 : payrateHours;

                foreach (var breakDown in breakDowns)
                {
                    breakDown.Amount = breakDown.Amount * (decimal)payrateHours;
                }
            }

            return breakDowns;
        }

        public async Task<DateTime> GetEffectiveDateAsync(long companyId, long employeeId, CompanyLeaveDetail leaveDetail, CompanyRun companyRun)
        {
            var effectiveDate = DateTime.MinValue;
            switch (leaveDetail.EffectiveDateForfeit)
            {
                case SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.GroupJoinDate:
                    {
                        var groupJoinDate = await this.GetGroupJoinDateAsync(employeeId, companyRun.PeriodEndDate);
                        if (groupJoinDate.HasValue)
                        {
                            effectiveDate = groupJoinDate.GroupJoinDate;
                        }

                        if (groupJoinDate.AdditionalDate1.HasValue && await this.companySettingService.IsActiveAsync(companyId, "LSLDATE"))
                        {
                            effectiveDate = groupJoinDate.AdditionalDate1.Value;
                        }
                    }

                    break;

                case SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.EffectiveDateEmpLinked:
                    var employeeSetup = await this.employeeLeaveService.GetLeaveSetupAsync(employeeId, companyRun.PeriodCodeEndDate);
                    effectiveDate = employeeSetup.EffectiveDate;
                    break;

                case SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.SpecifyMonth:
                    if (leaveDetail.DropOffMonthId.HasValue)
                    {
                        effectiveDate = new DateTime(companyRun.PeriodCodeEndDate.Year, leaveDetail.DropOffMonthId.Value, 1);
                    }

                    break;

                case SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.EmploymentDate:
                    {
                        var employmentDate = await this.GetEmploymentDateAsync(employeeId, companyRun.PeriodEndDate);
                        if (employmentDate.HasValue)
                        {
                            effectiveDate = employmentDate.EmploymentDate;
                        }
                    }

                    break;
            }

            if (effectiveDate == DateTime.MinValue || leaveDetail.ForfeitPeriod < 1)
            {
                // Catering for bad data here, if leaveDetail.ForfeitPeriod is less than 1 then the while loop below will never end
                // and if effectiveDate == DateTime.MinValue then the loop will run for a long time
                return DateTime.MinValue;
            }

            var newEffectiveDate = effectiveDate;
            var periodCodeEndDate = companyRun.PeriodCodeEndDate;
            while (newEffectiveDate <= periodCodeEndDate)
            {
                switch (leaveDetail.LeaveForfeitPeriodId)
                {
                    case (int)LeaveForfeitPeriod.Year:
                        newEffectiveDate = newEffectiveDate.AddYears(leaveDetail.ForfeitPeriod!.Value);
                        break;

                    case (int)LeaveForfeitPeriod.Month:
                        newEffectiveDate = newEffectiveDate.AddMonths(leaveDetail.ForfeitPeriod!.Value);
                        break;

                    case (int)LeaveForfeitPeriod.Day:
                        newEffectiveDate = newEffectiveDate.AddDays(leaveDetail.ForfeitPeriod!.Value);
                        break;
                    default:
                        // LeaveForfeitPeriod must be year/month/day. it will never exit the loop if it is not any of those.
                        throw new InvalidOperationException();
                }
            }

            return newEffectiveDate.AddMonths(-1);
        }

        public async Task<(ExpireType Type, string Description, DateTime EffectiveDate, double? Value)> GetDueToExpireAsync(
            long companyId,
            long employeeId,
            EmployeeLeaveBalanceResult balance,
            CompanyLeaveDetail leaveDetail,
            CompanyRun companyRun)
        {
            if (leaveDetail.LeaveForfeitPeriodId == null || leaveDetail.LeaveForfeitPeriodId <= 0 || leaveDetail.EffectiveDateForfeit == null)
            {
                return (ExpireType.None, null, DateTime.MinValue, null);
            }

            DateTime? newEffectiveDate;
            var daysDueToCarryOver = (double?)balance.DaysDueToCarryOver;
            var daysDueToForfeit = balance.DaysDueToForfeit;

            if (balance.ExpiryOrCarryOverDate.HasValue)
            {
                newEffectiveDate = balance.ExpiryOrCarryOverDate.Value.AddMonths(-1);
            }
            else
            {
                newEffectiveDate = await this.GetEffectiveDateAsync(companyId, employeeId, leaveDetail, companyRun);

                if (leaveDetail.CarryOverDays.HasValue)
                {
                    daysDueToCarryOver = Math.Min(balance.LeaveBalance, (double)leaveDetail.CarryOverDays);
                    daysDueToForfeit = (decimal?)(balance.LeaveBalance >= (double?)leaveDetail.CarryOverDays
                                  ? balance.LeaveBalance - (double)leaveDetail.CarryOverDays
                                  : 0);
                }
                else
                {
                    daysDueToForfeit = (decimal?)balance.LeaveBalance;
                }
            }

            var newEffectiveDateStr = newEffectiveDate.Value.ToString("MMMM yyyy");

            if (leaveDetail.CarryOverDays.HasValue && leaveDetail.ForfeitCompanyLeaveSetupId.HasValue)
            {
                var forfeitBucket = await this.companyLeaveService.GetLeaveSetupAsync(leaveDetail.ForfeitCompanyLeaveSetupId, companyRun.PeriodCodeEndDate);
                if (forfeitBucket == null)
                {
                    return (ExpireType.None, null, DateTime.MinValue, null);
                }

                return (
                    ExpireType.Move,
                    this.localizer.GetString(
                        SystemAreas.Leave.Keys.Move,
                        daysDueToCarryOver,
                        forfeitBucket.CompanyLeaveSetup.LeaveDescription,
                        newEffectiveDateStr),
                    newEffectiveDate.Value,
                    daysDueToCarryOver);
            }

            var localizedDescription = this.localizer.GetString(SystemAreas.Leave.Keys.Expire, daysDueToForfeit, newEffectiveDateStr);
            if (balance.ReflectInHours)
            {
                var effectiveDate = companyRun.Status == RunStatus.Open ? DateTime.Now : companyRun.PeriodEndDate;
                var payrateHours = await this.employeeLeaveSettingService.GetDefaultHoursPerDayAsync(employeeId, effectiveDate);
                payrateHours = payrateHours == 0 ? 1 : payrateHours;

                daysDueToForfeit = daysDueToForfeit * (decimal)payrateHours;
                localizedDescription = this.localizer.GetString(SystemAreas.Leave.Keys.ExpireHours, daysDueToForfeit, newEffectiveDateStr);
            }

            return (
                ExpireType.Expire,
                localizedDescription,
                newEffectiveDate.Value,
                (double?)daysDueToForfeit);
        }

        public async Task<(IList<CompanyRun> Runs, CompanyRun Current)> GetCompanyRunsAsync(long employeeId, long frequencyId)
        {
            var periodDate = DateTime.Today.AddMonths(2);
            var periodCodes = new List<string>();

            var monthCount = 1;
            do
            {
                var periodCode = periodDate.ToString("yyyyM");
                periodCodes.Add(periodCode);

                monthCount += 1;
                periodDate = periodDate.AddMonths(-1);
            }
            while (monthCount <= 24);

            var runs = await this.context.Set<CompanyRun>().AsNoTracking()
                .Where(_ => _.Status == RunStatus.Open || _.Status == RunStatus.Closed)
                .Where(_ => _.CompanyFrequencyId == frequencyId && periodCodes.Contains(_.PeriodCode))
                .Where(_ => _.PayslipHeaders.Any(x => x.EmployeeId == employeeId))
                .OrderBy(_ => _.PeriodCode)
                    .ThenByDescending(_ => _.PeriodEndDate)
                        .ThenByDescending(_ => _.OrderNumber)
                .ToListAsync();

            var current = runs.FirstOrDefault(_ => _.PeriodStartDate <= DateTime.Today && _.PeriodEndDate >= DateTime.Today)
                ?? runs.OrderByDescending(_ => _.PeriodCodeEndDate).FirstOrDefault(); // No run found for the effective date. Get the latest run.

            return (runs, current);
        }

        public async Task<CompanyRun> GetCurrentRunAsync(long employeeId, long frequencyId, long? runId)
        {
            var runs = this.context.Set<CompanyRun>().AsNoTracking()
                            .Where(_ => _.Status == RunStatus.Open || _.Status == RunStatus.Closed)
                            .Where(_ => _.CompanyFrequencyId == frequencyId)
                            .Where(_ => _.PayslipHeaders.Any(x => x.EmployeeId == employeeId))
                            .OrderBy(_ => _.PeriodCode)
                                .ThenByDescending(_ => _.PeriodEndDate)
                                    .ThenByDescending(_ => _.OrderNumber);

            if (runId.HasValue)
            {
                return await runs.FirstOrDefaultAsync(_ => _.RunId == runId);
            }

            return await runs.FirstOrDefaultAsync(_ => _.PeriodStartDate <= DateTime.Today && _.PeriodEndDate >= DateTime.Today)
                ?? await runs.OrderByDescending(_ => _.PeriodEndDate).FirstOrDefaultAsync(); // No run found for the effective date. Get the latest run.
        }

        public Task<bool> LeaveBalanceReflectsInHoursAsync(long employeeId, long runId)
        {
            return this.context.Set<EmployeeLeaveDetail>()
                .Where(_ => _.EmployeeId == employeeId && _.CompanyRunId == runId && _.CompanyLeaveSetup.LeaveType == LeaveType.Annual)
                .Select(_ => _.CompanyLeaveSetup.LeaveDetails
                    .Where(x => x.EffectiveDate <= DateTime.Today)
                    .OrderByDescending(x => x.EffectiveDate)
                    .Select(x => x.LeaveAccrualValueId == (int)LeaveAccrualValue.Hours)
                    .FirstOrDefault())
                .FirstOrDefaultAsync();
        }

        public Task<string> GetAnnualLeaveExpiryAsync(long companyId, long employeeId, long frequencyId)
        {
            return this.distributedCache.GetOrCreateAsync($"Employee:{employeeId}:Dashboard:LeaveExpiry:", async () => await this.GetAnnualLeaveExpiry(companyId, employeeId, frequencyId));
        }

        public Task<bool> ShowLeaveDaysOptionAsync(long employeeId, long runId)
        {
            return this.readOnlyContext.Set<EmployeeLeaveDetail>()
                 .TagWith("EmployeeLeaveBalanceService:ShouldShowBalanceUnitsAsync")
                 .Where(_ => _.EmployeeId == employeeId && _.CompanyRunId == runId && _.CompanyLeaveSetup.LeaveDetails
                     .Any(_ => _.ReflectInHours.HasValue && (bool)_.ReflectInHours))
                 .AnyAsync();
        }

        public Task<(string SchemeName, DateTime EffectiveDate)> GetEmployeeLeaveSchemeInfoAsync(long employeeId, DateTime effectiveDate)
        {
            return this.readOnlyContext.Set<EmployeeLeaveSetup>().TagWithSource()
                .Where(_ => _.EmployeeId == employeeId &&
                            _.CompanyLeaveScheme.CompanyLeaveSchemeId == _.CompanyLeaveSchemeId &&
                            _.EffectiveDate <= effectiveDate)
                .OrderByDescending(_ => _.EffectiveDate)
                .Select(_ => ValueTuple.Create(_.CompanyLeaveScheme.SchemeName, _.EffectiveDate))
                .FirstOrDefaultAsync();
        }

        private async Task<string> GetAnnualLeaveExpiry(long companyId, long employeeId, long frequencyId)
        {
            var daysValue = await this.companySettingService.GetSettingValueAsync(companyId, "LVEFORFEITPRD");
            var days = this.ConvertDaysToInt(daysValue);

            var runs = await this.GetCompanyRunsAsync(employeeId, frequencyId);
            if (runs.Current == null)
            {
                return null;
            }

            var expires = new List<LeaveExpiry>();

            var balances = await this.GetLeaveBalances(employeeId, runs.Current.RunId).Where(_ => _.LeaveType == LeaveType.Annual).ToListAsync();
            foreach (var balance in balances)
            {
                var leaveDetail = await this.companyLeaveService.GetLeaveSetupAsync(balance.CompanyLeaveSetupId, runs.Current.PeriodCodeEndDate);
                if (leaveDetail == null)
                {
                    continue;
                }

                if (leaveDetail.CompanyLeaveSetupType == CompanyLeaveSetupType.NonAccumulative)
                {
                    var due = await this.GetDueToExpireAsync(companyId, employeeId, balance, leaveDetail, runs.Current);
                    if (due.Type == ExpireType.Expire && due.Value > 0 && DateHelper.ToLastDayOfMonth(due.EffectiveDate) - DateTime.Today < TimeSpan.FromDays(days))
                    {
                        // Check if leave is expiring within the specified days.
                        var index = expires.FindIndex(_ => _.EffectiveDate == due.EffectiveDate);
                        if (index >= 0)
                        {
                            expires[index].Value += due.Value.Value;
                        }
                        else
                        {
                            expires.Add(new LeaveExpiry
                            {
                                EffectiveDate = due.EffectiveDate,
                                Value = due.Value.Value
                            });
                        }
                    }
                }
            }

            return expires.OrderBy(_ => _.EffectiveDate).Select(_ => this.localizer.GetString(SystemAreas.Leave.Keys.Expire, _.Value, _.EffectiveDate.ToString("MMMM yyyy"))).FirstOrDefault();
        }

        private Task<(bool HasValue, DateTime GroupJoinDate, DateTime? AdditionalDate1)> GetGroupJoinDateAsync(long employeeId, DateTime effectiveDate)
        {
            return this.context.Set<EmployeeEmploymentStatus>()
                .Where(_ => _.EmployeeId == employeeId && _.EmploymentDate <= effectiveDate)
                .OrderByDescending(_ => _.EmploymentDate)
                .Select(_ => ValueTuple.Create(true, _.GroupJoinDate, _.AdditionalDate1))
                .FirstOrDefaultAsync();
        }

        private Task<(bool HasValue, DateTime EmploymentDate)> GetEmploymentDateAsync(long employeeId, DateTime effectiveDate)
        {
            return this.context.Set<EmployeeEmploymentStatus>()
                .Where(_ => _.EmployeeId == employeeId && _.EmploymentDate <= effectiveDate)
                .OrderByDescending(_ => _.EmploymentDate)
                .Select(_ => ValueTuple.Create(true, _.EmploymentDate))
                .FirstOrDefaultAsync();
        }

        private async Task ConvertHourlyBuckets(long employeeId, CompanyRun companyRun, EmployeeLeaveBalanceResult balance, CompanyLeaveDetail leaveDetail)
        {
            if (leaveDetail.ReflectInHours.HasValue && (bool)leaveDetail.ReflectInHours)
            {
                var effectiveDate = companyRun.Status == RunStatus.Open ? DateTime.Now : companyRun.PeriodEndDate;
                var payrateHours = await this.employeeLeaveSettingService.GetDefaultHoursPerDayAsync(employeeId, effectiveDate);
                payrateHours = payrateHours == 0 ? 1 : payrateHours;

                balance.ReflectInHours = true;
                balance.LeaveBalanceInDays = balance.LeaveBalance;
                balance.LeaveBalance = balance.LeaveBalance * payrateHours;
                balance.MaxBalance = balance.MaxBalance * (decimal)payrateHours;
                balance.Accrual = balance.Accrual * (decimal)payrateHours;
                balance.BalanceUnit = this.localizer.GetString(SystemAreas.Leave.Keys.LeaveHours);
            }
            else
            {
                balance.ReflectInHours = false;
                balance.BalanceUnit = this.localizer.GetString(SystemAreas.Leave.Keys.LeaveDays);
            }
        }

        private int ConvertDaysToInt(string daysValue)
        {
            daysValue = daysValue?.ToLower().Replace("days", string.Empty).Replace("day", string.Empty).Trim();
            if (!string.IsNullOrEmpty(daysValue) && int.TryParse(daysValue, out int parsedDays))
            {
                return parsedDays;
            }
            else
            {
                return 60;
            }
        }

        private class LeaveExpiry
        {
            public DateTime EffectiveDate { get; set; }

            public double Value { get; set; }
        }
    }
}