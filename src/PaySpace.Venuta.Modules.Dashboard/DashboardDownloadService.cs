namespace PaySpace.Venuta.Modules.Dashboard.Excel.Services
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using DevExpress.Spreadsheet;

    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging.Abstractions.Messages;
    using PaySpace.Venuta.Modules.Dashboard.Extensions;
    using PaySpace.Venuta.Modules.Dashboard.Messages;

    public interface IDashboardDownloadService
    {
        Task<byte[]> BuildExcelPackageAsync<TEntity>(CompanyDashboardMessage context, IList<TEntity> data);
    }

    [DisplayName(SystemAreas.BulkUpload.Area)]
    public class DashboardDownloadService : IDashboardDownloadService
    {
        private readonly IModelMetadataProvider metadataProvider;

        public DashboardDownloadService(IModelMetadataProvider metadataProvider)
        {
            this.metadataProvider = metadataProvider;
        }

        public async Task<byte[]> BuildExcelPackageAsync<TEntity>(CompanyDashboardMessage context, IList<TEntity> data)
        {
            using (Workbook workbook = new Workbook())
            {
                workbook.Unit = DevExpress.Office.DocumentUnit.Point;

                try
                {
                    workbook.BeginUpdate();

                    var activeWorksheet = workbook.Worksheets[0];
                    activeWorksheet.Name = this.GetSheetName<TEntity>();

                    await this.AddDataToWorkSheet(context, activeWorksheet, data);
                }
                finally
                {
                    workbook.EndUpdate();
                }

                return await workbook.SaveDocumentAsync(DocumentFormat.OpenXml);
            }
        }

        protected async Task AddDataToWorkSheet<TEntity>(CompanyDashboardMessage context, Worksheet worksheet, IList<TEntity> data)
        {
            await this.BuildHeader<TEntity>(worksheet);
            await this.AddDataToWorksheet(worksheet, data);

            worksheet.CreateTableUsedRange();
            this.AddSummary(context, worksheet);
        }

        protected void AddSummary(CompanyDashboardMessage context, Worksheet worksheet)
        {
            worksheet.Rows.Insert(0);
            worksheet.Cells[0, 0].AddHeaderText(context.Title, ExcelWorksheetExtensions.DefaultFontColor, ExcelWorksheetExtensions.DefaultFillColor);
            worksheet.MergeCells(worksheet.Range["A1:D1"]);

            worksheet.Rows.Insert(1);
            worksheet.Cells[1, 0].AddBodyDefaultText($"Company : {context.CompanyName}");
            worksheet.MergeCells(worksheet.Range["A2:D2"]);

            worksheet.Rows.Insert(2);
            worksheet.Cells[2, 0].AddBodyDefaultText($"Month : {context.Period.ToString("MMMM yyyy")}");
            worksheet.MergeCells(worksheet.Range["A3:D3"]);

            worksheet.Rows.Insert(3);
            worksheet.Cells[3, 0].AddBodyDefaultText($"Run on : {DateTime.Now}");
            worksheet.MergeCells(worksheet.Range["A4:D4"]);
        }

        protected Task<IList<ModelMetadata>> GetPropertiesAsync<TEntity>()
        {
            var properties = this.metadataProvider.GetMetadataForProperties(typeof(TEntity)).ToList();
            return Task.FromResult(properties.Order());
        }

        protected string GetSheetName<TEntity>()
        {
            return ModelHelper.GetEntityType<TEntity>().Name;
        }

        private async Task BuildHeader<TEntity>(Worksheet worksheet)
        {
            var properties = await this.GetPropertiesAsync<TEntity>();

            var columnIndex = -1;
            foreach (var metadata in properties)
            {
                if (metadata.IsKey())
                {
                    continue;
                }

                columnIndex = metadata.GetAttributes<System.ComponentModel.DataAnnotations.DisplayAttribute>().FirstOrDefault()?.Order ?? ModelMetadata.DefaultOrder;

                if (columnIndex == ModelMetadata.DefaultOrder)
                {
                    continue;
                }

                var cleanName = metadata.Name.SanitizeHeaderName();

                var refersTo = $"={worksheet.Name}!{columnIndex.GetColumnAddress()}";
                worksheet.DefinedNames.Add(cleanName, refersTo);
                worksheet[1, columnIndex].SetValue(metadata.GetColumnDisplayName());

                worksheet.Cells[1, columnIndex].Font.Color = ExcelWorksheetExtensions.DefaultHeaderFontColor;
                worksheet.Cells[1, columnIndex].Font.Bold = true;

                worksheet.Cells[1, columnIndex].AddColor(ExcelWorksheetExtensions.DefaultHeaderBackground);

                if (metadata.IsKey())
                {
                    worksheet.Cells[1, columnIndex].FillColor = ExcelWorksheetExtensions.RestrictedHeaderBackground;
                }
                else
                {
                    worksheet.Cells[1, columnIndex].Style.Font.Color = ExcelWorksheetExtensions.DefaultHeaderFontColor;
                }
            }
        }

        private async Task AddDataToWorksheet<TEntity>(Worksheet worksheet, IList<TEntity> data)
        {
            var properties = await this.GetPropertiesAsync<TEntity>();
            foreach (var metadata in properties)
            {
                if (metadata.IsKey())
                {
                    continue;
                }

                var columnIndex = metadata.GetAttributes<System.ComponentModel.DataAnnotations.DisplayAttribute>().FirstOrDefault()?.Order ?? ModelMetadata.DefaultOrder;

                if (columnIndex == ModelMetadata.DefaultOrder)
                {
                    continue;
                }

                worksheet.AddGeneral(data, columnIndex, metadata);
            }
        }
    }
}