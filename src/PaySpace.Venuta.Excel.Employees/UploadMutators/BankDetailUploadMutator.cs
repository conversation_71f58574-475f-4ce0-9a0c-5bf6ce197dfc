namespace PaySpace.Venuta.Excel.Employees.UploadMutators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache.Distributed;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Storage;

    using RedLockNet;

    public class BankDetailUploadMutator : BulkUploadMutator<EmployeeBankDetailDto, EmployeeBankDetail, long>
    {
        private readonly IMapper mapper;
        private readonly ApplicationContext applicationContext;
        private readonly IEmployeeBankDetailService employeeBankDetailService;
        private readonly IStringLocalizer<EmployeeBankDetail> localizer;
        private readonly ICalcSchedulingService calcSchedulingService;
        private readonly IDistributedLockFactory lockFactory;
        private readonly ICompanyService companyService;
        private readonly IEnumService enumService;

        public BankDetailUploadMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IAttachmentStorageService attachmentService,
            IEntityValidator<EmployeeBankDetailDto, EmployeeBankDetail> entityValidator,
            IEmployeeBankDetailService employeeBankDetailService,
            IStringLocalizer<EmployeeBankDetail> localizer,
            ICalcSchedulingService calcSchedulingService,
            IDistributedLockFactory lockFactory,
            ICompanyService companyService,
            IEnumService enumService)
            : base(mapper, applicationContext, attachmentService, entityValidator)
        {
            this.mapper = mapper;
            this.applicationContext = applicationContext;
            this.employeeBankDetailService = employeeBankDetailService;
            this.localizer = localizer;
            this.calcSchedulingService = calcSchedulingService;
            this.lockFactory = lockFactory;
            this.companyService = companyService;
            this.enumService = enumService;
        }

        protected override Task<EmployeeBankDetailDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set.Where(_ => _.BankDetailId == key && _.EmployeeBankHeader.Employee.CompanyId == context.CompanyId)
                .ProjectTo<EmployeeBankDetailDto>(this.mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
        }

        protected override Task<EmployeeBankDetail?> FindAsync(MutatorContext context, long key)
        {
            return this.Set.FirstOrDefaultAsync(_ => _.BankDetailId == key && _.EmployeeBankHeader.Employee.CompanyId == context.CompanyId);
        }

        protected override async Task BeforeValidateAsync(MutatorContext context, EmployeeBankDetailDto dto, EmployeeBankDetail entity)
        {
            var bankdetails = await this.EnsureMainAccountAsync(entity);
            var isSplitModified = this.IsFieldModified(entity.EmployeeBankHeader, nameof(EmployeeBankHeader.SplitType));
            entity.BankAccountOwner = entity.BankAccountOwner.HasValue && entity.BankAccountOwner != default(long) ? entity.BankAccountOwner : BankAccountOwnerType.Own;

            // If EmployeeBankHeaderId is default at this point, then it is the first record being inserted.
            if (entity.EmployeeBankHeaderId == default)
            {
                entity.IsMainAccount = true;
                entity.Amount = this.GetInitialAmount(context, entity);
            }
            else if (dto.SplitType == BankDetailSplitType.Component || (entity.IsMainAccount && dto.SplitType == BankDetailSplitType.Amount))
            {
                // Spit type of component or if it's amount on the main account then we need to have amount set to 0.
                entity.Amount = 0;
            }
            else if (entity.IsMainAccount && dto.SplitType == BankDetailSplitType.Percentage)
            {
                this.SetAccountPercentages(entity, isSplitModified, bankdetails);
            }
            else if (!entity.IsMainAccount && isSplitModified && dto.SplitType == BankDetailSplitType.Percentage)
            {
                this.SetMainAccountPercentage(entity, bankdetails);
            }
            else if (entity.EmployeeBankHeaderId != default && !entity.IsMainAccount && (isSplitModified && dto.SplitType == BankDetailSplitType.Amount))
            {
                this.SetMainAccountAmount(bankdetails);
            }

            // If it is the first bank details created for the employee or you only have one detail then the split type must be percent.
            if ((entity.EmployeeBankHeader.EmployeeBankHeaderId == default && !context.TryGetValue<bool>("HasRelatedRows", out _))
                || (entity.BankDetailId != default && (await this.employeeBankDetailService.GetBankDetails(entity.EmployeeBankHeader.EmployeeId).CountAsync()) == 1))
            {
                entity.EmployeeBankHeader.SplitType = BankDetailSplitType.Percentage;
            }

            // These fields cannot be set if split type is not component nor on the main account.
            if (entity.EmployeeBankHeader.SplitType != BankDetailSplitType.Component || entity.IsMainAccount)
            {
                entity.CurrencyId = default;
                entity.ComponentCompanyId = default;
            }

            context.TryAdd("BankHeaderId", entity.EmployeeBankHeaderId);

            if (context.TryGetValue<bool>("HasRelatedRows", out _)
                && entity.EmployeeBankHeader.SplitType == BankDetailSplitType.Percentage
                && context.TryGetValue<IList<EmployeeBankDetailDto>>("RelatedRows", out var allRows))
            {
                // the excel file has multiple rows, we should then take the amount set in file, and not default to 100.
                var relatedRows = allRows.Where(_ => !entity.BankAccountNo.Equals(_.BankAccountNo, StringComparison.InvariantCultureIgnoreCase));
                context.TryAdd("RelatedDetails", relatedRows.Select(_ => new ValueTuple<long, decimal?>(_.BankDetailId, _.Amount)).ToList());
            }
        }

        protected override async Task AddAsync(MutatorContext context, long? employeeId, EmployeeBankDetail entity)
        {
            var key = CacheKeys.CreateBankDetail(employeeId.Value);

            try
            {
                await this.lockFactory.TryUpdateWithLockAsync(key, async () => await this.AddBankDetailAsync(context, employeeId, entity));
            }
            catch (LockTimedOutException)
            {
                throw new InvalidOperationException("Error creating record, a Post for this record is already in progress.");
            }
        }

        protected override async Task AfterMapAsync(MutatorContext context, EmployeeBankDetailDto dto, EmployeeBankDetail entity)
        {
            // With Uploads EF Core + Automapper causes the navigation property EmployeeBankHeader not to be tracked properly, we need to set the bank header after the mapping.
            // this error mostly happens when there are duplicate bank details in a excel file.
            // This logic needs to be in the API mutator and upload mutator becasue of the change in the mapping profile
            if (entity.EmployeeBankHeader == null)
            {
                if (entity.EmployeeBankHeaderId > 0)
                {
                    entity.EmployeeBankHeader = await this.employeeBankDetailService.GetBankHeaderAsync(entity.EmployeeBankHeaderId);
                }
                else
                {
                    entity.EmployeeBankHeader = new EmployeeBankHeader();
                    entity.EmployeeBankHeader.EmployeeId = dto.EmployeeId;
                }
            }

            await this.SetPaymentMethodAsync(context, dto, entity);
            entity.EmployeeBankHeader.SplitType = dto.SplitType ?? BankDetailSplitType.Percentage;
            entity.EmployeeId = dto.EmployeeId;

            await base.AfterMapAsync(context, dto, entity);
        }

        protected override Task UpdateAsync(MutatorContext context, long key, EmployeeBankDetail entity)
        {
            return this.employeeBankDetailService.UpdateAsync(entity, context.CompanyId);
        }

        protected override async Task DeleteAsync(MutatorContext context, long? employeeId, EmployeeBankDetail entity)
        {
            await this.DefaultAmountAfterDeleteAsync(entity);
            await base.DeleteAsync(context, employeeId, entity);
        }

        protected override Task AfterAddAsync(MutatorContext context, EmployeeBankDetail entity, EmployeeBankDetailDto dto)
        {
            return this.RecalculatePaysplipAsync(entity);
        }

        protected override Task AfterDeleteAsync(MutatorContext context, EmployeeBankDetail entity)
        {
            return this.RecalculatePaysplipAsync(entity);
        }

        protected override Task AfterUpdateAsync(MutatorContext context, EmployeeBankDetail entity, EmployeeBankDetailDto dto)
        {
            return this.RecalculatePaysplipAsync(entity);
        }

        protected override async Task<EntityValidationResult> ValidateAsync(
            MutatorContext context,
            EmployeeBankDetailDto dto,
            EmployeeBankDetail entity,
            string ruleSet,
            CancellationToken cancellationToken = default)
        {
            var validationResult = await base.ValidateAsync(context, dto, entity, ruleSet, cancellationToken);

            if (entity.EmployeeBankHeaderId == default && entity.EmployeeBankHeader.SplitType != BankDetailSplitType.Percentage && !context.TryGetValue<bool>("HasRelatedRows", out _))
            {
                validationResult.Errors.Add(new ErrorCodeValidationResult(nameof(entity.EmployeeBankHeader.SplitType)) { ErrorMessage = this.localizer.GetString("lblSplitPercentageOnFirstPost") });
            }

            return new EntityValidationResult(!validationResult.Errors.Any(), validationResult.Errors);
        }

        private decimal GetInitialAmount(MutatorContext context, EmployeeBankDetail entity)
        {
            if (entity.EmployeeBankHeaderId != default)
            {
                return entity.Amount;
            }

            if (context.TryGetValue<bool>("HasRelatedRows", out _))
            {
                if (entity.EmployeeBankHeader.SplitType != BankDetailSplitType.Percentage)
                {
                    return 0;
                }

                // the excel file has multiple rows, we should then take the amount set in file, and not default to 100.
                if (context.TryGetValue<IList<EmployeeBankDetailDto>>("RelatedRows", out var allRows))
                {
                    var relatedRows = allRows.Where(_ => !entity.BankAccountNo.Equals(_.BankAccountNo, StringComparison.InvariantCultureIgnoreCase));

                    return 100 - relatedRows.Sum(_ => _.Amount ?? 0);
                }
            }

            return 100;
        }

        private async Task<IList<EmployeeBankDetail>> EnsureMainAccountAsync(EmployeeBankDetail entity)
        {
            if (entity.EmployeeBankHeaderId != default && !entity.IsMainAccount)
            {
                var bankDetails = await this.employeeBankDetailService.GetBankDetailsByHeader(entity.EmployeeBankHeaderId).AsTracking().ToListAsync();
                if (bankDetails.Count > 0 && !bankDetails.Any(_ => _.IsMainAccount))
                {
                    // Ensure that the 1st account is the main account if there is no main accounts
                    bankDetails.First().IsMainAccount = true;
                }
                else if (bankDetails.Count == 0)
                {
                    entity.IsMainAccount = true;
                    entity.Amount = 100;
                }

                return bankDetails;
            }

            return Array.Empty<EmployeeBankDetail>();
        }

        private void SetMainAccountPercentage(EmployeeBankDetail entity, IList<EmployeeBankDetail> details)
        {
            var mainAccount = details.FirstOrDefault(_ => _.IsMainAccount == true);
            if (mainAccount != null)
            {
                var sumOtherValues = details.Where(_ => _.IsMainAccount == false).Sum(_ => _.Amount);

                // BankDetailId in model does not exist in the DB, so add it to the sumOtherValues for the calculation.
                if (!details.Any(_ => _.BankDetailId == entity.BankDetailId))
                {
                    sumOtherValues += entity.Amount;
                }

                // if sumOtherValues is smaller than 100, then subtract it from the MainAccounts' Amount, else 0
                var newMainAmount = sumOtherValues < 100 ? 100 - sumOtherValues : 0;
                if (newMainAmount > 0)
                {
                    mainAccount.Amount = newMainAmount;
                }
            }
        }

        // This is to make sure that if your split is by amount that the MainAccount is set to 0 if there are multiple EFT records.
        private void SetAccountPercentages(EmployeeBankDetail entity, bool isSplitModified, IList<EmployeeBankDetail> details)
        {
            var entry = this.applicationContext.Entry(entity);

            if (isSplitModified || (entry.State == EntityState.Modified && entry.Property(nameof(EmployeeBankDetail.Amount)).IsModified))
            {
                // Update second account only if split is being changed to percent on main and the amount is being specified. This should only take place if you have 2 accounts.
                if (details.Count == 2)
                {
                    var alternativeAccount = details.FirstOrDefault(_ => !_.IsMainAccount);
                    if (alternativeAccount != null)
                    {
                        var mainAmount = details.Where(_ => _.IsMainAccount).Select(_ => _.Amount).FirstOrDefault();

                        // if sumOtherValues is smaller than 100, then subtract it from the MainAccounts' Amount, else 0
                        alternativeAccount.Amount = mainAmount < 100 ? 100 - mainAmount : 0;
                    }
                }
                else if (isSplitModified)
                {
                    if (details.Count == 0)
                    {
                        entity.Amount = 100;
                    }
                    else
                    {
                        var sumOtherValues = details.Where(_ => _.IsMainAccount == false).Sum(_ => _.Amount);

                        // BankDetailId in model does not exist in the DB, so add it to the sumOtherValues for the calculation.
                        if (!details.Any(_ => _.BankDetailId == entity.BankDetailId))
                        {
                            sumOtherValues += entity.Amount;
                        }

                        // if sumOtherValues is smaller than 100, then subtract it from the MainAccounts' Amount, else 0
                        var amount = sumOtherValues < 100 ? 100 - sumOtherValues : 0;

                        entity.Amount = amount;
                    }
                }
            }
        }

        private void SetMainAccountAmount(IList<EmployeeBankDetail> bankDetails)
        {
            var mainAccount = bankDetails.FirstOrDefault(_ => _.IsMainAccount == true);
            if (mainAccount != null)
            {
                mainAccount.Amount = 0;
            }
        }

        private bool IsFieldModified(EmployeeBankHeader entity, string fieldName)
        {
            var entry = this.applicationContext.Entry(entity);
            if (entry.State == EntityState.Detached)
            {
                return true;
            }

            if (entry.State != EntityState.Modified)
            {
                return false;
            }

            if (entry.Property(fieldName).IsModified)
            {
                return true;
            }

            return false;
        }

        private Task RecalculatePaysplipAsync(EmployeeBankDetail entity)
        {
            return this.calcSchedulingService.RecalculatePayslip(entity.EmployeeBankHeader.EmployeeId, CalcSource.BankDetails);
        }

        private async Task DefaultAmountAfterDeleteAsync(EmployeeBankDetail model)
        {
            if (model.EmployeeBankHeaderId != default)
            {
                // If only 2 record exists then deleting 1 will leave 1 record remaining.
                var employeeBankDetails = await this.employeeBankDetailService.GetBankDetailsByHeader(model.EmployeeBankHeaderId).AsTracking().Include(_ => _.EmployeeBankHeader).ToListAsync();
                if (employeeBankDetails.Count == 2)
                {
                    // If the SplitType of the remaining record is not Amount, then default back to 100.
                    var remainingAccount = employeeBankDetails.FirstOrDefault(_ => _.BankDetailId != model.BankDetailId);
                    if (remainingAccount != null)
                    {
                        remainingAccount.Amount = 100;
                        remainingAccount.EmployeeBankHeader.SplitType = BankDetailSplitType.Percentage;
                    }
                }
            }
        }

        private async Task AddBankDetailAsync(MutatorContext context, long? employeeId, EmployeeBankDetail entity)
        {
            if (!await this.employeeBankDetailService.HasBankHeaderAsync(employeeId!.Value))
            {
                await this.employeeBankDetailService.AddAsync(entity);
                return;
            }

            var header = await this.employeeBankDetailService.GetHeaderByEmployeeIdAsync(employeeId.Value);
            var bankingDetail = await this.employeeBankDetailService.GetSingleBankingDetailOrDefaultAsync(employeeId.Value, entity.EmployeeBankHeaderId);

            // Add the new account if more than one banking detail account exists
            if (header.PaymentMethod is PaymentMethod.EFT or PaymentMethod.PIX || bankingDetail is null)
            {
                await this.employeeBankDetailService.AddAsync(entity);
                return;
            }

            this.mapper.Map(entity.EmployeeBankHeader, header);
            this.mapper.Map(entity, bankingDetail);

            entity.BankDetailId = bankingDetail.BankDetailId;
            await this.UpdateAsync(context, bankingDetail.BankDetailId, bankingDetail);
        }

        private async Task SetPaymentMethodAsync(MutatorContext context, EmployeeBankDetailDto dto, EmployeeBankDetail entity)
        {
            var countryId = await this.companyService.GetTaxCountryIdAsync(context.CompanyId);
            var paymentMethod = this.enumService.GetEmployeePaymentMethods(countryId).FirstOrDefault(_ => _.PayMethodDescription == dto.PaymentMethod);
            if (paymentMethod != null)
            {
                entity.EmployeeBankHeader.PaymentMethod = (PaymentMethod)paymentMethod.PayMethodId;
                dto.PaymentMethodId = (PaymentMethod)paymentMethod.PayMethodId;
            }
        }
    }
}