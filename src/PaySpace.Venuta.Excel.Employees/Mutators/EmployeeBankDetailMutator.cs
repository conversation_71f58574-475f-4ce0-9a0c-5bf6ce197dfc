namespace PaySpace.Venuta.Excel.Employees.Mutators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache.Distributed;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Storage;

    using RedLockNet;

    internal sealed class EmployeeBankDetailMutator : EntityMutator<EmployeeBankDetailDto, EmployeeBankDetail, long>
    {
        private readonly IEmployeeBankDetailService employeeBankDetailService;
        private readonly IStringLocalizer localizer;
        private readonly ICompanyService companyService;
        private readonly IEnumService enumService;
        private readonly ICalcSchedulingService calcSchedulingService;
        private readonly IMapper mapper;
        private readonly ApplicationContext applicationContext;
        private readonly IDistributedLockFactory lockFactory;

        public EmployeeBankDetailMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IDistributedLockFactory lockFactory,
            IEntityValidator<EmployeeBankDetailDto, EmployeeBankDetail> entityValidator,
            IAttachmentStorageService attachmentService,
            IEmployeeBankDetailService employeeBankDetailService,
            ICalcSchedulingService calcSchedulingService,
            IStringLocalizer<EmployeeBankDetail> localizer,
            ICompanyService companyService,
            IEnumService enumService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.employeeBankDetailService = employeeBankDetailService;
            this.localizer = localizer;
            this.companyService = companyService;
            this.enumService = enumService;
            this.calcSchedulingService = calcSchedulingService;
            this.mapper = mapper;
            this.applicationContext = applicationContext;
            this.lockFactory = lockFactory;
        }

        public override Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, EmployeeBankDetailDto Dto)> AddEntity(MutatorContext context, EmployeeBankDetailDto dto)
        {
            var key = CacheKeys.CreateBankDetail(dto.EmployeeId);
            return this.TryCreateOrUpdateResourceAsync(dto, key, async () => await base.AddEntity(context, dto));
        }

        public override async Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, EmployeeBankDetailDto Dto)> UpdateEntity(MutatorContext context, long key, Func<EmployeeBankDetailDto, Task<bool>> patchDelegate)
        {
            var employeeId = await this.applicationContext.Set<EmployeeBankDetail>()
                .Where(_ => _.BankDetailId == key)
                .Select(_ => _.EmployeeBankHeader.EmployeeId)
                .FirstOrDefaultAsync();

            var cacheKey = CacheKeys.CreateBankDetail(employeeId);
            return await this.TryCreateOrUpdateResourceAsync(null, cacheKey, async () => await base.UpdateEntity(context, key, patchDelegate));
        }

        protected override async Task AddAsync(MutatorContext context, long? employeeId, EmployeeBankDetail entity)
        {
            if (!await this.employeeBankDetailService.HasBankHeaderAsync(employeeId!.Value))
            {
                await this.employeeBankDetailService.AddAsync(entity);
                return;
            }

            var header = await this.employeeBankDetailService.GetHeaderByEmployeeIdAsync(employeeId.Value);
            var bankingDetail = await this.employeeBankDetailService.GetSingleBankingDetailOrDefaultAsync(employeeId.Value, entity.EmployeeBankHeaderId);

            // Add the new account if more than one banking detail account exists
            if (header.PaymentMethod is PaymentMethod.EFT or PaymentMethod.PIX || bankingDetail is null)
            {
                await this.employeeBankDetailService.AddAsync(entity);
                return;
            }

            this.mapper.Map(entity.EmployeeBankHeader, header);
            this.mapper.Map(entity, bankingDetail);

            entity.BankDetailId = bankingDetail.BankDetailId;
            await this.UpdateAsync(context, bankingDetail.BankDetailId, bankingDetail);
        }

        protected override async Task AfterMapAsync(MutatorContext context, EmployeeBankDetailDto dto, EmployeeBankDetail entity)
        {
            // With Uploads EF Core + Automapper causes the navigation property EmployeeBankHeader not to be tracked properly, we need to set the bank header after the mapping.
            // this error mostly happens when there are duplicate bank details in an Excel file.
            // This logic needs to be in the API mutator and upload mutator because of the change in the mapping profile
            if (entity.EmployeeBankHeader == null)
            {
                if (entity.EmployeeBankHeaderId > 0)
                {
                    entity.EmployeeBankHeader = await this.employeeBankDetailService.GetBankHeaderAsync(entity.EmployeeBankHeaderId);
                }
                else
                {
                    entity.EmployeeBankHeader = new EmployeeBankHeader
                    {
                        EmployeeId = dto.EmployeeId
                    };
                }
            }

            await this.SetPaymentMethodAsync(context, dto, entity);
            entity.EmployeeBankHeader.SplitType = dto.SplitType ?? BankDetailSplitType.Percentage;
            entity.EmployeeId = dto.EmployeeId;

            await base.AfterMapAsync(context, dto, entity);
        }

        protected override Task<EmployeeBankDetail?> FindAsync(MutatorContext context, long key)
        {
            return this.employeeBankDetailService.FindByIdAsync(key)!;
        }

        protected override Task UpdateAsync(MutatorContext context, long key, EmployeeBankDetail entity)
        {
            return this.employeeBankDetailService.UpdateAsync(entity, context.CompanyId);
        }

        protected override async Task BeforeValidateAsync(MutatorContext context, EmployeeBankDetailDto dto, EmployeeBankDetail entity)
        {
            var bankdetails = await this.EnsureMainAccount(entity);
            var isSplitModified = this.IsFieldModified(entity.EmployeeBankHeader, nameof(EmployeeBankHeader.SplitType));
            entity.BankAccountOwner = entity.BankAccountOwner.HasValue && entity.BankAccountOwner != default(long) ? entity.BankAccountOwner : BankAccountOwnerType.Own;

            // If EmployeeBankHeaderId is default at this point, then it is the first record being inserted.
            if (entity.EmployeeBankHeaderId == default)
            {
                entity.IsMainAccount = true;
                entity.Amount = 100;
            }
            else if (dto.SplitType == BankDetailSplitType.Component || (entity.IsMainAccount && dto.SplitType == BankDetailSplitType.Amount))
            {
                // Spit type of component or if it's amount on the main account then we need to have amount set to 0.
                entity.Amount = 0;
            }
            else if (entity.IsMainAccount && dto.SplitType == BankDetailSplitType.Percentage)
            {
                this.SetAccountPercentages(entity, isSplitModified, bankdetails);
            }
            else if (!entity.IsMainAccount && isSplitModified && dto.SplitType == BankDetailSplitType.Percentage)
            {
                this.SetMainAccountPercentage(entity, bankdetails);
            }
            else if (entity.EmployeeBankHeaderId != default && !entity.IsMainAccount && (isSplitModified && dto.SplitType == BankDetailSplitType.Amount))
            {
                this.SetMainAccountAmount(bankdetails);
            }

            // If it is the first bank details created for the employee, or you only have one detail then the split type must be percent.
            if (entity.EmployeeBankHeader.EmployeeBankHeaderId == default
                || (entity.BankDetailId != default
                    && (await this.employeeBankDetailService.GetBankDetails(entity.EmployeeBankHeader.EmployeeId).CountAsync()) == 1))
            {
                entity.EmployeeBankHeader.SplitType = BankDetailSplitType.Percentage;
            }

            // These fields cannot be set if split type is not component nor on the main account.
            if (entity.EmployeeBankHeader.SplitType != BankDetailSplitType.Component || entity.IsMainAccount)
            {
                entity.CurrencyId = default;
                entity.ComponentCompanyId = default;
            }

            context.TryAdd("BankHeaderId", entity.EmployeeBankHeaderId);

            await base.BeforeValidateAsync(context, dto, entity);
        }

        protected override async Task<EntityValidationResult> ValidateAsync(
            MutatorContext context,
            EmployeeBankDetailDto dto,
            EmployeeBankDetail entity,
            string ruleSet,
            CancellationToken cancellationToken = default)
        {
            var validationResult = await base.ValidateAsync(context, dto, entity, ruleSet, cancellationToken);

            if (entity.EmployeeBankHeaderId == default && entity.EmployeeBankHeader.SplitType != BankDetailSplitType.Percentage)
            {
                validationResult.Errors.Add(new ErrorCodeValidationResult(nameof(entity.EmployeeBankHeader.SplitType)) { ErrorMessage = this.localizer.GetString("lblSplitPercentageOnFirstPost") });
            }

            return new EntityValidationResult(!validationResult.Errors.Any(), validationResult.Errors);
        }

        protected override Task AfterAddAsync(MutatorContext context, EmployeeBankDetail entity, EmployeeBankDetailDto dto)
        {
            return this.calcSchedulingService.RecalculatePayslip(entity.EmployeeBankHeader.EmployeeId, CalcSource.BankDetails);
        }

        protected override Task AfterUpdateAsync(MutatorContext context, EmployeeBankDetail entity, EmployeeBankDetailDto dto)
        {
            return this.calcSchedulingService.RecalculatePayslip(entity.EmployeeBankHeader.EmployeeId, CalcSource.BankDetails);
        }

        protected override Task AfterDeleteAsync(MutatorContext context, EmployeeBankDetail entity)
        {
            return this.calcSchedulingService.RecalculatePayslip(entity.EmployeeBankHeader.EmployeeId, CalcSource.BankDetails);
        }

        protected override async Task DeleteAsync(MutatorContext context, long? employeeId, EmployeeBankDetail entity)
        {
            await this.DefaultAmountAfterDelete(entity);
            await base.DeleteAsync(context, employeeId, entity);
        }

        private async Task<IList<EmployeeBankDetail>> EnsureMainAccount(EmployeeBankDetail entity)
        {
            if (entity.EmployeeBankHeaderId != default && !entity.IsMainAccount)
            {
                var bankDetails = await this.employeeBankDetailService.GetBankDetailsByHeader(entity.EmployeeBankHeaderId).AsTracking().ToListAsync();
                if (bankDetails.Count > 0 && !bankDetails.Any(_ => _.IsMainAccount))
                {
                    // Ensure that the 1st account is the main account if there is no main accounts
                    bankDetails.First().IsMainAccount = true;
                }
                else if (bankDetails.Count == 0)
                {
                    entity.IsMainAccount = true;
                    entity.Amount = 100;
                }

                return bankDetails;
            }

            return Array.Empty<EmployeeBankDetail>();
        }

        private void SetMainAccountPercentage(EmployeeBankDetail entity, IList<EmployeeBankDetail> details)
        {
            var mainAccount = details.FirstOrDefault(_ => _.IsMainAccount == true);
            if (mainAccount != null)
            {
                var sumOtherValues = details.Where(_ => _.IsMainAccount == false).Sum(_ => _.Amount);

                // BankDetailId in model does not exist in the DB, so add it to the sumOtherValues for the calculation.
                if (!details.Any(_ => _.BankDetailId == entity.BankDetailId))
                {
                    sumOtherValues += entity.Amount;
                }

                // if sumOtherValues is smaller than 100, then subtract it from the MainAccounts' Amount, else 0
                var newMainAmount = sumOtherValues < 100 ? 100 - sumOtherValues : 0;
                if (newMainAmount > 0)
                {
                    mainAccount.Amount = newMainAmount;
                }
            }
        }

        // This is to make sure that if your split is by amount that the MainAccount is set to 0 if there are multiple EFT records.
        private void SetAccountPercentages(EmployeeBankDetail entity, bool isSplitModified, IList<EmployeeBankDetail> details)
        {
            var entry = this.applicationContext.Entry(entity);

            if (isSplitModified || (entry.State == EntityState.Modified && entry.Property(nameof(EmployeeBankDetail.Amount)).IsModified))
            {
                // Update second account only if split is being changed to percent on main and the amount is being specified. This should only take place if you have 2 accounts.
                if (details.Count == 2)
                {
                    var alternativeAccount = details.FirstOrDefault(_ => _.IsMainAccount == false);
                    if (alternativeAccount != null)
                    {
                        var mainAmount = details.Where(_ => _.IsMainAccount == true).Select(_ => _.Amount).FirstOrDefault();

                        // if sumOtherValues is smaller than 100, then subtract it from the MainAccounts' Amount, else 0
                        alternativeAccount.Amount = mainAmount < 100 ? 100 - mainAmount : 0;
                    }
                }
                else if (isSplitModified)
                {
                    if (details.Count == 0)
                    {
                        entity.Amount = 100;
                    }
                    else
                    {
                        var sumOtherValues = details.Where(_ => _.IsMainAccount == false).Sum(_ => _.Amount);

                        // BankDetailId in model does not exist in the DB, so add it to the sumOtherValues for the calculation.
                        if (!details.Any(_ => _.BankDetailId == entity.BankDetailId))
                        {
                            sumOtherValues += entity.Amount;
                        }

                        // if sumOtherValues is smaller than 100, then subtract it from the MainAccounts' Amount, else 0
                        var amount = sumOtherValues < 100 ? 100 - sumOtherValues : 0;

                        entity.Amount = amount;
                    }
                }
            }
        }

        private void SetMainAccountAmount(IList<EmployeeBankDetail> bankDetails)
        {
            var mainAccount = bankDetails.FirstOrDefault(_ => _.IsMainAccount == true);
            if (mainAccount != null)
            {
                mainAccount.Amount = 0;
            }
        }

        private async Task DefaultAmountAfterDelete(EmployeeBankDetail model)
        {
            if (model.EmployeeBankHeaderId != default)
            {
                // If only 2 record exists then deleting 1 will leave 1 record remaining.
                var employeeBankDetails = await this.employeeBankDetailService.GetBankDetailsByHeader(model.EmployeeBankHeaderId)
                    .AsTracking()
                    .Include(_ => _.EmployeeBankHeader)
                    .ToListAsync();
                if (employeeBankDetails.Count == 2)
                {
                    // If the SplitType of the remaining record is not Amount, then default back to 100.
                    var remainingAccount = employeeBankDetails.FirstOrDefault(_ => _.BankDetailId != model.BankDetailId);
                    if (remainingAccount != null)
                    {
                        remainingAccount.Amount = 100;
                        remainingAccount.EmployeeBankHeader.SplitType = BankDetailSplitType.Percentage;
                    }
                }
            }
        }

        private bool IsFieldModified(EmployeeBankHeader entity, string fieldName)
        {
            var entry = this.applicationContext.Entry(entity);
            if (entry.State == EntityState.Detached)
            {
                return true;
            }

            if (entry.State != EntityState.Modified)
            {
                return false;
            }

            if (entry.Property(fieldName).IsModified)
            {
                return true;
            }

            return false;
        }

        private async Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, EmployeeBankDetailDto Dto)> TryCreateOrUpdateResourceAsync(
            EmployeeBankDetailDto dto,
            string key,
            Func<Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, EmployeeBankDetailDto Dto)>> create)
        {
            try
            {
                return await this.lockFactory.TryUpdateWithLockAsync(key, create);
            }
            catch (LockTimedOutException)
            {
                return (false, new[] { new ErrorCodeValidationResult("Error creating record, a Post for this record is already in progress.", "ErrDuplicatePost", string.Empty) }, dto);
            }
        }

        private async Task SetPaymentMethodAsync(MutatorContext context, EmployeeBankDetailDto dto, EmployeeBankDetail entity)
        {
            var countryId = await this.companyService.GetTaxCountryIdAsync(context.CompanyId);
            var paymentMethod = this.enumService.GetEmployeePaymentMethods(countryId).FirstOrDefault(_ => _.PayMethodDescription == dto.PaymentMethod);
            if (paymentMethod != null)
            {
                entity.EmployeeBankHeader.PaymentMethod = (PaymentMethod)paymentMethod.PayMethodId;
                dto.PaymentMethodId = (PaymentMethod)paymentMethod.PayMethodId;
            }
        }
    }
}