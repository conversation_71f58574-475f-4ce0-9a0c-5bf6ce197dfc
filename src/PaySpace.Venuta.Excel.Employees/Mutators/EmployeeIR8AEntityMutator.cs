namespace PaySpace.Venuta.Excel.Employees.Mutators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using AutoMapper;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Services.Employees.SG;
    using PaySpace.Venuta.Storage;

    public class EmployeeIR8AEntityMutator : EntityMutator<EmployeeIR8ADto, EmployeeIR8A, long>
    {
        private const string DefaultDesignatedFundName = "Central Provident Fund";

        private readonly ISingaporeEmployeeIR8AService employeeIR8AService;

        public EmployeeIR8AEntityMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<EmployeeIR8ADto, EmployeeIR8A> entityValidator,
            IAttachmentStorageService attachmentService,
            ISingaporeEmployeeIR8AService employeeIR8AService)
                : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.employeeIR8AService = employeeIR8AService;
        }

        protected override Task AddAsync(MutatorContext context, long? employeeId, EmployeeIR8A entity)
        {
            if (string.IsNullOrEmpty(entity.DesignatedFundName))
            {
                entity.DesignatedFundName = DefaultDesignatedFundName;
            }

            return this.employeeIR8AService.AddAsync(entity);
        }

        protected override Task UpdateAsync(MutatorContext context, long key, EmployeeIR8A entity)
        {
            if (string.IsNullOrEmpty(entity.DesignatedFundName))
            {
                entity.DesignatedFundName = DefaultDesignatedFundName;
            }

            return base.UpdateAsync(context, key, entity);
        }

        protected override Task DeleteAsync(MutatorContext context, long? employeeId, EmployeeIR8A entity)
        {
            return this.employeeIR8AService.DeleteAsync(entity);
        }
    }
}
