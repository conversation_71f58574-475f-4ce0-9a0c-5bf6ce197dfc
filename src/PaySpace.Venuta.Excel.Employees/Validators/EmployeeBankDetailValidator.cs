namespace PaySpace.Venuta.Excel.Employees.Validators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;

    public class EmployeeBankDetailValidator : DtoValidator<EmployeeBankDetailDto>
    {
        private readonly IStringLocalizer localizer;
        private readonly IEmployeeBankDetailService employeeBankDetailService;

        public EmployeeBankDetailValidator(
            IStringLocalizer<EmployeeBankDetailDto> localizer,
            ApplicationContext context,
            IAuthorizationService authorizationService,
            IEmployeeBankDetailService employeeBankDetailService,
            IModelMetadataProvider modelMetadataProvider,
            IObjectModelValidator objectModelValidator,
            IScopedCache scopedCache,
            IServiceProvider serviceProvider)
            : base(context, authorizationService, localizer, modelMetadataProvider, objectModelValidator, serviceProvider, scopedCache)
        {
            this.localizer = localizer;
            this.employeeBankDetailService = employeeBankDetailService;

            this.RuleSet(RuleSetNames.Create, this.CreateRules);
            this.RuleSet(RuleSetNames.Update, this.UpdateRules);
        }

        protected override ValidationContext<EmployeeBankDetailDto> SetValidationContext(MutatorContext mutatorContext, EmployeeBankDetailDto entity, string[] ruleSets)
        {
            var context = base.SetValidationContext(mutatorContext, entity, ruleSets);

            mutatorContext.TryGetValue("BankHeaderId", out long? headerId);
            context.RootContextData.Add("BankHeaderId", headerId);

            mutatorContext.TryGetValue("RelatedDetails", out List<(long, decimal?)> relatedIds);
            context.RootContextData.Add("RelatedDetails", relatedIds);

            return context;
        }

        private void CreateRules()
        {
            this.FieldChecks();
        }

        private void UpdateRules()
        {
            // Primary key required
            this.RuleFor(_ => _.BankDetailId)
               .NotEqual(0)
               .WithMessage(this.localizer.GetString(ErrorCodes.BankDetails.BankDetailRequired));

            this.FieldChecks();
        }

        private void FieldChecks()
        {
            // This is to make sure that if your split is by percentage then the total is 100%.
            this.RuleFor(_ => _)
                .MustAsync(this.CheckSplitSum)
                .When(_ => (_.PaymentMethodId == PaymentMethod.EFT || _.PaymentMethodId == PaymentMethod.PIX) && _.SplitType == BankDetailSplitType.Percentage)
                .WithMessage(this.localizer.GetString(ErrorCodes.BankDetails.InvalidSplit));

            this.RuleFor(_ => _.BankAccountNo)
                .Must(ValidationHelper.IsValidBankAccountNumber)
                .When(_ => _.PaymentMethodId == PaymentMethod.EFT)
                .WithMessage(this.localizer.GetString(ErrorCodes.BankDetails.InvalidAccountNumber));
        }

        private Task<bool> CheckSplitSum(EmployeeBankDetailDto model, EmployeeBankDetailDto property, ValidationContext<EmployeeBankDetailDto> context, CancellationToken cancellationToken = default)
        {
            // Amount needs to be a percentage value between 0 and 100
            if (model.Amount is null or > 100 or < 0)
            {
                return Task.FromResult(false);
            }

            var headerId = this.GetBankHeaderId(context);
            if (headerId > 0)
            {
                var relatedDetails = this.GetRelatedIds(context);
                var percentage = model.Amount;
                var relatedIds = new List<long>();
                if (relatedDetails is { Count: > 0 })
                {
                    percentage += relatedDetails.Sum(_ => _.Percentage);
                    relatedIds = relatedDetails.Where(_ => _.BankDetailId > 0).Select(_ => _.BankDetailId).ToList();
                }

                return this.employeeBankDetailService.HasValidSplitSumAsync(headerId.Value, model.BankDetailId, percentage!.Value, relatedIds);
            }

            return Task.FromResult(true);
        }

        private List<(long BankDetailId, decimal? Percentage)> GetRelatedIds(IValidationContext context)
        {
            return (List<(long, decimal?)>)context.RootContextData["RelatedDetails"];
        }

        private long? GetBankHeaderId(IValidationContext context)
        {
            return (long?)context.RootContextData["BankHeaderId"];
        }
    }
}