namespace PaySpace.Venuta.Excel.Employees.Services.BR
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Maddalena;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions.Exceptions;
    using PaySpace.Venuta.Modules.Leave.Abstractions.Models;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;

    [CountryService(CountryCode.BR)]
    public class BrazilEmployeeLeaveApplicationEntityService : EmployeeLeaveApplicationEntityService
    {
        private readonly IStringLocalizer<EmployeeLeaveApplicationDto> localizer;
        private readonly IBrazilEmployeeLeaveBalanceService brazilEmployeeLeaveBalanceService;
        private readonly ICompanyRunService companyRunService;
        private readonly ICompanyFrequencyService companyFrequencyService;
        private readonly IEmployeeLeaveConcessionService employeeLeaveConcessionService;
        private readonly IScopedCache scopedCache;

        public BrazilEmployeeLeaveApplicationEntityService(
            IMapper mapper,
            ApplicationContext applicationContext,
            IStringLocalizer<EmployeeLeaveApplicationDto> localizer,
            IEmployeeService employeeService,
            IEmployeeLeaveValidationService employeeLeaveValidationService,
            IEmployeeLeaveValueService employeeLeaveValueService,
            IEmployeeLeaveSettingService employeeLeaveSettingService,
            ITenantProvider tenantProvider,
            IBrazilEmployeeLeaveBalanceService brazilEmployeeLeaveBalanceService,
            ICompanyRunService companyRunService,
            ICompanyFrequencyService companyFrequencyService,
            IEmployeeLeaveConcessionService employeeLeaveConcessionService,
            IScopedCache scopedCache)
            : base(
                mapper,
                applicationContext,
                localizer,
                employeeService,
                employeeLeaveValidationService,
                employeeLeaveValueService,
                employeeLeaveSettingService,
                tenantProvider,
                scopedCache)
        {
            this.localizer = localizer;
            this.brazilEmployeeLeaveBalanceService = brazilEmployeeLeaveBalanceService;
            this.companyRunService = companyRunService;
            this.companyFrequencyService = companyFrequencyService;
            this.employeeLeaveConcessionService = employeeLeaveConcessionService;
            this.scopedCache = scopedCache;
        }

        protected override async Task SetCountrySpecificPropertiesAsync(
            long companyId,
            long frequencyId,
            EmployeeLeaveApplicationDto dto,
            EmployeeLeaveAdjustment entity,
            EmployeeLeaveApplicationSettings settings)
        {
            if (entity.LeaveType != LeaveType.Annual)
            {
                await base.SetCountrySpecificPropertiesAsync(companyId, frequencyId, dto, entity, settings);
            }

            var runByRunDate = await this.companyFrequencyService.GetPostRunByRunBeforeDateAsync(frequencyId);
            var periodEndDate = await this.companyRunService.GetPeriodEndDateAsync(entity.CompanyRunId);
            if (runByRunDate == null || !IsValidRunByRunPeriod(periodEndDate, runByRunDate))
            {
                dto.ConcessionYearEnd = string.Empty;
            }

            var concessionDays = (decimal)entity.TotalDays + (decimal)(dto.TotalDaysSell ?? 0);
            var concession = await this.GetHistoricalConcessionAsync(
                companyId,
                frequencyId,
                entity.EmployeeId,
                entity.CompanyRunId,
                entity.CompanyLeaveSetupId,
                concessionDays,
                dto.ConcessionYearEnd,
                periodEndDate,
                dto is { SellVacationDays: true, TotalDaysSell: null or 0 });

            if (entity.LeaveAdjustmentId == 0 && !dto.HistoricalConcessionId.HasValue && concession != null)
            {
                entity.HistoricalConcessionId = concession.HistoricalConcessionId;
                dto.HistoricalConcessionId = concession.HistoricalConcessionId;
                dto.ConcessionYearEnd = $"{concession.ConcessionYearStartDate:d} - {concession.ConcessionYearEndDate:d}";
            }

            settings.LeaveSettings.EmployeeHistoricConcession = concession;
            await this.SetSoldDaysAsync(companyId, frequencyId, dto, settings);
        }

        protected override async Task GetBalanceAsync(EmployeeLeaveAdjustment entity, EmployeeLeaveApplicationSettings settings, long companyId, long frequencyId)
        {
            if (settings.LeaveSettings.EmployeeHistoricConcession != null)
            {
                var runningBalance = (double)settings.LeaveSettings.EmployeeHistoricConcession.RunningLeaveBalance;
                settings.Balance = new LeaveBalance
                {
                    ReflectInHours = settings.LeaveSettings.ReflectInHours,
                    HoursPerDay = settings.LeaveSettings.PayRateHours,
                    TimeSpan = settings.LeaveSettings.ReflectInHours
                        ? TimeSpan.FromHours(runningBalance * settings.LeaveSettings.PayRateHours)
                        : TimeSpan.FromDays(runningBalance)
                };
            }
            else
            {
                await base.GetBalanceAsync(entity, settings, companyId, frequencyId);

            }
        }

        protected override async Task CalculateDaysAsync(long companyId, long frequencyId, EmployeeLeaveApplicationDto dto, EmployeeLeaveAdjustment entity, EmployeeLeaveApplicationSettings settings)
        {
            await base.CalculateDaysAsync(companyId, frequencyId, dto, entity, settings);

            dto.TotalDaysCurrent = settings.LeaveValue.TotalDaysCurrent;
            dto.TotalDaysNext = settings.LeaveValue.TotalDaysFuture;
            dto.TotalDaysAfterNext = settings.LeaveValue.TotalDaysAfterNext;
        }

        private async Task SetSoldDaysAsync(
            long companyId,
            long frequencyId,
            EmployeeLeaveApplicationDto dto,
            EmployeeLeaveApplicationSettings settings)
        {
            if (dto.SellVacationDays && dto.LeaveTransactionType != LeaveEntryType.Cancellation && dto is { LeaveType: LeaveType.Annual, TotalDaysSell: null })
            {
                dto.TotalDaysSell = (double?)settings.LeaveSettings.EmployeeHistoricConcession?.TotalDaysSold
                                    ?? await this.brazilEmployeeLeaveBalanceService.GetDaysToSellAsync(
                                        companyId,
                                        dto.EmployeeId,
                                        frequencyId,
                                        dto.CompanyLeaveSetupId,
                                        dto.RunId,
                                        settings.LeaveSettings.EmployeeHistoricConcession);
            }

            if (dto is { TotalDaysSell: not null, SellVacationDays: false })
            {
                dto.SellVacationDays = true;
            }
        }

        private static bool IsValidRunByRunPeriod(DateTime periodEndDate, [DisallowNull] DateTime? runByRunDate) => periodEndDate.Date <= runByRunDate.Value.Date;

        private async Task<EmployeeHistoricConcession?> GetHistoricalConcessionAsync(
            long companyId,
            long frequencyId,
            long employeeId,
            long runId,
            long? companyLeaveSetupId,
            decimal leaveValue,
            string concessionYearEnd,
            DateTime periodEndDate,
            bool sellingDays)
        {
            // Calculate the sold days for each concession on load, use the sold days + leaveValue to determine what concession to use
            var concessionScopeKey = $"EmployeeHistoricalConcession:{employeeId}";
            var concessions = await this.scopedCache.GetOrCreateAsync(concessionScopeKey,
                async () =>
                {
                    var concessions = await this.employeeLeaveConcessionService.GetEmployeeHistoricalConcessions(employeeId)
                        .AsNoTracking()
                        .OrderBy(_ => _.ConcessionYearEndDate)
                        .ToListAsync();

                    foreach (var concession in concessions)
                    {
                        concession.TotalDaysSold = (decimal?)await this.brazilEmployeeLeaveBalanceService.GetDaysToSellAsync(
                            companyId,
                            employeeId,
                            frequencyId,
                            companyLeaveSetupId,
                            runId,
                            concession);
                    }

                    return concessions;
                });

            if (concessions == null || concessions.Count == 0)
            {
                return null;
            }

            EmployeeHistoricConcession? concession;
            if (!string.IsNullOrWhiteSpace(concessionYearEnd))
            {
                concession = concessions.SingleOrDefault(_ => _.ConcessionYearStartDate.Date <= periodEndDate.Date
                                                              && _.ConcessionYearStartDate.Year + " - " + _.ConcessionYearEndDate.Year == concessionYearEnd);
                if (concession == null)
                {
                    // Catch this and return it as validation
                    throw new InvalidConcessionPeriodException(this.localizer);
                }
            }
            else
            {
                concession = concessions
                    .Where(_ => _.ConcessionYearStartDate.Date <= periodEndDate.Date && _.RunningLeaveBalance > 0)
                    .FirstOrDefault(_ => _.RunningLeaveBalance >= leaveValue + (sellingDays ? _.TotalDaysSold ?? 0 : 0));

                if (concession == null)
                {
                    return null;
                }
            }

            concession.RunningLeaveBalance -= leaveValue + (sellingDays ? concession.TotalDaysSold ?? 0 : 0);
            return new EmployeeHistoricConcession
            {
                HistoricalConcessionId = concession.HistoricalConcessionId,
                EmployeeId = concession.EmployeeId,
                CompanyLeaveSetupId = concession.CompanyLeaveSetupId,
                ConcessionYearStartDate = concession.ConcessionYearStartDate,
                ConcessionYearEndDate = concession.ConcessionYearEndDate,
                CarryOverLeaveBalance = concession.CarryOverLeaveBalance,
                RunningLeaveBalance = concession.RunningLeaveBalance + leaveValue,
                TotalDaysSold = concession.TotalDaysSold
            };
        }
    }
}