namespace PaySpace.Venuta.Controllers.Api
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Reflection;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Validation.ModelBinders;

    [AllowAll]
    public class MetadataController : ApiController
    {
        private readonly IModelMetadataProvider metadataProvider;
        private readonly IStringLocalizerFactory stringLocalizerFactory;
        private static readonly List<Assembly> Assemblies = AppDomain.CurrentDomain.GetAssemblies()
            .Where(assembly => assembly.FullName!.StartsWith("PaySpace."))
            .ToList();

        public MetadataController(IModelMetadataProvider metadataProvider, IStringLocalizerFactory stringLocalizerFactory)
        {
            this.metadataProvider = metadataProvider;
            this.stringLocalizerFactory = stringLocalizerFactory;
        }

        [HttpGet("{countryCode?}")]
        //[ResponseCache(Duration = 60 * 60, Location = ResponseCacheLocation.Client, VaryByHeader = "Accept-Language", VaryByQueryKeys = new[] { "*" })]
        public Dictionary<string, object> GetMetadataForTypes(string? countryCode, string lang, long userId, [CommaArrayBinder] string[] modelTypes, [CommaArrayBinder] string[] areas)
        {
            if (modelTypes.Length > 0)
            {
                return this.CreateMetadataDictionary(modelTypes);
            }

            if (areas.Length > 0)
            {
                return this.CreateMetadataAreaDictionary(areas);
            }

            return new Dictionary<string, object>();
        }

        private Dictionary<string, object> CreateMetadataAreaDictionary(string[] areas)
        {
            var metadataDictionary = new Dictionary<string, object>();
            foreach (var area in areas)
            {
                var localizer = this.stringLocalizerFactory.Create(area, null);
                var propertiesDictionary = localizer.GetAllStrings()
                    .ToDictionary<LocalizedString, string, object>(resource => resource.Name, resource => resource.Value);

                metadataDictionary[area] = propertiesDictionary;
            }

            return metadataDictionary;
        }

        private Dictionary<string, object> CreateMetadataDictionary(string[] modelTypes)
        {
            var metadataDictionary = new Dictionary<string, object>();
            foreach (var assembly in Assemblies)
            {
                var namespaces = assembly.GetTypes().Select(type => type.Namespace);
                foreach (var @namespace in namespaces)
                {
                    foreach (var modelType in modelTypes)
                    {
                        var type = assembly.GetType($"{@namespace}.{modelType}");
                        if (type == null)
                        {
                            continue;
                        }

                        var metadata = this.metadataProvider.GetMetadataForType(type);
                        var propertiesDictionary = new Dictionary<string, object>();
                        foreach (var modelProperty in metadata.Properties)
                        {
                            if (!modelProperty.ShowForDisplay || !modelProperty.ShowForEdit)
                            {
                                continue;
                            }

                            var propertyDict = new Dictionary<string, object>
                            {
                                { "DisplayName", modelProperty.GetDisplayName() },
                                { "Description", modelProperty.Description },
                                { "IsRequired", modelProperty.IsRequired },
                                { "IsReadOnly", modelProperty.IsReadOnly },
                                { "Type", modelProperty.UnderlyingOrModelType.Name }
                            };

                            var rangeAttribute = modelProperty.ValidatorMetadata?.OfType<RangeAttribute>().FirstOrDefault();
                            if (rangeAttribute != null)
                            {
                                propertyDict["Range"] = new Dictionary<string, object>
                                {
                                    { "Minimum", rangeAttribute.Minimum },
                                    { "Maximum", rangeAttribute.Maximum }
                                };
                            }

                            if (modelProperty.IsEnum)
                            {
                                propertyDict["EnumNamesAndValues"] = modelProperty.EnumNamesAndValues;
                            }

                            propertiesDictionary[modelProperty.Name!] = propertyDict;
                        }

                        var metadataDict = new Dictionary<string, object>
                        {
                            { "IsReadOnly", metadata.IsReadOnly },
                            { "Properties", propertiesDictionary }
                        };

                        metadataDictionary[modelType] = metadataDict;
                    }
                }
            }

            return metadataDictionary;
        }
    }
}