@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.Extensions.Configuration
@using Microsoft.Extensions.Hosting
@using PaySpace.Venuta.TagHelpers
@using PaySpace.Configuration

@inject IHostEnvironment env
@inject IConfiguration configuration
@{
    var region = configuration.GetRegion();
    var apiUrl = configuration.GetValue<Uri>("ClientSettings:ApiUrl");
    var dynamicFomBuilderApiUrl = configuration.GetValue<Uri>("ClientSettings:DynamicFormBuilderApiUrl");
}

<!DOCTYPE html>
<html lang="@CultureInfo.CurrentUICulture.Name">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" />
    <meta name="robots" content="disallow">
    <base href="@Url.Content("~/")" />
    <title>@ViewData["Title"]</title>

    <meta name="ps:region" content="@region" />
    <meta name="ps:env" content="@env.EnvironmentName" />
    <meta name="ps:api" content="@apiUrl" />
    <meta name="ps:dynamicFormBuilderApi" content="@dynamicFomBuilderApiUrl" />

    <partial name="_Layout-Auth-Header" />
</head>
<body>
    @RenderBody()

    <script src="~/bundles/validation.min.js" asp-append-version="true"></script>
    <script src="~/js/site.min.js" asp-append-version="true"></script>

    @RenderSection("scripts", required: false)
</body>
</html>
