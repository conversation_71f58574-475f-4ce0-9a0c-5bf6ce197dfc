@using Microsoft.Extensions.Configuration
@using Microsoft.Extensions.Hosting
@using PaySpace.Configuration

@inject IHostEnvironment env
@inject IConfiguration configuration
@inject IStringLocalizerFactory localizerFactory
@{
    var region = configuration.GetRegion();
    var apiUrl = configuration.GetValue<Uri>("ClientSettings:ApiUrl");
    var dynamicFomBuilderApiUrl = configuration.GetValue<Uri>("ClientSettings:DynamicFormBuilderApiUrl");
    var localizer = localizerFactory.Create(SystemAreas.Navigation.Area, null);
    var generalLocalizer = localizerFactory.Create(SystemAreas.General.Area, null);
}

<!DOCTYPE html>
<html lang="@CultureInfo.CurrentUICulture.Name">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" />
    <meta name="robots" content="disallow" />
    <base href="@Url.Content("~/")" />
    <title>@ViewData["Title"]</title>

    <meta name="ps:region" content="@region" />
    <meta name="ps:env" content="@env.EnvironmentName" />
    <meta name="ps:api" content="@apiUrl" />
    <meta name="ps:dynamicFormBuilderApi" content="@dynamicFomBuilderApiUrl" />
    <meta name="ps:path" content="@Context.Request.PathBase" />

    <environment exclude="Development">
        <vc:track-page></vc:track-page>
    </environment>

    @RenderSection("header", required: false)

    <partial name="_Layout-Auth-Header" />
</head>
<body class="overflow-hidden">
    <div class="d-flex flex-column h-100">
        <vc:nav />

        @if (!this.User.IsInRole(UserTypeCodes.Employee))
        {
            <vc:top-menu />
        }

        <div class="d-flex flex-grow-1" style="min-height:0">
            <div class="d-flex flex-grow-1" style="min-width:0">
                <vc:menu />

				<div class="page flex-grow-1 overflow-auto" id="drawerContainer">
					<environment exclude="Production">
                        <vc:maintenance-message />
                    </environment>

                    @if (!this.User.IsInRole(UserTypeCodes.Employee))
                    {
                        <vc:force-recalculation-button />
                        @await Component.InvokeAsync("WorkflowActivitySteps")
                    }

                    @RenderBody()
                </div>
            </div>
        </div>
    </div>

    <modal id="audit-modal" modal-size="Lg">
        <modal-header>
            <modal-title title="@generalLocalizer.GetString("lblAudit")" />
            <modal-close-button />
        </modal-header>
        <modal-body />
    </modal>

    <modal id="subordinates-modal">
        <modal-header>
            <modal-title title="@localizer.GetString("lblTeamAccess")" />
            <modal-close-button />
        </modal-header>
        <modal-body />
    </modal>

    <modal id="employee-info-modal">
        <modal-header>
            <modal-title />
            <modal-close-button />
        </modal-header>
        <modal-body />
    </modal>

    <div id="load-panel"></div>

    @if (!this.User.IsInRole(UserTypeCodes.Employee))
    {
        <vc:employee-sidebar return-url="@Context.Request.GetEncodedPathAndQuery()" allow-dashboard="false" allow-close="true" />
        <vc:company-sidebar return-url="@Context.Request.GetEncodedPathAndQuery()" allow-close="true" />
        <vc:frequency return-url="@Context.Request.GetEncodedPathAndQuery()" allow-close="true" />
        <vc:config-sidebar />
    }

    <partial name="_Layout-Auth-Footer" />

    @RenderSection("modals", required: false)
    @RenderSection("scripts", required: false)

    <vc:notifications />
</body>
</html>