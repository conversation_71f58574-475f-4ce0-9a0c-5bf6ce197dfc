namespace PaySpace.Venuta.Infrastructure
{
    using System.Diagnostics.CodeAnalysis;

    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Routing;

    public static class CompanyEndpointRouteBuilder
    {
        private const string CompanyArea = "Company";

        [SuppressMessage("Roslynator", "RCS0056:A line is too long")]
        public static void MapCompanyRoutes(this IEndpointRouteBuilder router)
        {
            router.MapAreaControllerRoute("CompanyProfile", CompanyArea, "company/{CompanyId:long}/company-profile", Defaults("CompanyProfile", "Index"));
            router.MapAreaControllerRoute("AddNewCompany", CompanyArea, "company/company-profile/create/{CountryCode?}/{CountryId?}", Defaults("AddCompany", "Index"));

            router.MapAreaControllerRoute("OrganizationGroup", CompanyArea, "company/{CompanyId:long}/company-org-group", Defaults("OrganizationGroup", "Index"));
            router.MapAreaControllerRoute("OrganizationGroupEdit", CompanyArea, "company/{CompanyId:long}/company-org-group/edit", Defaults("OrganizationGroup", "Edit"));

            // User Profile
            router.MapAreaControllerRoute("CompanyUserProfile", CompanyArea, "company/{CompanyId:long}/{FrequencyId:long}/user-profile", Defaults("CompanyUserProfile", "Index"));
            router.MapAreaControllerRoute("CompanyUserProfileEdit", CompanyArea, "company/{CompanyId:long}/{FrequencyId:long}/user-profile/edit", Defaults("CompanyUserProfile", "Edit"));
            router.MapAreaControllerRoute("ActivateUsers", CompanyArea, "company/{CompanyId:long}/{FrequencyId:long}/user-activate", Defaults("UserActivate", "Index"));

            router.MapAreaControllerRoute("JobManagement", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/job-management", Defaults("JobManagement", "Index"));
            router.MapAreaControllerRoute("JobManagementCreateDetail", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/job-management/budget-period/{BudgetPeriodId}/create", Defaults("JobManagement", "Create"));
            router.MapAreaControllerRoute("JobManagementEditDetail", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/job-management/budget-period/{BudgetPeriodId}/edit/{JobId?}", Defaults("JobManagement", "Edit"));
            router.MapAreaControllerRoute("JobManagementDeleteDetail", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/job-management/budget-period/{BudgetPeriodId}/delete/{JobId}", Defaults("JobManagement", "Delete"));
            router.MapAreaControllerRoute("JobManagementCopyJob", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/job-management/budget-period/{BudgetPeriodId}/job/{JobId}/copy/{Count}", Defaults("JobManagement", "Copy"));
            router.MapAreaControllerRoute("JobManagementCreateJobATS", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/job-management/budget-period/{BudgetPeriodId}/ats/{JobId}", Defaults("JobManagement", "CreateJobATS"));

            router.MapAreaControllerRoute("CompanyFrequency", CompanyArea, "company/{CompanyId:long}/company-frequency", Defaults("CompanyFrequency", "Index"));
            router.MapAreaControllerRoute("CompanyFrequencyEdit", CompanyArea, "company/{CompanyId:long}/company-frequency/edit/{FrequencyId:long}", Defaults("CompanyFrequency", "Edit"));
            router.MapAreaControllerRoute("CompanyFrequencyCreate", CompanyArea, "company/{CompanyId:long}/company-frequency/create", Defaults("CompanyFrequency", "Create"));

            router.MapAreaControllerRoute("CompanyPensionEnrolment", CompanyArea, "company/{CompanyId:long}/company-pension-enrolment", Defaults("CompanyPensionEnrolment", "Index"));
            router.MapAreaControllerRoute("EmployeePensionLetters", CompanyArea, "company/{CompanyId:long}/pension-letters", Defaults("EmployeePensionLetters", "Index"));

            router.MapAreaControllerRoute("PayRateCategories", CompanyArea, "company/{CompanyId:long}/company-pay-rate-categories", Defaults("PayRateCategory", "Index"));
            router.MapAreaControllerRoute("PayRateCategoriesEdit", CompanyArea, "company/{CompanyId:long}/company-pay-rate-categories/edit/{categoryId?}", Defaults("PayRateCategory", "Edit"));

            // Company Settings
            router.MapAreaControllerRoute("CompanyCalculationSettings", CompanyArea, "company/{CompanyId:long}/calculation-settings", Defaults("CompanySettings", "CalculationSettings"));
            router.MapAreaControllerRoute("CompanyClaimSettings", CompanyArea, "company/{CompanyId:long}/claim-settings", Defaults("CompanySettings", "ClaimSettings"));
            router.MapAreaControllerRoute("CompanyCostingSettings", CompanyArea, "company/{CompanyId:long}/costing-settings", Defaults("CompanySettings", "CostingSettings"));
            router.MapAreaControllerRoute("CompanyGeneralSettings", CompanyArea, "company/{CompanyId:long}/general-settings", Defaults("CompanySettings", "GeneralSettings"));
            router.MapAreaControllerRoute("CompanyLeaveSettings", CompanyArea, "company/{CompanyId:long}/leave-settings", Defaults("CompanySettings", "LeaveSettings"));
            router.MapAreaControllerRoute("CompanyPerformanceManagementSettings", CompanyArea, "company/{CompanyId:long}/performance-management-settings", Defaults("CompanySettings", "PerformanceManagementSettings"));
            router.MapAreaControllerRoute("CompanyPositionSettings", CompanyArea, "company/{CompanyId:long}/position-settings", Defaults("CompanySettings", "PositionSettings"));
            router.MapAreaControllerRoute("CompanySecuritySettings", CompanyArea, "company/{CompanyId:long}/security-settings", Defaults("CompanySettings", "SecuritySettings"));
            router.MapAreaControllerRoute("CompanyWorkforcePlanningSettings", CompanyArea, "company/{CompanyId:long}/workforce-planning-settings", Defaults("CompanySettings", "WorkforcePlanningSettings"));

            router.MapAreaControllerRoute("CompanyDynamicFormBuilder", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/dynamic-form-builder", Defaults("CompanyDynamicFormBuilder", "Index"));
            router.MapAreaControllerRoute("SuccessfulQuickAdd", CompanyArea, "company/{CompanyId:long}/employees/{EmployeeId:long}/dynamic-form-builder/successful-quick-add", Defaults("CompanyDynamicFormBuilder", "SuccessfulQuickAdd"));

            // CloudRoom
            router.MapAreaControllerRoute("CloudRoom", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/cloud-room", Defaults("CloudRoom", "Index"));
            router.MapAreaControllerRoute("CloudRoom", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/cloud-process", Defaults("CloudRoom", "Process"));
            router.MapAreaControllerRoute("CloudRoomCreate", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/cloud-room/create", Defaults("CloudRoom", "Create"));
            router.MapAreaControllerRoute("CloudRoomEdit", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/cloud-room/{CloudRoomId}", Defaults("CloudRoom", "Edit"));
            router.MapAreaControllerRoute("CloudRoomAttachmentsExport", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/cloud-room/{CloudRoomId}/export", Defaults("CloudRoom", "ExportAttachments"));

            router.MapAreaControllerRoute("PowerBi", CompanyArea, "company/{CompanyId:long}/powerbi", Defaults("PowerBi", "Index"));

            // Security Role
            router.MapAreaControllerRoute("CompanySecurityRole", CompanyArea, "company/{CompanyId:long}/security-role", Defaults("CompanySecurityRole", "Index"));
            router.MapAreaControllerRoute("CompanySecurityRoleEdit", CompanyArea, "company/{CompanyId:long}/security-role/edit/{securityGroupId?}", Defaults("CompanySecurityRole", "Edit"));
            router.MapAreaControllerRoute("CompanySecurityRoleCopy", CompanyArea, "company/{CompanyId:long}/security-role/copy", Defaults("CompanySecurityRole", "Copy"));

            // Organization Hierarchy
            router.MapAreaControllerRoute("OrganizationLevel", CompanyArea, "company/{CompanyId:long}/organization-levels", Defaults("OrganizationLevel", "Index"));
            router.MapAreaControllerRoute("OrganizationGrade", CompanyArea, "company/{CompanyId:long}/grades", Defaults("OrganizationGrade", "Index"));
            router.MapAreaControllerRoute("OrganizationGradeEdit", CompanyArea, "company/{CompanyId:long}/grades/edit/{gradeId?}", Defaults("OrganizationGrade", "Edit"));

            // User Organization Permission
            router.MapAreaControllerRoute("UserOrgPermissions", CompanyArea, "company/{CompanyId:long}/user-org-permissions", Defaults("UserOrgPermissions", "Index"));
            router.MapAreaControllerRoute("UserOrgPermissionsAllocateAccess", CompanyArea, "company/{CompanyId:long}/user-org-permissions/allocate-access", Defaults("UserOrgPermissions", "AllocateAccess"));

            // Company Leave Scheme
            router.MapAreaControllerRoute("CompanyLeaveSchemes", CompanyArea, "company/{CompanyId:long}/company-leave-schemes", Defaults("CompanyLeaveSchemes", "Index"));
            router.MapAreaControllerRoute("CompanyLeaveSchemeParameter", CompanyArea, "company/{CompanyId:long}/company-leave-scheme-parameters", Defaults("CompanyLeaveSchemeParameter", "Index"));

            router.MapAreaControllerRoute("CompanyRun", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/company-run", Defaults("CompanyRun", "Index"));

            // Government Hub
            router.MapAreaControllerRoute("GovernmentHub", CompanyArea, "company/{CompanyId:long}/government-hub", Defaults("GovernmentHub", "Index"));

            router.MapAreaControllerRoute("AddNewEmployee", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/employee/create", Defaults("EmployeeProfile", "Create"));
            router.MapAreaControllerRoute("BulkUpload", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/bulk-upload", Defaults("BulkUpload", "Index"));
            router.MapAreaControllerRoute("CompanyComponents", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/payroll-component", Defaults("PayrollComponent", "Index"));
            router.MapAreaControllerRoute("CompanyConfiguration", CompanyArea, "company/{CompanyId:long}/configuration", Defaults("Configuration", "Index"));
            router.MapAreaControllerRoute("CompanyCustomFields", CompanyArea, "company/{CompanyId:long}/custom-fields", Defaults("CompanyCustomFields", "Index"));
            router.MapAreaControllerRoute("CompanyCustomFormConfiguration", CompanyArea, "company/{CompanyId:long}/custom-form-config", Defaults("CompanyCustomFormConfig", "Index"));
            router.MapAreaControllerRoute("CompanyCustomForms", CompanyArea, "/company/{CompanyId:long}/custom-forms", Defaults("CompanyCustomForms", "Index"));
            router.MapAreaControllerRoute("CompanyPublicHoliday", CompanyArea, "company/{CompanyId:long}/public-holiday", Defaults("CompanyPublicHoliday", "Index"));
            router.MapAreaControllerRoute("CompanyQualifications", CompanyArea, "company/{CompanyId:long}/company-qualifications", Defaults("CompanyQualifications", "Index"));
            router.MapAreaControllerRoute("CompanyReports", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/reports", Defaults("Reports", "Index"));
            router.MapAreaControllerRoute("CompanyRosterSchedules", CompanyArea, "company/{CompanyId:long}/company-roster-schedules", Defaults("CompanyRosterSchedules", "Index"));
            router.MapAreaControllerRoute("CompanySkills", CompanyArea, "company/{CompanyId:long}/company-skills", Defaults("CompanySkills", "Index"));
            router.MapAreaControllerRoute("CompanyShiftTypes", CompanyArea, "company/{CompanyId:long}/shift-types", Defaults("CompanyShiftTypes", "Index"));
            router.MapAreaControllerRoute("CompanyTableBuilder", CompanyArea, "company/{CompanyId:long}/table-builder", Defaults("CompanyTableBuilder", "Index"));
            router.MapAreaControllerRoute("CompanyWorkflowRoles", CompanyArea, "company/{CompanyId:long}/workflow-roles", Defaults("CompanyWorkflowRoles", "Index"));
            router.MapAreaControllerRoute("ComponentSubCodes", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/payroll-component-sub-code/{ComponentId}", Defaults("PayrollComponentSubCode", "Index"));
            router.MapAreaControllerRoute("CopyConfiguration", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/copy-payroll-components", Defaults("CopyConfiguration", "Index"));
            router.MapAreaControllerRoute("CostingProjectActivity", CompanyArea, "company/{CompanyId:long}/project-costing", Defaults("CostingProjectActivity", "Index"));
            router.MapAreaControllerRoute("EquityPlan", CompanyArea, "company/{CompanyId:long}/company-equity-plan", Defaults("CompanyEmploymentEquityPlanValues", "Index"));
            router.MapAreaControllerRoute("EquityPlanSetup", CompanyArea, "company/{CompanyId:long}/company-equity-setup", Defaults("CompanyEmploymentEquityPlan", "Index"));
            router.MapAreaControllerRoute("EsocialDashboard", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/esocial/dashboard", Defaults("EsocialDashboard", "Index"));
            router.MapAreaControllerRoute("ExternalQuickLinks", CompanyArea, "company/{CompanyId:long}/company-external-quick-links", Defaults("ExternalQuickLinks", "Index"));
            router.MapAreaControllerRoute("GeneralLedger", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/general-ledger", Defaults("GeneralLedger", "Index"));
            router.MapAreaControllerRoute("GeneralLedgerLogin", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/general-ledger/login", Defaults("GeneralLedger", "Login"));
            router.MapAreaControllerRoute("GeneralLedgerLogout", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/general-ledger/logout", Defaults("GeneralLedger", "Logout"));
            router.MapAreaControllerRoute("HmrcPaymentRecord", CompanyArea, "company/{CompanyId:long}/hmrc", Defaults("HmrcPaymentRecord", "Index"));
            router.MapAreaControllerRoute("Integrations", CompanyArea, "company/{CompanyId:long}/integrations", Defaults("Integrations", "Index"));
            router.MapAreaControllerRoute("PublicHolidayCategories", CompanyArea, "company/{CompanyId:long}/public-holiday-categories", Defaults("PublicHolidayCategory", "Index"));
            router.MapAreaControllerRoute("Regions", CompanyArea, "company/{CompanyId:long}/regions", Defaults("Regions", "Index"));
            router.MapAreaControllerRoute("Roster", CompanyArea, "company/{CompanyId:long}/roster", Defaults("Roster", "Index"));
            router.MapAreaControllerRoute("SkillsCategories", CompanyArea, "company/{CompanyId:long}/company-skills-categories", Defaults("SkillsCategories", "Index"));

            router.MapAreaControllerRoute("Iras", CompanyArea, "company/{CompanyId:long}/iras", Defaults("Iras", "Index"));
            router.MapAreaControllerRoute("Mscoa", CompanyArea, "company/{CompanyId:long}/mscoa", Defaults("Mscoa", "Login"));
            router.MapAreaControllerRoute("OrgChart", CompanyArea, "company/{CompanyId:long}/orgchart", Defaults("OrgChart", "Login"));
            router.MapAreaControllerRoute("PaceyDashboard", CompanyArea, "company/{CompanyId:long}/pacey/dashboard", Defaults("PaceyDashboard", "Index"));
            router.MapAreaControllerRoute("Rti", CompanyArea, "company/{CompanyId:long}/rti", Defaults("Rti", "Index"));

            router.MapAreaControllerRoute("OrganizationPosition", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/organization-positions", Defaults("OrganizationPosition", "Index"));
            router.MapAreaControllerRoute("OrganizationPositionEdit", CompanyArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/organization-positions/edit", Defaults("OrganizationPosition", "Edit"));
        }

        private static object Defaults(string controller, string action)
        {
            return new
            {
                controller,
                action
            };
        }
    }
}