namespace PaySpace.Venuta.Authentication
{
    using System.Security.Claims;

    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.DependencyInjection;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;

    public class ManagerTenantProvider : HttpContextTenantProvider
    {
        public ManagerTenantProvider(IHttpContextAccessor contextAccessor)
            : base(contextAccessor)
        {
        }

        public override long GetAgencyId(ClaimsPrincipal user)
        {
            if (this.IsValidManagerRole(user))
            {
                if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetUserSessionSettings(this.HttpContext, user, out var session))
                {
                    return session.AgencyId;
                }
            }

            return base.GetAgencyId(user);
        }

        public override long? GetCompanyGroupId(ClaimsPrincipal user)
        {
            if (this.IsValidManagerRole(user))
            {
                if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetUserSessionSettings(this.HttpContext, user, out var session))
                {
                    return session.CompanyGroupId;
                }
            }

            return base.GetCompanyGroupId(user);
        }

        public override long? GetCompanyId(ClaimsPrincipal user)
        {
            if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetSubordinate(this.HttpContext, out var subordinate))
            {
                return subordinate.CompanyId;
            }

            if (this.IsValidManagerRole(user))
            {
                if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetUserSessionSettings(this.HttpContext, user, out var session))
                {
                    return session.CompanyId;
                }
            }

            return base.GetCompanyId(user);
        }

        public override long? GetEmployeeId(ClaimsPrincipal user)
        {
            if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetSubordinate(this.HttpContext, out var subordinate))
            {
                return subordinate.EmployeeId;
            }

            if (this.IsValidManagerRole(user))
            {
                if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetUserSessionSettings(this.HttpContext, user, out var session))
                {
                    return session.EmployeeId;
                }
            }

            return base.GetEmployeeId(user);
        }

        public override long? GetFrequencyId(ClaimsPrincipal user)
        {
            if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetSubordinate(this.HttpContext, out var subordinate))
            {
                return subordinate.FrequencyId;
            }

            if (this.IsValidManagerRole(user))
            {
                if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetUserSessionSettings(this.HttpContext, user, out var session))
                {
                    return session.FrequencyId;
                }
            }

            return base.GetFrequencyId(user);
        }

        public override string? GetTaxCountryCode(ClaimsPrincipal user)
        {
            if (this.HttpContext.Request.RouteValues.TryGetValue("countryCode", out var countryCode))
            {
                return countryCode.ToString();
            }

			if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetSubordinate(this.HttpContext, out var subordinate))
			{
				return subordinate.TaxCountryCode;
            }

            if (this.IsValidManagerRole(user))
            {
                if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetUserSessionSettings(this.HttpContext, user, out var session))
                {
                    return session.TaxCountryCode;
                }
            }

            return base.GetTaxCountryCode(user);
        }

        public override int GetDecimalPlaces(ClaimsPrincipal user)
        {
            if (this.IsValidManagerRole(user))
            {
                if (this.HttpContext.RequestServices.GetRequiredService<IManagerService>().TryGetUserSessionSettings(this.HttpContext, user, out var session))
                {
                    return session.DecimalPlaces;
                }
            }

            return base.GetDecimalPlaces(user);
        }

        public override int? GetTaxCountryId()
        {
            if (this.HttpContext.Request.RouteValues.TryGetValue("countryId", out var countryId))
            {
                if (!int.TryParse(countryId.ToString(), out var result))
                {
                    return null;
                }

                return result;
            }

            return base.GetTaxCountryId();
        }

        private bool IsValidManagerRole(ClaimsPrincipal user)
        {
            return !user.IsInRole(UserTypeCodes.Employee);
        }
    }
}
