<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
	<TargetFramework>net9.0</TargetFramework>
	<!--<Nullable>enable</Nullable>-->
	<NeutralLanguage>en-ZA</NeutralLanguage>
	<RazorRuntimeCompilation>true</RazorRuntimeCompilation>
	<ApplicationInsightsResourceId>/subscriptions/8d9221e8-f3d9-4773-a334-79c9085522bc/resourcegroups/VenutaApplicationInsights/providers/microsoft.insights/components/Venuta</ApplicationInsightsResourceId>
	<CodeAnalysisRuleSet>..\..\PaySpace.Venuta.ruleset</CodeAnalysisRuleSet>
	<UserSecretsId>c84c4213-646d-4a09-8ea7-cc32278332de</UserSecretsId>
	<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <PropertyGroup>
	<EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
	<Content Remove="package.json" />
	<Content Include="..\..\.dockerignore">
	  <Link>.dockerignore</Link>
	</Content>
	<None Include="package.json" />
	<Content Remove="Client/package.json" />
	<None Include="Client/package.json" />
	<Content Remove="tsconfig.json" />
	<None Include="tsconfig.json" />
	<Content Remove="Client/tsconfig.json" />
	<None Include="Client/tsconfig.json" />
  </ItemGroup>

  <ItemGroup>
	<ProjectReference Include="..\PaySpace.Configuration\PaySpace.Configuration.csproj" />
	<ProjectReference Include="..\PaySpace.Integrations.Acumatica\PaySpace.Integrations.Acumatica.csproj" />
	<ProjectReference Include="..\PaySpace.Integrations.AwsLogs\PaySpace.Integrations.AwsLogs.csproj" />
	<ProjectReference Include="..\PaySpace.Integrations.QuickBooks\PaySpace.Integrations.QuickBooks.csproj" />
	<ProjectReference Include="..\PaySpace.Integrations.Xero\PaySpace.Integrations.Xero.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Data.AutoMapper\PaySpace.Venuta.Data.AutoMapper.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Data.Models.Validation\PaySpace.Venuta.Data.Models.Validation.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Data.Models\PaySpace.Venuta.Data.Models.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Data\PaySpace.Venuta.Data.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.DependencyInjection\PaySpace.Venuta.DependencyInjection.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Health\PaySpace.Venuta.Health.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Infrastructure\PaySpace.Venuta.Infrastructure.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Localization\PaySpace.Venuta.Localization.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Company.CloudRoom\PaySpace.Venuta.Modules.Company.CloudRoom.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.CompanyLeaveSchemes\PaySpace.Venuta.Modules.CompanyLeaveSchemes.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.CompanySettings\PaySpace.Venuta.Modules.CompanySettings.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Component.SubCodes.Abstractions\PaySpace.Venuta.Modules.Component.SubCodes.Abstractions.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Component.SubCodes\PaySpace.Venuta.Modules.Component.SubCodes.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Components\PaySpace.Venuta.Modules.Components.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.CustomForms.Abstractions\PaySpace.Venuta.Modules.CustomForms.Abstractions.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.CustomForms\PaySpace.Venuta.Modules.CustomForms.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Dashboard\PaySpace.Venuta.Modules.Dashboard.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.DraftValues\PaySpace.Venuta.Modules.DraftValues.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.DynamicFormBuilder\PaySpace.Venuta.Modules.DynamicFormBuilder.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Employee.Claims\PaySpace.Venuta.Modules.Employee.Claims.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Employee.Inbox\PaySpace.Venuta.Modules.Employee.Inbox.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Employee.Positions\PaySpace.Venuta.Modules.Employee.Positions.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Employee.SuspensionSnapshot\PaySpace.Venuta.Modules.Employee.SuspensionSnapshot.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.EmployeeHistory\PaySpace.Venuta.Modules.EmployeeHistory.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.EmployeeTakeOns\PaySpace.Venuta.Modules.EmployeeTakeOns.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.EmploymentStability\PaySpace.Venuta.Modules.EmploymentStability.csproj" />
	<ProjectReference Include="..\Payspace.Venuta.Modules.GeneralLedger\PaySpace.Venuta.Modules.GeneralLedger.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Leave\PaySpace.Venuta.Modules.Leave.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Organization\PaySpace.Venuta.Modules.Organization.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.OrgChart\PaySpace.Venuta.Modules.OrgChart.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.PayRate\PaySpace.Venuta.Modules.PayRate.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Payslips\PaySpace.Venuta.Modules.Payslips.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.PensionEnrolment\PaySpace.Venuta.Modules.PensionEnrolment.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.PublicHolidays\PaySpace.Venuta.Modules.PublicHolidays.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.RecordOfEmployment\PaySpace.Venuta.Modules.RecordOfEmployment.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.SecurityRoles\PaySpace.Venuta.Modules.SecurityRoles.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.UserOrgPermissions.Abstractions\PaySpace.Venuta.Modules.UserOrgPermissions.Abstractions.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.UserOrgPermissions\PaySpace.Venuta.Modules.UserOrgPermissions.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Users\PaySpace.Venuta.Modules.Users.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Search\PaySpace.Venuta.Search.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Security.Authentication\PaySpace.Venuta.Security.Authentication.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Security.Authorization\PaySpace.Venuta.Security.Authorization.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Security\PaySpace.Venuta.Security.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Logging\PaySpace.Venuta.Logging.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Services.External\PaySpace.Venuta.Services.External.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Services\PaySpace.Venuta.Services.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Services.Reports\PaySpace.Venuta.Services.Reports.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Shared\PaySpace.Venuta.Shared.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Storage\PaySpace.Venuta.Storage.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Telemetry\PaySpace.Venuta.Telemetry.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Validation\PaySpace.Venuta.Validation.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Workflow.Activities\PaySpace.Venuta.Workflow.Activities.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Workflow\PaySpace.Venuta.Workflow.csproj" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="DevExtreme.AspNet.Data" />
	<PackageReference Include="PaySpace.Cache" />
	<PackageReference Include="PaySpace.Venuta.Messaging" />
	<PackageReference Include="AutoMapper" />
	<PackageReference Include="AutoMapper.Collection" />
	<PackageReference Include="DevExpress.AspNetCore.Dashboard" />
	<PackageReference Include="DevExpress.Drawing.Skia" />
	<PackageReference Include="FluentValidation.AspNetCore" />
	<PackageReference Include="Humanizer" />
	<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
	<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
	<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
	<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" />
	<PackageReference Include="NWebsec.AspNetCore.Middleware" />
	<PackageReference Include="Roslynator.Formatting.Analyzers">
	  <PrivateAssets>all</PrivateAssets>
	  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	</PackageReference>
	<PackageReference Include="Seq.Extensions.Logging" />
	<PackageReference Include="SixLabors.ImageSharp" />
	<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" />
	<PackageReference Include="Azure.Security.KeyVault.Secrets" />
	<PackageReference Include="SkiaSharp" />
	<PackageReference Include="SkiaSharp.NativeAssets.Linux" />
	<PackageReference Include="System.Interactive.Async" />
	<PackageReference Include="TimeZoneConverter" />
	<PackageReference Include="TimeZoneNames" />
  </ItemGroup>

  <ItemGroup>
	<Reference Include="DevExtreme.AspNet.Core.PaySpace">
	  <HintPath>..\..\DevExtreme.AspNet.Core.PaySpace.dll</HintPath>
	</Reference>
  </ItemGroup>

  <ItemGroup>
	<None Include="web.release.config" />
  </ItemGroup>

  <Target Name="CustomClean" BeforeTargets="Clean">
	<RemoveDir Directories="node_modules;wwwroot/bundles;wwwroot/pages;wwwroot/lib" />
	<RemoveDir Directories="Client/node_modules" />
  </Target>

  <Target Name="Npm" BeforeTargets="Rebuild">
	<Exec Command="npm i %26%26 npm run dev" WorkingDirectory="Client" />
	<Exec Command="npm i %26%26 npm run build" WorkingDirectory="../PaySpace.Client" />
  </Target>

  <Target Name="Bundle" BeforeTargets="Build">
	<MakeDir Directories="wwwroot/bundles" />
	<Exec Command="npm i" />
	<Exec Command="npm run ts" />
	<Exec Command="dotnet tool restore" />
	<Exec Command="dotnet tool run libman restore" />
	<Exec Command="dotnet tool run bundle" />
  </Target>

</Project>