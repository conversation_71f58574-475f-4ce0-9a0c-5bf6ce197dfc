namespace PaySpace.Venuta.Areas.Company.Controllers.Api
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Caching.Memory;

    using PaySpace.Venuta.Controllers.Api;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models;
    using PaySpace.Venuta.Validation.Annotations;

    [Area("Company")]
    [DisplayName(SystemAreas.CompanyLeaveScheme.Area)]
    [Route("company/{companyId}/leave-scheme-parameter/api")]
    [NavigationControllerName("CompanyLeaveSchemeParameter")]
    public class CompanyLeaveSchemeParameterApiController : ApiController
    {
        private readonly ICompanyLeaveSchemeParameterService leaveSchemeParameterService;
        private readonly IMemoryCache memoryCache;

        public CompanyLeaveSchemeParameterApiController(
            ICompanyLeaveSchemeParameterService leaveSchemeParameterService,
            IMemoryCache memoryCache)
        {
            this.leaveSchemeParameterService = leaveSchemeParameterService;
            this.memoryCache = memoryCache;
        }

        [HttpGet]
        public async Task<List<LeaveSchemeResult>> Get(long companyId)
        {
            var cacheKey = $"LeaveSchemeParameters_{companyId}";

            if (this.memoryCache.TryGetValue(cacheKey, out List<LeaveSchemeResult> cachedResult))
            {
                return cachedResult;
            }

            var result = await this.leaveSchemeParameterService.GetTreeListStructureAsync(companyId);

            // Cache for 5 minutes with sliding expiration
            var cacheOptions = new MemoryCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromMinutes(5),
                Priority = CacheItemPriority.Normal
            };

            this.memoryCache.Set(cacheKey, result, cacheOptions);
            return result;
        }

        [HttpGet("order-numbers/{companyLeaveSchemeId}/{leaveTypeId}/{isNew}")]
        public object GetOrderNumbers(long companyLeaveSchemeId, int leaveTypeId, bool isNew = false)
        {
            if (!Enum.IsDefined(typeof(LeaveType), leaveTypeId))
            {
                return this.BadRequest("Invalid Leave Type");
            }

            var orderNumbers = this.leaveSchemeParameterService.GetOrderNumbersLookup(companyLeaveSchemeId, (LeaveType)leaveTypeId).ToList();
            if (!isNew)
            {
                return orderNumbers;
            }

            // Add one for new / copy records
            var nextOrderNumber = orderNumbers.Count + 1;
            var newOrderNumberObj = new
            {
                text = nextOrderNumber.ToString(),
                value = nextOrderNumber
            };

            orderNumbers.Add(newOrderNumberObj);
            return orderNumbers;
        }

        [HttpPost("invalidate-cache")]
        public IActionResult InvalidateCache(long companyId)
        {
            var cacheKey = $"LeaveSchemeParameters_{companyId}";
            this.memoryCache.Remove(cacheKey);
            return this.Ok(new { message = "Cache invalidated successfully" });
        }
    }
}