<template>
    <div class="offcanvas offcanvas-end" tabindex="-1" :id="id" ref="offcanvas">
        <div v-if="$slots.header" class="offcanvas-header">
            <slot name="header" />
        </div>
        <div v-else class="offcanvas-header hstack gap-2 justify-content-between w-100">
            <h5 class="m-0" v-text="title" />
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" />
        </div>

        <div class="offcanvas-body">
            <slot name="body" />
        </div>
        <div class="d-flex justify-content-end gap-2 p-15" v-if="type === 'form'">
            <DxButton
                :text="formatMessage('lblCancel')"
                type="default"
                styling-mode="outlined"
                @click="() => close()" />
            <DxButton
                :text="saveButtonText ?? formatMessage('lblSave')"
                type="default"
                @click="(e: DxButtonTypes.ClickEvent) => $emit('save', e)" />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { DxButton } from "devextreme-vue";
    import { DxButtonTypes } from "devextreme-vue/cjs/button";
    import { formatMessage } from "devextreme/localization";
    import { defineEmits, defineProps, onMounted, onUnmounted, PropType, ref } from "vue";

    defineProps({
        id: {
            type: String
        },
        saveButtonText: {
            type: String
        },
        title: {
            type: String,
            default: ""
        },
        type: {
            type: String as PropType<"default" | "form">,
            default: "default"
        }
    });

    //TODO: Remove closed, why is it used in custom forms.
    const emit = defineEmits<{
        (e: "closed"): void;
        (e: "save", event: DxButtonTypes.ClickEvent): void;
    }>();

    const offcanvas = ref<any>(null);
    const offcanvasInstance = ref<any>(null);

    const open = () => offcanvasInstance.value.show();
    const close = () => offcanvasInstance.value.hide();

    defineExpose({ open, close });

    onMounted(() => {
        // Init offcanvas instance
        offcanvasInstance.value = new window.bootstrap.Offcanvas(offcanvas.value);
        // Emit events for future needed use
        offcanvas.value.addEventListener("hidden.bs.offcanvas", () => emit("closed"));
    });

    onUnmounted(() => offcanvasInstance.value.dispose());
</script>
