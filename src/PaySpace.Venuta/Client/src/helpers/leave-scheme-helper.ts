import { formatMessage } from "devextreme/localization";
import { useLocalization } from "@/localization";
import { custom } from "devextreme/ui/dialog";

export interface LeaveLookupDataSet {
    Id?: number | string,
    Description?: string,
    Text?: number | string,
    Value?: number | string,
    OrderNumber?: number | string
}

export async function deletionConfirmDialog(): Promise<any> {
    await useLocalization("Company.LeaveSchemeParameter", "System.Notification", "General");

    return await custom({
        showTitle: false,
        messageHtml: formatMessage("lblDeleteRecord"),
        buttons: [
            { text: formatMessage("lblCancel"), onClick: () => false },
            { text: formatMessage("Continue"), onClick: () => true }
        ]
    });
}

export const LeaveEntitlementBands = {
    EmployeeDefined: "ApplyEmployeeDefined",
    ServiceLength: "ApplyServiceLength",
    GradeBands: "ApplyGradeBands"
}

export function getLeaveEntitlementBandOptions() {
    return [
        { text: formatMessage(LeaveEntitlementBands.EmployeeDefined), value: LeaveEntitlementBands.EmployeeDefined },
        { text: formatMessage(LeaveEntitlementBands.ServiceLength), value: LeaveEntitlementBands.ServiceLength },
        { text: formatMessage(LeaveEntitlementBands.GradeBands), value: LeaveEntitlementBands.GradeBands }
    ];
}

// The saved values for the Forfeiture Period dropdown are string characters
export const LeaveForfeiturePeriodValues = {
    EffectiveDateEELinked: "E",
    EmploymentDate: "D",
    GroupJoinDate: "G",
    SpecifyMonth: "M"
}

// No lookup exists for these, only boolean properties on the CompanyLeaveDetail entity
export function getLeaveForfeiturePeriodOptions() {
    return [
        { text: formatMessage("lblGroupJoinDate"), value: LeaveForfeiturePeriodValues.GroupJoinDate },
        { text: formatMessage("lblEffectiveDateEmpLinked"), value: LeaveForfeiturePeriodValues.EffectiveDateEELinked },
        { text: formatMessage("lblSpecifyMonth"), value: LeaveForfeiturePeriodValues.SpecifyMonth },
        { text: formatMessage("lblEmploymentDate"), value: LeaveForfeiturePeriodValues.EmploymentDate }
    ];
}