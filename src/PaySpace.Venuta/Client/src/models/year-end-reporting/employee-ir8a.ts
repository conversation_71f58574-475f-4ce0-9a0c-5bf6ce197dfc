export interface EmployeeIR8A {
    EmployeeIR8AId: number;
    TaxYear: string;
    IsDeclarationByAgent: boolean;
    IsSection45Applicable: boolean;
    IsIR21Submitted: boolean;
    IsIncomeTaxBorneByEmployer: boolean;
    IncomeTaxOption: number;
    EmployerIncomeTaxAmount: number;
    EmployeeLiabilityAmount: number;
    RemissionIncomeAmount: number;
    IsExemptRemissionIncomeApplicable: boolean;
    RemissionExemptIncomeReason: number;
    RemissionExemptIncomeAmount: number;
    OverseasPostingReason: number;
    DesignatedFundName: string | null;
    LumpSumPaymentReason: string | null;
    LumpSumPaymentBasis: string;
    PensionOrProvidentFundName: string | null;
    AmountAccruedFrom1993: number;
    BonusDeclarationDate: Date;
    DirectorsFeesApprovalDate: Date;
    EmployerCpfRefundClaimed: number;
    EmployeeCpfRefundClaimed: number;
    EmployerRefundInterest: number;
    EmployeeRefundInterest: number;
}