<template>
    <DxTabPanel :defer-rendering="false" :selected-index="selectedIndex">
        <DxItem :title="formatMessage('lblIR8A')">
            <EmployeeIR8aGrid :metadata="metadata.EmployeeIR8ADto"
                              :employee-id="employeeId"
                              :edit-url="ir8aEditUrl"
                              :tax-year="currentTaxYear"
                              :ir8-new-record-allowed="ir8NewRecordAllowed"
                              :allow-edit-for-tax-year="allowEditForTaxYear"
                              :tax-year-data-source="taxYearDataSource"
                              @taxYearChanged="setCurrentTaxYear" />
        </DxItem>

        <DxItem :title="formatMessage('lblAppendix8A')">
            <h1>Appendix 8A</h1>
        </DxItem>

        <DxItem :title="formatMessage('lblAppendix8B')">
            <EmployeeAppendix8BGrid
                :metadata="metadata.EmployeeAppendix8BDto"
                :tax-year="currentTaxYear"
                :company-id="companyId"
                :edit-url="editAppendix8bUrl"
                :tax-year-api-url="taxYearUrl"
                :allow-new-appendix8b-record="allowNewAppendix8bRecord"
                :allow-edit-for-tax-year="allowEditForTaxYear"
                :tax-year-data-source="taxYearDataSource" />
        </DxItem>
    </DxTabPanel>
</template>

<script setup lang="ts">
    import { ref } from "vue";

    import DxTabPanel, { DxItem } from "devextreme-vue/tab-panel";
    import DataSource from "devextreme/data/data_source";
    import EmployeeIR8aGrid from "@/pages/employee/year-end-reporting/employee-ir8a-grid.vue";
    import EmployeeAppendix8BGrid from "@/pages/employee/year-end-reporting/employee-appendix8b-grid.vue";

    import { createStore } from "devextreme-aspnet-data-nojquery";
    import { formatMessage } from "devextreme/localization";
    import { useLocalization } from "@/localization";
    import { useMetadata } from "@/metadata";

    const props = defineProps({
        companyId: { type: Number, required: true },
        allowNewAppendix8bRecord: { type: Boolean, required: true },
        ir8aEditUrl: { type: String, required: true },
        ir8NewRecordAllowed: { type: Boolean, required: true },
        employeeId: { type: Number, required: true },
        taxYearId: { type: Number, required: true },
        editAppendix8bUrl: { type: String, required: true },
        taxYearUrl: { type: String, required: true },
        allowEditForTaxYear: { type: Boolean, required: true },
        selectedIndex: { type: Number, required: true }
    });

    await useLocalization("YearEndReporting");
    const metadata = await useMetadata("EmployeeIR8ADto", "EmployeeAppendix8ADto", "EmployeeAppendix8BDto");
    const currentTaxYear = ref<string>("");
    const taxYearDataSource = ref();

    taxYearDataSource.value = await new DataSource({
        store: createStore({
            key: "Value",
            loadUrl: `${props.taxYearUrl}?companyId=${props.companyId}`
        })
    }).load();

    if (props.taxYearId) {
        currentTaxYear.value = taxYearDataSource.value.find(
            (item: { value: number }) => item.value === props.taxYearId
        ).text;
    } else {
        currentTaxYear.value = taxYearDataSource.value.find(
            (item: { text: string }) => item.text === new Date().getFullYear().toString()
        ).text;
    }

    function setCurrentTaxYear(taxYear: string) {
        currentTaxYear.value = taxYear;
    }
</script>
