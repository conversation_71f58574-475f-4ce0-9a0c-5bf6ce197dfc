<template>
    <Card>
        <DxDataGrid
            :data-source="gridDataSource"
            @editing-start="onEditingStart"
            @content-ready="onContentReady"
            @editor-preparing="onEditorPreparing"
            :word-wrap-enabled="true"
            @row-removed="dataGridFactory.onRowRemoved">
            <DxEditing
                :allow-deleting="!metadata.isReadOnly() && allowEditForTaxYear"
                :allow-adding="!metadata.isReadOnly()"
                :allow-updating="true" />
            <DxFilterRow :visible="true" />

            <DxColumn v-bind="metadata.attr('TaxYear')" v-model:filter-value="selectedTaxYear" data-type="string">
                <DxLookup :data-source="taxYearDataSource" displayExpr="text" valueExpr="text" />
            </DxColumn>

            <DxColumn v-bind="metadata.attr('IsIncomeTaxBorneByEmployer')" data-type="boolean" />

            <DxColumn v-bind="metadata.attr('IsExemptRemissionIncomeApplicable')" data-type="boolean" />

            <DxColumn v-bind="metadata.attr('IsSection45Applicable')" data-type="boolean" />

            <DxColumn v-bind="metadata.attr('IsIR21Submitted')" data-type="boolean" />

            <DxColumn v-bind="metadata.attr('IsDeclarationByAgent')" data-type="boolean" />

            <DxToolbar>
                <DxItem location="after">
                    <DxButton
                        v-if="!metadata.isReadOnly()"
                        :disabled="!canAddNewRecord"
                        icon="add"
                        @click="addNewRow"
                        stylingMode="contained"
                        type="default" />
                </DxItem>
            </DxToolbar>
        </DxDataGrid>
    </Card>
</template>

<script setup lang="ts">
    import Card from "@/components/ui/card.vue";
    import dataGridFactory from "@/datagrid-extensions";
    import { IMetadata } from "@/metadata";
    import { odataFactory } from "@/odata-extensions";
    import DataSource from "devextreme/data/data_source";
    import type { PropType } from "vue";
    import { ref, watch } from "vue";

    import { DxButton } from "devextreme-vue/button";
    import {
        DxColumn,
        DxDataGrid,
        DxDataGridTypes,
        DxEditing,
        DxFilterRow,
        DxItem,
        DxLookup,
        DxToolbar
    } from "devextreme-vue/data-grid";

    const props = defineProps({
        editUrl: { type: String, required: true },
        taxYear: { type: String, required: true },
        employeeId: { type: Number, required: true },
        metadata: { type: Object as PropType<IMetadata>, required: true },
        allowEditForTaxYear: { type: Boolean, required: true },
        ir8NewRecordAllowed: { type: Boolean, required: true },
        taxYearDataSource: { type: Array, required: true }
    });

    const emit = defineEmits<(e: "taxYearChanged", year: string) => void>();

    const canAddNewRecord = ref<boolean>();
    const gridDataSource = new DataSource({
        store: odataFactory.createStore("EmployeeIR8A", "EmployeeIR8AId")
    });

    const selectedTaxYear = ref(props.taxYear);

    watch(
        () => props.taxYear,
        newTaxYear => {
            if (selectedTaxYear.value !== newTaxYear) {
                selectedTaxYear.value = newTaxYear;
            }
        }
    );

    watch(selectedTaxYear, newValue => {
        if (newValue) {
            emit("taxYearChanged", newValue);
        }
    });

    function addNewRow() {
        window.location.href = `${props.editUrl}?taxYear=${props.taxYear}&allowEditForTaxYear=${props.allowEditForTaxYear}`;
    }

    function onEditingStart(e: DxDataGridTypes.EditingStartEvent) {
        e.cancel = true;
        window.location.href = `${props.editUrl}?taxYear=${props.taxYear}&allowEditForTaxYear=${props.allowEditForTaxYear}&employeeIR8AId=${e.key}`;
    }

    function onContentReady(e: DxDataGridTypes.ContentReadyEvent) {
        const rowCount = e.component.getVisibleRows().length;
        canAddNewRecord.value = rowCount <= 10;
    }

    function onEditorPreparing(e: DxDataGridTypes.EditorPreparingEvent) {
        if (e.parentType === "filterRow" && e.dataField === "TaxYear") {
            // Remove the (All) option on the dropdown filter as user should never see all tax years at once.
            e.editorOptions.dataSource.postProcess = undefined;
        }
    }
</script>
