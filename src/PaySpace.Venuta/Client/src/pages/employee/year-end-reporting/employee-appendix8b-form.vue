<template>
    <DxForm ref="editFormRef" v-model:form-data="formData">
        <DxGroupItem :caption="formatMessage('lblGeneralDetailsCaption')" :col-count="3">
            <DxSimpleItem
                v-bind="metadata.attr('TaxYear')"
                editor-type="dxSelectBox"
                :editor-options="taxYearOptions" />
            <DxSimpleItem v-bind="metadata.attr('CompanyRegistrationNumber')" />
            <DxSimpleItem v-bind="metadata.attr('CompanyName')" />
            <DxSimpleItem v-bind="metadata.attr('PlanType')" editor-type="dxLookup" :editor-options="planTypeOptions" />
            <DxSimpleItem
                v-bind="metadata.attr('GrantDate')"
                editor-type="dxDateBox"
                :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }" />
            <DxSimpleItem
                v-bind="metadata.attr('ExerciseDate')"
                editor-type="dxDateBox"
                :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }" />
            <DxSimpleItem v-bind="metadata.attr('ExercisePrice')" editor-type="dxNumberBox" :format="format" />
            <DxSimpleItem
                v-bind="metadata.attr('ExerciseOpenMarketPricePerShare')"
                editor-type="dxNumberBox"
                :format="format" />
            <DxSimpleItem v-bind="metadata.attr('SharesAmount')" editor-type="dxNumberBox" :format="format" />
            <DxSimpleItem
                v-bind="metadata.attr('GainsGrossAmount')"
                :editor-options="{ disabled: true }"
                editor-type="dxNumberBox"
                :format="format" />
        </DxGroupItem>
    </DxForm>
    <PageFooter>
        <DxButton
            :text="formatMessage('lblCancel')"
            type="default"
            styling-mode="outlined"
            @click="cancelForm" />

        <DxButton
            v-if="!metadata.isReadOnly() && allowEditForTaxYear"
            :text="formatMessage('lblSave')"
            @click="submitForm"
            type="default" />
    </PageFooter>
</template>

<script setup lang="ts">
    import { DxForm, DxGroupItem, DxSimpleItem } from "devextreme-vue/form";
    import PageFooter from "@/components/ui/page-footer.vue";
    import { DxButton } from "devextreme-vue/button";
    import { odataFactory } from "@/odata-extensions";
    import { ODataFormService } from "@/services/odata-form-service";
    import { ref, watch } from "vue";
    import { useMetadata } from "@/metadata";
    import { formatMessage } from "devextreme/localization";
    import { useLocalization } from "@/localization";

    interface Appendix8B {
        EmployeeAppendix8BId: number | undefined;
        EmployeeId: number;
        CompanyRegistrationNumber: string | undefined;
        CompanyName: string | undefined;
        TaxYear: string;
        PlanType: string | undefined;
        GrantDate: Date | null;
        ExerciseDate: Date | null;
        ExercisePrice: number | undefined;
        ExerciseOpenMarketPricePerShare: number | undefined;
        SharesAmount: number | undefined;
        GainsGrossAmount: number | undefined;
        TotalGainsGrossAmountESOPAndESOWAfter2003: number | undefined;
        TotalGainsGrossAmountESOPBefore2003: number | undefined;
    }

    const props = defineProps({
        baseUrl: { type: String, required: true },
        taxYear: { type: String, required: true },
        employeeId: { type: Number, required: true },
        employeeAppendix8bId: { type: Number, required: false },
        copy: { type: Boolean, required: true },
        allowEditForTaxYear: { type: Boolean, required: true }
    });

    await useLocalization("General", "YearEndReporting", "System.Notification");

    const oDataFormService = new ODataFormService("EmployeeAppendix8B");
    const formData = ref<Appendix8B>({
        EmployeeAppendix8BId: undefined,
        EmployeeId: props.employeeId,
        CompanyRegistrationNumber: undefined,
        CompanyName: undefined,
        TaxYear: props.taxYear,
        PlanType: undefined,
        GrantDate: null,
        ExerciseDate: null,
        ExercisePrice: undefined,
        ExerciseOpenMarketPricePerShare: undefined,
        SharesAmount: undefined,
        GainsGrossAmount: undefined,
        TotalGainsGrossAmountESOPAndESOWAfter2003: undefined,
        TotalGainsGrossAmountESOPBefore2003: undefined
    });
    const editFormRef = ref();
    const isNewEntry = !(props.employeeAppendix8bId && props.employeeAppendix8bId > 0);
    const { EmployeeAppendix8BDto: metadata } = await useMetadata("EmployeeAppendix8BDto");
    
    if (!isNewEntry) {
        formData.value = await oDataFormService.getDataAsync(props.employeeAppendix8bId, isNewEntry);
    }

    const format = {
        type: "decimal"
    };

    const taxYearOptions = {
        dataSource: odataFactory.createLookupStore("TaxYear", "Value"),
        displayExpr: "Description",
        valueExpr: "Value",
        disabled: true
    };

    const planTypeOptions = {
        dataSource: odataFactory.createLookupStore("PlanType", "Value"),
        displayExpr: "Description",
        valueExpr: "Value"
    };

    watch(
        [
            () => formData.value.ExerciseOpenMarketPricePerShare,
            () => formData.value.ExercisePrice,
            () => formData.value.SharesAmount
        ],
        ([ExerciseOpenMarketPricePerShare, ExercisePrice, SharesAmount]) => {
            if (ExerciseOpenMarketPricePerShare && ExercisePrice && SharesAmount) {
                formData.value.GainsGrossAmount = (ExerciseOpenMarketPricePerShare - ExercisePrice) * SharesAmount;
            }
        }
    );

    function cancelForm() {
        window.location.href = props.baseUrl;
    }

    async function submitForm() {
        let success;
        const formInstance = editFormRef.value?.instance;
        const validationResults = formInstance?.validate();

        if (!validationResults.isValid) {
            let errorMessages = validationResults.brokenRules!.map((err: any) => err.message);
            Notifications.showErrors(errorMessages, window.Route.NotificationId);
        } else {
            if (!isNewEntry && !props.copy) {
                success = await oDataFormService.putDataAsync(props.employeeAppendix8bId, formData.value);
            } else {
                if (props.copy) {
                    formData.value.EmployeeAppendix8BId = undefined;
                }

                success = await oDataFormService.postDataAsync(formData.value);
            }

            if (success) {
                window.location.href = props.baseUrl;
            }
        }
    }
</script>
