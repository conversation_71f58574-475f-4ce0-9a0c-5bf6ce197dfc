<template>
    <Card>
        <DxDataGrid
            :data-source="dataSource"
            :key-expr="'employeeAppendix8BId'"
            :word-wrap-enabled="true"
            @editing-start="onEditingStart"
            @editor-preparing="onEditorPreparing"
            @row-removed="dataGridFactory.onRowRemoved">
            <DxEditing
                :allow-deleting="!metadata.isReadOnly() && allowEditForTaxYear"
                :allow-updating="!metadata.isReadOnly()" />
            <DxFilterRow :visible="true" />
            <DxColumn v-bind="metadata.attr('TaxYear')" v-model:filter-value="selectedTaxYear" data-type="string">
                <DxLookup :data-source="taxYearDataSource" displayExpr="text" valueExpr="text" />
            </DxColumn>
            <DxColumn v-bind="metadata.attr('EmployeeEquityBasedRemuneration')" />
            <DxColumn v-bind="metadata.attr('ExerciseDate')" data-type="date" />
            <DxColumn v-bind="metadata.attr('ExercisePrice')" :format="format" />
            <DxColumn v-bind="metadata.attr('ExerciseOpenMarketPricePerShare')" :format="format" />
            <DxColumn v-bind="metadata.attr('SharesAmount')" :format="format" />
            <DxColumn v-bind="metadata.attr('GainsGrossAmount')" :format="format" />
            <DxColumn v-bind="metadata.attr('TotalGainsGrossAmountESOPAndESOWAfter2003')" :format="format" />
            <DxColumn v-bind="metadata.attr('TotalGainsGrossAmountESOPBefore2003')" :format="format" />
            <DxColumn type="buttons">
                <DxGridButton
                    :disabled="metadata.isReadOnly() && !allowEditForTaxYear && !allowNewAppendix8bRecord"
                    icon="copy"
                    @click="copyRow" />
                <DxGridButton name="edit" />
                <DxGridButton name="delete" />
            </DxColumn>
            <DxSummary>
                <DxGroupItem
                    column="ExercisePrice"
                    summary-type="sum"
                    display-format="{0}"
                    :show-in-group-footer="false"
                    :align-by-column="true" />
                <DxGroupItem
                    column="ExerciseOpenMarketPricePerShare"
                    summary-type="sum"
                    display-format="{0}"
                    :show-in-group-footer="false"
                    :align-by-column="true" />
                <DxGroupItem
                    column="SharesAmount"
                    summary-type="sum"
                    display-format="{0}"
                    :show-in-group-footer="false"
                    :align-by-column="true" />
                <DxGroupItem
                    column="GainsGrossAmount"
                    summary-type="sum"
                    display-format="{0}"
                    :show-in-group-footer="false"
                    :align-by-column="true" />
                <DxGroupItem
                    column="TotalGainsGrossAmountESOPAndESOWAfter2003"
                    summary-type="sum"
                    display-format="{0}"
                    :show-in-group-footer="false"
                    :align-by-column="true" />
                <DxGroupItem
                    column="TotalGainsGrossAmountESOPBefore2003"
                    summary-type="sum"
                    display-format="{0}"
                    :show-in-group-footer="false"
                    :align-by-column="true" />

                <DxTotalItem column="ExercisePrice" summary-type="sum" display-format="{0}" />
                <DxTotalItem column="ExerciseOpenMarketPricePerShare" summary-type="sum" display-format="{0}" />
                <DxTotalItem column="SharesAmount" summary-type="sum" display-format="{0}" />
                <DxTotalItem column="GainsGrossAmount" summary-type="sum" display-format="{0}" />
                <DxTotalItem
                    column="TotalGainsGrossAmountESOPAndESOWAfter2003"
                    summary-type="sum"
                    display-format="{0}" />
                <DxTotalItem column="TotalGainsGrossAmountESOPBefore2003" summary-type="sum" display-format="{0}" />
            </DxSummary>

            <DxToolbar>
                <DxItem location="after">
                    <DxButton
                        :disabled="metadata.isReadOnly() && !allowNewAppendix8bRecord"
                        icon="add"
                        @click="addNewRow"
                        stylingMode="contained"
                        type="default" />
                </DxItem>
            </DxToolbar>
        </DxDataGrid>
    </Card>
</template>

<script setup lang="ts">
    import { watch, ref } from "vue";
    import type { PropType } from "vue";
    import dataGridFactory from "@/datagrid-extensions";
    import { odataFactory } from "@/odata-extensions";
    import DataSource from "devextreme/data/data_source";
    import { DxButton } from "devextreme-vue/button";
    import {
        DxDataGrid,
        DxColumn,
        DxButton as DxGridButton,
        DxEditing,
        DxFilterRow,
        DxLookup,
        DxItem,
        DxToolbar,
        DxSummary,
        DxTotalItem,
        DxGroupItem,
        DxDataGridTypes
    } from "devextreme-vue/data-grid";
    import { IMetadata } from "@/metadata";
    import Card from "@/components/ui/card.vue";

    const emit = defineEmits<(e: "taxYearChanged", year: string) => void>();

    const props = defineProps({
        taxYear: { type: String, required: true },
        companyId: { type: Number, required: true },
        allowNewAppendix8bRecord: { type: Boolean, required: true },
        editUrl: { type: String, required: true },
        taxYearApiUrl: { type: String, required: true },
        metadata: { type: Object as PropType<IMetadata>, required: true },
        allowEditForTaxYear: { type: Boolean, required: true },
        taxYearDataSource: { type: Array, required: true }
    });

    const format = {
        type: "decimal"
    };

    const selectedTaxYear = ref(props.taxYear);

    const dataSource = new DataSource({
        store: odataFactory.createStore("EmployeeAppendix8B", "EmployeeAppendix8BId")
    });

    watch(
        () => props.taxYear,
        newTaxYear => {
            if (selectedTaxYear.value !== newTaxYear) {
                selectedTaxYear.value = newTaxYear;
            }
        },
        { immediate: true }
    );

    // Watch for user changes to the filter value
    watch(selectedTaxYear, newTaxYear => {
        if (newTaxYear && newTaxYear !== props.taxYear) {
            emit("taxYearChanged", newTaxYear);
        }
    });

    function addNewRow() {
        window.location.href = `${props.editUrl}?TaxYear=${props.taxYear}&AllowEditForTaxYear=${props.allowEditForTaxYear}`;
    }

    function copyRow(e: DxDataGridTypes.ColumnButtonClickEvent) {
        window.location.href = `${props.editUrl}?TaxYear=${props.taxYear}&AllowEditForTaxYear=${props.allowEditForTaxYear}&EmployeeAppendix8BId=${e.row.data.EmployeeAppendix8BId}&Copy=${true}`;
    }

    function onEditingStart(e: DxDataGridTypes.EditingStartEvent) {
        e.cancel = true;
        window.location.href = `${props.editUrl}?TaxYear=${props.taxYear}&AllowEditForTaxYear=${props.allowEditForTaxYear}&EmployeeAppendix8BId=${e.key}`;
    }

    function onEditorPreparing(e: DxDataGridTypes.EditorPreparingEvent) {
        if (e.parentType === "filterRow" && e.dataField === "TaxYear") {
            // Remove the (All) option on the dropdown filter as user should never see all tax years at once.
            e.editorOptions.dataSource.postProcess = undefined;
        }
    }
</script>
