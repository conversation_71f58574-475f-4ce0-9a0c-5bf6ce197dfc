<template>
    <DxForm ref="editFormRef" v-model:form-data="formData" validation-group="validationGroup">
        <!-- General Details -->
        <DxGroupItem :caption="formatMessage('lblGeneralDetailsCaption')" :col-count="3">
            <DxItem
                v-bind="metadata.attr('TaxYear')"
                editor-type="dxLookup"
                :disabled="true"
                :editor-options="{
                    value: taxYear.toString(),
                    dataSource: lookups.taxYearDataSource,
                    displayExpr: 'Description',
                    valueExpr: 'Value'
                }" />
            <DxItem
                v-bind="metadata.attr('IsDeclarationByAgent')"
                :editorOptions="{ text: formatMessage('lblIsDeclarationByAgent') }"
                :label="{ visible: false }"
                editorType="dxCheckBox" />

            <DxItem
                v-bind="metadata.attr('IsSection45Applicable')"
                editorType="dxCheckBox"
                :editorOptions="{ text: formatMessage('lblIsSection45Applicable') }"
                :label="{ visible: false }" />

            <DxItem
                v-bind="metadata.attr('IsIR21Submitted')"
                editorType="dxCheckBox"
                :editorOptions="{ text: formatMessage('lblIsIR21Submitted') }"
                :label="{ visible: false }" />

            <DxItem
                v-bind="metadata.attr('IsIncomeTaxBorneByEmployer')"
                editorType="dxCheckBox"
                :editorOptions="{ text: formatMessage('lblIsIncomeTaxBorneByEmployer') }"
                :label="{ visible: false }" />

            <DxItem
                v-bind="metadata.attr('IncomeTaxOptionId')"
                data-field="IncomeTaxOption"
                editor-type="dxLookup"
                :visible="formData?.IsIncomeTaxBorneByEmployer"
                :is-required="true"
                :editor-options="{
                    dataSource: lookups.incomeTaxOptionsDatasource,
                    displayExpr: 'Description',
                    valueExpr: 'Value',
                    searchEnabled: true,
                    showClearbutton: true
                }" />

            <DxItem
                v-bind="metadata.attr('EmployerIncomeTaxAmount')"
                :visible="visibilityExpressions.isEmployerIncomeTaxAmountVisible"
                editorType="dxNumberBox" />

            <DxItem
                v-bind="metadata.attr('EmployeeLiabilityAmount')"
                :visible="visibilityExpressions.isEmployeeLiabilityAmountVisible"
                editorType="dxNumberBox" />

            <DxItem v-bind="metadata.attr('RemissionIncomeAmount')" editorType="dxNumberBox" />

            <DxItem
                editorType="dxCheckBox"
                v-bind="metadata.attr('IsExemptRemissionIncomeApplicable')"
                :editorOptions="{ text: formatMessage('lblIsExemptRemissionIncomeApplicable') }"
                :label="{ visible: false }" />

            <DxItem
                v-bind="metadata.attr('RemissionExemptIncomeReasonId')"
                data-field="RemissionExemptIncomeReason"
                editor-type="dxLookup"
                :visible="formData?.IsExemptRemissionIncomeApplicable"
                :is-required="true"
                :editor-options="{
                    dataSource: lookups.remissionExemptIncomeOptionsDataSource,
                    displayExpr: 'Description',
                    valueExpr: 'Value',
                    showClearButton: true
                }" />

            <DxItem
                v-bind="metadata.attr('RemissionExemptIncomeAmount')"
                :visible="visibilityExpressions.isRemessionIncomeAmountVisible"
                editorType="dxNumberBox" />

            <DxItem
                v-bind="metadata.attr('OverseasPostingReasonId')"
                data-field="OverseasPostingReason"
                editor-type="dxLookup"
                :visible="visibilityExpressions.isOverseasPostingVisible"
                :is-required="true"
                :editor-options="{
                    dataSource: lookups.overseasPostingOptionsDataSource,
                    displayExpr: 'Description',
                    valueExpr: 'Value',
                    showClearButton: true
                }" />

            <DxItem v-bind="metadata.attr('DesignatedFundName')" />
        </DxGroupItem>

        <!-- Lump Sum -->
        <DxGroupItem :caption="formatMessage('lblLumpsumCaption')" :col-count="3">
            <DxItem v-bind="metadata.attr('LumpSumPaymentReason')" />

            <DxItem
                v-bind="metadata.attr('LumpSumPaymentBasis')"
                :is-required="true"
                :visible="formData?.LumpSumPaymentReason?.length > 0" />
        </DxGroupItem>

        <!-- Retirement -->
        <DxGroupItem :caption="formatMessage('lblRetirementCaption')" :col-count="3">
            <DxItem v-bind="metadata.attr('PensionOrProvidentFundName')" />

            <DxItem
                v-bind="metadata.attr('AmountAccruedFrom1993')"
                :visible="formData?.PensionOrProvidentFundName?.length > 0"
                editorType="dxNumberBox" />
        </DxGroupItem>

        <!-- Overrides -->
        <DxGroupItem :caption="formatMessage('lblCompanyDefaultsCaption')" :col-count="3">
            <DxItem v-bind="metadata.attr('BonusDeclarationDate')" editor-type="dxDateBox" />
            <DxItem v-bind="metadata.attr('DirectorsFeesApprovalDate')" editor-type="dxDateBox" />
        </DxGroupItem>

        <!-- CTF Refund Details -->
        <DxGroupItem :caption="formatMessage('lblCtfRefundDetailsCaption')" :col-count="3">
            <DxItem v-bind="metadata.attr('EmployerCpfRefundClaimed')" editorType="dxNumberBox" />
            <DxItem v-bind="metadata.attr('EmployeeCpfRefundClaimed')" editorType="dxNumberBox" />
            <DxItem v-bind="metadata.attr('EmployerRefundInterest')" editorType="dxNumberBox" />
            <DxItem v-bind="metadata.attr('EmployeeRefundInterest')" editorType="dxNumberBox" />
        </DxGroupItem>
    </DxForm>
    <PageFooter>
        <DxButton :text="formatMessage('lblCancel')" @click="goToIndexPage" styling-mode="outlined" type="default" />
        <DxButton
            v-if="!metadata.isReadOnly() || allowEditforTaxYear"
            :text="formatMessage('lblSave')"
            @click="saveData"
            type="default" />
    </PageFooter>
</template>

<script setup lang="ts">
    import { computed, ref } from "vue";

    import PageFooter from "@/components/ui/page-footer.vue";
    import { DxButton } from "devextreme-vue/button";
    import { DxForm, DxGroupItem, DxItem } from "devextreme-vue/form";

    import { useLocalization } from "@/localization";
    import { useMetadata } from "@/metadata";
    import { EmployeeIR8A } from "@/models/year-end-reporting/employee-ir8a";
    import { odataFactory } from "@/odata-extensions";
    import { ODataFormService } from "@/services/odata-form-service";
    import DataSource from "devextreme/data/data_source";
    import { formatMessage } from "devextreme/localization";

    const props = defineProps({
        employeeIr8aId: { type: Number, required: false },
        taxYear: { type: Number, required: true },
        employeeId: { type: Number, required: true },
        companyId: { type: Number, required: true },
        indexUrl: { type: String, required: true },
        taxYearId: { type: Number, required: true },
        allowEditforTaxYear: { type: Boolean, required: true }
    });

    await useLocalization("General", "YearEndReporting", "System.Notification");
    const { EmployeeIR8ADto: metadata } = await useMetadata("EmployeeIR8ADto");

    const remissionExemptIncomeRequiredCodes = ["OCLA", "SEAMAN", "EXMPT", "PENFUNDTAXCONC"];
    const overSeasPostingRequiredCodes = ["IOE", "OVERSEASEMPLPENFUND"];
    const employeeLiabilityAmountRequiredCodes = ["H"];
    const employerIncomeTaxAmount = ["P"];

    const oDataFormService = new ODataFormService("EmployeeIR8A");
    const formData = ref<EmployeeIR8A | any>();
    const editFormRef = ref();

    const isNewEntry = !(props.employeeIr8aId && props.employeeIr8aId > 0);
    if (isNewEntry) {
        formData.value = {
            EmployeeIR8AId: 0,
            IsIncomeTaxBorneByEmployer: false,
            IsExemptRemissionIncomeApplicable: false,
            LumpSumPaymentReason: "",
            PensionOrProvidentFundName: ""
        };
    } else {
        const response = await oDataFormService.getDataAsync(props.employeeIr8aId, false);
        formData.value = response;
    }

    const lookups = {
        taxYearDataSource: new DataSource({ store: odataFactory.createLookupStore("TaxYear", "Id") }),
        remissionExemptIncomeOptionsDataSource: new DataSource({
            store: odataFactory.createLookupStore("RemissionExemptIncomeReason", "Id")
        }),
        overseasPostingOptionsDataSource: new DataSource({
            store: odataFactory.createLookupStore("OverseasPostingReason", "Id")
        }),
        incomeTaxOptionsDatasource: new DataSource({ store: odataFactory.createLookupStore("IncomeTaxOption", "Id") })
    };

    const visibilityExpressions = computed(() => ({
        isRemessionIncomeAmountVisible: remissionExemptIncomeRequiredCodes.includes(
            formData.value?.RemissionExemptIncomeReason
        ),
        isOverseasPostingVisible: overSeasPostingRequiredCodes.includes(formData.value?.RemissionExemptIncomeReason),
        isEmployeeLiabilityAmountVisible: employeeLiabilityAmountRequiredCodes.includes(
            formData.value?.IncomeTaxOption
        ),
        isEmployerIncomeTaxAmountVisible: employerIncomeTaxAmount.includes(formData.value?.IncomeTaxOption)
    }));

    async function saveData() {
        let success;
        const formInstance = editFormRef.value?.instance;
        const validationResults = formInstance?.validate();

        if (!validationResults.isValid) {
            let errorMessages = validationResults.brokenRules!.map((err: any) => err.message);
            Notifications.showErrors(errorMessages, window.Route.NotificationId);
        } else {
            if (props.employeeIr8aId && !isNewEntry) {
                success = await oDataFormService.putDataAsync(props.employeeIr8aId, formData.value);
            } else {
                success = await oDataFormService.postDataAsync(formData.value);
            }

            if (success) {
                goToIndexPage(props.taxYearId);
            }
        }
    }

    function goToIndexPage(taxYearId: number = 0) {
        if (taxYearId > 0) {
            window.location.href = props.indexUrl + "?taxYearId=" + taxYearId;
        } else {
            window.location.href = props.indexUrl;
        }
    }
</script>
