<template>
    <DxForm
        ref="advancedFormRef"
        :form-data="formData"
        :col-count="1">
        <!-- Applications Group -->
        <DxGroupItem :caption="formatMessage('lblApplications')">
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('StopDate')"
                :editor-options="{ showClearButton: true }"
                editor-type="dxDateBox" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('AttachmentMandatory')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('AttachmentMandatory') }">
            </DxItem>

            <!-- Conditional Fields based on AttachmentMandatory -->
            <DxItem
                v-if="formData.AttachmentMandatory"
                v-bind="metadata.CompanyLeaveSetupDto.attr('ForceAttachmentOnSecond')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('ForceAttachOnSecond') }"></DxItem>
            <DxItem
                v-if="formData.AttachmentMandatory"
                v-bind="metadata.CompanyLeaveSetupDto.attr('DoNotForceAttachment')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('DoNotForceAttachment') }"></DxItem>
            <DxItem
                v-if="formData.attachmentMandatory"
                :is-required="true"
                v-bind="metadata.CompanyLeaveSetupDto.attr('AfterDays')"
                editor-type="dxNumberBox"></DxItem>

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('ValueLessThan')"
                editor-type="dxNumberBox" />

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('ValueMoreThan')" editor-type="dxNumberBox" />

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('ConsecutiveDays')" editor-type="dxNumberBox" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('IncludePendingApps')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('IncludePendingApps') }"></DxItem>

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('IncludePH')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('IncludePH') }"></DxItem>

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('ReflectInHours')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('ReflectInHours') }"></DxItem>

            <!-- Disallow applications with start dates less than the specified days before a holiday or rest day -->
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('ParcelCombination')"
                :is-required="true"
                editor-type="dxNumberBox"
                :visible="enumService.isBrazil(countryId)"></DxItem>
        </DxGroupItem>

        <!-- Accrual Group -->
        <DxGroupItem :caption="formatMessage('lblAccrual')">
            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('AccrualComponentCodeHours')" />

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('AccrualEngagementDay')" />

            <!-- Prorate leave accrual in engagement or termination month? -->
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('ProrateAccrual')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('ProrateAccrual') }"></DxItem>
        </DxGroupItem>

        <!-- Balances Group -->
        <DxGroupItem :caption="formatMessage('lblBalances')">
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('NegativeLeaveAmount')"
                editor-type="dxNumberBox" />

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('MaxBalance')" editor-type="dxNumberBox" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('DisplayBalanceESS')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('DisplayBalanceESS') }"></DxItem>

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('DoNotShowOnPaySlip')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('DoNotShowOnPaySlip') }"></DxItem>

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('BucketRules')" editor-type="dxTextArea" />
        </DxGroupItem>

        <!-- Leave Liability Group -->
        <DxGroupItem :caption="formatMessage('lblLeaveLiability')">
            <!-- Component that governs leave liability calculation for this bucket (same lookup as encashment component) -->
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('LiabilityComponentId')"
                editor-type="dxSelectBox"
                :editor-options="{
                    dataSource: encashmentComponentLookup,
                    showClearButton: true,
                    displayExpr: 'Description',
                    valueExpr: (item: any) => item?.Id?.toString() ?? null,
                }" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('EncashComponentId')"
                editor-type="dxSelectBox"
                :editor-options="{
                    dataSource: encashmentComponentLookup,
                    showClearButton: true,
                    displayExpr: 'Description',
                    valueExpr: (item: any) => item?.Id?.toString() ?? null
                }" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('DoNotCalculateBceaValue')"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('DoNotCalculateBceaValue') }"></DxItem>

            <!-- Parcel combination -->
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('OffDays')"
                :is-required="true"
                editor-type="dxNumberBox"
                :visible="enumService.isBrazil(countryId)"></DxItem>
        </DxGroupItem>
    </DxForm>
</template>

<script setup lang="ts">
    import DxForm, { DxGroupItem, DxItem } from "devextreme-vue/form";
    import { formatMessage } from "devextreme/localization";
    import enumService from "@/services/enum-service";
    import odataFactory from "@/odata-extensions";
    import { ref } from "vue";

    defineProps({
        formData: { type: Object, required: true },
        metadata: { type: Object, required: true },
        companyId: { type: Number, required: true },
        countryId: { type: Number, required: true }
    });

    const advancedFormRef = ref(null);
    defineExpose({ validate });

    const encashmentComponentLookup = odataFactory.createLookupStore('LeaveEncashmentComponent', 'Value');

    function validate() {
        return advancedFormRef.value?.instance.validate();
    }
</script>