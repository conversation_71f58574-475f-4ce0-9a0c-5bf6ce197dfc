<template>
    <DxForm
        ref="advancedFormRef"
        :form-data="formData"
        :col-count="1">
        <!-- Applications Group -->
        <DxGroupItem :caption="formatMessage('lblApplications')">
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('StopDate')"
                :editor-options="{ showClearButton: true }"
                editor-type="dxDateBox" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('AttachmentMandatory')"
                :editor-options="{ text: formatMessage('AttachmentMandatory') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />

            <DxItem
                v-if="formData.AttachmentMandatory === true"
                v-bind="metadata.CompanyLeaveSetupDto.attr('ForceAttachmentOnSecond')"
                :editor-options="{ text: formatMessage('ForceAttachOnSecond') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />
            <DxItem
                v-if="formData.AttachmentMandatory === true"
                v-bind="metadata.CompanyLeaveSetupDto.attr('DoNotForceAttachment')"
                :editor-options="{ text: formatMessage('DoNotForceAttachment') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />
            <DxItem
                v-if="formData.AttachmentMandatory === true"
                v-bind="metadata.CompanyLeaveSetupDto.attr('AfterDays')"
                editor-type="dxNumberBox"
                :is-required="true" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('ValueLessThan')"
                editor-type="dxNumberBox" />

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('ValueMoreThan')" editor-type="dxNumberBox" />

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('ConsecutiveDays')" editor-type="dxNumberBox" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('IncludePendingApps')"
                :editor-options="{ text: formatMessage('IncludePendingApps') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('IncludePH')"
                :editor-options="{ text: formatMessage('IncludePH') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('ReflectInHours')"
                :editor-options="{ text: formatMessage('ReflectInHours') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />

            <!-- Disallow applications with start dates less than the specified days before a holiday or rest day -->
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('ParcelCombination')"
                :visible="enumService.isBrazil(countryId)"
                editor-type="dxNumberBox"
                :is-required="true" />
        </DxGroupItem>

        <!-- Accrual Group -->
        <DxGroupItem :caption="formatMessage('lblAccrual')">
            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('AccrualComponentCodeHours')" />

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('AccrualEngagementDay')" />

            <!-- Prorate leave accrual in engagement or termination month? -->
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('ProrateAccrual')"
                :editor-options="{ text: formatMessage('ProrateAccrual') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />
        </DxGroupItem>

        <!-- Balances Group -->
        <DxGroupItem :caption="formatMessage('lblBalances')">
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('NegativeLeaveAmount')"
                editor-type="dxNumberBox" />

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('MaxBalance')" editor-type="dxNumberBox" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('DisplayBalanceESS')"
                :editor-options="{ text: formatMessage('DisplayBalanceESS') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('DoNotShowOnPaySlip')"
                :editor-options="{ text: formatMessage('DoNotShowOnPaySlip') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />

            <DxItem v-bind="metadata.CompanyLeaveSetupDto.attr('BucketRules')" editor-type="dxTextArea" />
        </DxGroupItem>

        <!-- Leave Liability Group -->
        <DxGroupItem :caption="formatMessage('lblLeaveLiability')">
            <!-- Component that governs leave liability calculation for this bucket (same lookup as encashment component) -->
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('LiabilityComponentId')"
                editor-type="dxSelectBox"
                :editor-options="{
                    dataSource: encashmentComponentLookup,
                    showClearButton: true,
                    displayExpr: 'Description',
                    valueExpr: (item: any) => item?.Id?.toString() ?? null,
                }" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('EncashComponentId')"
                editor-type="dxSelectBox"
                :editor-options="{
                    dataSource: encashmentComponentLookup,
                    showClearButton: true,
                    displayExpr: 'Description',
                    valueExpr: (item: any) => item?.Id?.toString() ?? null
                }" />

            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('DoNotCalculateBceaValue')"
                :editor-options="{ text: formatMessage('DoNotCalculateBceaValue') }"
                :label="{ visible: false }"
                editor-type="dxCheckBox" />

            <!-- Parcel combination (integers only) -->
            <DxItem
                v-bind="metadata.CompanyLeaveSetupDto.attr('OffDays')"
                :visible="enumService.isBrazil(countryId)"
                :editor-options="numberboxEditorOptions"
                editor-type="dxNumberBox"
                :is-required="true" />
        </DxGroupItem>
    </DxForm>
</template>

<script setup lang="ts">
    import DxForm, { DxGroupItem, DxItem } from "devextreme-vue/form";
    import { formatMessage } from "devextreme/localization";
    import enumService from "@/services/enum-service";
    import odataFactory from "@/odata-extensions";
    import { ref, computed } from "vue";

    const props = defineProps({
        formData: { type: Object, required: true },
        metadata: { type: Object, required: true },
        countryId: { type: Number, required: true }
    });

    const advancedFormRef = ref(null);
    defineExpose({ validate });

    const encashmentComponentLookup = odataFactory.createLookupStore('LeaveEncashmentComponent', 'Value');

    // Computed property to ensure reactivity for AttachmentMandatory
    const isAttachmentMandatory = computed(() => {
        return props.formData?.AttachmentMandatory === true;
    });

    function validate() {
        return advancedFormRef.value?.instance.validate();
    }

    const numberboxEditorOptions = {
        min: 0,
        max: 30,
        step: 1,
        format: "#",
        showSpinButtons: false
    };
</script>