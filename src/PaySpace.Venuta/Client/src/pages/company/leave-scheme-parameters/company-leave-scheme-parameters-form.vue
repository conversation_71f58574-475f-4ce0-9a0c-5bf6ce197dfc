<template>
    <DxTabPanel :defer-rendering="false">
        <DxTabItem :title="formatMessage('lblDetails')">
            <DxForm
                ref="detailsFormRef"
                :form-data="formData"
                :col-count="1">
                <!-- Details group -->
                <DxGroupItem :caption="formatMessage('lblDetails')">
                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('EffectiveDate')"
                        editor-type="dxDateBox"
                        :is-required="true"
                        :editor-options="{
                            readOnly: !props.isCopy && !props.isNew,
                            dateSerializationFormat: 'yyyy-MM-dd'
                        }" />

                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('CompanyLeaveSchemeId')"
                        :label="{ text: formatMessage('LeaveScheme') }"
                        editor-type="dxSelectBox"
                        :is-required="true"
                        :editor-options="{
                            readOnly: !props.isCopy && !props.isNew,
                            dataSource: leaveSchemeLookup,
                            displayExpr: 'Description',
                            valueExpr: 'Id'
                        }" />

                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('LeaveType')"
                        editor-type="dxSelectBox"
                        :is-required="true"
                        :editor-options="{
                            dataSource: leaveTypeLookup,
                            displayExpr: 'Description',
                            valueExpr: 'Id',
                            onValueChanged: onLeaveTypeChanged
                        }" />

                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('LeaveDescription')"
                        editor-type="dxTextBox"
                        :is-required="true" />

                    <!-- Order Number (custom lookup) -->
                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('OrderNumber')"
                        editor-type="dxSelectBox"
                        :is-required="true"
                        :editor-options="{
                            dataSource: orderNumberDataSource,
                            valueExpr: 'value',
                            displayExpr: 'text'
                        }" />

                    <!-- Entitlement Band (custom dropdown) -->
                    <DxItem
                        editor-type="dxSelectBox"
                        :label="{ text: formatMessage('lblEntitlementBand') }"
                        :editor-options="{
                            dataSource: computedLeaveEntitlementBandOptions,
                            valueExpr: 'value',
                            displayExpr: 'text',
                            showClearButton: true,
                            value: selectedEntitlementBand,
                            onValueChanged: (e: any) => { selectedEntitlementBand = e.value; }
                        }" />
                </DxGroupItem>

                <!-- Accrual group (Employee Defined) -->
                <DxGroupItem
                    :caption="formatMessage('lblAccrual')"
                    :visible="selectedEntitlementBand === LeaveEntitlementBands.EmployeeDefined || selectedEntitlementBand == null">
                    <!-- Leave credits to accrue -->
                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('Accrual')"
                        :visible="selectedEntitlementBand == null"
                        editor-type="dxNumberBox"
                        :is-required="true" />

                    <!-- Leave credit unit -->
                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('LeaveAccrualValue')"
                        editor-type="dxSelectBox"
                        :is-required="true"
                        :editor-options="{
                            dataSource: leaveAccrualValueLookup,
                            valueExpr: 'Id',
                            displayExpr: 'Description'
                        }" />

                    <!-- Accrue every -->
                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('AccrualPeriodValue')"
                        editor-type="dxNumberBox"
                        :is-required="true" />

                    <!-- Accrual period -->
                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('AccrualPeriod')"
                        :is-required="true"
                        editor-type="dxSelectBox"
                        :editor-options="{
                            dataSource: leaveAccrualPeriodLookup,
                            valueExpr: 'Id',
                            displayExpr: 'Description'
                        }" />

                    <!-- Occurence (required when Accrual Period is 'Years') -->
                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('AccrualOption')"
                        :is-required="formData?.AccrualPeriod === LeaveAccrualPeriod.Year"
                        :visible="formData?.AccrualPeriod === LeaveAccrualPeriod.Year"
                        editor-type="dxSelectBox"
                        :editor-options="{
                            dataSource: leaveAccrualOptionLookup,
                            valueExpr: 'Id',
                            displayExpr: 'Description',
                            onValueChanged: (item: any) => {
                                // Ensures prorate-check box does not show the hidden fields below
                                if (item.value !== LeaveAccrualOption.Upfront) {
                                    formData.UpfrontProRateOptions = false;
                                }
                            }
                        }" />
                </DxGroupItem>

                <!-- Accrual group (Length of Service) -->
                <DxGroupItem
                    :caption="formatMessage('lblAccrual')"
                    :visible="selectedEntitlementBand === LeaveEntitlementBands.ServiceLength">
                    <DxDataGrid :data-source="formData.CompanyLeaveServiceLengths" :column-auto-width="true">
                        <DxEditing
                            :allow-updating="allowEdit"
                            :allow-adding="allowEdit"
                            :allow-deleting="allowEdit"
                            mode="batch" />

                        <DxColumn v-bind="metadata.CompanyLeaveServiceLengthDto.attr('ServiceDescription')">
                            <DxRequiredRule />
                        </DxColumn>

                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('StartYear')"
                            editor-type="dxNumberBox">
                            <DxRequiredRule />
                        </DxColumn>

                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('EndYear')"
                            editor-type="dxNumberBox">
                            <DxRequiredRule />
                        </DxColumn>

                        <!-- only visible when the GradeBasedAccrual setting is not enabled -->
                        <DxColumn
                            v-if="!isGradeBasedAccrualEnabled"
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('Accrual')"
                            :caption="formatMessage('Accrual')"
                            editor-type="dxNumberBox">
                            <DxRequiredRule />
                        </DxColumn>

                        <!-- visible and uses the a lookup if the GradeBasedAccrual setting is enabled -->
                        <DxColumn
                            v-if="isGradeBasedAccrualEnabled"
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('ExtraFieldId')"
                            :caption="formatMessage('ServiceGrade')"
                            editor-type="dxLookup">
                            <DxLookup :data-source="gradeFieldValuesLookup" value-expr="Id" display-expr="Description" />
                            <DxRequiredRule />
                        </DxColumn>

                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('LeaveAccrualValue')"
                            editor-type="dxLookup">
                            <DxLookup :data-source="leaveAccrualValueLookup" value-expr="Id" display-expr="Description" />
                            <DxRequiredRule />
                        </DxColumn>

                        <!-- Accrue every -->
                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('AccrualPeriodValue')"
                            editor-type="dxNumberBox">
                            <DxRequiredRule />
                        </DxColumn>

                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('AccrualPeriod')"
                            :caption="formatMessage('LeaveForfeitPeriod')"
                            editor-type="dxSelectBox"
                            :editor-options="{
                                dataSource: leaveAccrualPeriodLookup,
                                valueExpr: 'Id',
                                displayExpr: 'Description'
                            }">
                            <DxRequiredRule />
                        </DxColumn>

                        <!-- Max balance -->
                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('MaxBalanceFieldId')"
                            :visible="isGradeBasedMaxBalanceEnabled"
                            editor-type="dxLookup">
                            <DxLookup :data-source="gradeFieldValuesLookup" value-expr="Id" display-expr="Description" />
                            <DxRequiredRule />
                        </DxColumn>
                    </DxDataGrid>
                </DxGroupItem>

                <!-- Accrual group (Grades) -->
                <DxGroupItem
                    :caption="formatMessage('lblAccrual')"
                    :visible="selectedEntitlementBand === LeaveEntitlementBands.GradeBands">
                    <DxDataGrid :data-source="formData.CompanyLeaveServiceLengths" :column-auto-width="true">
                        <DxEditing :allow-updating="true" :allow-adding="true" :allow-deleting="true" mode="batch" />

                        <DxColumn v-bind="metadata.CompanyLeaveServiceLengthDto.attr('ServiceDescription')">
                            <DxRequiredRule />
                        </DxColumn>

                        <!-- Grades (saves the code from the chosen OrganizationGrade) -->
                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('GradeCode')"
                            :caption="formatMessage('ApplyGradeBands')">
                            <DxLookup :data-source="organizationGradesLookup" value-expr="Value" display-expr="Description" />
                            <DxRequiredRule />
                        </DxColumn>

                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('Accrual')"
                            editor-type="dxNumberBox">
                            <DxRequiredRule />
                        </DxColumn>

                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('LeaveAccrualValue')"
                            editor-type="dxLookup">
                            <DxLookup :data-source="leaveAccrualValueLookup" value-expr="Id" display-expr="Description" />
                            <DxRequiredRule />
                        </DxColumn>

                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('AccrualPeriodValue')"
                            editor-type="dxNumberBox">
                            <DxRequiredRule />
                        </DxColumn>

                        <DxColumn
                            v-bind="metadata.CompanyLeaveServiceLengthDto.attr('AccrualPeriod')"
                            :caption="formatMessage('LeaveForfeitPeriod')"
                            editor-type="dxSelectBox"
                            :editor-options="{
                                dataSource: leaveAccrualPeriodLookup,
                                valueExpr: 'Id',
                                displayExpr: 'Description'
                            }">
                            <DxRequiredRule />
                        </DxColumn>
                    </DxDataGrid>
                </DxGroupItem>
            </DxForm>
        </DxTabItem>

        <!-- Tab 2: Proration -->
        <DxTabItem :title="formatMessage('lblProration')" v-if="formData?.AccrualOption == LeaveAccrualOption.Upfront">
            <DxForm
                ref="prorationFormRef"
                :form-data="formData"
                label-location="top"
                :col-count="1">
                <DxGroupItem :caption="formatMessage('lblProration')">
                    <!-- Prorate accrual -->
                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('UpfrontProRateOptions')"
                        :editor-options="{ text: formatMessage('UpfrontProRateOptions') }"
                        :label="{ visible: false }"
                        editor-type="dxCheckBox" />

                    <DxGroupItem :visible="formData?.UpfrontProRateOptions">
                        <!-- Leave days p/month to accrue -->
                        <DxItem
                            v-bind="metadata.CompanyLeaveSetupDto.attr('UpfrontMonthlyAccrual')"
                            editor-type="dxNumberBox"
                            :is-required="true" />

                        <!-- For the first number of months -->
                        <DxItem
                            v-bind="metadata.CompanyLeaveSetupDto.attr('UpfrontAccrualPeriod')"
                            editor-type="dxNumberBox"
                            :is-required="true" />
                    </DxGroupItem>
                </DxGroupItem>
            </DxForm>
        </DxTabItem>

        <!-- Tab 3: Forfeiture Rules -->
        <DxTabItem :title="formatMessage('lblForfeitureRules')">
            <DxForm
                ref="forfeitureRulesFormRef"
                :form-data="formData"
                label-location="top"
                :col-count="1">
                <DxGroupItem :caption="formatMessage('lblForfeitureRules')">
                    <!-- Enable forfeiture rules -->
                    <DxItem
                        editor-type="dxCheckBox"
                        :label="{ visible: false }"
                        :editor-options="{
                            text: formatMessage('CompanyLeaveSetupType'),
                            value: isSetupTypeStandard,
                            onValueChanged: (e: any) => isSetupTypeStandard = e.value
                        }" />

                    <DxGroupItem :visible="isSetupTypeStandard">
                        <!-- Forfeited after every -->
                        <DxItem
                            v-bind="metadata.CompanyLeaveSetupDto.attr('ForfeitPeriod')"
                            editor-type="dxNumberBox" />

                        <!-- Accrual period -->
                        <DxItem
                            v-bind="metadata.CompanyLeaveSetupDto.attr('LeaveForfeitPeriod')"
                            editor-type="dxSelectBox"
                            :editor-options="{
                                dataSource: leaveForfeiturePeriodLookup,
                                valueExpr: 'Id',
                                displayExpr: 'Description'
                            }" />

                        <!-- Accumulation balance -->
                        <DxItem
                            v-bind="metadata.CompanyLeaveSetupDto.attr('CarryOverDays')"
                            editor-type="dxNumberBox" />

                        <!-- Carried forward bucket (custom lookup) -->
                        <DxItem
                            v-bind="metadata.CompanyLeaveSetupDto.attr('ForfeitCompanyLeaveSetupId')"
                            editor-type="dxSelectBox"
                            :editor-options="{
                                dataSource: selectedCarriedForwardBucket,
                                valueExpr: (item: any) => item?.value?.toString() ?? null,
                                displayExpr: 'text'
                            }" />

                        <!-- Forfeiture Period (custom lookup) -->
                        <DxItem
                            v-bind="metadata.CompanyLeaveSetupDto.attr('EffectiveDateForfeit')"
                            editor-type="dxSelectBox"
                            :editor-options="{
                                dataSource: computedLeaveForfeiturePeriodOptions,
                                valueExpr: 'value',
                                displayExpr: 'text',
                                value: selectedForfeiturePeriod,
                                onValueChanged: (e: any) => selectedForfeiturePeriod = e.value
                            }" />
                    </DxGroupItem>

                    <!-- Month -->
                    <DxItem
                        v-bind="metadata.CompanyLeaveSetupDto.attr('DropOffMonth')"
                        :visible="selectedForfeiturePeriod === LeaveForfeiturePeriodValues.SpecifyMonth"
                        editor-type="dxSelectBox"
                        :editor-options="{
                            dataSource: monthsOfYearLookup,
                            displayExpr: 'Description',
                            valueExpr: (item: any) => item?.Id?.toString() ?? null,
                            readOnly: !formData?.CompanyLeaveSetupType
                        }" />
                </DxGroupItem>
            </DxForm>
        </DxTabItem>

        <!-- Tab 4: Advanced Options -->
        <DxTabItem :title="formatMessage('lblAdvancedOptions')">
            <lsp-advanced-options-tab
                ref="advancedRef"
                :form-data="formData"
                :metadata="props.metadata"
                :country-id="props.countryId" />
        </DxTabItem>
    </DxTabPanel>
</template>

<script setup lang="ts">
    import odataFactory from "@/odata-extensions";
    import { computed, ref, watchEffect } from "vue";
    import DataSource from "devextreme/data/data_source";
    import { formatMessage } from "devextreme/localization";
    import { DxButtonTypes } from "devextreme-vue/cjs/button";
    import { ExternalHttpClient, LocalHttpClient } from "@/http-client";
    import { DxTabPanel, DxItem as DxTabItem } from "devextreme-vue/tab-panel";
    import DxForm, { DxGroupItem, DxItem, DxRequiredRule } from "devextreme-vue/form";
    import { DxDataGrid, DxColumn, DxEditing, DxLookup } from "devextreme-vue/data-grid";
    import LspAdvancedOptionsTab from "@pages/company/leave-scheme-parameters/lsp-advanced-options-tab.vue";
    import { CompanyLeaveSetupType, LeaveAccrualOption, LeaveAccrualPeriod, LeaveAccrualValue, LeaveType } from "@/enums";
    import {
        LeaveEntitlementBands,
        LeaveForfeiturePeriodValues,
        getLeaveEntitlementBandOptions,
        getLeaveForfeiturePeriodOptions
    } from "@/helpers/leave-scheme-helper";

    const props = defineProps({
        isNew: { type: Boolean },
        isCopy: { type: Boolean },
        apiUrl: { type: String, required: true },
        metadata: { type: Object, required: true },
        companyId: { type: Number, required: true },
        countryId: { type: Number, required: true },
        allowEdit: { type: Boolean, required: true },
        leaveDetailFormData: { type: Object, required: true },
        carriedForwardBuckets: { type: Object, required: true },
        isGradeBasedAccrualEnabled: { type: Boolean, required: true },
        isGradeBasedMaxBalanceEnabled: { type: Boolean, required: true }
    });

    defineExpose({ save });

    const localHttpClient = new LocalHttpClient();
    const emit = defineEmits<{ (e: "updated"): void; }>();

    // --- Refs ---
    const formData = ref(props.leaveDetailFormData);
    const forfeitureRulesFormRef = ref();
    const orderNumberDataSource = ref();
    const prorationFormRef = ref();
    const detailsFormRef = ref();
    const advancedRef = ref();

    // Used for dynamic OrderNumber lookup which is changed based on the selected LeaveScheme & LeaveType
    const companyLeaveSchemeId = computed(() => formData.value?.CompanyLeaveSchemeId);
    const leaveType = computed(() => formData.value?.LeaveType);

    // Store the LeaveType description to pre-populate the LeaveDescription field on New records

    watchEffect(async () => {
            if (companyLeaveSchemeId.value && leaveType.value !== null && leaveType.value !== undefined) {
                if (leaveType.value === LeaveType.Special) {
                    orderNumberDataSource.value = [{ text: "1", value: 1 }];
                    return;
                }

                const isNewOrCopy = props.isNew || props.isCopy;
                orderNumberDataSource.value = await localHttpClient.get(
                    `${props.apiUrl}/order-numbers/${companyLeaveSchemeId.value}/${leaveType.value}/${isNewOrCopy}`
                );
            } else {
                orderNumberDataSource.value = [];
            }
        }
    );

    // --- Computed Properties for Localized Options ---
    const computedLeaveEntitlementBandOptions = computed(() => { return getLeaveEntitlementBandOptions(); });
    const computedLeaveForfeiturePeriodOptions = computed(() => { return getLeaveForfeiturePeriodOptions(); });

    // --- Computed Properties ---
    const selectedEntitlementBand = computed({
        get() {
            const currentFormData = formData.value;
            if (!currentFormData) {
                return null;
            }
            if (currentFormData.ApplyGradeBands) {
                return LeaveEntitlementBands.GradeBands;
            }
            if (currentFormData.ApplyServiceLength) {
                return LeaveEntitlementBands.ServiceLength;
            }

            if (currentFormData.ApplyEmployeeDefined) {
                return LeaveEntitlementBands.EmployeeDefined;
            }

            return null;
        },
        set(newValue) {
            const currentFormData = formData.value;
            if (!currentFormData) {
                return;
            }

            // set the corresponding flag to true, others false
            formData.value.ApplyEmployeeDefined = newValue === LeaveEntitlementBands.EmployeeDefined;
            formData.value.ApplyServiceLength = newValue === LeaveEntitlementBands.ServiceLength;
            formData.value.ApplyGradeBands = newValue === LeaveEntitlementBands.GradeBands;
        }
    });

    const selectedForfeiturePeriod = computed({
        get() {
            return formData?.value.EffectiveDateForfeit ?? null;
        },
        set(newValue) {
            if (formData?.value) {
                formData.value.EffectiveDateForfeit = newValue;
            }
        }
    });

    // The CompanyLeaveSetupType field stores an integer value of 1 or 2 (Forfeiture rules enabled)
    const isSetupTypeStandard = computed({
        get(): boolean {
            return formData?.value.CompanyLeaveSetupType === CompanyLeaveSetupType.NonAccumulative;
        },
        set(newValue: boolean): void {
            if (formData.value) {
                formData.value.CompanyLeaveSetupType = newValue
                    ? CompanyLeaveSetupType.NonAccumulative
                    : CompanyLeaveSetupType.Accumulative;
            }
        }
    });

    const selectedCarriedForwardBucket = computed(() => {
        const baseOption = [{ text: formatMessage("lblNextCycle"), value: -1 }];

        // Relevant data needed to build the dynamic dropdown
        const currentOrderNumber = formData?.value.OrderNumber;
        const carriedForwardBucketMap = Array.isArray(props.carriedForwardBuckets)
            ? props.carriedForwardBuckets : [];

        // orderNumber 1: The only option allowed is 'The next cycle'
        // Special leave does not allow buckets to be carried forward
        if (currentOrderNumber === 1 || formData?.value.LeaveType === LeaveType.Special) {
            // This is an attempt to get 'The next cycle' to be populated however null/-1 conflicts
            return [{ text: formatMessage("lblNextCycle"), value: formData?.value.ForfeitCompanyLeaveSetupId }];
        }

        // orderNumber 2: Only options allowed are, 'The next cycle' & the 1st LeaveSetup
        if (currentOrderNumber === 2) {
            const carriedForwardBuckets = carriedForwardBucketMap
                .filter(_ => _.order === 1)
                .map(_ => ({
                    text: _.text,
                    value: _.value
                }));

            return [...baseOption, ...carriedForwardBuckets];

        // orderNumber 3+: Has access to all except current LeaveSetup
        } else {
            const carriedForwardBuckets = carriedForwardBucketMap
                .filter(_ => _.value !== formData?.value.CompanyLeaveSetupId)
                .map(_ => ({
                    text: _.text,
                    value: _.value
                }));

            return [...baseOption, ...carriedForwardBuckets];
        }
    });

    // Filters LeaveAccrualPeriodLookup options based on the selected LeaveAccrualValue option

    const leaveAccrualPeriodLookup = computed(() => {
        return new DataSource({
            store: odataFactory.createLookupStore("LeaveAccrualPeriod", "Value"),
            filter: formData.value.LeaveAccrualValue === LeaveAccrualValue.Hours
                ? [["Id", "=", LeaveAccrualPeriod.Days], "or", ["Id", "=", LeaveAccrualPeriod.Hours ]]
                : null
        });
    });

    // --- Lookups ---
    const leaveTypeLookup = odataFactory.createLookupStore("LeaveType", "Value");
    const leaveSchemeLookup = odataFactory.createLookupStore("CompanyLeaveScheme", "Value");
    const leaveAccrualValueLookup = odataFactory.createLookupStore("LeaveAccrualValue", "Value");
    const leaveAccrualOptionLookup = odataFactory.createLookupStore("LeaveAccrualOption", "Value");
    const leaveForfeiturePeriodLookup = odataFactory.createLookupStore("LeaveForfeitPeriod", "Value");
    const monthsOfYearLookup = odataFactory.createLookupStore("MonthsOfYear", "Value");
    const organizationGradesLookup = odataFactory.createLookupStore("Grade", "Value");
    const gradeFieldValuesLookup = odataFactory.createLookupStore("CompanyGradeFieldValue", "Value");

    async function save(e: DxButtonTypes.ClickEvent) {
        e.component.option("disabled", true);

        const { isValid, messages } = validateAllForms();
        if (!isValid) {
            if (messages) {
                Notifications.showErrors(messages, window.Route.NotificationId);
            }

            e.component.option("disabled", false);
            return;
        }

        const data = formData.value;
        const response = await saveOrUpdate(data);

        if (response && response.Message) {
            Notifications.showErrors(response.Message, window.Route.NotificationId);
        } else {
            Notifications.showSuccess(
                formatMessage(props.isNew ? "Create.Success" : "Update.Success"),
                window.Route.NotificationId
            );

            emit("updated");
        }

        e.component.option("disabled", false);
    }

    // --- Helper Methods & Event Handlers ---

    async function saveOrUpdate(data: any) {
        if (props.isNew || props.isCopy) {
            return await new ExternalHttpClient().post(
                `${props.companyId}/CompanyLeaveSetup`,
                data,
                { "Content-Type": "application/json" }
            );
        } else {
            return await new ExternalHttpClient().patch(
                `${props.companyId}/CompanyLeaveSetup/${data.CompanyLeaveDetailId}`,
                data,
                { "Content-Type": "application/json" }
            );
        }
    }

    function validateAllForms() {
        const formsToValidate = [detailsFormRef.value.instance, forfeitureRulesFormRef.value?.instance, advancedRef.value];
        if (prorationFormRef.value) {
            formsToValidate.push(prorationFormRef.value.instance);
        }

        let isAllValid = true;
        let errorMessages: string[] = [];

        // Validate all forms
        for (const form of formsToValidate) {
            if (form) {
                const validationResults = form.validate();
                if (!validationResults.isValid) {
                    isAllValid = false;

                    // Add error messages to the array
                    if (validationResults?.brokenRules) {
                        errorMessages = errorMessages.concat(
                            validationResults.brokenRules.map((err: any) => err.message)
                        );
                    }
                }
            }
        }

        return { isValid: isAllValid, messages: errorMessages };
    }

    // Pre-populate LeaveDescription based on the selected LeaveType
    async function onLeaveTypeChanged(e: any) {
        const selectedLeaveTypeId = e.value;
        if (selectedLeaveTypeId) {
            const selectedItem = await leaveTypeLookup.byKey(selectedLeaveTypeId);

            // Append 'Copy' to the LeaveDescription if this is a copied record
            formData.value.LeaveDescription = props.isCopy
                ? selectedItem.Description + ' Copy'
                : selectedItem.Description ?? '';
        }
    }
</script>