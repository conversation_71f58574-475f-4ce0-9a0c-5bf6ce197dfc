<template>
    <div class="d-flex gap-2 align-items-start h-100">
        <Card>
            <DxTreeList
                items-expr="leaveSchemeParameterResults"
                :data-source="gridDataSource"
                data-structure="tree"
                key-expr="nodeId"
                ref="treeListRef"
                :root-value="null"
                :show-borders="false"
                :show-row-lines="true"
                :auto-expand-all="false"
                :column-auto-width="true"
                :word-wrap-enabled="true"
                :show-column-lines="false"
                :filter-value="filterValue"
                @row-prepared="rowPrepared">
                <DxSearchPanel :visible="true" />
                <DxFilterRow :visible="true" />
                <DxSorting mode="none" />
                <DxEditing
                    :allow-adding="props.allowEdit"
                    :allow-updating="props.allowEdit"
                    :allow-deleting="props.allowEdit" />
                <DxToolbar>
                    <DxItem location="left">
                        <div class="d-flex gap-1">
                            <span>{{ formatMessage("ShowInactiveUnits") }}</span>
                            <DxSwitch v-model:value="showInactiveItems" />
                        </div>
                    </DxItem>
                </DxToolbar>

                <!-- Inactive date -->
                <DxColumn v-bind="metadata.LeaveSchemeParameterResult.attr('stopDate')" :visible="false" />

                <DxColumn v-bind="metadata.LeaveSchemeResult.attr('schemeName')" />

                <DxColumn
                    v-bind="metadata.LeaveSchemeParameterResult.attr('leaveType')"
                    editor-type="dxLookup"
                    sort-order="asc"
                    :sort-index="0">
                    <DxLookup :data-source="leaveTypeLookup" display-expr="Description" value-expr="Id" />
                </DxColumn>

                <DxColumn v-bind="metadata.LeaveSchemeParameterResult.attr('leaveTypeDescription')" />

                <!-- This is a dynamic lookup in the offcanvas only -->
                <DxColumn
                    v-bind="metadata.LeaveSchemeParameterResult.attr('orderNumber')"
                    data-type="string"
                    sort-order="asc"
                    :sort-index="1">
                </DxColumn>

                <DxColumn v-bind="metadata.LeaveSchemeParameterResult.attr('effectiveDate')" data-type="date" />

                <DxColumn type="buttons" width="auto">
                    <DxButton name="add" :visible="isParentRow" @click="handleAddClick" />
                    <DxButton icon="copy" :hint="formatMessage('Copy')" :visible="isChildRow" @click="handleCopyClick" />
                    <DxButton name="edit" :visible="isChildRow" @click="handleEditClick" />
                    <DxButton name="delete" :visible="isChildRow" @click="handleDeleteClick" />
                </DxColumn>
            </DxTreeList>
        </Card>
    </div>

    <Offcanvas
        type="form"
        ref="offCanvasRef"
        @save="saveDetails"
        :title="offcanvasTitleValue"
        @closed="leaveDetailFormData = null">
        <template #body>
            <Suspense>
                <LeaveSchemeParametersForm
                    ref="formRef"
                    @updated="detailsSaved"
                    v-if="leaveDetailFormData"
                    :is-new="isNew"
                    :is-copy="isCopy"
                    :metadata="metadata"
                    :api-url="props.apiUrl"
                    :company-id="props.companyId"
                    :country-id="props.countryId"
                    :allow-edit="props.allowEdit"
                    :leave-detail-form-data="leaveDetailFormData"
                    :carried-forward-buckets="carriedForwardBucketsDataSource"
                    :is-grade-based-accrual-enabled="props.isGradeBasedAccrualEnabled"
                    :is-grade-based-max-balance-enabled="props.isGradeBasedMaxBalanceEnabled" />
                <template #fallback>
                    <div>Loading offcanvas...</div>
                </template>
            </Suspense>
        </template>
    </Offcanvas>
</template>

<script setup lang="ts">
    import {
        DxButton,
        DxColumn,
        DxEditing,
        DxFilterRow,
        DxItem,
        DxLookup,
        DxSearchPanel,
        DxSorting,
        DxToolbar,
        DxTreeList,
        DxTreeListTypes
    } from "devextreme-vue/tree-list";
    import { useMetadata } from "@/metadata";
    import Card from "@components/ui/card.vue";
    import DxSwitch from "devextreme-vue/switch";
    import { computed, nextTick, ref } from "vue";
    import odataFactory from "@/odata-extensions";
    import { CompanyLeaveSetupType } from "@/enums";
    import { useLocalization } from "@/localization";
    import { ExternalHttpClient } from "@/http-client";
    import DataSource from "devextreme/data/data_source";
    import Offcanvas from "@/components/ui/offcanvas.vue";
    import { formatMessage } from "devextreme/localization";
    import { DxButtonTypes } from "devextreme-vue/cjs/button";
    import { createStore } from "devextreme-aspnet-data-nojquery";
    import { deletionConfirmDialog } from "@/helpers/leave-scheme-helper";
    import LeaveSchemeParametersForm from "@pages/company/leave-scheme-parameters/company-leave-scheme-parameters-form.vue";

    const props = defineProps({
        apiUrl: { type: String, required: true },
        companyId: { type: Number, required: true },
        countryId: { type: Number, required: true },
        allowEdit: { type: Boolean, required: true },
        isGradeBasedAccrualEnabled: { type: Boolean, required: true },
        isGradeBasedMaxBalanceEnabled: { type: Boolean, required: true }
    });

    await useLocalization("Company.LeaveSchemeParameter", "System.Notification", "General");

    const metadata = await useMetadata(
        "LeaveSchemeResult",
        "LeaveSchemeParameterResult",
        "CompanyLeaveSetupDto",
        "CompanyLeaveDetailDto",
        "CompanyLeaveServiceLengthDto"
    );

    const formRef = ref();
    const treeListRef = ref();
    const offCanvasRef = ref();
    const isNew = ref(false);
    const isCopy = ref(false);
    const leaveDetailFormData = ref();
    const showInactiveItems = ref(false);
    const carriedForwardBucketsDataSource = ref();

    // This is set when CRUD buttons are clicked
    const offcanvasTitleValue = ref(formatMessage("lblEditLeaveScheme"));

    const leaveTypeLookup = odataFactory.createLookupStore('LeaveType', 'Value');

    const filterValue = computed(() => {
        return showInactiveItems.value ? null : [["stopDate", "=", null], "or", ["stopDate", ">", new Date()]];
    });

    const gridDataSource = new DataSource({
        store: createStore({
            key: "nodeId",
            loadUrl: props.apiUrl
        })
    });

    // --- Event Handlers ---
    async function handleAddClick(options: DxTreeListTypes.ColumnButtonClickEvent) {
        offcanvasTitleValue.value = formatMessage("lblAddLeaveScheme");
        options.event?.stopPropagation();
        isCopy.value = false;
        isNew.value = true;

        const companyLeaveSchemeId = options.row?.data.companyLeaveSchemeId;
        const schemeName = options.row?.data.schemeName;

        // Initialize required values for CompanyLeaveSetup to be created
        leaveDetailFormData.value = {
            CompanyLeaveSetupType: CompanyLeaveSetupType.Accumulative, // Forfeiture Rules disabled by default
            CompanyLeaveSchemeId: companyLeaveSchemeId,
            CompanyLeaveScheme: schemeName,
            CompanyLeaveServiceLengths: [],
            CompanyLeaveDetailId: 0
        };

        await openOffcanvas();
    }

    async function handleEditClick(options: DxTreeListTypes.ColumnButtonClickEvent) {
        offcanvasTitleValue.value = formatMessage("lblEditLeaveScheme");
        options.event?.stopPropagation();
        const rowData = options.row?.data;
        isCopy.value = false;
        isNew.value = false;

        await getSelectedLeaveDetailData(rowData.companyLeaveDetailId);

        const companyLeaveSchemeId = options.row?.node.parent?.data.companyLeaveSchemeId;
        await getCarriedForwardBuckets(companyLeaveSchemeId, rowData.leaveType);

        await openOffcanvas();
    }

    async function handleCopyClick(options: DxTreeListTypes.ColumnButtonClickEvent) {
        offcanvasTitleValue.value = formatMessage("lblCopyLeaveScheme");
        options.event?.stopPropagation();
        const rowData = options.row?.data;
        isNew.value = false;
        isCopy.value = true;

        await getSelectedLeaveDetailData(rowData.companyLeaveDetailId);
        leaveDetailFormData.value.LeaveDescription = leaveDetailFormData.value.LeaveDescription + ' Copy';

        const companyLeaveSchemeId = options.row?.node.parent?.data.companyLeaveSchemeId;
        await getCarriedForwardBuckets(companyLeaveSchemeId, rowData.leaveType);

        await openOffcanvas();
    }

    async function handleDeleteClick(options: DxTreeListTypes.ColumnButtonClickEvent) {
        options.event?.stopPropagation();
        const companyLeaveDetailId = options.row?.data?.companyLeaveDetailId;

        const confirmDialog = await deletionConfirmDialog();

        const confirmed = await confirmDialog.show();
        if (confirmed !== null && confirmed) {
            const response = await new ExternalHttpClient().del(
                `${props.companyId}/CompanyLeaveSetup/${companyLeaveDetailId}`,
                { "Content-Type": "application/json" }
            );

            const responseResult = await response.json();
            if (responseResult && responseResult.Message) {
                Notifications.showErrors(responseResult.Message, window.Route.NotificationId);
            } else if (responseResult.Success) {
                Notifications.showSuccess(
                    formatMessage("Delete.Success"),
                    window.Route.NotificationId
                );
            }
        }

        treeListRef.value.instance.refresh();
    }

    async function openOffcanvas() {
        await nextTick();
        if (offCanvasRef.value) {
            offCanvasRef.value.open();
        }
    }

    async function getSelectedLeaveDetailData(companyLeaveDetailId: number) {
        leaveDetailFormData.value = await new ExternalHttpClient().get(
            `${props.companyId}/CompanyLeaveSetup(${companyLeaveDetailId})`
        );
        console.log(leaveDetailFormData.value);
    }
    async function getCarriedForwardBuckets(companyLeaveSchemeId: number, leaveType: number) {
        const carriedForwardBuckets = await new ExternalHttpClient().get(
            `${props.companyId}/Lookup/ForfeitCompanyLeaveSetup?$filter=CompanyLeaveSchemeId eq ${companyLeaveSchemeId} and LeaveType eq ${leaveType}`
        );

        carriedForwardBucketsDataSource.value = carriedForwardBuckets.value.map((item: any) => ({
            value: item.Id,
            text: item.Description,
            order: item.OrderNumber
        }));
    }

    function saveDetails(e: DxButtonTypes.ClickEvent) {
        // Awaiting save endpoint
        formRef.value.save(e);
    }

    function detailsSaved() {
        offCanvasRef.value.close();
        gridDataSource.reload();
    }

    function rowPrepared(e: DxTreeListTypes.RowPreparedEvent) {
        if (e.rowType !== "data" || !e.data.StopDate) return;
        if (new Date(e.data.StopDate) < new Date()) {
            e.rowElement?.classList.add("alert", "alert-danger");
        }
    }

    // --- Helper Functions for Button Visibility ---
    function isParentRow(options: { row: DxTreeListTypes.Row<any> }): boolean {
        // Parent rows have the children array property
        return !options.row.data.parentId;
    }
    function isChildRow(options: { row: DxTreeListTypes.Row<any> }): boolean {
        // Child rows do NOT have the children array property AND have the detail ID
        return !options.row.data.leaveSchemeParameterResults && options.row.data.companyLeaveDetailId !== undefined;
    }
</script>