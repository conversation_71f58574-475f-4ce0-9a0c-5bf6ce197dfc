import dxForm, { GroupItem } from "devextreme/ui/form";
import dxLookup from "devextreme/ui/lookup";
import { CustomFieldConfig } from "./custom-fields-service";
import { CustomFieldMetadata } from "./field-parser";

export class LookupFieldParser {
    constructor(private entity: string, private form: dxForm | undefined, private config: CustomFieldConfig) {
    }

    public getEntityPath = (): string => {
        let entityPath = this.entity;
        if (this.config.CustomFormCategory) {
            entityPath = this.entity + "/" + this.config.CustomFormCategory;
        }

        let entityUrl = "Lookup/CustomField/" + entityPath + "/lookup?fieldCode=" + this.config.Code;
        if (this.config.ParentFieldCode) {
            entityUrl = "Lookup/CustomField/" + entityPath + "/lookup/cascade?fieldCode=" + this.config.ParentFieldCode;
        }

        return entityUrl;
    }

    public getCountryEntityPath = (): string => {
        let path = (`country-lookup/{countryId}/customField/${this.entity}`)
        let entityUrl = path + `/lookup?fieldCode=${this.config.Code}`;
        if (this.config.ParentFieldCode) {
            entityUrl = path + `/lookup/cascade?fieldCode=${this.config.ParentFieldCode}`;
        }

        return entityUrl;
    }

    public getCustomFormEntityPath = (): string => {
        const categoryCode = this.config.CustomFormCode + "_" + this.config.Level;
        const selector = "$select=Code,Description&$orderby=EffectiveDate desc";

        return `${this.entity}/${categoryCode}?${selector}`;
    }

    public onInitialized = (e: DevExpress.ui.dxLookup.InitializedEvent): void => {
        if (this.config.ParentFieldCode) {
            // Sometimes the field is initialized because the of other fields changing visibility within the group.
            // When that happens, this field should not be set as read only if its parent lookup already has a value.
            // See bug #86187 for more details.
            const formCustomFields = this.form?.option("formData")?.CustomFields;
            const parentField = formCustomFields?.find((field: any) => field.Code === this.config.ParentFieldCode);
            if (!e.component?.option("value") && !parentField?.Value) {
                e.component?.option("readOnly", true);
            }
        }
        PaySpace.ui.DxLookup.onInitialized(e)
    }

    public onValueChanged = (e: { value: any }): void => {
        const dataField = this.getChildDataField(this.config.Code);
        if (dataField) {
            const childEditor = this.form?.getEditor(dataField) as dxLookup;
            if (e.value && childEditor?.option("readOnly")) {
                childEditor?.option("readOnly", false);
            } else if (e.value) {
                childEditor?.resetOption("value");
            } else {
                childEditor?.resetOption("value");
                childEditor?.option("readOnly", true);
            }

            childEditor?.getDataSource().reload();
        }
    }

    public onSelectionChanged = (e: any): void => {
        if (e.selectedItem) {
            e.component.option("OptionCode", e.selectedItem.Code);
        }
    }

    public onRadioSelectionChanged = (e: any): void => {
        const value = e.component.option("value");
        const item = e.component.getDataSource().items().find(_ => _.Description == value);
        if (item) {
            e.component.option("OptionCode", item.Code);
        }
    }

    public beforeSend = (e: { headers: object, params: any }): void => {
        if (this.config.ParentFieldCode) {
            e.params["parentValue"] = this.getParentValue(this.config.ParentFieldCode);
            e.params["filterParentByCode"] = this.config.IsBasicFormat;
        }
    }

    private getParentValue = (parentFieldCode: string): string | undefined => {
        const items = (this.form?.option("items") as GroupItem[]).flatMap(_ => _.items) as GroupItem[];
        const customFields = items?.flatMap(_ => _.items) as GroupItem[];
        let value: string | undefined;
        customFields?.forEach((group) => {
            const items: CustomFieldMetadata[] | undefined = group.items as CustomFieldMetadata[];
            const item = items?.find(_ => _.code === parentFieldCode);
            if (item?.dataField) {
                const formData = this.form?.option("formData");
                if (!this.config.IsBasicFormat) {
                    value = formData.CustomFields[item.index].Value;
                } else {
                    value = formData.CustomFields[item.index].Code;
                }
            }
        });

        return value;
    }

    private getChildDataField = (parentFieldCode: string): string | undefined => {
        const items = (this.form?.option("items") as GroupItem[])?.flatMap(_ => _.items) as GroupItem[];
        const customFields = items?.flatMap(_ => _.items) as GroupItem[];
        let dataField: string | undefined;

        customFields?.forEach((group) => {
            const items: CustomFieldMetadata[] | undefined = group.items as CustomFieldMetadata[];
            const item = items?.find(_ => _.parentCode === parentFieldCode);
            if (item) {
                dataField = item.dataField;
            }
        });

        return dataField;
    }
}