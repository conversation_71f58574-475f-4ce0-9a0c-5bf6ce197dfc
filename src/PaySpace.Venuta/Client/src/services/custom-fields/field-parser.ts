import { EditorType } from "@/enums";
import { LocalHttpClient } from "@/http-client";
import { useLocalization } from "@/localization";
import NextGenODataStore from "@nextgen/nextgen-metadata/dist/odata-store";
import { DxFormTypes } from "devextreme-vue/cjs/form";
import { formatMessage } from "devextreme/localization";
import { Properties as dxDateBoxProperties } from 'devextreme/ui/date_box';
import dxForm, { GroupItem, SimpleItem } from 'devextreme/ui/form';
import { Properties as dxLookupProperties } from 'devextreme/ui/lookup';
import { Properties as dxRadioGroupProperties } from 'devextreme/ui/radio_group';
import env from "../../endpoint-selector";
import { CustomFieldValue } from '../../models/custom-fields/custom-field-value';
import session from '../../session';
import { CustomFieldConfig, Level } from "./custom-fields-service";
import { LookupFieldParser } from "./lookup-field-parser";
import odataFactory from "@/odata-extensions";

export interface CustomFieldMetadata extends SimpleItem {
    customFieldId: number;
    code: string;
    parentCode?: string;
    level: Level;
    group: string;
    fieldGroupOrder?: number;
    index: number;
    advancedSettings: any;
    shouldValidate: boolean;
    showOnGrid: boolean;
    template?: string;
    propertyName: string;
    isReadOnly?: boolean;
    validationExpression?: string;
    customFormCode?: string;
    customFieldFormAreaId: number;
}

useLocalization('CustomFields');

class FieldParser {
    public Parse = (isBasicFormat: boolean, entity: string, form: dxForm | undefined, config: CustomFieldConfig) => {
        const fieldType = this.getFieldType(isBasicFormat, entity, form, config);
        return {
            label: {
                text: config.Label
            },
            helpText: config.Tooltip,
            isRequired: config.IsRequired,
            editorType: fieldType.editorType,
            editorOptions: fieldType.editorOptions,
            template: fieldType.template,
            visibleIndex: fieldType.visibleIndex ?? config.OrderNumber,
            advancedSettings: config.AdvancedSettings,
            shouldValidate: !config.AdvancedSettings ? true : false,
            showOnGrid: config.ShowOnGrid ?? false,
            customFieldId: config.CustomFieldId,
            code: config.Code,
            validationExpression: config.ValidationExpression,
            parentCode: config.ParentFieldCode,
            level: config.Level,
            fieldGroupOrder: config.FieldGroupOrder,
            group: config.Group,
            index: 0,
            colSpan: fieldType.colSpan,
            name: config.Code,
            isReadOnly: config.IsReadOnly,
            propertyName: `${config.Level}/${config.CustomFieldId}/${config.Code}`,
            customFormCode: config.CustomFormCode,
            customFieldFormAreaId: config.CustomFieldFormAreaId
        } as CustomFieldMetadata;
    }

    public OrderMetadata = (isBasicFormat: boolean, customFields: CustomFieldValue[] | undefined, metadata: CustomFieldMetadata[], customFieldFormAreaId: number) => {
        // Handle a new entity.
        if (customFields == null || customFields.length === 0) {
            metadata.forEach((field, index) => {
                //Only load custom fields on the correct tab
                if (field.customFieldFormAreaId == customFieldFormAreaId)
                {
                    field.dataField = `CustomFields[${index}].${this.getDataField(isBasicFormat, field.editorType)}`;
                }
                else {
                    field.visible = false;
                }

                field.index = index;

                if (field.editorType == "dxCheckBox") {
                    field.editorOptions = { ...field.editorOptions, text: field.label?.text };
                    field.label = { ...field.label, visible: false };
                }

                if (field.template == "fileManagerTemplate") {
                    field.editorOptions = { ...field.editorOptions, propertyName: field.propertyName, customFieldId: field.customFieldId, level: field.level, enitytKey: 0 };
                }
            });

            return metadata;
        }

        let orderedFields: CustomFieldMetadata[] = [];
        customFields.forEach((value, index) => {
            let field = isBasicFormat ?
                metadata.find(_ => _.customFieldId == value.CustomFieldId && _.level == value.CustomFieldType) : // NextGen
                metadata.find(_ => _.code == value.Code); // API

            if (field) {
                if (field.editorType == "dxCheckBox") {
                    field.editorOptions = { ...field.editorOptions, text: field.label?.text };
                    field.label = { ...field.label, visible: false };

                    if (isBasicFormat == false && value.Value) {
                        let v = typeof value.Value === 'boolean' ? value.Value : value.Value.toLowerCase();
                        value.Value = (v == "false") ? false : (v == "true") ? true : v;
                    }
                }

                if (field.template == "fileManagerTemplate") {
                    field.editorOptions = { ...field.editorOptions, propertyName: field.propertyName, customFieldId: field.customFieldId, level: field.level, entityKey: value.EntityKey };
                }
                
                //Only load custom fields on the correct tab
                if (field.customFieldFormAreaId == customFieldFormAreaId) {
                    field.dataField = `CustomFields[${index}].${this.getDataField(isBasicFormat, field.editorType)}`;
                }
                else {
                    field.visible = false;
                }
                
                field.index = index;
                orderedFields.push(field);
            }
        });

        return orderedFields.sort(field => {
            if (field.level === "B") {
                return -1;
            }

            if (field.level === "C") {
                return 1;
            }

            return 0;
        });
    }

    public GroupItems(metadata: CustomFieldMetadata[], colCount: number) {
        metadata.forEach(_ => {
            if (_.level === "B") {
                if (_.group == null || _.group === "null") {
                    _.group = formatMessage('lblStatutoryFieldsCaption');
                }
            }

            if (_.level === "C") {
                if (_.group == null || _.group === "null") {
                    _.group = formatMessage('lblCustomFieldsCaption');
                } else {
                    _.group = formatMessage('lblCustomFieldsCaption') + ": " + _.group;
                }
            }
        });

        metadata.sort((n1: any, n2: any) => {
            if (n1.level !== n2.level) {
                return n1.level > n2.level ? 1 : -1;
            }

            if (n1.fieldGroupOrder > n2.fieldGroupOrder) {
                return 1;
            }
            if (n1.fieldGroupOrder < n2.fieldGroupOrder) {
                return -1;
            }
            return 0;
        });

        // see (bottom comments) https://github.com/DevExpress/DevExtreme/issues/7295
        // to prevent jumping of screen, updates to a group should be for a group that is inside a group
        return [{
                itemType: "group",
                items: Object.entries(this.groupBy(metadata, "group")).map(group => {
                    return {
                        itemType: "group",
                    items: [{
                                caption: group[0],
                                itemType: "group",
                                items: group[1],
                                colCount,
                                name: this.FormatGroupName(group[0])
                    } as GroupItem],
                    } as GroupItem;
                })
        } as GroupItem];
    }

    public organizeFormItemsLayout(items: DxFormTypes.Item[]) {
        const stack = [items];

        while (stack.length) {
            const currentItems = stack.pop() || [];
            const nonChildLibraryItems = [];
            const hasChildLibraryItem = currentItems.some(item => item.template === "child-library-template");

            for (const childItem of [...currentItems]) {
                if (childItem.itemType === "group") {
                    const groupItem: DxFormTypes.GroupItem = childItem;
                    stack.push(groupItem.items!);
                    continue;
                }

                if (hasChildLibraryItem && childItem.template !== "child-library-template") {
                    nonChildLibraryItems.push(childItem);
                    currentItems.splice(currentItems.indexOf(childItem), 1);
                }
            }

            if (hasChildLibraryItem) {
                currentItems.push({
                    itemType: "group",
                    colSpan: 3,
                    colCount: 3,
                    items: nonChildLibraryItems
                });
            }
        }
    }

    public FormatGroupName(name: string) {
        return name.replaceAll(" ", "").replaceAll(".", "").replaceAll(":", "");
    }

    public SetDefaultValues(customFields: CustomFieldValue[], metadata: CustomFieldMetadata[]) {
        metadata.forEach(field => {
            if (field) {
                const getItems = (editorOptions: any) => {
                    return Array.isArray(editorOptions.dataSource)
                        ? editorOptions.dataSource
                        : Array.isArray(editorOptions.items)
                          ? editorOptions.items
                          : [];
                };

                const items = getItems(field.editorOptions);

                const defaultOption = items
                    .flatMap((item: { Code: string; Description: string; DefaultValue: boolean | null }) => [item])
                    .find((opt: { DefaultValue: boolean | null }) => opt.DefaultValue === true);

                if (defaultOption) {
                    const fieldToUpdate = customFields.find(_ => _.CustomFieldId === field.customFieldId);

                    if (fieldToUpdate && !fieldToUpdate.Value) {
                        fieldToUpdate.Value = defaultOption.Code; // Set grid custom field value
                    }

                    if (fieldToUpdate && !fieldToUpdate.Code) {
                        fieldToUpdate.Code = defaultOption.Code; // Set form custom field value
                    }
                }
            }
        });

        return metadata;
    }

    private getDataField(isBasicFormat: boolean, editorType?: string) {
        if (isBasicFormat) {
            if (editorType === "dxLookup" || editorType === "dxRadioGroup") {
                return "Code";
            }

            return "FieldValue";
        }

        return "Value";
    }

    private getFieldType(isBasicFormat: boolean, entity: string, form: dxForm | undefined, config: CustomFieldConfig) {
        config.IsBasicFormat = isBasicFormat;

        function isDxSelectBox() {
            const lookupFieldParser = new LookupFieldParser(entity, form, config);
            const onSelectionChanged = lookupFieldParser.onSelectionChanged;
            return {
                editorType: "dxLookup",
                helpText: null,
                editorOptions: {
                    dataSource: config.Options,
                    valueExpr: isBasicFormat ? "Code" : "Description",
                    displayExpr: "Description",
                    disabled: config.IsReadOnly ?? false,
                    onSelectionChanged
                } as dxLookupProperties
            };
        }

        function isDxTextBox() {
            return {
                helpText: null,
                editorType: "dxTextBox",
                editorOptions: {
                    disabled: config.IsReadOnly ?? false
                }
            };
        }

        function isDxCheckBox() {
            return {
                helpText: null,
                editorType: "dxCheckBox",
                editorOptions: {
                    disabled: config.IsReadOnly ?? false
                }
            };
        }

        function isDxNumberBox() {
            return {
                helpText: null,
                editorType: "dxNumberBox",
                editorOptions: {
                    disabled: config.IsReadOnly ?? false
                }
            };
        }

        function isDxDateBox() {
            return {
                helpText: null,
                editorType: "dxDateBox",
                editorOptions: {
                    type: 'date',
                    dateSerializationFormat: 'yyyy/MM/dd',
                    disabled: config.IsReadOnly ?? false
                } as dxDateBoxProperties
            };
        }

        function isDxRadioGroup() {
            const lookupFieldParser = new LookupFieldParser(entity, form, config);
            const onOptionChanged = lookupFieldParser.onRadioSelectionChanged;
            return {
                helpText: null,
                editorType: "dxRadioGroup",
                editorOptions: {
                    items: config.Options,
                    valueExpr: isBasicFormat ? "Code" : "Description",
                    displayExpr: "Description",
                    disabled: config.IsReadOnly ?? false,
                    onOptionChanged
                } as dxRadioGroupProperties
            };
        }

        function isDxTextArea() {
            return {
                helpText: null,
                editorType: "dxTextArea",
                editorOptions: {
                    disabled: config.IsReadOnly ?? false
                }
            };
        }

        function isDxLookup() {
            const lookupFieldParser = new LookupFieldParser(entity, form, config);
            const entityUrl = lookupFieldParser.getEntityPath();
            const countryEntityUrl = lookupFieldParser.getCountryEntityPath();

            const beforeSend = lookupFieldParser.beforeSend;
            const onInitialized = lookupFieldParser.onInitialized;
            const onValueChanged = lookupFieldParser.onValueChanged;
            const onSelectionChanged = lookupFieldParser.onSelectionChanged;
            const countryDataSource = odataFactory.createCountryLookupStore("Company", "Value", countryEntityUrl, undefined, beforeSend);
            const companyDataSource = new NextGenODataStore(env.api_url.toString(), session.CompanyId!, entityUrl, () => session.AccessToken, { beforeSend });

            return {
                helpText: null,
                editorType: "dxLookup",
                editorOptions: {
                    dataSource: session.CompanyId != null ? companyDataSource : countryDataSource,
                    valueExpr: isBasicFormat ? "Code" : "Description",
                    displayExpr: "Description",
                    onInitialized,
                    onValueChanged,
                    onSelectionChanged
                } as dxLookupProperties
            };
        }

        function isRepositoryDxLookup() {
            const lookupFieldParser = new LookupFieldParser("CompanyCustomForm", form, config);
            const entityUrl = lookupFieldParser.getCustomFormEntityPath();

            return {
                editorType: "dxLookup",
                editorOptions: {
                    dataSource: {
                        store: new NextGenODataStore(env.api_url.toString(), session.CompanyId!, entityUrl, () => session.AccessToken, {}),
                        filter: [
                            ["Code", "<>", null],
                            "and",
                            [
                                ["InactiveDate", ">", new Date()],
                                "or",
                                ["InactiveDate", "=", null]
                            ],
                            "and",
                            ["EffectiveDate", "<", new Date()],
                        ]
                    },
                    valueExpr: isBasicFormat ? "Code" : "Description",
                    displayExpr: "Description",
                    showClearButton: true,
                    dropDownOptions: {
                        showTitle: false,
                        hideOnOutsideClick: true
                    }
                } as dxLookupProperties
            };
        }

        function isFileManager() {
            const fileUploaderUrl = new LocalHttpClient().getUrl(`{companyid}/FileUploadHandler/api/file-uploader-config?EmployeeId=${session.EmployeeId ?? 0}`);

            return {
                editorType: null,
                editorOptions: {
                    fileUploaderUrl,
                    userId: window.User.UserId,
                    companyId: session.CompanyId,
                    employeeId: session.EmployeeId ?? 0
                },
                template: "fileManagerTemplate"
            }
        }

        function isChildLibrary() {
            return {
                editorType: null,
                colSpan: 3,
                editorOptions: config,
                visibleIndex: 99999 + config.OrderNumber, //Use visible index 99999 + order number for child libraries to ensure they are always last
                template: "child-library-template"
            };
        }

        const fieldTypes = {
            [EditorType.select]: isDxSelectBox,
            [EditorType.text]: isDxTextBox,
            [EditorType.checkbox]: isDxCheckBox,
            [EditorType.number]: isDxNumberBox,
            [EditorType.date]: isDxDateBox,
            [EditorType.radiogroup]: isDxRadioGroup,
            [EditorType.textarea]: isDxTextArea,
            [EditorType.lookup]: isDxLookup,
            [EditorType.repository]: isRepositoryDxLookup,
            [EditorType.attachment]: isFileManager,
            [EditorType.childlibrary]: isChildLibrary
        };

        return fieldTypes[config.Type]();
    }

    private groupBy(list: any[], key: string) {
        return list.reduce((hash: any, obj: any) => {
            if (obj[key] === undefined) {
                return hash;
            }
            return Object.assign(hash, {
                [obj[key]]: (hash[obj[key]] || []).concat(obj)
            })
        }, {});
    }
}

const fieldParser = new FieldParser();
export default fieldParser;
