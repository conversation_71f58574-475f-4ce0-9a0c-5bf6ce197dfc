export interface Session {
    AccessToken: string;
    SessionId: string;
    IsMSS: boolean;
    IsESS: boolean;
    Culture: string;
    NotificationId: string;
    CompanyId?: number;
    EmployeeId?: number;
    FrequencyId?: number;
    CountryId?: number;
}

const session = {
    AccessToken: window.Auth.AccessToken,
    SessionId: window.User.SessionId,
    IsMSS: window.User.IsMSS,
    IsESS: window.User.IsESS,
    Culture: document.documentElement.lang,
    NotificationId: window.Route.NotificationId,
    CompanyId: window.Route.CompanyId,
    EmployeeId: window.Route.EmployeeId,
    FrequencyId: window.Route.FrequencyId,
    CountryId: window.User.CountryId
} as Session;
export default session;