namespace PaySpace.Venuta.Services
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Linq.Expressions;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.Caching.Memory;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;

    public class EnumLookupService : IEnumLookupService
    {
        private readonly ReadOnlyContext context;
        private readonly IDistributedCache distributedCache;
        private readonly IModelMetadataProvider metadataProvider;
        private readonly ICompanyGroupExchangeRateService companyGroupExchangeRateService;

        public EnumLookupService(
            ReadOnlyContext context,
            IDistributedCache distributedCache,
            IModelMetadataProvider metadataProvider,
            ICompanyGroupExchangeRateService companyGroupExchangeRateService)
        {
            this.context = context;
            this.distributedCache = distributedCache;
            this.metadataProvider = metadataProvider;
            this.companyGroupExchangeRateService = companyGroupExchangeRateService;
        }

        public IList<EnumNqfLevel> GetNqfLevels(CultureInfo? culture = null)
        {
            return this.GetData<EnumNqfLevel>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumUIFExemption> GetUIFExemptions(CultureInfo? culture = null)
        {
            return this.GetData<EnumUIFExemption>(culture, (item, desc) => item.ExemptionDescription = desc);
        }

        public IList<EnumDisabledType> GetDisabledTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumDisabledType>(culture, (item, desc) => item.DisabledTypeDescription = desc);
        }

        public IList<EnumTradeUnion> GetTradeUnions(CultureInfo? culture = null)
        {
            return this.GetData<EnumTradeUnion>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumTitle> GetTitles(int countryId, CultureInfo? culture = null)
        {
            var items = this.GetData<EnumTitle>(culture, _ => _.TaxCountryId == null || _.TaxCountryId == countryId, _ => _.TitleCode, 0, (item, desc) => item.TitleDescription = desc, countryId);
            if (items.Count == 0)
            {
                items = this.GetData<EnumTitle>(culture, _ => _.TaxCountryId == 0, _ => _.TitleCode, 0, (item, desc) => item.TitleDescription = desc, 0);
            }

            return items;
        }

        public IList<EnumRace> GetRace(int countryId, CultureInfo? culture = null)
        {
            var items = this.GetData<EnumRace>(culture, _ => _.TaxCountryId == null || _.TaxCountryId == countryId, _ => _.RaceCode, 0, (item, desc) => item.RaceDescription = desc, countryId);
            if (items.Count == 0)
            {
                items = this.GetData<EnumRace>(culture, _ => _.TaxCountryId == 0, _ => _.RaceCode, 0, (item, desc) => item.RaceDescription = desc, 0);
            }

            return items;
        }

        public IList<EnumMaritalStatus> GetMaritalStatus(int countryId, CultureInfo? culture = null)
        {
            var items = this.GetData<EnumMaritalStatus>(culture, _ => _.TaxCountryId == countryId, _ => _.MaritalStatusDescription, 0, (item, desc) => item.MaritalStatusDescription = desc, countryId);
            if (items.Count == 0)
            {
                items = this.GetData<EnumMaritalStatus>(culture, _ => _.TaxCountryId == 0, _ => _.MaritalStatusCode, 0, (item, desc) => item.MaritalStatusDescription = desc, 0);
            }

            return items;
        }

        public IList<EnumCountry> GetAllCountries(CultureInfo? culture = null)
        {
            return this.GetData<EnumCountry>(culture, (item, desc) => item.CountryDescription = desc);
        }

        public IList<EnumPassportCountry> GetPassportCountries(CultureInfo? culture = null)
        {
            return this.GetData<EnumPassportCountry>(culture, (item, desc) => item.PassportCountryDescription = desc);
        }

        public IList<EnumAddressCountry> GetAddressCountries(CultureInfo? culture = null)
        {
            return this.GetData<EnumAddressCountry>(culture, (item, desc) => item.AddressCountryDescription = desc);
        }

        public IList<EnumAccountType> GetAccountTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumAccountType>(culture, _ => true, _ => _.AccountTypeDescription, 0, (item, desc) => item.AccountTypeDescription = desc, string.Empty);
        }

        public IList<EnumCarPaymentType> GetCarPaymentTypes(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumCarPaymentType>(culture, _ => _.TaxCountryId == null || _.TaxCountryId == countryId, _ => _.CarPaymentTypeCode, 0, (item, desc) => item.CarPaymentTypeDescription = desc, countryId);
        }

        public IList<EnumKenyaPurchasedCarOption> GetKenyaPurchasedCarOptions(CultureInfo? culture = null)
        {
            return this.GetData<EnumKenyaPurchasedCarOption>(culture, (item, desc) => item.CarOptionDescription = desc);
        }

        public IList<EnumEmploymentAction> GetEmploymentActions(CultureInfo? culture = null)
        {
            return this.GetData<EnumEmploymentAction>(culture, (item, desc) => item.EmploymentStatusDescription = desc);
        }

        public IList<EnumSDLExemption> GetSDLExemptions(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumSDLExemption>(culture, _ => _.TaxCountryId == countryId, _ => _.ExemptionId, 0, (item, desc) => item.ExemptionDescription = desc, countryId);
        }

        public IList<EnumTaxStatus> GetTaxStatuses(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumTaxStatus>(culture, _ => _.TaxCountryId == countryId && _.IsActive != false, _ => _.TaxStatusCode, 0, (item, desc) => item.TaxStatusDescription = desc, countryId);
        }

        public virtual IList<EnumTerminationReason> GetTerminationReasons(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumTerminationReason>(culture, _ => _.TaxCountryId == countryId || _.TaxCountryId == 0, _ => _.TerminationCode, 0, (item, desc) => item.TerminationDescription = desc, countryId);
        }

        public IList<EnumIdentityType> GetIdentityTypes(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumIdentityType>(culture, _ => _.TaxCountryId == countryId, _ => _.IdentityCode, 0, (item, desc) => item.IdentityDescription = desc, countryId);
        }

        public IList<EnumNaturePerson> GetNaturePersons(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumNaturePerson>(culture, _ => _.TaxCountryId == countryId, _ => _.NaturePersonCode, 0, (item, desc) => item.NaturePersonDescription = desc, countryId);
        }

        public IList<EnumLanguage> GetLanguages(int countryId, CultureInfo culture = null)
        {
            return this.GetData<EnumLanguage>(culture, _ => _.TaxCountryId == countryId, _ => _.LanguageDescription, 0, (item, desc) => item.LanguageDescription = desc, countryId);
        }

        public IList<EnumProvince> GetProvinces(int countryId, CultureInfo? culture = null)
        {
            var items = this.GetData<EnumProvince>(culture, _ => _.AddressCountryId == countryId, _ => _.ProvinceId, 0, (item, desc) => item.ProvinceDescription = desc, countryId);
            if (items.Count == 0)
            {
                // Not all countries have provinces, need to return the default one with the description "not available"
                items = this.GetData<EnumProvince>(culture, _ => _.AddressCountryId == 0, _ => _.ProvinceId, 0, (item, desc) => item.ProvinceDescription = desc, 0);
            }

            return items;
        }

        public IList<EnumMedicalCategory> GetMedicalAidCategories(CultureInfo? culture = null)
        {
            return this.GetData<EnumMedicalCategory>(culture, (item, desc) => item.MedicalCategoryDescription = desc);
        }

        public IList<EnumCurrency> GetCurrencies(long companyGroupId, CultureInfo? culture = null)
        {
            return this.companyGroupExchangeRateService.GetLocalizedCurrenciesByCompanyGroup(companyGroupId);
        }

        public IList<EnumCurrency> GetAllCurrencies(CultureInfo? culture = null)
        {
            return this.GetData<EnumCurrency>(culture, (item, desc) => item.Currency = desc);
        }

        public IList<EnumStandardIndustryCodeSub> GetStandardIndustryCodeSubs(CultureInfo? culture = null)
        {
            return this.GetData<EnumStandardIndustryCodeSub>(culture, (item, desc) => item.SubStandardIndustryCodeDescription = desc);
        }

        public IList<EnumProvince> GetAllProvinces(CultureInfo? culture = null)
        {
            return this.GetData<EnumProvince>(culture, (item, desc) => item.ProvinceDescription = desc);
        }

        public IList<EnumSwazilandAccommodationOption> GetSwazilandAccommodationOptions(CultureInfo? culture = null)
        {
            return this.GetData<EnumSwazilandAccommodationOption>(culture, _ => true, _ => _.AccommodationDescription, 0, (item, desc) => item.AccommodationDescription = desc, string.Empty);
        }

        public IList<EnumSuspensionReason> GetSuspensionReason(int countryId, CultureInfo? culture = null)
        {
            var items = this.GetData<EnumSuspensionReason>(culture, _ => _.TaxCountryId == countryId, _ => _.SuspensionReasonId, 0, (item, desc) => item.SuspensionDescription = desc, countryId);
            if (items.Count == 0)
            {
                items = this.GetData<EnumSuspensionReason>(culture, _ => _.TaxCountryId == 0, _ => _.SuspensionReasonId, 0, (item, desc) => item.SuspensionDescription = desc, 0);
            }

            return items;
        }

        public IList<EnumPositionType> GetPositionTypes(int countryId, CultureInfo? culture = null)
        {
            var positionTypes = this.GetData<EnumPositionType>(culture, _ => _.TaxCountryId == countryId, _ => _.PositionTypeId, 0, (item, desc) => item.PositionTypeDesc = desc, countryId);
            if (positionTypes.Count > 0)
            {
                return positionTypes;
            }

            return this.GetData<EnumPositionType>(culture, _ => _.TaxCountryId == default(long), _ => _.PositionTypeId, 0, (item, desc) => item.PositionTypeDesc = desc, string.Empty);
        }

        public IList<EnumLocalization> GetLocalization(CultureInfo? culture = null)
        {
            return this.GetData<EnumLocalization>(culture, (item, desc) => item.LocalizationDescription = desc);
        }

        public IList<EnumOccupationalLevel> GetOccupationalLevels(int countryId, CultureInfo? culture = null)
        {
            var levels = this.GetData<EnumOccupationalLevel>(culture, _ => _.TaxCountryId == countryId, _ => _.OccupationalLevelId, 0, (item, desc) => item.LevelDescription = desc, countryId);
            if (levels.Count > 0)
            {
                return levels;
            }

            return this.GetData<EnumOccupationalLevel>(culture, _ => _.TaxCountryId == default(long), _ => _.OccupationalLevelId, 0, (item, desc) => item.LevelDescription = desc, string.Empty);
        }

        public IList<EnumOccupationalCategory> GetOccupationalCategories(CultureInfo? culture = null)
        {
            return this.GetData<EnumOccupationalCategory>(culture, (item, desc) => item.CategoryDescription = desc);
        }

        public IList<EnumOfoLevel> GetOfoLevels(int setaId, CultureInfo? culture = null)
        {
            return this.GetData<EnumOfoLevel>(culture, _ => _.SetaId == setaId, _ => _.OfoLevelId, 0, (item, desc) => item.LevelDescription = desc, setaId);
        }

        public IList<EnumRegistrationType> GetMultiContractWorkRegistrationTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumRegistrationType>(culture, (item, desc) => item.RegistrationTypeDescription = desc);
        }

        public IList<EnumRemunerationType> GetMultiContractWorkRemunerationTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumRemunerationType>(culture, (item, desc) => item.RemunerationTypeDescription = desc);
        }

        public IList<EnumInssDeductionIndicator> GetMultiContractWorkInssDeductionIndicators(CultureInfo? culture = null)
        {
            return this.GetData<EnumInssDeductionIndicator>(culture, (item, desc) => item.InssDeductionIndicatorDescription = desc);
        }

        public IList<EnumAlimonyBase> GetAlimonyBases(CultureInfo? culture = null)
        {
            return this.GetData<EnumAlimonyBase>(culture, (item, desc) => item.AlimonyBaseDescription = desc);
        }

        public IList<EnumLeaveType> GetLeaveTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumLeaveType>(culture, (item, desc) => item.LeaveTypeDescription = desc);
        }

        public IList<EnumLeaveEntryType> GetLeaveEntryTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumLeaveEntryType>(culture, (item, desc) => item.LeaveEntryTypeDescription = desc);
        }

        public IList<EnumReportPath> GetReportPaths(long companyId, CultureInfo? culture = null)
        {
            return this.GetData<EnumReportPath>(
                culture,
                _ => _.CompanyReportOptions.Any(o => o.CompanyId == companyId && o.Display == true),
                _ => _.ReportName,
                0,
                (item, desc) => item.ReportName = desc,
                companyId);
        }

        public IList<EnumEducationLevel> GetEducationLevels(CultureInfo? culture = null)
        {
            return this.GetData<EnumEducationLevel>(culture, (item, desc) => item.EducationLevelDescription = desc);
        }

        public IList<EnumInstituteType> GetInstituteTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumInstituteType>(culture, (item, desc) => item.InstituteTypeDescription = desc);
        }

        public IList<EnumExperience> GetExperiences(CultureInfo? culture = null)
        {
            return this.GetData<EnumExperience>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumCompetency> GetCompetencies(CultureInfo? culture = null)
        {
            return this.GetData<EnumCompetency>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumAccommodationType> GetAccommodationTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumAccommodationType>(culture, _ => true, _ => _.AccommodationTypeDescription, 0, (item, desc) => item.AccommodationTypeDescription = desc, string.Empty);
        }

        public IList<EnumMonthsOfYear> GetMonthsOfYear(CultureInfo? culture = null)
        {
            return this.GetData<EnumMonthsOfYear>(culture, (item, desc) => item.MonthDescription = desc);
        }

        public IList<EnumAccommodationOption> GetAccommodationOptions(CultureInfo? culture = null)
        {
            return this.GetData<EnumAccommodationOption>(culture, _ => true, _ => _.AccommodationOptionDescription, 0, (item, desc) => item.AccommodationOptionDescription = desc, string.Empty);
        }

        public IList<EnumHotelAccommodationOption> GetHotelAccommodationOptions(CultureInfo? culture = null)
        {
            return this.GetData<EnumHotelAccommodationOption>(culture, _ => true, _ => _.HotelAccommodationDescription, 0, (item, desc) => item.HotelAccommodationDescription = desc, string.Empty);
        }

        public IList<EnumInboxEntryType> GetInboxEntryTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumInboxEntryType>(culture, (item, desc) => item.EntryTypeDescription = desc);
        }

        public IList<EnumPropertyOwnership> GetPropertyOwnership(CultureInfo? culture = null)
        {
            return this.GetData<EnumPropertyOwnership>(culture, _ => true, _ => _.PropertyOwnershipDescription, 0, (item, desc) => item.PropertyOwnershipDescription = desc, string.Empty);
        }

        public IList<EnumPayDay> GetPayDay(CultureInfo? culture = null)
        {
            return this.GetData<EnumPayDay>(culture, _ => true, _ => _.Description, 0, (item, desc) => item.Description = desc, string.Empty);
        }

        public IList<EnumPayslipFrequency> GetPayslipFrequency(CultureInfo? culture = null)
        {
            return this.GetData<EnumPayslipFrequency>(culture, _ => true, _ => _.PayslipDescription, 0, (item, desc) => item.PayslipDescription = desc, string.Empty);
        }

        public IList<EnumMessageTemplate> GetMessageTemplate(CultureInfo? culture = null)
        {
            return this.GetData<EnumMessageTemplate>(culture, _ => true, _ => _.MessageDescription, 0, (item, desc) => item.MessageDescription = desc, string.Empty);
        }

        public IList<EnumThemeColour> GetThemeColours(CultureInfo culture = null)
        {
            return this.GetData<EnumThemeColour>(culture, _ => true, _ => _.ThemeColourDescription, 0, (item, desc) => item.ThemeColourDescription = desc, string.Empty);
        }

        public IList<EnumMfaOption> GetMFAOptions(CultureInfo culture = null)
        {
            return this.GetData<EnumMfaOption>(culture, _ => true, _ => _.MfaDescription, 0, (item, desc) => item.MfaDescription = desc, string.Empty);
        }

        public IList<EnumMunicipality> GetMunicipality(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumMunicipality>(culture, _ => _.TaxCountryId == countryId, _ => _.FieldCodeDescription, 0, (item, desc) => item.FieldCodeDescription = desc, countryId);
        }

        public IList<EnumAddressStreetType> GetAddressStreetType(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumAddressStreetType>(culture, _ => _.TaxCountryId == countryId, _ => _.FieldCodeDescription, 0, (item, desc) => item.FieldCodeDescription = desc, countryId);
        }

        public IList<EnumCustomFormScreenType> GetCustomFormScreenTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumCustomFormScreenType>(culture, _ => true, _ => _.PageName, 0, (item, desc) => item.PageName = desc, string.Empty);
        }

        public IList<EnumTrainingProgramCategory> GetTrainingProgramCategory(CultureInfo? culture = null)
        {
            return this.GetData<EnumTrainingProgramCategory>(culture, _ => true, _ => _.ProgramCategoryDescription, 0, (item, desc) => item.ProgramCategoryDescription = desc, string.Empty);
        }

        public IList<EnumCourseType> GetCourseType(CultureInfo? culture = null)
        {
            return this.GetData<EnumCourseType>(culture, _ => true, _ => _.CourseDescription, 0, (item, desc) => item.CourseDescription = desc, string.Empty);
        }

        public IList<EnumTrainingSkillsPriority> GetTrainingSkillsPriority(CultureInfo? culture = null)
        {
            return this.GetData<EnumTrainingSkillsPriority>(culture, _ => true, _ => _.PriorityDescription, 0, (item, desc) => item.PriorityDescription = desc, string.Empty);
        }

        public IList<EnumOffenceOutcome> GetOffenceOutcomes(CultureInfo? culture = null)
        {
            return this.GetData<EnumOffenceOutcome>(culture, (item, desc) => item.OutcomeDescription = desc);
        }

        public IList<EnumOffenceCategory> GetOffenceCategories(CultureInfo? culture = null)
        {
            return this.GetData<EnumOffenceCategory>(culture, (item, desc) => item.OffenceCategoryDescription = desc);
        }

        public IList<EnumAppealReason> GetAppealReasons(CultureInfo? culture = null)
        {
            return this.GetData<EnumAppealReason>(culture, (item, desc) => item.AppealDescription = desc);
        }

        public IList<EnumAppealOutcome> GetAppealOutcomes(CultureInfo? culture = null)
        {
            return this.GetData<EnumAppealOutcome>(culture, (item, desc) => item.AppealOutcomeDescription = desc);
        }

        public IList<EnumAwardFavour> GetAwardFavours(CultureInfo? culture = null)
        {
            return this.GetData<EnumAwardFavour>(culture, (item, desc) => item.AwardFavourDescription = desc);
        }

        public IList<EnumOtherOutcome> GetOtherOutcomes(CultureInfo? culture = null)
        {
            return this.GetData<EnumOtherOutcome>(culture, (item, desc) => item.OtherOutcomeDescription = desc);
        }

        public IList<EnumSettlementReinstate> GetSettlementReinstatements(CultureInfo? culture = null)
        {
            return this.GetData<EnumSettlementReinstate>(culture, (item, desc) => item.SettlementReinstateDescription = desc);
        }

        public IList<EnumBureauTaxabilityOption> GetEnumBureauTaxabilityOptions(CultureInfo? culture = null)
        {
            return this.GetData<EnumBureauTaxabilityOption>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumExpectedReturnType> GetExpectedReturnTypes(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumExpectedReturnType>(culture, _ => _.TaxCountryId == countryId, _ => _.ExpectedReturnTypeDescription, 0, (item, desc) => item.ExpectedReturnTypeDescription = desc, countryId);
        }

        public IList<EnumIncidentType> GetIncidentTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumIncidentType>(culture, (item, desc) => item.IncidentTypeDescription = desc);
        }

        public IList<EnumSpecialComponentType> GetSpecialComponentTypes(int countryId, CultureInfo? culture = null)
        {
            // EnumSpecialComponentType - this is client added; no need to have a resource file.
            if (culture == null)
            {
                culture = CultureData.DefaultCulture;
            }

            return this.GetData<EnumSpecialComponentType>(culture, _ => _.TaxCountryId == countryId, _ => _.SpecialComponentCode, 0, (item, desc) => item.SpecialComponentDescription = desc, countryId);
        }

        public IList<EnumPensionExclusionReason> GetPensionExclusionReasons(CultureInfo culture = null)
        {
            return this.GetData<EnumPensionExclusionReason>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumPensionEnrolmentStatus> GetPensionEnrolmentStatuses(CultureInfo culture = null)
        {
            return this.GetData<EnumPensionEnrolmentStatus>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumPensionWorkerCategory> GetPensionWorkerCategories(CultureInfo culture = null)
        {
            return this.GetData<EnumPensionWorkerCategory>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumTaxCountry> GetTaxCountries(CultureInfo? culture = null)
        {
            return this.GetData<EnumTaxCountry>(culture, (item, desc) => item.CountryDescription = desc);
        }

        public IList<EnumPensionLetterStatus> GetPensionLetterStatuses(CultureInfo culture = null)
        {
            return this.GetData<EnumPensionLetterStatus>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumPensionLetterType> GetPensionLetterTypes(CultureInfo culture = null)
        {
            return this.GetData<EnumPensionLetterType>(culture, (item, desc) => item.Description = desc);
        }

        public IList<EnumDentalBenefit> GetDentalBenefits(CultureInfo culture = null)
        {
            return this.GetData<EnumDentalBenefit>(culture, (item, desc) => item.DentalBenefitDescription = desc);
        }

        public IList<EnumSeveranceDays> GetSeveranceDays(CultureInfo culture = null)
        {
            return this.GetData<EnumSeveranceDays>(culture, (item, desc) => item.SeveranceDayDescription = desc);
        }

        public IList<EnumGender> GetGenders(int countryId, CultureInfo? culture = null)
        {
            var items = this.GetData<EnumGender>(culture, _ => _.TaxCountryId == countryId, _ => _.GenderDescription, 0, (item, desc) => item.GenderDescription = desc, countryId);
            if (items.Count == 0)
            {
                items = this.GetData<EnumGender>(culture, _ => _.TaxCountryId == 0, _ => _.GenderDescription, 0, (item, desc) => item.GenderDescription = desc, 0);
            }

            return items;
        }

        public IList<EnumPaymentModule> GetPaymentModules(CultureInfo culture = null)
        {
            return this.GetData<EnumPaymentModule>(culture, (item, desc) => item.PaymentModuleDescription = desc);
        }

        public IList<EnumCalculationMethod> GetCalculationMethods(CultureInfo? culture = null)
        {
            return this.GetData<EnumCalculationMethod>(culture, _ => true, _ => _.CalculationMethodDescription, 0, (item, desc) => item.CalculationMethodDescription = desc, string.Empty);
        }

        public IList<EnumPaymentMethod> GetEmployeePaymentMethods(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumPaymentMethod>(culture, _ => !_.TaxCountryId.HasValue || _.TaxCountryId == countryId, _ => _.PayMethodDescription, 0, (item, desc) => item.PayMethodDescription = desc, countryId);
        }

        public IList<EnumRunType> GetRunTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumRunType>(culture, (item, desc) => item.RunTypeDescription = desc);
        }

        public IList<EnumRunStatus> GetRunStatus(CultureInfo? culture = null)
        {
            return this.GetData<EnumRunStatus>(culture, (item, desc) => item.RunStatusDescription = desc);
        }

        public IList<EnumPlanType> GetPlanTypes(CultureInfo culture = null)
        {
            return this.GetData<EnumPlanType>(culture, (item, desc) => item.PlanCode = desc);
        }

        public IList<EnumRunTypeExtension> GetRunTypeExtensions(int countryId, CultureInfo culture = null)
        {
            var items = this.GetData<EnumRunTypeExtension>(culture, _ => _.TaxCountryId == countryId, _ => _.RunTypeExtDescription, 0, (item, desc) => item.RunTypeExtDescription = desc, countryId);
            if (items.Count == 0)
            {
                items = this.GetData<EnumRunTypeExtension>(culture, _ => _.TaxCountryId == 0, _ => _.RunTypeExtDescription, 0, (item, desc) => item.RunTypeExtDescription = desc, 0);
            }

            return items;
        }

        public IList<EnumTaxCountryStatus> GetTaxCountryStatus(CultureInfo? culture = null)
        {
            return this.GetData<EnumTaxCountryStatus>(culture, _ => true, _ => _.Description, 0, (item, desc) => item.Description = desc, string.Empty);
        }

        public IList<EnumCustomFieldFormArea> GetCustomFieldFormAreas(CultureInfo? culture = null)
        {
            return this.GetData<EnumCustomFieldFormArea>(culture, (item, desc) => item.SystemKey = desc);
        }

        public IList<EnumTaxType> GetTaxTypes(int countryId, CultureInfo? culture = null)
        {
            return this.GetData<EnumTaxType>(culture, _ => _.CountryId == countryId, _ => _.TaxTypeCode, 0, (item, desc) => item.TaxTypeDescription = desc, countryId);
        }

        public IList<EnumIncomeTaxOption> GetIncomeTaxOptions(CultureInfo culture = null)
        {
            return this.GetData<EnumIncomeTaxOption>(culture, (item, desc) => item.TaxOptionDescription = desc);
        }

        public IList<EnumRemissionExemptIncomeReason> GetRemissionExemptIncomeReasons(CultureInfo culture = null)
        {
            return this.GetData<EnumRemissionExemptIncomeReason>(culture, (item, desc) => item.ExemptIncomeReasonDescription = desc);
        }

        public IList<EnumOverseasPosting> GetOverseasPostings(CultureInfo culture = null)
        {
            return this.GetData<EnumOverseasPosting>(culture, (item, desc) => item.OverseasPostingDescription = desc);
        }

        public IList<EnumBasedOnSplitOption> GetBasedOnSplitOptions(CultureInfo? culture = null)
        {
            return this.GetData<EnumBasedOnSplitOption>(culture, (item, desc) => item.BasedOnSplitOptionDescription = desc);
        }

        public IList<EnumLeaveForfeitPeriod> GetLeaveForfeitPeriods(CultureInfo culture = null)
        {
            return this.GetData<EnumLeaveForfeitPeriod>(culture, (item, desc) => item.ForfeitPeriodDescription = desc);
        }

        public IList<EnumLeaveAccrualPeriod> GetLeaveAccrualPeriods(CultureInfo culture = null)
        {
            return this.GetData<EnumLeaveAccrualPeriod>(culture, (item, desc) => item.AccrualPeriodDescription = desc);
        }

        public IList<EnumLeaveAccrualOption> GetLeaveAccrualOptions(CultureInfo culture = null)
        {
            return this.GetData<EnumLeaveAccrualOption>(culture, (item, desc) => item.AccrualOptionDescription = desc);
        }

        public IList<EnumLeaveAccrualValue> GetLeaveAccrualValues(CultureInfo culture = null)
        {
            return this.GetData<EnumLeaveAccrualValue>(culture, (item, desc) => item.AccrualValue = desc);
        }

        protected IList<T> GetData<T>(CultureInfo? culture, Action<T, string> localize)
            where T : class
        {
            if (culture == null)
            {
                culture = CultureInfo.CurrentCulture;
            }

            return this.distributedCache.GetOrCreate(
                CacheKeys.Enums(culture.TwoLetterISOLanguageName, typeof(T).Name),
                () =>
                {
                    var data = this.context.Set<T>().AsNoTracking().ToList();
                    return this.Localize(data, culture, localize);
                });
        }

        protected IList<T> GetData<T>(CultureInfo? culture, Expression<Func<T, bool>> predicate, Expression<Func<T, object>> order, int skip, Action<T, string> localize, object cacheKey)
            where T : class
        {
            if (culture == null)
            {
                culture = CultureInfo.CurrentCulture;
            }

            return this.distributedCache.GetOrCreate(
                CacheKeys.Enums(culture.TwoLetterISOLanguageName, typeof(T).Name) + ":" + cacheKey,
                () =>
                {
                    var data = this.context.Set<T>().AsNoTracking()
                        .Where(predicate)
                        .OrderBy(order)
                        .Skip(skip)
                        .ToList();

                    return this.Localize(data, culture, localize);
                });
        }

        protected IList<T> Localize<T>(IList<T> data, CultureInfo culture, Action<T, string> localize)
            where T : class
        {
            if (culture.TwoLetterISOLanguageName == CultureData.DefaultCulture.TwoLetterISOLanguageName)
            {
                // Enum are EN by default.
                return data;
            }

            var metadata = this.metadataProvider.GetMetadataForType(typeof(T));

            var local = new List<T>();
            foreach (var entry in data.Select(this.context.Entry))
            {
                if (metadata.EnumNamesAndValues != null && metadata.EnumNamesAndValues.TryGetValue(Convert.ToString(ModelHelper.GetPrimaryKeyValue(entry)), out var localizedValue))
                {
                    localize(entry.Entity, localizedValue);
                }

                local.Add(entry.Entity);
            }

            return local;
        }

        public IList<EnumRaterTypes> GetRaterTypes(CultureInfo? culture = null)
        {
            return this.GetData<EnumRaterTypes>(culture, (item, desc) => item.RaterTypeDescription = desc);
        }
    }
}