namespace PaySpace.Venuta.Services.Employees.MY
{
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Maddalena;

    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Logging;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Tax;

    [CountryService(CountryCode.MY)]
    public class MalaysiaEmploymentStatusService : EmploymentStatusService
    {
        private readonly ICustomFieldService customFieldService;
        private readonly IEmployeeService employeeService;

        private const string PersonWithoutIDCode = "B";
        private const string RetirementEndOfContractDateCustomField = "SEPDTE";
        private const int DefaultRetirementAge = 60;

        public MalaysiaEmploymentStatusService(
            IDbContextRepository<EmployeeEmploymentStatus> repository,
            IMapper mapper,
            ICalcSchedulingService calcSchedulingService,
            ICompanySettingService companySettingService,
            ICompanyJobManagementService companyJobManagementService,
            IEmployeePositionService employeePositionService,
            ICompanyService companyService,
            IEmployeeService employeeService,
            ICompanyRunService companyRunService,
            IIrp5NumberService irp5NumberService,
            ILogger<EmploymentStatusService> logger,
            IPayslipHeaderService payslipHeaderService,
            IPayRateService payRateService,
            ICountryTaxYearService countryTaxYearService,
            IDistributedCache distributedCache,
            IServiceScopeFactory serviceScopeFactory,
            ReadOnlyContext readOnlyContext,
            ICustomFieldService customFieldService,
            IEmployeeComponentService employeeComponentService)
            : base(
                repository,
                mapper,
                calcSchedulingService,
                companySettingService,
                companyJobManagementService,
                employeePositionService,
                companyService,
                employeeService,
                companyRunService,
                irp5NumberService,
                logger,
                payslipHeaderService,
                payRateService,
                countryTaxYearService,
                distributedCache,
                serviceScopeFactory,
                readOnlyContext,
                employeeComponentService)
        {
            this.customFieldService = customFieldService;
            this.employeeService = employeeService;
        }

        protected override async Task ClearIdFields(EmployeeEmploymentStatus status)
        {
            var natureOfPersonCode = await this.GetNatureOfPersonCodeByIdAsync(status.NatureOfPersonId);
            var identityCode = await this.GetIdentityTypeCodeByIdAsync(status.IdentityTypeId.Value);

            await base.ClearIdFields(status);

            if (natureOfPersonCode == PersonWithoutIDCode)
            {
                status.MyKadExpiry = null;
                status.MyKadIssued = null;
                status.OtherIssued = null;
                status.OtherExpiry = null;
            }
            else
            {
                if (identityCode is null)
                {
                    return;
                }

                if (identityCode.Equals("Others"))
                {
                    status.PermitIssued = null;
                    status.PermitExpiry = null;
                }

                if (identityCode.Equals("Work"))
                {
                    status.OtherIssued = null;
                    status.OtherExpiry = null;
                }
            }
        }

        public override async Task LoadDefaultValuesAsync(long companyId, EmployeeEmploymentStatus model)
        {
            var employeeCustomFields = await this.customFieldService.GetCustomFieldsAsync(companyId, nameof(EmployeeEmploymentStatus), null);

            var retirementField = employeeCustomFields
                .FirstOrDefault(f => f.FieldCode == RetirementEndOfContractDateCustomField);

            if (retirementField != null)
            {
                var customFieldValue = model.CustomFields
                    .FirstOrDefault(t => t.CustomFieldId == retirementField.CustomFieldId);
                var birthday = await this.employeeService.GetBirthdayAsync(model.EmployeeId);
                var retirementAge = birthday.AddYears(DefaultRetirementAge).ToString("yyyy-MM-dd");

                if (customFieldValue != null)
                {
                    customFieldValue.FieldValue ??= retirementAge;
                }
                else
                {
                    model.CustomFields.Add(new EmployeeEmploymentStatusCustomFieldValue
                    {
                        CustomFieldId = retirementField.CustomFieldId,
                        CustomFieldType = retirementField.CustomFieldType,
                        FieldValue = retirementAge
                    });
                }
            }
        }
    }
}