namespace PaySpace.Venuta.Services.Employees.SG
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Services.Abstractions;

    public interface ISingaporeEmployeeIR8AService : ISingaporeYearEndReportingService<EmployeeIR8A>
    {
        Task<bool> CanAddRecordAsync(long taxYearId, long employeeId);

        Task<bool> IsMaxRecordCountForTaxYearAsync(long employeeId, int taxYearId);

        Task<int> GetRecordCountAsync(int taxYearId, long employeeId);
    }

    public class SingaporeEmployeeIR8AService : SingaporeYearEndReportingService<EmployeeIR8A>, ISingaporeEmployeeIR8AService
    {
        private const int MaxRecordCount = 10;

        public SingaporeEmployeeIR8AService(IDbContextRepository<EmployeeIR8A> repository)
            : base(repository)
        {
        }

        public Task<int> GetRecordCountAsync(int taxYearId, long employeeId)
        {
            return this.Repository.Set
                .Where(_ => _.TaxYearId == taxYearId && _.EmployeeId == employeeId)
                .CountAsync();
        }

        public async Task<bool> CanAddRecordAsync(long taxYearId, long employeeId)
        {
            return await this.Repository.Set
                .TagWithSource()
                .Where(_ => _.TaxYearId == taxYearId && _.EmployeeId == employeeId)
                .CountAsync() < MaxRecordCount;
        }

        public async Task<bool> IsMaxRecordCountForTaxYearAsync(long employeeId, int taxYearId)
        {
            return await this.Repository.Set.Where(_ => _.EmployeeId == employeeId && _.TaxYearId == taxYearId).CountAsync() >= 10;
        }
    }
}
