namespace PaySpace.Venuta.Services.Country.AU
{
    using System.Collections.Generic;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;

    internal static class AustraliaAddCompanyDefaultHelper
    {
        public static IEnumerable<CompanyLeaveScheme> GetLeaveSchemeParameters(long companyId)
        {
            var schemeNames = new[]
            {
                AustraliaLeaveConstants.LeaveSchemeDescription.Nsw20Days,
                AustraliaLeaveConstants.LeaveSchemeDescription.Qld20Days,
                AustraliaLeaveConstants.LeaveSchemeDescription.Vic20Days,
                AustraliaLeaveConstants.LeaveSchemeDescription.Sa20Days,
                AustraliaLeaveConstants.LeaveSchemeDescription.Tas20Days,
                AustraliaLeaveConstants.LeaveSchemeDescription.Nt20Days,
                AustraliaLeaveConstants.LeaveSchemeDescription.Act20Days
            };

            return schemeNames.Select(schemeName => CreateLeaveScheme(schemeName, companyId));
        }

        private static CompanyLeaveScheme CreateLeaveScheme(string schemeName, long companyId)
        {
            return new CompanyLeaveScheme
            {
                SchemeName = schemeName,
                CompanyId = companyId,
                CompanyLeaveSetups = CreateStandardLeaveSetups()
            };
        }

        private static List<CompanyLeaveSetup> CreateStandardLeaveSetups()
        {
            var leaveSetups = new List<CompanyLeaveSetup>
            {
                CreateAnnualLeaveSetup(),
                CreateSickLeaveSetup()
            };

            var specialLeaveTypes = new[]
            {
                AustraliaLeaveConstants.SpecialDescription.Compassionate,
                AustraliaLeaveConstants.SpecialDescription.TimeInLieu,
                AustraliaLeaveConstants.SpecialDescription.LeaveWithoutPay,
                AustraliaLeaveConstants.SpecialDescription.FamilyAndDomesticViolence,
                AustraliaLeaveConstants.SpecialDescription.ParentalLeave,
                AustraliaLeaveConstants.SpecialDescription.LongService
            };

            leaveSetups.AddRange(specialLeaveTypes.Select(CreateSpecialLeaveSetup));
            return leaveSetups;
        }

        private static CompanyLeaveSetup CreateAnnualLeaveSetup()
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Annual,
                LeaveDescription = LeaveSchemeConstants.Description.AnnualLeave,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.Accumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 0.0769m,
                        LeaveAccrualValueId = (int)LeaveAccrualValue.Hours,
                        AccrualPeriodValue = 1,
                        AccrualPeriodId = 5,
                        AccrualOptionId = 2,
                        ApplyServiceLength = false,
                        ApplyGradeBands = false,
                        ReflectInHours = true
                    }
                }
            };
        }

        private static CompanyLeaveSetup CreateSickLeaveSetup()
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Sick,
                LeaveDescription = AustraliaLeaveConstants.SickDescription.Personal,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.Accumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 0.0384m,
                        LeaveAccrualValueId = (int)LeaveAccrualValue.Hours,
                        AccrualPeriodValue = 1,
                        AccrualPeriodId = 5,
                        AccrualOptionId = 2,
                        ApplyServiceLength = false,
                        ApplyGradeBands = false,
                        ReflectInHours = true
                    }
                }
            };
        }

        private static CompanyLeaveSetup CreateSpecialLeaveSetup(string description)
        {
            return description switch
            {
                AustraliaLeaveConstants.SpecialDescription.LongService => CreateLongServiceLeaveSetup(description),
                AustraliaLeaveConstants.SpecialDescription.ParentalLeave => CreateParentalLeaveSetup(description),
                AustraliaLeaveConstants.SpecialDescription.FamilyAndDomesticViolence => CreateFamilyViolenceLeaveSetup(description),
                _ => CreateDefaultSpecialLeaveSetup(description)
            };
        }

        private static CompanyLeaveSetup CreateLongServiceLeaveSetup(string description)
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Special,
                LeaveDescription = description,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.Accumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 0,
                        AccrualPeriodId = 0,
                        AccrualOptionId = 2,
                        ApplyServiceLength = true,
                        ApplyGradeBands = false,
                        ReflectInHours = true
                    }
                }
            };
        }

        private static CompanyLeaveSetup CreateParentalLeaveSetup(string description)
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Special,
                LeaveDescription = description,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.NonAccumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 365,
                        LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                        AccrualPeriodValue = 1,
                        AccrualPeriodId = 3,
                        AccrualOptionId = 1,
                        UpfrontProRateOptions = true,
                        UpfrontMonthlyAccrual = 0,
                        UpfrontAccrualPeriod = 12,
                        ForfeitPeriod = 1,
                        LeaveForfeitPeriodId = (int)LeaveForfeitPeriod.Year,
                        CarryOverDays = 0,
                        EffectiveDateForfeit = SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.EmploymentDate,
                        ApplyServiceLength = false,
                        ApplyGradeBands = false,
                        ReflectInHours = false
                    }
                }
            };
        }

        private static CompanyLeaveSetup CreateFamilyViolenceLeaveSetup(string description)
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Special,
                LeaveDescription = description,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.NonAccumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 10,
                        LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                        AccrualPeriodValue = 1,
                        AccrualPeriodId = 3,
                        AccrualOptionId = 1,
                        ForfeitPeriod = 1,
                        LeaveForfeitPeriodId = (int)LeaveForfeitPeriod.Year,
                        CarryOverDays = 0,
                        EffectiveDateForfeit = SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.EmploymentDate,
                        ApplyServiceLength = false,
                        ApplyGradeBands = false,
                        ReflectInHours = false
                    }
                }
            };
        }

        private static CompanyLeaveSetup CreateDefaultSpecialLeaveSetup(string description)
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Special,
                LeaveDescription = description,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.NonAccumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 0,
                        LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                        AccrualPeriodValue = 1,
                        AccrualPeriodId = 3,
                        AccrualOptionId = 1,
                        ForfeitPeriod = 1,
                        LeaveForfeitPeriodId = (int)LeaveForfeitPeriod.Year,
                        CarryOverDays = 0,
                        EffectiveDateForfeit = SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.EmploymentDate,
                        ApplyServiceLength = false,
                        ApplyGradeBands = false,
                        ReflectInHours = false
                    }
                }
            };
        }

        public static List<CompanyLeaveServiceLength> GetLeaveServiceLengthNsw20DaysDetails(long companyLeaveDetailKey)
        {
            return
            [
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.ZeroToTenYears, 0, 10, 0),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.TenYears, 10, 15, 329.33m),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.FifteenYears, 15, 999, 164.66m)
            ];
        }

        // two schemes have the same service length details: Vic20Days and Qld20Days
        public static List<CompanyLeaveServiceLength> GetLeaveServiceLengthQldAndVic20DaysDetails(long companyLeaveDetailKey)
        {
            return
            [
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.ZeroToTenYears, 0, 10, 0),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.TenYears, 10, 15, 494.00m),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.FifteenYears, 15, 999, 164.66m)
            ];
        }

        public static List<CompanyLeaveServiceLength> GetLeaveServiceLengthSa20DaysDetails(long companyLeaveDetailKey)
        {
            return
            [
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.ZeroToTenYears, 0, 10, 0),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.TenYears, 10, 11, 494.00m),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.ElevenYears, 11, 999, 49.40m)
            ];
        }

        public static List<CompanyLeaveServiceLength> GetLeaveServiceLengthTas20DaysDetails(long companyLeaveDetailKey)
        {
            return
            [
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.ZeroToFifteenYears, 0, 15, 0),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.FifteenYears, 15, 25, 494.00m),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.TwentyFiveYears, 25, 999, 329.33m)
            ];
        }

        public static List<CompanyLeaveServiceLength> GetLeaveServiceLengthNt20DaysDetails(long companyLeaveDetailKey)
        {
            return
            [
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.ZeroToTenYears, 0, 10, 0),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.TenYears, 10, 15, 494.00m),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.FifteenYears, 15, 999, 247.00m)
            ];
        }

        public static List<CompanyLeaveServiceLength> GetLeaveServiceLengthAct20DaysDetails(long companyLeaveDetailKey)
        {
            return
            [
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.ZeroToSevenYears, 0, 7, 6.0667m),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.EightYears, 7, 8, 38.93m),
                CreateServiceLength(companyLeaveDetailKey, AustraliaLeaveConstants.ServiceLengthDescription.NineYears, 8, 999, 38.93m)
            ];
        }

        private static CompanyLeaveServiceLength CreateServiceLength(long companyLeaveDetailKey, string description, int startYear, int endYear, decimal accrual)
        {
            return new CompanyLeaveServiceLength
            {
                CompanyLeaveDetailId = companyLeaveDetailKey,
                ServiceDescription = description,
                StartYear = startYear,
                EndYear = endYear,
                Accrual = accrual,
                LeaveAccrualValueId = (int)LeaveAccrualValue.Hours,
                AccrualPeriodValue = 1,
                AccrualPeriodId = 3
            };
        }
    }
}