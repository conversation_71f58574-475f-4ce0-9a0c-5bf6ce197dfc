namespace PaySpace.Venuta.Services.Company
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;

    public interface ICompanyLeaveSetupService : IGenericService<CompanyLeaveSetup>
    {
        Task<bool> HasEffectiveLeaveDetailsForScheme(long leaveSchemeId, DateTime effectiveDate);

        Task<long> GetLeaveSetupIdForLeaveTypeAsync(long companySchemeId, LeaveType leaveType);

        Task<long> GetCompanyLeaveSchemeIdAsync(long companyLeaveSetupId);

        Task<bool> IsCompanyLeaveSetupPartOfSchemeAsync(long companyLeaveSetupId, long companySchemeId);

        Task<bool> DoesSetupAllowEmployeeDefinedEntitlementsAsync(long companyLeaveSetupId);

        Task<List<CompanyLeaveSetup>> GetCompanyLeaveSetupsByLeaveTypeAsync(long companyLeaveSchemeId, LeaveType leaveType);

        Task<CompanyLeaveSetup> GetCompanyLeaveSetupByDescriptionAsync(long companyLeaveSchemeId, LeaveType leaveType, string leaveDescription);

        Task<int> GetTotalLeaveSetupByLeaveTypeAsync(long companyLeaveSchemeId, LeaveType leaveType);
    }

    public class CompanyLeaveSetupService : GenericService<CompanyLeaveSetup>, ICompanyLeaveSetupService
    {
        public CompanyLeaveSetupService(IDbContextRepository<CompanyLeaveSetup> repository)
            : base(repository)
        {
        }

        public Task<bool> HasEffectiveLeaveDetailsForScheme(long leaveSchemeId, DateTime effectiveDate)
        {
            return this.Repository.Set
                .AnyAsync(_ => _.CompanyLeaveSchemeId == leaveSchemeId && _.LeaveDetails.Any(l => l.EffectiveDate <= effectiveDate));
        }

        public Task<long> GetLeaveSetupIdForLeaveTypeAsync(long companySchemeId, LeaveType leaveType)
        {
            return this.Repository.Set
                .Where(_ => _.CompanyLeaveSchemeId == companySchemeId && _.LeaveType == leaveType)
                .OrderBy(_ => _.OrderNumber)
                .Select(_ => _.CompanyLeaveSetupId)
                .FirstOrDefaultAsync();
        }

        public Task<long> GetCompanyLeaveSchemeIdAsync(long companyLeaveSetupId)
        {
            return this.Repository.Set.TagWithSource()
                .Where(_ => _.CompanyLeaveSetupId == companyLeaveSetupId)
                .Select(_ => _.CompanyLeaveSchemeId)
                .FirstOrDefaultAsync();
        }

        public Task<bool> IsCompanyLeaveSetupPartOfSchemeAsync(long companyLeaveSetupId, long companySchemeId)
        {
            return this.Repository.Set.TagWithSource()
                .AnyAsync(_ => _.CompanyLeaveSetupId == companyLeaveSetupId && _.CompanyLeaveSchemeId == companySchemeId);
        }

        public Task<bool> DoesSetupAllowEmployeeDefinedEntitlementsAsync(long companyLeaveSetupId)
        {
            return this.Repository.Context.Set<CompanyLeaveDetail>().TagWithSource()
                .AnyAsync(_ => _.CompanyLeaveSetupId == companyLeaveSetupId && _.ApplyEmployeeDefined == true);
        }

        public Task<List<CompanyLeaveSetup>> GetCompanyLeaveSetupsByLeaveTypeAsync(long companyLeaveSchemeId, LeaveType leaveType)
        {
            return this.Repository.Set.TagWithSource()
                .Where(_ => _.CompanyLeaveSchemeId == companyLeaveSchemeId && _.LeaveType == leaveType)
                .OrderBy(_ =>_.OrderNumber)
                .ToListAsync();
        }

        public Task<CompanyLeaveSetup> GetCompanyLeaveSetupByDescriptionAsync(long companyLeaveSchemeId, LeaveType leaveType, string leaveDescription)
        {
            return this.Repository.Set.TagWithSource()
                .Where(_ => _.CompanyLeaveSchemeId == companyLeaveSchemeId && _.LeaveType == leaveType && _.LeaveDescription == leaveDescription)
                .OrderBy(_ => _.OrderNumber)
                .SingleOrDefaultAsync();
        }

        public async Task<int> GetTotalLeaveSetupByLeaveTypeAsync(long companyLeaveSchemeId, LeaveType leaveType)
        {
            return await this.Repository.Set.TagWithSource()
                .CountAsync(_ => _.CompanyLeaveSchemeId == companyLeaveSchemeId && _.LeaveType == leaveType);
        }
    }
}
