namespace PaySpace.Venuta.Services.Company.Helpers
{
    using System.Collections.Generic;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Infrastructure;

    internal static class AddCompanyDefaultHelper
    {
        public static CompanyLeaveScheme GetLeaveSchemeParameters(long companyId)
        {
            var companyLeaveScheme = new CompanyLeaveScheme
            {
                SchemeName = LeaveSchemeConstants.StandardLeaveDescription,
                CompanyId = companyId,
                CompanyLeaveSetups = new List<CompanyLeaveSetup>()
                {
                    new() {
                        LeaveType = LeaveType.Annual,
                        LeaveDescription = LeaveSchemeConstants.Description.AnnualLeave,
                        OrderNumber = 1,
                        LeaveDetails = new List<CompanyLeaveDetail>()
                        {
                            new() {
                                EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                                Accrual = 15,
                                CompanyLeaveSetupType = CompanyLeaveSetupType.Accumulative,
                                AccrualPeriodId = 3,
                                AccrualPeriodValue = 1,
                                LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                                ApplyServiceLength = false,
                                AccrualOptionId = 2,
                                ApplyGradeBands = false,
                            }
                        }
                    },
                    new() {
                        LeaveType = LeaveType.Sick,
                        LeaveDescription = LeaveSchemeConstants.Description.SickLeave,
                        OrderNumber = 1,
                        LeaveDetails = new List<CompanyLeaveDetail>()
                        {
                            new() {
                                EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                                Accrual = 30,
                                CompanyLeaveSetupType = CompanyLeaveSetupType.NonAccumulative,
                                AccrualPeriodId = 3,
                                LeaveForfeitPeriodId = (int)LeaveForfeitPeriod.Year,
                                ForfeitPeriod = 3,
                                AccrualPeriodValue = 3,
                                LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                                ApplyServiceLength = false,
                                AccrualOptionId = 1,
                                ApplyGradeBands = false,
                                EffectiveDateForfeit = LeaveSchemeConstants.EffectiveDateForfeit.GroupJoinDate,
                            }
                        }
                    },
                    new() {
                        LeaveType = LeaveType.FamilyResponsibility,
                        LeaveDescription = LeaveSchemeConstants.Description.FamilyResponsibilityLeave,
                        OrderNumber = 1,
                        LeaveDetails = new List<CompanyLeaveDetail>()
                        {
                            new() {
                                EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                                Accrual = 3,
                                CompanyLeaveSetupType = CompanyLeaveSetupType.NonAccumulative,
                                AccrualPeriodId = 3,
                                LeaveForfeitPeriodId = (int)LeaveForfeitPeriod.Year,
                                ForfeitPeriod = 1,
                                AccrualPeriodValue = 1,
                                LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                                ApplyServiceLength = false,
                                AccrualOptionId = 1,
                                ApplyGradeBands = false,
                                EffectiveDateForfeit = LeaveSchemeConstants.EffectiveDateForfeit.GroupJoinDate
                            }
                        }
                    }
                }
            };

            return companyLeaveScheme;
        }

        public static List<OrganizationLevel> GetOrganizationLevels(long companyId, string companyName)
        {
            // By default, the Org Levels screen will now have two parent entries,
            // with the Org Units screen having a record linked to the "Company" Org Level
            var organizationLevels = new List<OrganizationLevel>
            {
                new ()
                {
                    CompanyId = companyId,
                    Description = "Company",
                    OrganizationGroups = new List<OrganizationGroup>
                    {
                        new ()
                        {
                            CompanyId = companyId,
                            Description = companyName,
                            CostCentre = true,
                            ParentOrganizationGroupId = -1,
                            UploadCode = "COMP"
                        }
                    }
                },
                new ()
                {
                    CompanyId = companyId,
                    Description = "Cost Center"
                }
            };

            return organizationLevels;
        }

        public static CompanyLeaveScheme GetSpainLeaveSchemeParametersAsync(long companyId)
        {
            var companyLeaveScheme = new CompanyLeaveScheme
            {
                SchemeName = LeaveSchemeConstants.StandardLeaveDescription,
                CompanyId = companyId,
                CompanyLeaveSetups = new List<CompanyLeaveSetup>()
                {
                    new() {
                        LeaveType = LeaveType.Annual,
                        LeaveDescription = LeaveSchemeConstants.Description.AnnualLeave,
                        OrderNumber = 1,
                        LeaveDetails = new List<CompanyLeaveDetail>()
                        {
                            new() {
                                EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                                Accrual = 15,
                                CompanyLeaveSetupType = CompanyLeaveSetupType.Accumulative,
                                AccrualPeriodId = 3,
                                AccrualPeriodValue = 1,
                                LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                                ApplyServiceLength = false,
                                AccrualOptionId = 2,
                                ApplyGradeBands = false,
                            }
                        }
                    },
                }
            };

            return companyLeaveScheme;
        }
    }
}