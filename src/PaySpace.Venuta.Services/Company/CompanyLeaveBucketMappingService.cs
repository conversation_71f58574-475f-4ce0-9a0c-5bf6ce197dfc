namespace PaySpace.Venuta.Services.Company
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Dynamic.Core;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Extensions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;

    public interface ICompanyLeaveBucketMappingDetailService : IGenericService<CompanyLeaveBucketMappingDetail>
    {
        IQueryable<CompanyLeaveBucketMappingDetail> GetLeaveBucketMappings(long fromSchemeId, long toSchemeId);

        Task MapLeave(
            long companyId,
            long employeeId,
            long previousLeaveRunId,
            CompanyRun companyRun,
            DateTime effectiveDate,
            IList<CompanyLeaveBucketMappingDetail> leaveBucketMappings,
            IList<EmployeeLeaveDetail> previousLeaveDetails);

        Task<long> GetCompanyLeaveBucketMappingKeyAsync(long companyId, long fromSchemeId, long toSchemeId);

        Task<long> GetToCompanyLeaveSetupIdAsync(long companyLeaveBucketMappingKey, long fromCompanyLeaveSetupId);
    }

    public class CompanyLeaveBucketMappingDetailService : GenericService<CompanyLeaveBucketMappingDetail>, ICompanyLeaveBucketMappingDetailService
    {
        private readonly ICompanyRunService companyRunService;
        private readonly ApplicationContext context;

        public CompanyLeaveBucketMappingDetailService(
            IDbContextRepository<CompanyLeaveBucketMappingDetail> repository,
            ICompanyRunService companyRunService,
            ApplicationContext context)
            : base(repository)
        {
            this.companyRunService = companyRunService;
            this.context = context;
        }

        public IQueryable<CompanyLeaveBucketMappingDetail> GetLeaveBucketMappings(long fromSchemeId, long toSchemeId)
        {
            return this.Repository.Set.Where(_ => _.LeaveMapping.FromSchemeId == fromSchemeId && _.LeaveMapping.ToSchemeId == toSchemeId);
        }

        public async Task MapLeave(
            long companyId,
            long employeeId,
            long previousLeaveRunId,
            CompanyRun companyRun,
            DateTime effectiveDate,
            IList<CompanyLeaveBucketMappingDetail> leaveBucketMappings,
            IList<EmployeeLeaveDetail> previousLeaveDetails)
        {
            using (var transaction = await this.Repository.Context.CreateTransactionAsync())
            {
                var mappings = leaveBucketMappings.Where(_ => previousLeaveDetails.Select(p => p.CompanyLeaveSetupId).Contains(_.FromCompanyLeaveSetupId));
                var companyLeaveDetails = await this.Repository.Context.Set<CompanyLeaveDetail>().AsNoTracking().Include(_ => _.CompanyLeaveSetup)
                    .Where(_ => _.EffectiveDate <= effectiveDate)
                    .Where(_ => _.StopDate == null || _.StopDate >= effectiveDate)
                    .Where(_ => mappings.Select(m => m.FromCompanyLeaveSetupId).Contains(_.CompanyLeaveSetupId) || mappings.Select(m => m.ToCompanyLeaveSetupId).Contains(_.CompanyLeaveSetupId)).ToListAsync();

                foreach (var mapping in mappings)
                {
                    var previousDetail = previousLeaveDetails.Single(_ => _.CompanyLeaveSetupId == mapping.FromCompanyLeaveSetupId);
                    if (previousDetail.LeaveBalance != 0)
                    {
                        var toCompanyLeaveSetupId = mapping.ToCompanyLeaveSetupId;
                        var isAnniversary = false;
                        var carryOverDays = default(double);
                        var oldLeaveDetail = companyLeaveDetails.OrderByDescending(_ => _.EffectiveDate)
                            .FirstOrDefault(_ => _.CompanyLeaveSetupId == mapping.FromCompanyLeaveSetupId);
                        var newLeaveDetail = companyLeaveDetails.OrderByDescending(_ => _.EffectiveDate)
                            .FirstOrDefault(_ => _.CompanyLeaveSetupId == mapping.ToCompanyLeaveSetupId);

                        if (oldLeaveDetail == null || newLeaveDetail == null)
                        {
                            continue;
                        }

                        var dateDiff = previousDetail.ExpiryOrCarryOverDate.HasValue
                            ? DateDiff((LeaveForfeitPeriod)oldLeaveDetail.LeaveForfeitPeriodId!, previousDetail.ExpiryOrCarryOverDate, companyRun.PeriodStartDate) : default;

                        // Use the values already stored in the EmployeeLeaveDetails table to specify whether carry-over days must be set
                        if (oldLeaveDetail.CompanyLeaveSetupType == CompanyLeaveSetupType.NonAccumulative &&
                            oldLeaveDetail.LeaveForfeitPeriodId.HasValue &&
                            previousDetail.DaysDueToCarryOver != 0 &&
                            previousDetail.ExpiryOrCarryOverDate.HasValue &&
                            dateDiff == 0)
                        {
                            isAnniversary = true;
                            if (oldLeaveDetail.CarryOverDays.HasValue)
                            {
                                carryOverDays = Convert.ToDouble(previousDetail.DaysDueToCarryOver);
                            }

                            if (oldLeaveDetail.ForfeitPeriod.HasValue && oldLeaveDetail.ForfeitCompanyLeaveSetupId.HasValue)
                            {
                                var carryOverMapping = oldLeaveDetail.ForfeitCompanyLeaveSetupId;
                                var mappedBucket = carryOverMapping.HasValue ? mappings.SingleOrDefault(_ => _.FromCompanyLeaveSetupId == carryOverMapping.Value) : null;
                                if (mappedBucket != null)
                                {
                                    toCompanyLeaveSetupId = mappedBucket.ToCompanyLeaveSetupId;
                                    newLeaveDetail = companyLeaveDetails.SingleOrDefault(_ => _.CompanyLeaveSetupId == mappedBucket.ToCompanyLeaveSetupId);
                                }
                                else
                                {
                                    continue;
                                }
                            }

                            if (carryOverDays == 0)
                            {
                                continue;
                            }
                        }

                        if (isAnniversary && carryOverDays == 0)
                        {
                            throw new InvalidOperationException("Leave Setup mapping with carry over days as 0 when it is the anniversary.");
                        }

                        await this.MigrateExistingAdjustments(employeeId, toCompanyLeaveSetupId, mapping.FromCompanyLeaveSetupId, companyRun, newLeaveDetail.CompanyLeaveSetup.LeaveType, effectiveDate);
                        this.Repository.Context.Add(new EmployeeLeaveAdjustment
                        {
                            LeaveType = newLeaveDetail.CompanyLeaveSetup.LeaveType,
                            CompanyLeaveSetupId = newLeaveDetail.CompanyLeaveSetup.CompanyLeaveSetupId,
                            LeaveEntryType = LeaveEntryType.Adjustment,
                            CompanyRunId = companyRun.RunId,
                            Status = LeaveStatus.Approved,
                            EmployeeId = employeeId,
                            Comments = "Balance from previous scheme",
                            TotalDays = isAnniversary ? carryOverDays : previousDetail.LeaveBalance,
                            IsEditable = true
                        });
                    }
                }

                await this.Repository.Context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
        }

        public Task<long> GetCompanyLeaveBucketMappingKeyAsync(long companyId, long fromSchemeId, long toSchemeId)
        {
            return this.context.Set<CompanyLeaveBucketMapping>().TagWithSource()
                .Where(_ => _.CompanyId == companyId && _.FromSchemeId == fromSchemeId && _.ToSchemeId == toSchemeId)
                .Select(_ => _.LeaveMappingId)
                .FirstOrDefaultAsync();
        }

        public Task<long> GetToCompanyLeaveSetupIdAsync(long companyLeaveBucketMappingKey, long fromCompanyLeaveSetupId)
        {
            return this.context.Set<CompanyLeaveBucketMappingDetail>().TagWithSource()
                .Where(_ => _.LeaveMappingId == companyLeaveBucketMappingKey &&
                            _.FromCompanyLeaveSetupId == fromCompanyLeaveSetupId)
                .Select(_ => _.ToCompanyLeaveSetupId)
                .FirstOrDefaultAsync();
        }

        private static int DateDiff(LeaveForfeitPeriod? leaveForfeitPeriod, DateTime? expiryOrCarryOverDate, DateTime periodStartDate)
        {
            switch (leaveForfeitPeriod)
            {
                case LeaveForfeitPeriod.Day:
                case LeaveForfeitPeriod.Month:
                    return Math.Abs(expiryOrCarryOverDate.Value.Month - periodStartDate.Month);

                case LeaveForfeitPeriod.Year:
                    // Ensure the day is the same so as to only compare the Year and Month
                    var expiryOrCarryOverDateSetToFirstDay = new DateTime(expiryOrCarryOverDate.Value.Year, expiryOrCarryOverDate.Value.Month, 1);
                    var periodStartDateSetToFirstDay = new DateTime(periodStartDate.Year, periodStartDate.Month, 1);
                    return Math.Abs((int)(expiryOrCarryOverDateSetToFirstDay - periodStartDateSetToFirstDay).TotalDays);

                default:
                    return 0;
            }
        }

        private async Task MigrateExistingAdjustments(long employeeId, long toCompanyLeaveSetupId, long fromCompanyLeaveSetupId, CompanyRun companyRun, LeaveType leaveType, DateTime effectiveDate)
        {
            var runsByFrequency = await this.companyRunService.GetCompanyRuns(companyRun.CompanyFrequencyId, RunStatus.Future).ToListAsync();
            await this.MigrateExistingAdjustmentForRunAsync(employeeId, toCompanyLeaveSetupId, fromCompanyLeaveSetupId, companyRun.RunId, leaveType, true, effectiveDate);

            foreach (var run in runsByFrequency)
            {
                await this.MigrateExistingAdjustmentForRunAsync(employeeId, toCompanyLeaveSetupId, fromCompanyLeaveSetupId, run.RunId, leaveType, false, effectiveDate);
            }
        }

        private async Task MigrateExistingAdjustmentForRunAsync(long employeeId, long toCompanyLeaveSetupId, long fromCompanyLeaveSetupId, long runId, LeaveType leaveType, bool checkComment, DateTime effectiveDate)
        {
            var existingAdjustments = await this.GetTrackedEmployeeLeaveAdjustmentsByRunId(employeeId, runId, effectiveDate);
            var isSaveRequired = false;

            foreach (var existingAdjustment in existingAdjustments)
            {
                var isPreviousScheme = (!existingAdjustment.Comments.Contains("Balance from previous scheme") && checkComment) || !checkComment;
                var isNotNewScheme = !existingAdjustment.CompanyLeaveSetupId.HasValue || existingAdjustment.CompanyLeaveSetupId != toCompanyLeaveSetupId;
                var isSpecialType = existingAdjustment.CompanyLeaveSetupId == fromCompanyLeaveSetupId && existingAdjustment.LeaveType == LeaveType.Special;
                var isLeaveType = leaveType != LeaveType.Special && existingAdjustment.LeaveType == leaveType;

                if (isPreviousScheme && isNotNewScheme && (isSpecialType || isLeaveType))
                {
                    existingAdjustment.CompanyLeaveSetupId = toCompanyLeaveSetupId;
                    isSaveRequired = true;
                }
            }

            if (isSaveRequired)
            {
                await this.Repository.Context.SaveChangesAsync();
            }
        }

        private async Task<IList<EmployeeLeaveAdjustment>> GetTrackedEmployeeLeaveAdjustmentsByRunId(long employeeId, long runId, DateTime effectiveDate)
        {
            return await this.Repository.Context.Set<EmployeeLeaveAdjustment>()
                .Where(_ => _.CompanyRunId == runId && _.EmployeeId == employeeId && (!_.StartDate.HasValue || _.StartDate >= effectiveDate)).ToListAsync();
        }
    }
}