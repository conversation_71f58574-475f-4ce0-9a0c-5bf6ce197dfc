namespace PaySpace.Venuta.Modules.Employee.Positions
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Services;

    public class CompanyGradeFieldService : GenericService<CompanyGradeField>, ICompanyGradeFieldService
    {
        private readonly IScopedCache scopedCache;

        public CompanyGradeFieldService(
            IDbContextRepository<CompanyGradeField> repository,
            IScopedCache scopedCache)
            : base(repository)
        {
            this.scopedCache = scopedCache;
        }

        public Task<bool> GradeFieldExistsAsync(long gradeFieldId, CancellationToken cancellationToken)
        {
            return this.Repository.Context.Set<CompanyGradeFieldValue>()
                  .AsNoTracking()
                  .TagWithSource()
                  .Where(_ => _.CompanyGradeFieldId == gradeFieldId)
                  .AnyAsync(cancellationToken);
        }

        public IQueryable<CompanyGradeField> GetCompanyGradesFields(long companyId)
        {
            return this.Repository.Set
                    .AsNoTracking()
                    .TagWithSource()
                    .Where(_ => _.CompanyId == companyId)
                    .OrderBy(_ => _.OrderNumber);
        }

        public IList<CompanyGradeField> GetGradesFields(long companyId)
        {
            var cacheKey = $"CompanyGradeFields:{companyId}";
            return this.scopedCache.GetOrCreate(
                cacheKey,
                () => this.Repository.Set
                    .AsNoTracking()
                    .TagWithSource()
                    .Where(_ => _.CompanyId == companyId)
                    .OrderBy(_ => _.OrderNumber)
                    .ToList());
        }

        public IList<CompanyGradeFieldValue> GetCompanyGradeFieldValues(long organizationGradeId)
        {
            var cacheKey = $"CompanyGradeFieldValues:{organizationGradeId}";
            return this.scopedCache.GetOrCreate(
                cacheKey,
                () => this.Repository.Context.Set<CompanyGradeFieldValue>()
                    .AsNoTracking()
                    .TagWithSource()
                    .Where(_ => _.OrganizationGradeId == organizationGradeId)
                    .ToList());
        }

        public IList<CompanyGradeFieldValue> GetCompanyGradeFieldValuesByCompanyId(long companyId)
        {
            return this.scopedCache.GetOrCreate(
                $"CompanyGradeFieldValues:CompanyId:{companyId}",
                () => (from cgf in this.Repository.Context.Set<CompanyGradeField>()
                    join cgfv in this.Repository.Context.Set<CompanyGradeFieldValue>()
                        on cgf.GradeFieldId equals cgfv.CompanyGradeFieldId into cgvGroup
                    from cgfvItem in cgvGroup.DefaultIfEmpty()

                    // Filter out null values as CGFV records are only created if values are set for that CGF record
                    where cgf.CompanyId == companyId && cgfvItem != null
                    select cgfvItem).ToList()
                );
        }
    }
}