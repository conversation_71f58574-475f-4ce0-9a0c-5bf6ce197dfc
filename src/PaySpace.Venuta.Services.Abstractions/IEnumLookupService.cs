namespace PaySpace.Venuta.Services.Abstractions
{
    using System.Collections.Generic;
    using System.Globalization;

    using PaySpace.Venuta.Data.Models.Enums;

    public interface IEnumLookupService
    {
        IList<EnumNqfLevel> GetNqfLevels(CultureInfo? culture = null);

        IList<EnumUIFExemption> GetUIFExemptions(CultureInfo? culture = null);

        IList<EnumDisabledType> GetDisabledTypes(CultureInfo? culture = null);

        IList<EnumTradeUnion> GetTradeUnions(CultureInfo? culture = null);

        IList<EnumMaritalStatus> GetMaritalStatus(int countryId, CultureInfo? culture = null);

        IList<EnumCountry> GetAllCountries(CultureInfo? culture = null);

        IList<EnumPassportCountry> GetPassportCountries(CultureInfo? culture = null);

        IList<EnumAddressCountry> GetAddressCountries(CultureInfo? culture = null);

        IList<EnumEmploymentAction> GetEmploymentActions(CultureInfo? culture = null);

        IList<EnumTaxStatus> GetTaxStatuses(int countryId, CultureInfo? culture = null);

        IList<EnumTerminationReason> GetTerminationReasons(int countryId, CultureInfo? culture = null);

        IList<EnumIdentityType> GetIdentityTypes(int countryId, CultureInfo? culture = null);

        IList<EnumNaturePerson> GetNaturePersons(int countryId, CultureInfo? culture = null);

        IList<EnumProvince> GetProvinces(int countryId, CultureInfo? culture = null);

        IList<EnumMunicipality> GetMunicipality(int countryId, CultureInfo? culture = null);

        IList<EnumAddressStreetType> GetAddressStreetType(int countryId, CultureInfo? culture = null);

        IList<EnumMedicalCategory> GetMedicalAidCategories(CultureInfo? culture = null);

        IList<EnumCurrency> GetCurrencies(long companyGroupId, CultureInfo? culture = null);

        IList<EnumCurrency> GetAllCurrencies(CultureInfo? culture = null);

        IList<EnumStandardIndustryCodeSub> GetStandardIndustryCodeSubs(CultureInfo? culture = null);

        IList<EnumProvince> GetAllProvinces(CultureInfo? culture = null);

        IList<EnumCarPaymentType> GetCarPaymentTypes(int countryId, CultureInfo? culture = null);

        IList<EnumKenyaPurchasedCarOption> GetKenyaPurchasedCarOptions(CultureInfo? culture = null);

        IList<EnumSwazilandAccommodationOption> GetSwazilandAccommodationOptions(CultureInfo? culture = null);

        IList<EnumSuspensionReason> GetSuspensionReason(int countryId, CultureInfo? culture = null);

        IList<EnumOccupationalCategory> GetOccupationalCategories(CultureInfo? culture = null);

        IList<EnumRegistrationType> GetMultiContractWorkRegistrationTypes(CultureInfo? culture = null);

        IList<EnumRemunerationType> GetMultiContractWorkRemunerationTypes(CultureInfo? culture = null);

        IList<EnumInssDeductionIndicator> GetMultiContractWorkInssDeductionIndicators(CultureInfo? culture = null);

        IList<EnumAlimonyBase> GetAlimonyBases(CultureInfo? culture = null);

        IList<EnumLeaveType> GetLeaveTypes(CultureInfo? culture = null);

        IList<EnumLeaveEntryType> GetLeaveEntryTypes(CultureInfo? culture = null);

        IList<EnumReportPath> GetReportPaths(long companyId, CultureInfo? culture = null);

        IList<EnumEducationLevel> GetEducationLevels(CultureInfo? culture = null);

        IList<EnumInstituteType> GetInstituteTypes(CultureInfo? culture = null);

        IList<EnumExperience> GetExperiences(CultureInfo? culture = null);

        IList<EnumCompetency> GetCompetencies(CultureInfo? culture = null);

        IList<EnumAccommodationType> GetAccommodationTypes(CultureInfo? culture = null);

        IList<EnumAccommodationOption> GetAccommodationOptions(CultureInfo? culture = null);

        IList<EnumHotelAccommodationOption> GetHotelAccommodationOptions(CultureInfo? culture = null);

        IList<EnumInboxEntryType> GetInboxEntryTypes(CultureInfo? culture = null);

        IList<EnumPropertyOwnership> GetPropertyOwnership(CultureInfo? culture = null);

        IList<EnumPayDay> GetPayDay(CultureInfo? culture = null);

        IList<EnumPayslipFrequency> GetPayslipFrequency(CultureInfo? culture = null);

        IList<EnumMessageTemplate> GetMessageTemplate(CultureInfo? culture = null);

        IList<EnumThemeColour> GetThemeColours(CultureInfo? culture = null);

        IList<EnumMfaOption> GetMFAOptions(CultureInfo? culture = null);

        IList<EnumCustomFormScreenType> GetCustomFormScreenTypes(CultureInfo? culture = null);

        IList<EnumTrainingProgramCategory> GetTrainingProgramCategory(CultureInfo? culture = null);

        IList<EnumCourseType> GetCourseType(CultureInfo? culture = null);

        IList<EnumTrainingSkillsPriority> GetTrainingSkillsPriority(CultureInfo? culture = null);

        IList<EnumBureauTaxabilityOption> GetEnumBureauTaxabilityOptions(CultureInfo? culture = null);

        IList<EnumRace> GetRace(int countryId, CultureInfo? culture = null);

        IList<EnumSDLExemption> GetSDLExemptions(int countryId, CultureInfo? culture = null);

        IList<EnumSettlementReinstate> GetSettlementReinstatements(CultureInfo? culture = null);

        IList<EnumTitle> GetTitles(int countryId, CultureInfo? culture = null);

        IList<EnumAccountType> GetAccountTypes(CultureInfo? culture = null);

        IList<EnumAppealReason> GetAppealReasons(CultureInfo? culture = null);

        IList<EnumLanguage> GetLanguages(int countryId, CultureInfo? culture = null);

        IList<EnumAwardFavour> GetAwardFavours(CultureInfo? culture = null);

        IList<EnumAppealOutcome> GetAppealOutcomes(CultureInfo? culture = null);

        IList<EnumLocalization> GetLocalization(CultureInfo? culture = null);

        IList<EnumMonthsOfYear> GetMonthsOfYear(CultureInfo? culture = null);

        IList<EnumOccupationalLevel> GetOccupationalLevels(int countryId, CultureInfo? culture = null);

        IList<EnumOffenceCategory> GetOffenceCategories(CultureInfo? culture = null);

        IList<EnumOffenceOutcome> GetOffenceOutcomes(CultureInfo? culture = null);

        IList<EnumOfoLevel> GetOfoLevels(int setaId, CultureInfo? culture = null);

        IList<EnumOtherOutcome> GetOtherOutcomes(CultureInfo? culture = null);

        IList<EnumPositionType> GetPositionTypes(int countryId, CultureInfo? culture = null);

        IList<EnumSpecialComponentType> GetSpecialComponentTypes(int countryId, CultureInfo? culture = null);

        IList<EnumTaxCountry> GetTaxCountries(CultureInfo? culture = null);

        IList<EnumExpectedReturnType> GetExpectedReturnTypes(int countryId, CultureInfo? culture = null);

        IList<EnumPensionExclusionReason> GetPensionExclusionReasons(CultureInfo? culture = null);

        IList<EnumPensionEnrolmentStatus> GetPensionEnrolmentStatuses(CultureInfo? culture = null);

        IList<EnumPensionWorkerCategory> GetPensionWorkerCategories(CultureInfo? culture = null);

        IList<EnumPensionLetterStatus> GetPensionLetterStatuses(CultureInfo? culture = null);

        IList<EnumPensionLetterType> GetPensionLetterTypes(CultureInfo? culture = null);

        IList<EnumIncidentType> GetIncidentTypes(CultureInfo? culture = null);

        IList<EnumDentalBenefit> GetDentalBenefits(CultureInfo? culture = null);

        IList<EnumGender> GetGenders(int countryId, CultureInfo? culture = null);

        IList<EnumPaymentModule> GetPaymentModules(CultureInfo? culture = null);

        IList<EnumCalculationMethod> GetCalculationMethods(CultureInfo? culture = null);

        IList<EnumPaymentMethod> GetEmployeePaymentMethods(int countryId, CultureInfo? culture = null);

        IList<EnumRunType> GetRunTypes(CultureInfo? culture = null);

        IList<EnumRunStatus> GetRunStatus(CultureInfo? culture = null);

        IList<EnumRunTypeExtension> GetRunTypeExtensions(int countryId, CultureInfo? culture = null);

        IList<EnumTaxCountryStatus> GetTaxCountryStatus(CultureInfo? culture = null);

        IList<EnumCustomFieldFormArea> GetCustomFieldFormAreas(CultureInfo? culture = null);

        IList<EnumTaxType> GetTaxTypes(int countryId, CultureInfo? culture = null);

        IList<EnumIncomeTaxOption> GetIncomeTaxOptions(CultureInfo? culture = null);

        IList<EnumRemissionExemptIncomeReason> GetRemissionExemptIncomeReasons(CultureInfo? culture = null);

        IList<EnumOverseasPosting> GetOverseasPostings(CultureInfo? culture = null);

        IList<EnumSeveranceDays> GetSeveranceDays(CultureInfo? culture = null);

        IList<EnumPlanType> GetPlanTypes(CultureInfo? culture = null);

        IList<EnumRaterTypes> GetRaterTypes(CultureInfo? culture = null);

        IList<EnumBasedOnSplitOption> GetBasedOnSplitOptions(CultureInfo? culture = null);

        IList<EnumLeaveForfeitPeriod> GetLeaveForfeitPeriods(CultureInfo? culture = null);

        IList<EnumLeaveAccrualPeriod> GetLeaveAccrualPeriods(CultureInfo? culture = null);

        IList<EnumLeaveAccrualOption> GetLeaveAccrualOptions(CultureInfo? culture = null);

        IList<EnumLeaveAccrualValue> GetLeaveAccrualValues(CultureInfo? culture = null);
    }
}