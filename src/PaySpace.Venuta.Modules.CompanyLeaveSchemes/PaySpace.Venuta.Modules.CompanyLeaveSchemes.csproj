<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <CodeAnalysisRuleSet>..\..\PaySpace.Venuta.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <PropertyGroup>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Roslynator.Formatting.Analyzers">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PaySpace.Venuta.Data.Models.Validation\PaySpace.Venuta.Data.Models.Validation.csproj" />
    <ProjectReference Include="..\PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions\PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.csproj" />
    <ProjectReference Include="..\PaySpace.Venuta.Data\PaySpace.Venuta.Data.csproj" />
    <ProjectReference Include="..\PaySpace.Venuta.Services\PaySpace.Venuta.Services.csproj" />
  </ItemGroup>

</Project>