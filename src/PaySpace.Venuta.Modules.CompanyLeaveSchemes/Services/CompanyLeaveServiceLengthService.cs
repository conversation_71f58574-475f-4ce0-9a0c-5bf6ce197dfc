namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Services
{
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Services;

    public class CompanyLeaveServiceLengthService : GenericService<CompanyLeaveServiceLength>, ICompanyLeaveServiceLengthService
    {
        private readonly ApplicationContext context;

        public CompanyLeaveServiceLengthService(
            IDbContextRepository<CompanyLeaveServiceLength> repository,
            ApplicationContext context)
                : base(repository)
        {
            this.context = context;
        }

        public Task<bool> ServiceLengthsExistAsync(long companyLeaveDetailId)
        {
            return this.context.Set<CompanyLeaveServiceLength>().TagWithSource()
                .AnyAsync(_ => _.CompanyLeaveDetailId == companyLeaveDetailId);
        }
    }
}