namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Services
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Tax;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;

    public class CompanyLeaveSchemeParameterService : ICompanyLeaveSchemeParameterService
    {
        private readonly ApplicationContext context;
        private readonly ICompanySettingService companySettingService;

        public CompanyLeaveSchemeParameterService(ApplicationContext context, ICompanySettingService companySettingService)
        {
            this.context = context;
            this.companySettingService = companySettingService;
        }

        public async Task<List<LeaveSchemeResult>> GetTreeListStructureAsync(long companyId)
        {
            // Fetch all relevant data for the TreeList Grid
            var query = this.context.Set<CompanyLeaveDetail>().TagWithSource()
                .Where(detail => detail.CompanyLeaveSetup.CompanyLeaveScheme.CompanyId == companyId);

            var flattenedData = await query
                .Select(detail => new
                {
                    // Scheme Information
                    detail.CompanyLeaveSetup.CompanyLeaveScheme.CompanyLeaveSchemeId,
                    detail.CompanyLeaveSetup.CompanyLeaveScheme.SchemeName,

                    // Setup Information
                    detail.CompanyLeaveSetup.CompanyLeaveSetupId,
                    detail.CompanyLeaveSetup.LeaveDescription,
                    detail.CompanyLeaveSetup.OrderNumber,
                    detail.CompanyLeaveSetup.LeaveType,

                    // Details Information
                    detail.CompanyLeaveDetailId,
                    detail.EffectiveDate,
                    detail.StopDate
                })
                .ToListAsync();

            // Group flat data by the SchemeId and project into a final hierarchical structure
            var results = flattenedData
                .GroupBy(_ => _.CompanyLeaveSchemeId)
                .Select(_ => new LeaveSchemeResult
                {
                    // Using First() as Scheme information is repeated for each detail in the group
                    CompanyId = companyId,
                    NodeId = $"scheme-{_.Key}",
                    CompanyLeaveSchemeId = _.Key,
                    SchemeName = _.First().SchemeName,

                    // Project children within the group into a result list
                    LeaveSchemeParameterResults = _.Select(item => new LeaveSchemeParameterResult
                    {
                        NodeId = $"param-{item.CompanyLeaveDetailId}",

                        // Details
                        CompanyLeaveDetailId = item.CompanyLeaveDetailId,
                        EffectiveDate = item.EffectiveDate,
                        StopDate = item.StopDate,

                        // Setup
                        CompanyLeaveSetupId = item.CompanyLeaveSetupId,
                        LeaveTypeDescription = item.LeaveDescription,
                        OrderNumber = item.OrderNumber,
                        LeaveType = item.LeaveType
                    })
                    .ToList()
                })
                .ToList();

            return results;
        }

        public IQueryable<object> GetOrderNumbersLookup(long companyLeaveSchemeId, LeaveType leaveTypeId)
        {
            return this.context.Set<CompanyLeaveSetup>().TagWithSource()
                .Where(_ => _.CompanyLeaveSchemeId == companyLeaveSchemeId && _.LeaveType == leaveTypeId)
                .OrderBy(_ => _.OrderNumber)
                .Select(_ => new
                {
                    text = _.OrderNumber,
                    value = _.OrderNumber
                });
        }

        // Used in the Carried Forward Bucket lookup
        public IQueryable<ForfeitCompanyLeaveSetupResult> GetForfeitCompanyLeaveSetups(long companyId)
        {
            return this.context.Set<CompanyLeaveScheme>().TagWithSource()
                .Where(_ => _.CompanyId == companyId)
                .SelectMany(_ => _.CompanyLeaveSetups) // Flatten the CompanyLeaveSetups from each scheme
                .Select(_ => new ForfeitCompanyLeaveSetupResult
                {
                    CompanyLeaveSetupId = _.CompanyLeaveSetupId,
                    CompanyLeaveSchemeId = _.CompanyLeaveSchemeId,
                    LeaveType = _.LeaveType,

                    Description = $"({_.OrderNumber}) - {_.LeaveDescription}",
                    Value = _.LeaveDescription,
                    Id = _.CompanyLeaveSetupId,
                    Order = _.OrderNumber
                });
        }

        // Formulated from classic's two stored procedures used to retrieve this lookup format
        public IQueryable<LeaveEncashmentComponentResult> GetEncashmentComponents(long companyId)
        {
            var query =
                from cmc in this.context.Set<ComponentCompany>().TagWithSource().AsNoTracking()

                join cmb_alias in this.context.Set<ComponentBureau>()
                    on cmc.ComponentBureauId equals cmb_alias.ComponentBureauId into cmb_group
                from cmb in cmb_group.DefaultIfEmpty() // LEFT OUTER JOIN for cmb (cmb can be null)

                join tc in this.context.Set<TaxCode>()
                    on cmb.TaxCodeId equals tc.TaxCodeId

                join crf_alias in this.context.Set<CompanyRunFrequency>()
                    on cmc.CompanyFrequencyId equals crf_alias.CompanyFrequencyId into crf_group
                from crf in crf_group.DefaultIfEmpty() // LEFT OUTER JOIN for crf (crf can be null)

                where crf != null && crf.CompanyId == companyId
                select new LeaveEncashmentComponentResult
                {
                    ComponentId = cmc.ComponentId,
                    AliasDescription = cmc.AliasDescription,
                    EncashmentDescription = crf.FrequencyName + " - " + cmc.AliasDescription
                };

            return query;
        }

        public async Task<(bool isGradeBasedAccrualEnabled, bool isGradeBasedMaxBalanceEnabled)> GetRelevantGradeSettingsAsync(long companyId)
        {
            var isGradeBasedAccrualEnabled = await this.companySettingService.IsActiveAsync(companyId, SystemAreas.CompanyLeaveSchemeParameter.Keys.GradeBasedAccrualSettingCode);
            var isGradeBasedMaxBalanceEnabled = await this.companySettingService.IsActiveAsync(companyId, SystemAreas.CompanyLeaveSchemeParameter.Keys.GradeBasedMaxBalanceSettingCode);

            return (isGradeBasedAccrualEnabled, isGradeBasedMaxBalanceEnabled);
        }
    }
}