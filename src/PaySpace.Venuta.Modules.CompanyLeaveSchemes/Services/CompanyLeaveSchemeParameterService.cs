namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Services
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Tax;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;

    public class CompanyLeaveSchemeParameterService : ICompanyLeaveSchemeParameterService
    {
        private readonly ApplicationContext context;
        private readonly ICompanySettingService companySettingService;

        public CompanyLeaveSchemeParameterService(ApplicationContext context, ICompanySettingService companySettingService)
        {
            this.context = context;
            this.companySettingService = companySettingService;
        }

        public async Task<List<LeaveSchemeResult>> GetTreeListStructureAsync(long companyId)
        {
            // Optimized query with proper includes and single database hit
            var results = await this.context.Set<CompanyLeaveScheme>().TagWithSource()
                .AsNoTracking() // Read-only optimization
                .Where(scheme => scheme.CompanyId == companyId)
                .Include(scheme => scheme.CompanyLeaveSetups)
                    .ThenInclude(setup => setup.CompanyLeaveDetails)
                .Select(scheme => new LeaveSchemeResult
                {
                    CompanyId = companyId,
                    NodeId = $"scheme-{scheme.CompanyLeaveSchemeId}",
                    CompanyLeaveSchemeId = scheme.CompanyLeaveSchemeId,
                    SchemeName = scheme.SchemeName,
                    LeaveSchemeParameterResults = scheme.CompanyLeaveSetups
                        .SelectMany(setup => setup.CompanyLeaveDetails)
                        .Select(detail => new LeaveSchemeParameterResult
                        {
                            NodeId = $"param-{detail.CompanyLeaveDetailId}",
                            CompanyLeaveDetailId = detail.CompanyLeaveDetailId,
                            EffectiveDate = detail.EffectiveDate,
                            StopDate = detail.StopDate,
                            CompanyLeaveSetupId = detail.CompanyLeaveSetup.CompanyLeaveSetupId,
                            LeaveTypeDescription = detail.CompanyLeaveSetup.LeaveDescription,
                            OrderNumber = detail.CompanyLeaveSetup.OrderNumber,
                            LeaveType = detail.CompanyLeaveSetup.LeaveType
                        })
                        .OrderBy(param => param.LeaveType)
                        .ThenBy(param => param.OrderNumber)
                        .ToList()
                })
                .OrderBy(scheme => scheme.SchemeName)
                .ToListAsync();

            return results;
        }

        public IQueryable<object> GetOrderNumbersLookup(long companyLeaveSchemeId, LeaveType leaveTypeId)
        {
            return this.context.Set<CompanyLeaveSetup>().TagWithSource()
                .Where(_ => _.CompanyLeaveSchemeId == companyLeaveSchemeId && _.LeaveType == leaveTypeId)
                .OrderBy(_ => _.OrderNumber)
                .Select(_ => new
                {
                    text = _.OrderNumber,
                    value = _.OrderNumber
                });
        }

        // Used in the Carried Forward Bucket lookup
        public IQueryable<ForfeitCompanyLeaveSetupResult> GetForfeitCompanyLeaveSetups(long companyId)
        {
            return this.context.Set<CompanyLeaveScheme>().TagWithSource()
                .Where(_ => _.CompanyId == companyId)
                .SelectMany(_ => _.CompanyLeaveSetups) // Flatten the CompanyLeaveSetups from each scheme
                .Select(_ => new ForfeitCompanyLeaveSetupResult
                {
                    CompanyLeaveSetupId = _.CompanyLeaveSetupId,
                    CompanyLeaveSchemeId = _.CompanyLeaveSchemeId,
                    LeaveType = _.LeaveType,

                    Description = $"({_.OrderNumber}) - {_.LeaveDescription}",
                    Value = _.LeaveDescription,
                    Id = _.CompanyLeaveSetupId,
                    Order = _.OrderNumber
                });
        }

        // Formulated from classic's two stored procedures used to retrieve this lookup format
        public IQueryable<LeaveEncashmentComponentResult> GetEncashmentComponents(long companyId)
        {
            var query =
                from cmc in this.context.Set<ComponentCompany>().TagWithSource().AsNoTracking()

                join cmb_alias in this.context.Set<ComponentBureau>()
                    on cmc.ComponentBureauId equals cmb_alias.ComponentBureauId into cmb_group
                from cmb in cmb_group.DefaultIfEmpty() // LEFT OUTER JOIN for cmb (cmb can be null)

                join tc in this.context.Set<TaxCode>()
                    on cmb.TaxCodeId equals tc.TaxCodeId

                join crf_alias in this.context.Set<CompanyRunFrequency>()
                    on cmc.CompanyFrequencyId equals crf_alias.CompanyFrequencyId into crf_group
                from crf in crf_group.DefaultIfEmpty() // LEFT OUTER JOIN for crf (crf can be null)

                where crf != null && crf.CompanyId == companyId
                select new LeaveEncashmentComponentResult
                {
                    ComponentId = cmc.ComponentId,
                    AliasDescription = cmc.AliasDescription,
                    EncashmentDescription = crf.FrequencyName + " - " + cmc.AliasDescription
                };

            return query;
        }

        public async Task<(bool isGradeBasedAccrualEnabled, bool isGradeBasedMaxBalanceEnabled)> GetRelevantGradeSettingsAsync(long companyId)
        {
            var isGradeBasedAccrualEnabled = await this.companySettingService.IsActiveAsync(companyId, SystemAreas.CompanyLeaveSchemeParameter.Keys.GradeBasedAccrualSettingCode);
            var isGradeBasedMaxBalanceEnabled = await this.companySettingService.IsActiveAsync(companyId, SystemAreas.CompanyLeaveSchemeParameter.Keys.GradeBasedMaxBalanceSettingCode);

            return (isGradeBasedAccrualEnabled, isGradeBasedMaxBalanceEnabled);
        }
    }
}