namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Services
{
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Services;

    public class CompanyLeaveDetailService : GenericService<CompanyLeaveDetail>, ICompanyLeaveDetailService
    {
        public CompanyLeaveDetailService(IDbContextRepository<CompanyLeaveDetail> repository)
            : base(repository)
        {
        }

        public void SetDefaultValues(CompanyLeaveDetail model)
        {
            SetEmployeeDefinedDefaults(model);
            SetServiceLengthAndGradeBandDefaults(model);

            ClearUnusedServiceLengthFields(model);
            ClearProrationFields(model);
            ClearForfeitureFields(model);

            // Ensure a value is set for Occurence as it is a non-nullable property
            // Validation will kick in if Acc<PERSON>al<PERSON>eri<PERSON> is Years and Occurence is not set
            if (model.AccrualPeriodId != (int)LeaveAccrualPeriod.Year && model.AccrualOptionId == 0)
            {
                model.AccrualOptionId = (int)LeaveAccrualOption.PerMonth;
            }
        }

        private static void SetEmployeeDefinedDefaults(CompanyLeaveDetail model)
        {
            if (model.ApplyEmployeeDefined == true)
            {
                model.Accrual = 0;
            }
        }

        // Set default values for the fields not being used based on the entitlement band option
        // This is also necessary for audit trail to ensure we omit fields we do not need to audit
        private static void SetServiceLengthAndGradeBandDefaults(CompanyLeaveDetail model)
        {
            if (!model.ApplyServiceLength && model.ApplyGradeBands != true)
            {
                return;
            }

            model.Accrual = 0;
            model.AccrualPeriodValue = null;
            model.LeaveAccrualValueId = (int)LeaveAccrualValue.None;

            if (model.ApplyServiceLength)
            {
                model.AccrualPeriodId = (int)LeaveAccrualPeriod.None;
                model.AccrualOptionId = (int)LeaveAccrualOption.PerMonth;
            }
            else
            {
                model.AccrualPeriodId = model.AccrualOptionId == (int)LeaveAccrualOption.Upfront
                    ? (int)LeaveAccrualPeriod.Year
                    : (int)LeaveAccrualPeriod.None;
            }
        }

        private static void ClearUnusedServiceLengthFields(CompanyLeaveDetail model)
        {
            if (model.ApplyEmployeeDefined == true || model.CompanyLeaveServiceLengths == null)
            {
                return;
            }

            if (model.ApplyServiceLength)
            {
                foreach (var serviceLength in model.CompanyLeaveServiceLengths)
                {
                    serviceLength.GradeCode = null;
                }
            }
            else if (model.ApplyGradeBands == true)
            {
                foreach (var serviceLength in model.CompanyLeaveServiceLengths)
                {
                    serviceLength.StartYear = 0;
                    serviceLength.EndYear = 0;
                    serviceLength.MaxBalanceFieldId = null;
                }
            }
        }

        private static void ClearProrationFields(CompanyLeaveDetail model)
        {
            if (model.UpfrontProRateOptions != true)
            {
                model.UpfrontMonthlyAccrual = null;
                model.UpfrontAccrualPeriod = null;
            }
        }

        private static void ClearForfeitureFields(CompanyLeaveDetail model)
        {
            // Accumulative counts as disabled
            if (model.CompanyLeaveSetupType == CompanyLeaveSetupType.Accumulative)
            {
                model.CarryOverDays = 0;
                model.ForfeitCompanyLeaveSetupId = null;
                model.EffectiveDateForfeit = string.Empty;
                model.DropOffMonthId = 1; // classic breaks if this is null (set to null once classic is phased out)
            }
        }
    }
}