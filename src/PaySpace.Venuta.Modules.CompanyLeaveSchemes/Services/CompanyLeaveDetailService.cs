namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Services
{
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Services;

    public class CompanyLeaveDetailService : GenericService<CompanyLeaveDetail>, ICompanyLeaveDetailService
    {
        private readonly ICompanyLeaveSchemeParameterService leaveSchemeParameterService;
        private readonly ICompanyGradeFieldService companyGradeFieldService;

        public CompanyLeaveDetailService(
            IDbContextRepository<CompanyLeaveDetail> repository,
            ICompanyLeaveSchemeParameterService leaveSchemeParameterService,
            ICompanyGradeFieldService companyGradeFieldService)
        : base(repository)
        {
            this.leaveSchemeParameterService = leaveSchemeParameterService;
            this.companyGradeFieldService = companyGradeFieldService;
        }

        public void SetDefaultValues(CompanyLeaveDetail model)
        {
            SetEmployeeDefinedDefaults(model);
            SetServiceLengthAndGradeBandDefaults(model);

            ClearUnusedServiceLengthFields(model);
            ClearProrationFields(model);
            ClearForfeitureFields(model);

            // Ensure a value is set for Occurence as it is a non-nullable property
            // Validation will kick in if AccrualPeriod is Years and Occurence is not set
            if (model.AccrualPeriodId != (int)LeaveAccrualPeriod.Year && model.AccrualOptionId == 0)
            {
                model.AccrualOptionId = (int)LeaveAccrualOption.PerMonth;
            }
        }

        // Note this is easier to do on the backend and accounts for the API
        public async Task ProcessServiceLengthRecordsAsync(CompanyLeaveDetail model, CompanyLeaveSetupDto dto)
        {
            LinkServiceLengthRecordToNewLeaveSetup(model, dto);

            var gradeSettings = await this.leaveSchemeParameterService.GetRelevantGradeSettingsAsync(model.CompanyId);
            if (!gradeSettings.isGradeBasedAccrualEnabled ||
                model.CompanyLeaveServiceLengths == null ||
                !model.ApplyServiceLength)
            {
                return;
            }

            // Based on the above setting check, set the Accrual value from the GradeField selected
            var serviceLengthsToUpdate = model.CompanyLeaveServiceLengths.Where(_ => _.ExtraFieldId != null).ToList();
            if (serviceLengthsToUpdate.Count == 0)
            {
                // Set Accrual to zero for these, as classic seems to follow this
                foreach (var serviceLength in model.CompanyLeaveServiceLengths)
                {
                    serviceLength.Accrual = 0m;
                    return;
                }
            }

            var companyGradeFieldValues = this.companyGradeFieldService.GetCompanyGradeFieldValuesByCompanyId(model.CompanyId);
            if (companyGradeFieldValues.Count == 0)
            {
                return;
            }

            // Build a dictionary for efficient lookup based on CompanyGradeFieldId
            var gradeFieldValueMap = companyGradeFieldValues.ToDictionary(_ => _.CompanyGradeFieldId, _ => _.FieldValue);
            foreach (var serviceLength in serviceLengthsToUpdate)
            {
                if (serviceLength.ExtraFieldId.HasValue &&
                    gradeFieldValueMap.TryGetValue(serviceLength.ExtraFieldId.Value, out var fieldValueString))
                {
                    serviceLength.Accrual = decimal.TryParse(fieldValueString, out var accrual) ? accrual : 0m;
                }
                else
                {
                    // Accrual is set to zero if the selected GradeField does not have a value set for it
                    serviceLength.Accrual = 0m;
                }
            }
        }

        private static void LinkServiceLengthRecordToNewLeaveSetup(CompanyLeaveDetail model, CompanyLeaveSetupDto dto)
        {
            if (dto.CompanyLeaveDetailId == 0 ||
                dto.CompanyLeaveServiceLengths == null ||
                !dto.CompanyLeaveServiceLengths.Any(_ => _.CompanyLeaveDetailId == 0))
            {
                return;
            }

            var unlinkedServiceLengths = dto.CompanyLeaveServiceLengths.Where(_ => _.CompanyLeaveDetailId == 0).ToList();
            foreach (var serviceLength in unlinkedServiceLengths)
            {
                serviceLength.CompanyLeaveDetailId = model.CompanyLeaveDetailId;
            }
        }

        private static void SetEmployeeDefinedDefaults(CompanyLeaveDetail model)
        {
            if (model.ApplyEmployeeDefined == true)
            {
                model.Accrual = 0;
            }
        }

        // Set default values for the fields not being used based on the entitlement band option
        // This is also necessary for audit trail to ensure we omit fields we do not need to audit
        private static void SetServiceLengthAndGradeBandDefaults(CompanyLeaveDetail model)
        {
            if (!model.ApplyServiceLength && model.ApplyGradeBands != true)
            {
                return;
            }

            model.Accrual = 0;
            model.AccrualPeriodValue = null;
            model.LeaveAccrualValueId = (int)LeaveAccrualValue.None;

            if (model.ApplyServiceLength)
            {
                model.AccrualPeriodId = (int)LeaveAccrualPeriod.None;
                model.AccrualOptionId = (int)LeaveAccrualOption.PerMonth;
            }
            else
            {
                model.AccrualPeriodId = model.AccrualOptionId == (int)LeaveAccrualOption.Upfront
                    ? (int)LeaveAccrualPeriod.Year
                    : (int)LeaveAccrualPeriod.None;
            }
        }

        private static void ClearUnusedServiceLengthFields(CompanyLeaveDetail model)
        {
            if (model.ApplyEmployeeDefined == true || model.CompanyLeaveServiceLengths == null)
            {
                return;
            }

            if (model.ApplyServiceLength)
            {
                foreach (var serviceLength in model.CompanyLeaveServiceLengths)
                {
                    serviceLength.GradeCode = null;
                }
            }
            else if (model.ApplyGradeBands == true)
            {
                foreach (var serviceLength in model.CompanyLeaveServiceLengths)
                {
                    serviceLength.StartYear = 0;
                    serviceLength.EndYear = 0;
                    serviceLength.MaxBalanceFieldId = null;
                }
            }
        }

        private static void ClearProrationFields(CompanyLeaveDetail model)
        {
            if (model.UpfrontProRateOptions != true)
            {
                model.UpfrontMonthlyAccrual = null;
                model.UpfrontAccrualPeriod = null;
            }
        }

        private static void ClearForfeitureFields(CompanyLeaveDetail model)
        {
            // Accumulative counts as disabled
            if (model.CompanyLeaveSetupType != CompanyLeaveSetupType.Accumulative)
            {
                return;
            }

            model.CarryOverDays = 0;
            model.ForfeitCompanyLeaveSetupId = null;
            model.EffectiveDateForfeit = string.Empty;

            // Only set DropOffMonthId to 1 if it's not already set from mapping
            // As classic breaks if this is null (set to null once classic is phased out)
            if (model.DropOffMonthId is null or 0)
            {
                model.DropOffMonthId = 1;
            }
        }
    }
}