namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    using PaySpace.Venuta.Data.Extensions;
    using PaySpace.Venuta.Data.Models.Company;

    public class CompanyLeaveDetailConfiguration : IEntityTypeConfiguration<CompanyLeaveDetail>
    {
        public void Configure(EntityTypeBuilder<CompanyLeaveDetail> builder)
        {
            builder.HasIntMinusOneConverter(_ => _.LeaveForfeitPeriodId)
                   .HasIntMinusOneConverter(_ => _.LeaveAccrualValueId);

            builder.HasMany(_ => _.CompanyLeaveServiceLengths)
                   .WithOne(_ => _.CompanyLeaveDetail);
        }
    }
}