namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using FluentValidation;

    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Services;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Validators;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddLeaveSchemeParameterModules(this IServiceCollection services)
        {
            services.AddScoped<ICompanyLeaveSchemeParameterService, CompanyLeaveSchemeParameterService>();
            services.AddScoped<ICompanyLeaveServiceLengthService, CompanyLeaveServiceLengthService>();
            services.AddScoped<ICompanyLeaveDetailService, CompanyLeaveDetailService>();

            // Register Validators
            services.AddCountryServices<IValidator<CompanyLeaveDetail>>(typeof(CompanyLeaveDetailValidator).Assembly, ServiceLifetime.Transient);

            foreach (var type in GetValidatorTypes())
            {
                foreach (var interfaceType in type.GetInterfaces())
                {
                    services.TryAddTransient(interfaceType, type);
                }
            }

            return services;
        }

        private static IEnumerable<Type> GetValidatorTypes()
        {
            return typeof(ServiceCollectionExtensions).Assembly.GetTypes()
                .Where(t => !t.IsAbstract && t.IsPublic && typeof(IValidator).IsAssignableFrom(t) && !t.ContainsGenericParameters);
        }
    }
}