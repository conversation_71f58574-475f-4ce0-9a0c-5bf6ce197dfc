namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Validators
{
    using FluentValidation;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Infrastructure;

    public class CompanyLeaveServiceLengthValidator : AbstractValidator<CompanyLeaveServiceLength>
    {
        private readonly IStringLocalizer<CompanyLeaveDetail> localizer;

        public CompanyLeaveServiceLengthValidator(IStringLocalizer<CompanyLeaveDetail> localizer)
        {
            this.localizer = localizer;

            this.RuleSet(RuleSetNames.Create, this.CreateRules);
            this.RuleSet(RuleSetNames.Update, this.UpdateRules);
        }

        private void CreateRules()
        {
            this.SharedRules();
        }

        private void UpdateRules()
        {
            this.SharedRules();
        }

        private void SharedRules()
        {
            this.RuleFor(_ => _.ServiceDescription)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.ServiceDescriptionRequired));

            this.RuleFor(_ => _.StartYear)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.StartYearsRequired));

            this.RuleFor(_ => _.EndYear)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.EndYearsRequired));
        }
    }
}