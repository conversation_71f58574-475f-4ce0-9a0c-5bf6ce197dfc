namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Validators.BR
{
    using System.ComponentModel;

    using FluentValidation;

    using Maddalena;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;

    [CountryService(CountryCode.BR)]
    [DisplayName(SystemAreas.CompanyLeaveSchemeParameter.Area)]
    public class BrazilCompanyLeaveDetailValidator : CompanyLeaveDetailValidator
    {
        public BrazilCompanyLeaveDetailValidator(
            ICompanyLeaveServiceLengthService leaveServiceLengthService,
            IStringLocalizer<CompanyLeaveDetail> localizer,
            ApplicationContext context)
                : base(leaveServiceLengthService, localizer, context)
        {
        }

        protected override void SharedRules()
        {
            base.SharedRules();

            this.RuleFor(_ => _.OffDays)
                .LessThan(100)
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.Max2DigitParcelNumber));

            // Parcel Combination
            this.RuleFor(_ => _.OffDays)
                .LessThan(31)
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.ParcelValueGreaterThan30));
        }
    }
}