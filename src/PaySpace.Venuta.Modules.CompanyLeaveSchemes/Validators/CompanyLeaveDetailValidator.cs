namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Validators
{
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;

    public class CompanyLeaveDetailValidator : AbstractValidator<CompanyLeaveDetail>
    {
        private readonly ICompanyLeaveServiceLengthService leaveServiceLengthService;
        private readonly ApplicationContext context;

        public CompanyLeaveDetailValidator(
            ICompanyLeaveServiceLengthService leaveServiceLengthService,
            IStringLocalizer<CompanyLeaveDetail> localizer,
            ApplicationContext context)
        {
            this.leaveServiceLengthService = leaveServiceLengthService;
            this.Localizer = localizer;
            this.context = context;

            this.RuleSet(RuleSetNames.Create, this.CreateRules);
            this.RuleSet(RuleSetNames.Update, this.UpdateRules);
            this.RuleSet(RuleSetNames.Delete, this.DeleteRules);
        }

        protected IStringLocalizer<CompanyLeaveDetail> Localizer { get; }

        private void CreateRules()
        {
            this.SharedRules();
        }

        private void UpdateRules()
        {
            this.SharedRules();

            this.RuleFor(_ => _.EffectiveDate)
                .Must((model, _) => !this.context.IsFieldModified(model, nameof(model.EffectiveDate)))
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.EffectiveDateNotEditable));
        }

        private void DeleteRules()
        {
            this.RuleFor(_ => _.CompanyLeaveDetailId)
                .MustAsync(this.DependantRecordsExistAsync)
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.CannotDeleteRecord));
        }

        protected virtual void SharedRules()
        {
            this.LeaveAccrualValidations();

            this.RuleFor(_ => _.EffectiveDate)
                .NotEmpty()
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.EffectiveDateRequired));

            // Only required when no entitlement band is selected
            this.When(entity => entity is { ApplyServiceLength: false, ApplyEmployeeDefined: not true, ApplyGradeBands: not true }, () =>
            {
                // Leave credits to accrue
                this.RuleFor(_ => _.Accrual)
                    .NotNull()
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.AccrualPeriodRequired))
                    .DependentRules(() =>
                    {
                        this.RuleFor(_ => _.Accrual)
                            .GreaterThan(0)
                            .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.AccrualPeriodZero));
                    });
            });

            this.When(_ => _.ApplyEmployeeDefined == true, this.EmployeeDefinedValidations);
            this.When(_ => _.UpfrontProRateOptions == true, this.UpfrontProRateOptionValidations);

            // Non-Accumulative enables Forfeiture Rule fields
            this.When(_ => _.CompanyLeaveSetupType == CompanyLeaveSetupType.NonAccumulative, this.NonAccumulativeValidations);
        }

        private void LeaveAccrualValidations()
        {
            this.When(_ => _.ApplyServiceLength, () =>
            {
                this.RuleFor(_ => _.AccrualOptionId)
                    .Must((model, _) => model.AccrualOptionId == (int)LeaveAccrualOption.PerMonth)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.UpfrontOptionNotAllowed));
            });

            this.When(_ => _.LeaveAccrualValueId == (int)LeaveAccrualValue.Hours, () =>
            {
                this.RuleFor(_ => _.AccrualPeriodId)
                    .Must((model, _) => model.AccrualPeriodId is ((int)LeaveAccrualPeriod.Month) or ((int)LeaveAccrualPeriod.Year))
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.OptionNotAllowedWithHours));
            });

            this.When(_ => _.AccrualPeriodId == (int)LeaveAccrualPeriod.Year, () =>
            {
                // Occurence (dropdown)
                this.RuleFor(_ => _.AccrualOptionId)
                    .NotEmpty()
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.AccrualOptionIdRequired));
            });
        }

        private void EmployeeDefinedValidations()
        {
            // Leave credit unit
            this.RuleFor(_ => _.LeaveAccrualValueId)
                .NotNull()
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.LeaveAccrualValueRequired));

            // Accrue every
            this.RuleFor(_ => _.AccrualPeriodValue)
                .NotNull()
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.AccrualPeriodValueRequired));

            // Accrual period (dropdown)
            this.RuleFor(_ => _.AccrualPeriodId)
                .NotEmpty()
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.AccrualPeriodIdRequired));
        }

        private void UpfrontProRateOptionValidations()
        {
            // Leave days p/month to accrue
            this.RuleFor(_ => _.UpfrontMonthlyAccrual)
                .NotEmpty()
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.UpfrontMonthlyAccrualRequired));

            // For the first number of months
            this.RuleFor(_ => _.UpfrontAccrualPeriod)
                .NotEmpty()
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.UpfrontAccrualPeriodRequired));
        }

        private void NonAccumulativeValidations()
        {
            // Forfeited after every
            this.RuleFor(_ => _.ForfeitPeriod)
                .GreaterThan(0)
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.ForfeitPeriodZero));

            this.When(_ => _.EffectiveDateForfeit == SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.SpecifyMonth, () =>
            {
                this.RuleFor(_ => _.DropOffMonthId)
                    .NotEmpty()
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyLeaveSchemeParameter.DropOffMonthRequired));
            });
        }

        private async Task<bool> DependantRecordsExistAsync(CompanyLeaveDetail entity, long companyLeaveDetailId, CancellationToken cancellationToken = default)
        {
            return !await this.leaveServiceLengthService.ServiceLengthsExistAsync(companyLeaveDetailId);
        }
    }
}