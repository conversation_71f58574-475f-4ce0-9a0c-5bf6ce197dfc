namespace PaySpace.Venuta.Data.Models.Employees
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;

    [DisplayName(SystemAreas.YearEndReporting.Area)]
    public class EmployeeIR8A : IEmployeeAuditEntity
    {
        [Key]
        [Column("pkEmployeeIR8AId")]
        public long EmployeeIR8AId { get; set; }

        [Column("fkEmployeeId")]
        public long EmployeeId { get; set; }

        public Employee Employee { get; set; }

        [Column("fkTaxYearId")]
        [Display(Name = SystemAreas.YearEndReporting.Keys.BasisYear)]
        public int TaxYearId { get; set; }

        public CountryTaxYear TaxYear { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsDeclarationByAgent)]
        public bool IsDeclarationByAgent { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsIR21Submitted)]
        public bool IsSection45Applicable { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsIR21Submitted)]
        public bool IsIR21Submitted { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsIncomeTaxBorneByEmployer)]
        public bool IsIncomeTaxBorneByEmployer { get; set; }

        [Column("fkIncomeTaxOptionId")]
        [Display(Name = SystemAreas.YearEndReporting.Keys.IncomeTaxOption)]
        public int? IncomeTaxOptionId { get; set; }

        public EnumIncomeTaxOption? IncomeTaxOption { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployerIncomeTaxAmount)]
        public int EmployerIncomeTaxAmount { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployeeLiabilityAmount)]
        public int EmployeeLiabilityAmount { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.RemissionIncomeAmount)]
        public int RemissionIncomeAmount { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsExemptRemissionIncomeApplicable)]
        public bool IsExemptRemissionIncomeApplicable { get; set; }

        [Column("fkRemissionExemptIncomeReasonId")]
        [Display(Name = SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeReason)]
        public int? RemissionExemptIncomeReasonId { get; set; }

        public EnumRemissionExemptIncomeReason? RemissionExemptIncomeReason { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeAmount)]
        public int RemissionExemptIncomeAmount { get; set; }

        [Column("fkOverseasPostingReasonId")]
        [Display(Name = SystemAreas.YearEndReporting.Keys.OverseasPostingReason)]
        public int? OverseasPostingReasonId { get; set; }

        public EnumOverseasPosting? OverseasPostingReason { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.DesignatedFundName)]
        public string? DesignatedFundName { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.LumpSumPaymentReason)]
        public string? LumpSumPaymentReason { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.LumpSumPaymentBasis)]
        public string? LumpSumPaymentBasis { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.PensionOrProvidentFundName)]
        public string? PensionOrProvidentFundName { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.AmountAccruedFrom1993)]
        public decimal AmountAccruedFrom1993 { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.BonusDeclarationDate)]
        public DateTime BonusDeclarationDate { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.DirectorsFeesApprovalDate)]
        public DateTime DirectorsFeesApprovalDate { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployerCpfRefundClaimed)]
        public int EmployerCpfRefundClaimed { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployeeCpfRefundClaimed)]
        public int EmployeeCpfRefundClaimed { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployerRefundInterest)]
        public int EmployerRefundInterest { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployeeRefundInterest)]
        public int EmployeeRefundInterest { get; set; }

        public override string ToString()
        {
            return $"{this.Employee.FirstName} - IR8A Details - Year: {this.TaxYear.YearStartDate.Year}";
        }
    }
}
