namespace PaySpace.Venuta.Data.Models.Company
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Text.Json.Serialization;

    using PaySpace.Data.Models.Converters;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;

    public enum CompanyLeaveSetupType
    {
        Accumulative = 1,
        NonAccumulative = 2 // Enables forfeiture rules
    }

    [Table("CompanyLeaveDetails")]
    [DisplayName(SystemAreas.CompanyLeaveSchemeParameter.Area)]
    public class CompanyLeaveDetail : LeaveDetailInfo, ICompanyAuditEntity
    {
        [Key]
        [Column("pkCompanyLeaveDetailsID")]
        public long CompanyLeaveDetailId { get; set; }

        [Column("fkCompanyLeaveID")]
        public long CompanyLeaveSetupId { get; set; }

        public virtual CompanyLeaveSetup CompanyLeaveSetup { get; set; }

        [Column("AccNonAcc")]
        public CompanyLeaveSetupType CompanyLeaveSetupType { get; set; }

        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        public DateTime EffectiveDate { get; set; }

        public string EffectiveDateForfeit { get; set; }

        [Column("fkDropOffMonthID")]
        public int? DropOffMonthId { get; set; }

        public EnumMonthsOfYear DropOffMonth { get; set; }

        [Column("fkForfeitPeriodID")]
        public int? LeaveForfeitPeriodId { get; set; }

        public EnumLeaveForfeitPeriod LeaveForfeitPeriod { get; set; }

        [Column("fkLeaveAccrualValueID")]
        public int? LeaveAccrualValueId { get; set; }

        public EnumLeaveAccrualValue LeaveAccrualValue { get; set; }

        [Column("fkAccrualPeriodID")]
        public int AccrualPeriodId { get; set; }

        public EnumLeaveAccrualPeriod AccrualPeriod { get; set; }

        [Column("fkAccrualOptionID")]
        public int AccrualOptionId { get; set; }

        public EnumLeaveAccrualOption AccrualOption { get; set; }

        public int? ForfeitPeriod { get; set; }

        [Column("ForfeitCompanyLeaveID")]
        public long? ForfeitCompanyLeaveSetupId { get; set; }

        public decimal? CarryOverDays { get; set; }

        public int? NegativeLeaveAmount { get; set; }

        public double? ValueLessThan { get; set; }

        public double? ValueMoreThan { get; set; }

        [Column("fkLiabilityComponentID")]
        public long? LiabilityComponentId { get; set; }

        [Column("DoNotCalcBCEAValue")]
        public bool? DoNotCalculateBceaValue { get; set; }

        public bool? AttachmentMandatory { get; set; }

        [Column("DoNotForceAttach")]
        public bool? DoNotForceAttachment { get; set; }

        [Column("ForceAttachOnSecond")]
        public bool? ForceAttachmentOnSecond { get; set; }

        [Column("AfterNoDays")]
        public double? AfterDays { get; set; }

        public bool? DisplayBalanceESS { get; set; }

        public bool? ReflectInHours { get; set; }

        public bool? IncludePendingApps { get; set; }

        [Column("includePH")]
        public bool? IncludePH { get; set; }

        public bool? ApplyGradeBands { get; set; }

        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        public DateTime? StopDate { get; set; }

        public string BucketRules { get; set; }

        public long? EncashComponentId { get; set; }

        public bool? DoNotShowOnPaySlip { get; set; }

        public bool? ShowOnPaySlip { get; set; }

        public int? ConsecutiveDays { get; set; }

        public int? OffDays { get; set; }

        public string? ParcelCombination { get; set; }

        public decimal Accrual { get; set; }

        public decimal? AccrualPeriodValue { get; set; }

        public bool ApplyServiceLength { get; set; }

        public bool? ApplyEmployeeDefined { get; set; }

        public bool? UpfrontProRateOptions { get; set; }

        public decimal? UpfrontMonthlyAccrual { get; set; }

        public int? UpfrontAccrualPeriod { get; set; }

        public decimal? MaxBalance { get; set; }

        public int? AccrualEngagementDay { get; set; }

        public bool? ProrateAccrual { get; set; }

        [Column("AccrualCompCodeHrs")]
        public string? AccrualComponentCodeHours { get; set; }

        public ICollection<CompanyLeaveServiceLength> CompanyLeaveServiceLengths { get; set; }
    }

    // Used in Audit Trail for Deletes due to parent entities being deleted
    public class LeaveDetailInfo
    {
        [NotMapped]
        public long CompanyId { get; set; }

        [NotMapped]
        public long CompanyLeaveSchemeId { get; set; }

        [NotMapped]
        public string? LeaveDescription { get; set; }

        [NotMapped]
        public LeaveType? LeaveType { get; set; }

        [NotMapped]
        public string? SchemeName { get; set; }

        [NotMapped]
        public int? OrderNumber { get; set; }
    }
}