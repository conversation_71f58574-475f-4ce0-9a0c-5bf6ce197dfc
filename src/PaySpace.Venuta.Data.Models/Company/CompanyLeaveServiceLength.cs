namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Data.Models.Enums;

    public class CompanyLeaveServiceLength : ICompanyAuditEntity
    {
        [Key]
        [Column("pkServiceLengthID")]
        public long ServiceLengthId { get; set; }

        [Column("fkCompanyLeaveDetailsID")]
        public long CompanyLeaveDetailId { get; set; }

        public CompanyLeaveDetail CompanyLeaveDetail { get; set; }

        public string ServiceDescription { get; set; }

        public int StartYear { get; set; }

        public int EndYear { get; set; }

        public decimal Accrual { get; set; }

        [Column("fkAccrualPeriodID")]
        public int AccrualPeriodId { get; set; }

        public EnumLeaveAccrualPeriod AccrualPeriod { get; set; }

        [Column("fkLeaveAccrualValueID")]
        public int LeaveAccrualValueId { get; set; }

        public EnumLeaveAccrualValue LeaveAccrualValue { get; set; }

        public decimal AccrualPeriodValue { get; set; }

        public string GradeCode { get; set; }

        [Column("fkExtraFieldID")]
        public long? ExtraFieldId { get; set; }

        [Column("fkMaxBalanceFieldID")]
        public long? MaxBalanceFieldId { get; set; }

        [NotMapped]
        public long CompanyId { get; set; }
    }
}