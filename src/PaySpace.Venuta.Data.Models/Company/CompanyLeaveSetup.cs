namespace PaySpace.Venuta.Data.Models.Company
{
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;

    [DisplayName(SystemAreas.Leave.Application)]
    public class CompanyLeaveSetup : ICompanyAuditEntity
    {
        [Key]
        [Column("pkCompanyLeaveID")]
        public long CompanyLeaveSetupId { get; set; }

        [Column("fkCompanySchemeID")]
        public long CompanyLeaveSchemeId { get; set; }

        public virtual CompanyLeaveScheme CompanyLeaveScheme { get; set; }

        [Column("fkLeaveTypeID")]
        public LeaveType LeaveType { get; set; }

        [Required]
        [Column(TypeName = "varchar(100)")] // We are setting the type here to improve SQL performance, EF makes the parameter for this colum nvarchar and not varchar.
        public string LeaveDescription { get; set; }

        public int OrderNumber { get; set; }

        public virtual ICollection<CompanyLeaveDetail> LeaveDetails { get; set; }

        [NotMapped]
        public long CompanyId { get; set; }

        public override string ToString()
        {
            return this.LeaveDescription;
        }
    }
}