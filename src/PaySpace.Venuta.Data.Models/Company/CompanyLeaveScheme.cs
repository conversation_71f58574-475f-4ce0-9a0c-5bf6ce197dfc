namespace PaySpace.Venuta.Data.Models.Company
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Infrastructure;

    [DisplayName(SystemAreas.CompanyLeaveScheme.Area)]
    [Table("CompanyLeaveSchemes")]
    public class CompanyLeaveScheme : ICompanyAuditEntity
    {
        [Key]
        [Column("pkCompanySchemeID")]
        public long CompanyLeaveSchemeId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "SchemeName")]
        public string SchemeName { get; set; }

        [StringLength(50)]
        [Display(Name = "SchemeCode")]
        public string SchemeCode { get; set; }

        [DataType(DataType.Date)]
        [Display(Name = "InactiveDate")]
        public DateTime? InactiveDate { get; set; }

        public virtual ICollection<CompanyLeaveSetup> CompanyLeaveSetups { get; set; }

        public override string ToString()
        {
            return this.SchemeName;
        }
    }
}