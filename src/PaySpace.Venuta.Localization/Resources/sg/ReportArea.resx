<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name=" lblLeg_Singapore_CPF_Adj_ListingDesc" xml:space="preserve">
    <value>This report is run on an annual basis to identify CPF contribution variances.</value>
  </data>
  <data name=" lblLeg_Singapore_CPF_Adj_ListingName" xml:space="preserve">
    <value>CPF Adjustment Listing</value>
  </data>
  <data name="btnNo" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="btnYes" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="DownloadReady" xml:space="preserve">
    <value>Download ready.</value>
  </data>
  <data name="ExtractFile" xml:space="preserve">
    <value>Extract File</value>
  </data>
  <data name="lbl_DisabilityDesc" xml:space="preserve">
    <value>Provides a listing of the employees Disability contributions for a chosen period.</value>
  </data>
  <data name="lbl_DisabilityName" xml:space="preserve">
    <value>Disability Report</value>
  </data>
  <data name="lbl_GroupLifeDesc" xml:space="preserve">
    <value>Provides a listing of the employees Group Life contributions for a chosen period.</value>
  </data>
  <data name="lbl_GroupLifeName" xml:space="preserve">
    <value>Group Life Report</value>
  </data>
  <data name="lbl_IncomeProtectionDesc" xml:space="preserve">
    <value>Provides a listing of the employees Income Protection contributions for a chosen period.</value>
  </data>
  <data name="lbl_IncomeProtectionName" xml:space="preserve">
    <value>Income Protection Report</value>
  </data>
  <data name="lbl_PensionDesc" xml:space="preserve">
    <value>Provides a listing of the employees Pension/Provident contributions for a chosen period.</value>
  </data>
  <data name="lbl_PensionName" xml:space="preserve">
    <value>Pension &amp; Provident  Report</value>
  </data>
  <data name="lbl_TradeUnionDesc" xml:space="preserve">
    <value>Provides a listing of the employee's Trade Union contributions for a chosen period or run.</value>
  </data>
  <data name="lbl_TradeUnionName" xml:space="preserve">
    <value>Trade Union Report</value>
  </data>
  <data name="lblAccountNumber" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lblAccrual" xml:space="preserve">
    <value>Accrual</value>
  </data>
  <data name="lblActualPeriodOrApprovedMonth" xml:space="preserve">
    <value>Render report by</value>
  </data>
  <data name="lblActualPeriodSelect" xml:space="preserve">
    <value>Actual period taken</value>
  </data>
  <data name="lblAdditional" xml:space="preserve">
    <value>Additional</value>
  </data>
  <data name="lblAgencyReport" xml:space="preserve">
    <value>Agency level</value>
  </data>
  <data name="lblAgencyTaxCountryReport" xml:space="preserve">
    <value>Agency tax country level</value>
  </data>
  <data name="lblAllActions" xml:space="preserve">
    <value>All Actions</value>
  </data>
  <data name="lblAllCompanies" xml:space="preserve">
    <value>All Companies</value>
  </data>
  <data name="lblAllComponents" xml:space="preserve">
    <value>All Components</value>
  </data>
  <data name="lblAllCurrencies" xml:space="preserve">
    <value>All Currencies</value>
  </data>
  <data name="lblAllEmployees" xml:space="preserve">
    <value>All Employees</value>
  </data>
  <data name="lblAllFrequencies" xml:space="preserve">
    <value>All Frequencies</value>
  </data>
  <data name="lblAllLeaveTypes" xml:space="preserve">
    <value>All Leave Types</value>
  </data>
  <data name="lblAllLevels" xml:space="preserve">
    <value>All Levels</value>
  </data>
  <data name="lblAllPayslipActions" xml:space="preserve">
    <value>All Payslip Actions</value>
  </data>
  <data name="lblAllPositions" xml:space="preserve">
    <value>All Positions</value>
  </data>
  <data name="lblAllRegions" xml:space="preserve">
    <value>All Regions</value>
  </data>
  <data name="lblAllReportTabName" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="lblAllUnits" xml:space="preserve">
    <value>All Units</value>
  </data>
  <data name="lblAltLang" xml:space="preserve">
    <value>Alternative Component Names</value>
  </data>
  <data name="lblAltLanguageParamNameKey" xml:space="preserve">
    <value>View the component names in an alternative language</value>
  </data>
  <data name="lblAmount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="lblAngola_Simplificado_IRTDesc" xml:space="preserve">
    <value>Simplified Monthly electronic tax report. Download the file in Excel for import onto the online platform.</value>
  </data>
  <data name="lblAngola_Simplificado_IRTName" xml:space="preserve">
    <value>Simplified IRT - Compensation Map</value>
  </data>
  <data name="lblApplicableEntities" xml:space="preserve">
    <value>Saved to the following</value>
  </data>
  <data name="lblApprovedMonthSelect" xml:space="preserve">
    <value>Approved month</value>
  </data>
  <data name="lblArchiveIDParamNameKey" xml:space="preserve">
    <value>Archived budget</value>
  </data>
  <data name="lblArchiveParamNameKey" xml:space="preserve">
    <value>Archived budget</value>
  </data>
  <data name="lblAttachmentClassificationDesc" xml:space="preserve">
    <value>Indicates what type of attachments have been loaded for each employee</value>
  </data>
  <data name="lblAttachmentClassificationName" xml:space="preserve">
    <value>Attachment Classification</value>
  </data>
  <data name="lblBalance" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="lblBalancesDesc" xml:space="preserve">
    <value>Provides a listing of all employees' leave balances for a chosen period.</value>
  </data>
  <data name="lblBalancesName" xml:space="preserve">
    <value>Leave Balances</value>
  </data>
  <data name="lblBalancingToolFormatSelect" xml:space="preserve">
    <value>Balancing tool format</value>
  </data>
  <data name="lblBankName" xml:space="preserve">
    <value>Bank Name</value>
  </data>
  <data name="lblBirthDate" xml:space="preserve">
    <value>Birth Date</value>
  </data>
  <data name="lblBRA_NH54_1_NetPayDesc" xml:space="preserve">
    <value>Net Salary Detail Report</value>
  </data>
  <data name="lblBRA_NH54_1_NetPayName" xml:space="preserve">
    <value>Net Salary Detail Report</value>
  </data>
  <data name="lblBranchCode" xml:space="preserve">
    <value>Branch Code</value>
  </data>
  <data name="lblBrazil_Component_Variance_ReportDesc" xml:space="preserve">
    <value>Provides components per employee and per payroll process for the selected month.</value>
  </data>
  <data name="lblBrazil_Component_Variance_ReportName" xml:space="preserve">
    <value>Employee Payroll Report</value>
  </data>
  <data name="lblBrazil_NH90_Payroll_Component_SubCodeDesc" xml:space="preserve">
    <value>This report displays payroll components with their respective sub codes for INSS, FGTS, IRRF and Income Bases</value>
  </data>
  <data name="lblBrazil_NH90_Payroll_Component_SubCodeName" xml:space="preserve">
    <value>Component with Sub Codes Report</value>
  </data>
  <data name="lblBrazil_Payroll_ReconDesc" xml:space="preserve">
    <value>Provides monthly figures of all employee components on the payslip per process.</value>
  </data>
  <data name="lblBrazil_Payroll_ReconName" xml:space="preserve">
    <value>Payroll Reconciliation - per process</value>
  </data>
  <data name="lblBudgetReportDesc" xml:space="preserve">
    <value>View a single budget period at a time. (snapshot, archive or actuals)  No comparisons are done in this report</value>
  </data>
  <data name="lblBudgetReportName" xml:space="preserve">
    <value>Budget Report</value>
  </data>
  <data name="lblBureauReport" xml:space="preserve">
    <value>Bureau level</value>
  </data>
  <data name="lblBureauTaxCountryReport" xml:space="preserve">
    <value>Bureau tax country level</value>
  </data>
  <data name="lblBureauTaxId" xml:space="preserve">
    <value>Tax Year</value>
  </data>
  <data name="lblBurkinaFasoDetailedFSPDesc" xml:space="preserve">
    <value>A detailed report that indicates the contribution base as well as the contribution due for the Patriotic support fund (FSP).</value>
  </data>
  <data name="lblBurkinaFasoDetailedFSPName" xml:space="preserve">
    <value>Annexed statement of declaration of compulsory deduction from the salaries of public employees and private sector workers</value>
  </data>
  <data name="lblBurkinaFasoSummaryFSPDesc" xml:space="preserve">
    <value>A summary report of the month’s FSP contribution.</value>
  </data>
  <data name="lblBurkinaFasoSummaryFSPName" xml:space="preserve">
    <value>Mandatory declaration of deduction from the salaries of public servants and private sector workers</value>
  </data>
  <data name="lblBusinessPartner" xml:space="preserve">
    <value>Business Partner</value>
  </data>
  <data name="lblBusinessPartners" xml:space="preserve">
    <value>Business Partners</value>
  </data>
  <data name="lblCantOverwriteDefault" xml:space="preserve">
    <value>Can't overwrite Default report level</value>
  </data>
  <data name="lblChangeDescriptionRequired" xml:space="preserve">
    <value>Description of changes made</value>
  </data>
  <data name="lblCollapseAllBtn" xml:space="preserve">
    <value>Collapse</value>
  </data>
  <data name="lblCollectionIDFundParamKey" xml:space="preserve">
    <value>data category</value>
  </data>
  <data name="lblCompanies" xml:space="preserve">
    <value>Companies</value>
  </data>
  <data name="lblCompany" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="lblCompanyCarReportDesc" xml:space="preserve">
    <value>Provides a listing of Company Car figures per month or per run for an employee or any organisational unit/s or for all units in a particular level.</value>
  </data>
  <data name="lblCompanyCarReportName" xml:space="preserve">
    <value>Company Car</value>
  </data>
  <data name="lblCompanyContributions" xml:space="preserve">
    <value>Company contributions</value>
  </data>
  <data name="lblCompanyEffectiveDate" xml:space="preserve">
    <value>Company Effective Date</value>
  </data>
  <data name="lblCompanyFrequencyName" xml:space="preserve">
    <value>Company - Frequency :</value>
  </data>
  <data name="lblCompanyGroup" xml:space="preserve">
    <value>Company Group</value>
  </data>
  <data name="lblCompanyGroupIDParamNameKey" xml:space="preserve">
    <value>Company GroupID</value>
  </data>
  <data name="lblCompanyGroupReport" xml:space="preserve">
    <value>Company group level</value>
  </data>
  <data name="lblCompanyGroups" xml:space="preserve">
    <value>Company Groups</value>
  </data>
  <data name="lblCompanyLeaveDetailsIDParamNameKey" xml:space="preserve">
    <value>Leave scheme</value>
  </data>
  <data name="lblCompanyName" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="lblCompanyRegNo" xml:space="preserve">
    <value>Company Reg. No</value>
  </data>
  <data name="lblCompanyReport" xml:space="preserve">
    <value>Company level</value>
  </data>
  <data name="lblCompanyRequired" xml:space="preserve">
    <value>A company is required</value>
  </data>
  <data name="lblComponent" xml:space="preserve">
    <value>Component</value>
  </data>
  <data name="lblComponentCode" xml:space="preserve">
    <value>Component</value>
  </data>
  <data name="lblComponentCodes" xml:space="preserve">
    <value>Component List</value>
  </data>
  <data name="lblComponentCodesParamNameKey" xml:space="preserve">
    <value>Component list</value>
  </data>
  <data name="lblComponentIDParamNameKey" xml:space="preserve">
    <value>Components</value>
  </data>
  <data name="lblComponentPostedUnitsDesc" xml:space="preserve">
    <value>Similiar to the component summary report however this report provides specific details regarding posted units/hours/days for each component</value>
  </data>
  <data name="lblComponentPostedUnitsName" xml:space="preserve">
    <value>Component Posted Units</value>
  </data>
  <data name="lblComponentReportDesc" xml:space="preserve">
    <value>This report provides a list of component figures per month or per run for an employee or any organisational unit/s or for all units in a particular level.</value>
  </data>
  <data name="lblComponentReportName" xml:space="preserve">
    <value>Component</value>
  </data>
  <data name="lblComponentTotalsParamNameKey" xml:space="preserve">
    <value>Show totals per component</value>
  </data>
  <data name="lblComponentVarianceReport-GoingAcrossDesc" xml:space="preserve">
    <value>Provides a comparative, month to month or run to run vertical listing of all component figures for a selected period per employee.</value>
  </data>
  <data name="lblComponentVarianceReport-GoingAcrossName" xml:space="preserve">
    <value>Component Variance - Going Across</value>
  </data>
  <data name="lblComponentVarianceReportDesc" xml:space="preserve">
    <value>Provides a comparative, month to month or run to run side by side listing of all component figures for a selected period per employee and includes a difference column.</value>
  </data>
  <data name="lblComponentVarianceReportName" xml:space="preserve">
    <value>Component Variance</value>
  </data>
  <data name="lblComponentVarianceTotalsReportDesc" xml:space="preserve">
    <value>Provides a comparative, month to month or run to run, side by side listing of all component figures for a selected period.</value>
  </data>
  <data name="lblComponentVarianceTotalsReportName" xml:space="preserve">
    <value>Component Variance Totals</value>
  </data>
  <data name="lblConfirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="lblConfirmReportOverride" xml:space="preserve">
    <value>Override report</value>
  </data>
  <data name="lblConfirmReportOverrideMessage" xml:space="preserve">
    <value>A report with the same file name already exists, do you want replace it?</value>
  </data>
  <data name="lblConsolidatedDynamicEmployeeDetailsDesc" xml:space="preserve">
    <value>Provides a list of various pre-defined employee fields that can be selected for all companies within the group.</value>
  </data>
  <data name="lblConsolidatedDynamicEmployeeDetailsName" xml:space="preserve">
    <value>Consolidated Dynamic Employee Details</value>
  </data>
  <data name="lblConsolidatedPayrollReconciliationReportDesc" xml:space="preserve">
    <value>Provides MTD figures of all employee components for selected companies within a group.</value>
  </data>
  <data name="lblConsolidatedPayrollReconciliationReportName" xml:space="preserve">
    <value>Consolidated Payroll Reconciliation</value>
  </data>
  <data name="lblConsolidatedPayrollReconciliationReportNameBra" xml:space="preserve">
    <value>Consolidated Payroll Reconciliation per Regions/Projects Report</value>
  </data>
  <data name="lblCostCentre" xml:space="preserve">
    <value>Cost Centre</value>
  </data>
  <data name="lblCostCentreDescription" xml:space="preserve">
    <value>Cost Centre Description</value>
  </data>
  <data name="lblCostingPayrollReconcilliationReportDesc" xml:space="preserve">
    <value>Provides a payroll reconcilliation report with a breakdown of each cost centre per employee.</value>
  </data>
  <data name="lblCostingPayrollReconcilliationReportName" xml:space="preserve">
    <value>Costing Payroll Reconciliation</value>
  </data>
  <data name="lblCountries" xml:space="preserve">
    <value>Countries</value>
  </data>
  <data name="lblCreated" xml:space="preserve">
    <value>Created</value>
  </data>
  <data name="lblCsvExportQuoteStringsWithSeparators" xml:space="preserve">
    <value>Quote Strings With Separators?</value>
  </data>
  <data name="lblCsvExportSeparator" xml:space="preserve">
    <value>Separator</value>
  </data>
  <data name="lblCsvExportSkipEmptyColumns" xml:space="preserve">
    <value>Skip Empty Columns</value>
  </data>
  <data name="lblCsvExportSkipEmptyRows" xml:space="preserve">
    <value>Skip Empty Rows</value>
  </data>
  <data name="lblCurrency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="lblCurrencyIDParamNameKey" xml:space="preserve">
    <value>Extract bank files setup in specific currencies</value>
  </data>
  <data name="lblCurrent" xml:space="preserve">
    <value>Current</value>
  </data>
  <data name="lblCurrentOrMtdOrYtd" xml:space="preserve">
    <value>Run/Month</value>
  </data>
  <data name="lblCurrentReport" xml:space="preserve">
    <value>Current report</value>
  </data>
  <data name="lblCustomFormsReportDesc" xml:space="preserve">
    <value>Retrieve historical dynamic employee details as of a specific date for specific data category.</value>
  </data>
  <data name="lblCustomFormsReportName" xml:space="preserve">
    <value>Custom Forms Report</value>
  </data>
  <data name="lblCustomizableReport" xml:space="preserve">
    <value>Customizable report</value>
  </data>
  <data name="lblCustomPayslips" xml:space="preserve">
    <value>Custom Payslips</value>
  </data>
  <data name="lblCustomReport" xml:space="preserve">
    <value>Custom Report</value>
  </data>
  <data name="lblCustomReportNameExists" xml:space="preserve">
    <value>Report name already exists for the specified category.</value>
  </data>
  <data name="lblCustomReportTabName" xml:space="preserve">
    <value>Custom</value>
  </data>
  <data name="lblDateComplete" xml:space="preserve">
    <value>Date Completed</value>
  </data>
  <data name="lblDateRange" xml:space="preserve">
    <value>This report cannot be run for a period of longer than 13 months.  Please adjust the date range accordingly</value>
  </data>
  <data name="lblDeductions" xml:space="preserve">
    <value>Deductions</value>
  </data>
  <data name="lblDefault" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="lblDefaultCurrencyParamNameKey" xml:space="preserve">
    <value>Extract bank files setup in specific currencies</value>
  </data>
  <data name="lblDefaultFormatSelect" xml:space="preserve">
    <value>Formatted</value>
  </data>
  <data name="lblDeleteConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete this report?</value>
  </data>
  <data name="lblDeleteReport" xml:space="preserve">
    <value>Delete report</value>
  </data>
  <data name="lblDeletionFailed" xml:space="preserve">
    <value>An unexpected error occurred while trying to delete report, please try again later.</value>
  </data>
  <data name="lblDeletionSuccessful" xml:space="preserve">
    <value>Report has been successfully deleted.</value>
  </data>
  <data name="lblDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="lblDescriptionRequired" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="lblDisplayOrientationParamNameKey" xml:space="preserve">
    <value>Orientation</value>
  </data>
  <data name="lblDivisionParamNameKey" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="lblDoNotHaveEmailAddresses" xml:space="preserve">
    <value>Exclude emps with email addresses</value>
  </data>
  <data name="lblDontGroupByTotals" xml:space="preserve">
    <value>No Totals</value>
  </data>
  <data name="lblDT0107aDesc" xml:space="preserve">
    <value>This is the employer's monthly tax deduction schedule for PAYE</value>
  </data>
  <data name="lblDT0107aName" xml:space="preserve">
    <value>DT0107a</value>
  </data>
  <data name="lblDynamicEmployeeDetailsDesc" xml:space="preserve">
    <value>Provides a list of various pre-defined employee fields that can be selected by a user.</value>
  </data>
  <data name="lblDynamicEmployeeDetailsName" xml:space="preserve">
    <value>Dynamic Employee Details</value>
  </data>
  <data name="lblEditableReport" xml:space="preserve">
    <value>Editable</value>
  </data>
  <data name="lblEditReport" xml:space="preserve">
    <value>Edit report</value>
  </data>
  <data name="lblEffectiveDate" xml:space="preserve">
    <value>Effective Date</value>
  </data>
  <data name="lblEffectiveDateParamKey" xml:space="preserve">
    <value>Effective date</value>
  </data>
  <data name="lbLeg_Canada_Advice_of_DebitName" xml:space="preserve">
    <value>Advice of Debit (Run)</value>
  </data>
  <data name="lblEmailReport" xml:space="preserve">
    <value>Email Report?</value>
  </data>
  <data name="lblEMP201BreakDownDesc" xml:space="preserve">
    <value>Provides a listing of employee figures that make up the EMP201 report figures.</value>
  </data>
  <data name="lblEMP201BreakDownName" xml:space="preserve">
    <value>EMP201 Breakdown</value>
  </data>
  <data name="lblEMP201ViewDesc" xml:space="preserve">
    <value>Provides EMP201 report for the company on a monthly basis.</value>
  </data>
  <data name="lblEMP201ViewName" xml:space="preserve">
    <value>EMP201</value>
  </data>
  <data name="lblEmplNo" xml:space="preserve">
    <value>EmplNo</value>
  </data>
  <data name="lblEmplNumber" xml:space="preserve">
    <value>Empl. Number</value>
  </data>
  <data name="lblEmployeeActionTypeHistoryDesc" xml:space="preserve">
    <value>Provides a list of action types for employees between a selected date range.</value>
  </data>
  <data name="lblEmployeeActionTypeHistoryName" xml:space="preserve">
    <value>Action Type History</value>
  </data>
  <data name="lblEmployeeDependantsListingDesc" xml:space="preserve">
    <value>Provides a list of dependants with their details.</value>
  </data>
  <data name="lblEmployeeDependantsListingName" xml:space="preserve">
    <value>Dependants Listing</value>
  </data>
  <data name="lblEmployeeFinancialHousePaymentsDesc" xml:space="preserve">
    <value>Provides a break down of the Financial House Payments component.</value>
  </data>
  <data name="lblEmployeeFinancialHousePaymentsName" xml:space="preserve">
    <value>Employee Financial House Payments</value>
  </data>
  <data name="lblEmployeeListingDesc" xml:space="preserve">
    <value>Provides a listing of the employees.</value>
  </data>
  <data name="lblEmployeeListingName" xml:space="preserve">
    <value>Employee Listing</value>
  </data>
  <data name="lblEmployeeNumber" xml:space="preserve">
    <value>Employee Number</value>
  </data>
  <data name="lblEmployeePayslipDesc" xml:space="preserve">
    <value>Provides a listing of the employees payslips for a chosen run.</value>
  </data>
  <data name="lblEmployeePayslipName" xml:space="preserve">
    <value>Payslips</value>
  </data>
  <data name="lblEmployees" xml:space="preserve">
    <value>Employees</value>
  </data>
  <data name="lblEmployeeSuspensionDesc" xml:space="preserve">
    <value>Provides a listing of all suspended employees as of a specified date.</value>
  </data>
  <data name="lblEmployeeSuspensionName" xml:space="preserve">
    <value>Employee Suspension</value>
  </data>
  <data name="lblEmpNoParamNameKey" xml:space="preserve">
    <value>Employee number</value>
  </data>
  <data name="lblEmpStatus" xml:space="preserve">
    <value>Employee Status</value>
  </data>
  <data name="lblEndDateParamNameKey" xml:space="preserve">
    <value>End date</value>
  </data>
  <data name="lblEngagementDate" xml:space="preserve">
    <value>Engagement Date</value>
  </data>
  <data name="lblETIUtilisedParamNameKey" xml:space="preserve">
    <value>ETI Utilised Amount</value>
  </data>
  <data name="lblExclDefSortParamNameKey" xml:space="preserve">
    <value>Exclude org. unit, pay point &amp; project sorting (only sort on the option selected above)</value>
  </data>
  <data name="lblExclOrgUnitGroupingParamNameKey" xml:space="preserve">
    <value>Exclude organisational unit grouping</value>
  </data>
  <data name="lblExcludeEESParamNameKey" xml:space="preserve">
    <value>Only include employees that do not have email addresses</value>
  </data>
  <data name="lblExcludePaid" xml:space="preserve">
    <value>Exclude Paid Payslips</value>
  </data>
  <data name="lblExecuteReport" xml:space="preserve">
    <value>Execute report</value>
  </data>
  <data name="lblExpandAllBtn" xml:space="preserve">
    <value>Expand</value>
  </data>
  <data name="lblFavouriteReportTabName" xml:space="preserve">
    <value>Favourites</value>
  </data>
  <data name="lblFilledCountTypeParamNameKey" xml:space="preserve">
    <value>Filled count as</value>
  </data>
  <data name="lblFilledCurrentCount" xml:space="preserve">
    <value>Fill current</value>
  </data>
  <data name="lblFilledEndOfPeriodCount" xml:space="preserve">
    <value>Fill end of period</value>
  </data>
  <data name="lblFilters" xml:space="preserve">
    <value>Apply filters</value>
  </data>
  <data name="lblFirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="lblfkFilterCurrencyIDParamNameKey" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="lblfkForeingCurrencyIDParamNameKey" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="lblfkReportIDParamNameKey" xml:space="preserve">
    <value>View only employees that are attached to this payslip</value>
  </data>
  <data name="lblForeingCurrencyParamNameKey" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="lblFormatPerLine" xml:space="preserve">
    <value>Per line</value>
  </data>
  <data name="lblFormatPivotHorizontally" xml:space="preserve">
    <value>Pivot horizontally</value>
  </data>
  <data name="lblFormatSelection" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="lblFormatSelectionNotSupported" xml:space="preserve">
    <value>This option is not supported</value>
  </data>
  <data name="lblFreqeuncyParamKey" xml:space="preserve">
    <value>Company - frequency list</value>
  </data>
  <data name="lblFrequency" xml:space="preserve">
    <value>Frequency</value>
  </data>
  <data name="lblFrequencyId" xml:space="preserve">
    <value>Frequency</value>
  </data>
  <data name="lblFrequencyIdConditionalParamNameKey" xml:space="preserve">
    <value>Run report for current frequency only</value>
  </data>
  <data name="lblFrequencyIds" xml:space="preserve">
    <value>Frequency</value>
  </data>
  <data name="lblFringeBenefits" xml:space="preserve">
    <value>Fringe benefits</value>
  </data>
  <data name="lblFromDateNameKey" xml:space="preserve">
    <value>Include terminations from this date onwards</value>
  </data>
  <data name="lblGarnisheeReportDesc" xml:space="preserve">
    <value>Provides a listing of the employees Garnishee orders for a chosen period.</value>
  </data>
  <data name="lblGarnisheeReportName" xml:space="preserve">
    <value>Garnishee</value>
  </data>
  <data name="lblGeneralLedgerReportDesc" xml:space="preserve">
    <value>Provides a GL listing of the entries for a chosen run or period.</value>
  </data>
  <data name="lblGeneralLedgerReportName" xml:space="preserve">
    <value>General Ledger Report</value>
  </data>
  <data name="lblGenerationCompleted" xml:space="preserve">
    <value>Report Generated</value>
  </data>
  <data name="lblGenerationFailed" xml:space="preserve">
    <value>An unexpected error occurred while generating your report, please try again later.</value>
  </data>
  <data name="lblGenericTaxCertificateDesc" xml:space="preserve">
    <value>Generate YTD payroll information for a specific tax year. Please ensure that you have configured the parameters by visiting the Tax Certificate Configuration screen (Config &gt; Basic Settings &gt; Legislative Configurations &gt; Tax Certificate Configuration).</value>
  </data>
  <data name="lblGenericTaxCertificateName" xml:space="preserve">
    <value>Generic Tax Certificate</value>
  </data>
  <data name="lblGhana_DT0107_Monthly_PAYEDesc" xml:space="preserve">
    <value>Monthly P.A.Y.E Deductions Return.</value>
  </data>
  <data name="lblGhana_DT0107_Monthly_PAYEName" xml:space="preserve">
    <value>DT0107</value>
  </data>
  <data name="lblGhana_SSNIT_Contribution_Tier1Desc" xml:space="preserve">
    <value>This report indicates the value of the monthly contributions made towards the SSNIT Tier 1 scheme. The report should be extracted in an Excel (xlsx format).</value>
  </data>
  <data name="lblGhana_SSNIT_Contribution_Tier1Name" xml:space="preserve">
    <value>SSNIT Contribution Report Tier 1</value>
  </data>
  <data name="lblGhana_SSNIT_Contribution_Tier2Desc" xml:space="preserve">
    <value>This is a monthly declaration that can be used to reconcile reported amounts for the SSNIT Tier 2 scheme, and can be supplied to the relevant trustees. The report should be extracted in an Excel (xlsx format).</value>
  </data>
  <data name="lblGhana_SSNIT_Contribution_Tier2Name" xml:space="preserve">
    <value>SSNIT Contribution Report Tier 2</value>
  </data>
  <data name="lblGoToDesigner" xml:space="preserve">
    <value>Designer</value>
  </data>
  <data name="lblGoToViewer" xml:space="preserve">
    <value>Viewer</value>
  </data>
  <data name="lblGrade" xml:space="preserve">
    <value>Grade</value>
  </data>
  <data name="lblGrandTotalsfor" xml:space="preserve">
    <value>Grand Totals for</value>
  </data>
  <data name="lblGroupAllByOrgUnit" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="lblGroupByComponent" xml:space="preserve">
    <value>Component</value>
  </data>
  <data name="lblGroupByGLNumber" xml:space="preserve">
    <value>Gl Number</value>
  </data>
  <data name="lblHeadcountComparisonDesc" xml:space="preserve">
    <value>To plan for future budget period by comparing the previous approved budget  (archived) vs. the current as is vs. the planned/forecasted  changes  (new period)</value>
  </data>
  <data name="lblHeadcountComparisonName" xml:space="preserve">
    <value>Headcount Comparison</value>
  </data>
  <data name="lblHideDetailParamNameKey" xml:space="preserve">
    <value>Hide detail</value>
  </data>
  <data name="lblhideRateParamNameKey" xml:space="preserve">
    <value>Hide rater</value>
  </data>
  <data name="lblHistory" xml:space="preserve">
    <value>History</value>
  </data>
  <data name="lblHistoryParamNameKey" xml:space="preserve">
    <value>Include history</value>
  </data>
  <data name="lblHourlyRate" xml:space="preserve">
    <value>Hourly Rate</value>
  </data>
  <data name="lblIDNumber" xml:space="preserve">
    <value>ID Number</value>
  </data>
  <data name="lblImage" xml:space="preserve">
    <value>image</value>
  </data>
  <data name="lblIncludeEngTerm_InThisPeriodParamNameKey" xml:space="preserve">
    <value>Include new engagements and terminations captured in this period</value>
  </data>
  <data name="lblIncludeTerminatedParamKey" xml:space="preserve">
    <value>Include terminations</value>
  </data>
  <data name="lblIncludeZeroNetPayParamNameKey" xml:space="preserve">
    <value>Include zero net pay payslips</value>
  </data>
  <data name="lblIncome" xml:space="preserve">
    <value>Income</value>
  </data>
  <data name="lblIndia_GPDesc" xml:space="preserve">
    <value>This report provides details on employee and employer monthly contributions towards ESIC (Employee's State Insurance Corporation) as applicable.</value>
  </data>
  <data name="lblIndia_GPName" xml:space="preserve">
    <value>ESI Monthly Statement</value>
  </data>
  <data name="lblIndia_Hold_Release_ReportDesc" xml:space="preserve">
    <value>This report provides data of employee(s) for whom salary has been put on hold for reasons such as absconding, serving notice period or vital information missing for salary release etc.</value>
  </data>
  <data name="lblIndia_Hold_Release_ReportName" xml:space="preserve">
    <value>Hold-Release Salary Report</value>
  </data>
  <data name="lblIndia_Income_Tax_StatementDesc" xml:space="preserve">
    <value>This report provides a comprehensive overview of the income tax applicable to employees for the financial year.</value>
  </data>
  <data name="lblIndia_Income_Tax_StatementName" xml:space="preserve">
    <value>Income Tax Statement</value>
  </data>
  <data name="lblIndia_IT_Savings_Other_Report_Monthly_RentDesc" xml:space="preserve">
    <value>Provides the monthly rent paid by employees during the tax year.</value>
  </data>
  <data name="lblIndia_IT_Savings_Other_Report_Monthly_RentName" xml:space="preserve">
    <value>IT Savings Others Report - Monthly Rent</value>
  </data>
  <data name="lblIndia_IT_Savings_Others_Report_Other_IncomeDesc" xml:space="preserve">
    <value>Provides the details of income received by employee due to Other Income as updated in IT declaration and income from house property (whether profit/ loss) is incurred to an employee.</value>
  </data>
  <data name="lblIndia_IT_Savings_Others_Report_Other_IncomeName" xml:space="preserve">
    <value>IT Savings Others Report - Other Income</value>
  </data>
  <data name="lblIndia_IT_Savings_Report_For_Previous_EmploymentDesc" xml:space="preserve">
    <value>This report is used to capture the previous employment income details if recorded for an employee for any selected financial year</value>
  </data>
  <data name="lblIndia_IT_Savings_Report_For_Previous_EmploymentName" xml:space="preserve">
    <value>IT Savings Report For Previous Employment</value>
  </data>
  <data name="lblIndia_IT_Savings_ReportsDesc" xml:space="preserve">
    <value>This report provides information on actual IT declaration applicable for employee(s).</value>
  </data>
  <data name="lblIndia_IT_Savings_ReportsName" xml:space="preserve">
    <value>Income Tax Savings Report</value>
  </data>
  <data name="lblIndia_IT_SavingsDesc" xml:space="preserve">
    <value>Provides the annual rent paid by employees during the tax year.</value>
  </data>
  <data name="lblIndia_IT_SavingsName" xml:space="preserve">
    <value>IT Savings Others Report - Section 24 Items</value>
  </data>
  <data name="lblIndia_Payroll_Bank_Transfer_AdviceDesc" xml:space="preserve">
    <value>This report provides information on the total amount employer extended towards a bank and the salary paid to employee(s) who hold their account in the bank</value>
  </data>
  <data name="lblIndia_Payroll_Bank_Transfer_AdviceName" xml:space="preserve">
    <value>Bank Transfer Advice</value>
  </data>
  <data name="lblIndia_Payroll_Component_VarianceDesc" xml:space="preserve">
    <value>Provides a comparative, month to month or run to run side by side listing of all component figures for a selected period per employee.</value>
  </data>
  <data name="lblIndia_Payroll_Component_VarianceName" xml:space="preserve">
    <value>Payroll Variance</value>
  </data>
  <data name="lblIndia_Payroll_ReconDesc" xml:space="preserve">
    <value>Provides current figures or MTD figures of all components on the payslips for active employees.</value>
  </data>
  <data name="lblIndia_Payroll_ReconName" xml:space="preserve">
    <value>Payroll Reconciliation - India</value>
  </data>
  <data name="lblIsTotalsParamNameKey" xml:space="preserve">
    <value>Run report</value>
  </data>
  <data name="lblIsTotalsPerEmp" xml:space="preserve">
    <value>Totals Per Employee</value>
  </data>
  <data name="lblIsTotalsPerOrgUnit" xml:space="preserve">
    <value>Totals Per Organization Unit</value>
  </data>
  <data name="lblLastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="lblLeave" xml:space="preserve">
    <value>Leave</value>
  </data>
  <data name="lblLeaveTypeIDParamNameKey" xml:space="preserve">
    <value>Leave type</value>
  </data>
  <data name="lblLeaveTypeParamNameKey" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="lblLeg_Angola_IRTDesc" xml:space="preserve">
    <value>Schedule of taxes to accompany Declaracao Anual Modelo 2 (Grupo A). The file must be exported to Excel for import into the tax authority employer portal.</value>
  </data>
  <data name="lblLeg_Angola_IRTName" xml:space="preserve">
    <value>Annual IRT - Modelo 2 (Group A)</value>
  </data>
  <data name="lblLeg_Benin_Monthly_IRPP_TSDesc" xml:space="preserve">
    <value>A summary report reflecting the monthly ITS value paid for a month.</value>
  </data>
  <data name="lblLeg_Benin_Monthly_IRPP_TSName" xml:space="preserve">
    <value>Declaration IRPP - TS</value>
  </data>
  <data name="lblLeg_Benin_Monthly_VPSDesc" xml:space="preserve">
    <value>A summary statement of VPS contributions.</value>
  </data>
  <data name="lblLeg_Benin_Monthly_VPSName" xml:space="preserve">
    <value>Monthly Declaration Form for VPS</value>
  </data>
  <data name="lblLeg_Botswana_ITW10Desc" xml:space="preserve">
    <value>The ITW10 is an annual PAYE return. The ITW10 includes the detail of the employer and the totals of tax deducted.</value>
  </data>
  <data name="lblLeg_Botswana_ITW10Name" xml:space="preserve">
    <value>ITW10 - Annual PAYE return</value>
  </data>
  <data name="lblLeg_Botswana_ITW7A_Summary_ReportDesc" xml:space="preserve">
    <value>Monthly PAYE Remittance Return - a summary of the PAYE deducted in a specific period.</value>
  </data>
  <data name="lblLeg_Botswana_ITW7A_Summary_ReportName" xml:space="preserve">
    <value>ITW7A Summary Report</value>
  </data>
  <data name="lblLeg_Botswana_ITW7ADesc" xml:space="preserve">
    <value>The monthly PAYE remittance return. This is the monthly BURS electronic submission file in .csv format for the selected month.</value>
  </data>
  <data name="lblLeg_Botswana_ITW7AName" xml:space="preserve">
    <value>ITW7A electronic submission file (valid from Jan 2024)</value>
  </data>
  <data name="lblLeg_BRA_Analytical_13th_Salary_ProvisionDesc" xml:space="preserve">
    <value>13th Salary Provision Summary Report</value>
  </data>
  <data name="lblLeg_BRA_Analytical_13th_Salary_ProvisionName" xml:space="preserve">
    <value>13th Salary Provision Summary Report</value>
  </data>
  <data name="lblLeg_BRA_NH18_Leave_Pre_WarningDesc" xml:space="preserve">
    <value>Employee Vacation Request Form</value>
  </data>
  <data name="lblLeg_BRA_NH18_Leave_Pre_WarningName" xml:space="preserve">
    <value>Vacation Request Form</value>
  </data>
  <data name="lblLeg_BRA_NH19_Vacation_ScheduleDesc" xml:space="preserve">
    <value>Vacation Schedule Report</value>
  </data>
  <data name="lblLeg_BRA_NH19_Vacation_ScheduleName" xml:space="preserve">
    <value>Vacation Schedule</value>
  </data>
  <data name="lblLeg_BRA_NH20_Vacation_PayslipDesc" xml:space="preserve">
    <value>Employee Vacation Notice and Receipt</value>
  </data>
  <data name="lblLeg_BRA_NH20_Vacation_PayslipName" xml:space="preserve">
    <value>Vacation Notice and Receipt</value>
  </data>
  <data name="lblLeg_BRA_NH21_Unpaid_LeaveDesc" xml:space="preserve">
    <value>Unpaid Value &amp; Days for Leave Vesting Period</value>
  </data>
  <data name="lblLeg_BRA_NH21_Unpaid_LeaveName" xml:space="preserve">
    <value>Unpaid Value &amp; Days for Leave Vesting Period</value>
  </data>
  <data name="lblLeg_BRA_NH21Desc" xml:space="preserve">
    <value>Unpaid Value &amp; Days for Leave Vesting Period Report OLD</value>
  </data>
  <data name="lblLeg_BRA_NH21Name" xml:space="preserve">
    <value>Unpaid Value &amp; Days for Leave Vesting Period Report OLD</value>
  </data>
  <data name="lblLeg_BRA_NH50_Payroll_RegisterDesc" xml:space="preserve">
    <value>Payroll - Analytics</value>
  </data>
  <data name="lblLeg_BRA_NH50_Payroll_RegisterName" xml:space="preserve">
    <value>Payroll - Analytics</value>
  </data>
  <data name="lblLeg_BRA_NH53_Net_Salary_AlimonyDesc" xml:space="preserve">
    <value>Net Salary for Alimony Report</value>
  </data>
  <data name="lblLeg_BRA_NH53_Net_Salary_AlimonyName" xml:space="preserve">
    <value>Net Salary for Alimony Report</value>
  </data>
  <data name="lblLeg_BRA_NH55_0_Net_PayDesc" xml:space="preserve">
    <value>Employees with 0 Net Pay</value>
  </data>
  <data name="lblLeg_BRA_NH55_0_Net_PayName" xml:space="preserve">
    <value>Employees with 0 Net Pay</value>
  </data>
  <data name="lblLeg_BRA_NH62_Provision_VacationDesc" xml:space="preserve">
    <value>Statement of Provisions Payroll Analytical</value>
  </data>
  <data name="lblLeg_BRA_NH62_Provision_VacationName" xml:space="preserve">
    <value>Statement of Provisions Payroll Analytical</value>
  </data>
  <data name="lblLeg_Bra_NH622_ReportDesc" xml:space="preserve">
    <value>The report displays, per location, establishment and company the amounts allocated to the Vacation provision for the selected period.</value>
  </data>
  <data name="lblLeg_Bra_NH622_ReportName" xml:space="preserve">
    <value>Vacation Provision Summary Report</value>
  </data>
  <data name="lblLeg_BRA_NT36_1_TQRCTDesc" xml:space="preserve">
    <value>Discharge Form on Termination of Employment Contract</value>
  </data>
  <data name="lblLeg_BRA_NT36_1_TQRCTName" xml:space="preserve">
    <value>TQRCT</value>
  </data>
  <data name="lblLeg_BRA_NT36_3_Auxilary_Termination_PayslipDesc" xml:space="preserve">
    <value>Termination Auxiliary Report</value>
  </data>
  <data name="lblLeg_BRA_NT36_3_Auxilary_Termination_PayslipName" xml:space="preserve">
    <value>Termination Auxiliary Report</value>
  </data>
  <data name="lblLeg_BRA_NT58_DARFDesc" xml:space="preserve">
    <value>Federal Revenue Collection Document - DARF (IRFF)</value>
  </data>
  <data name="lblLeg_BRA_NT58_DARFName" xml:space="preserve">
    <value>DARF (IRFF)</value>
  </data>
  <data name="lblLeg_BRA_NT58A_DARFDesc" xml:space="preserve">
    <value>Federal Revenue Collection Document - DARF (IRFF) by Company</value>
  </data>
  <data name="lblLeg_BRA_NT58A_DARFName" xml:space="preserve">
    <value>DARF Consolidated by Company</value>
  </data>
  <data name="lblLeg_BRA_NT60_Tax_DetailDesc" xml:space="preserve">
    <value>Income Tax Analytics</value>
  </data>
  <data name="lblLeg_BRA_NT60_Tax_DetailName" xml:space="preserve">
    <value>Income Tax Analytics</value>
  </data>
  <data name="lblLeg_BRA_NT61_Tax_SummaryDesc" xml:space="preserve">
    <value>Income Tax Summary</value>
  </data>
  <data name="lblLeg_BRA_NT61_Tax_SummaryName" xml:space="preserve">
    <value>Income Tax Summary</value>
  </data>
  <data name="lblLeg_Brazil_NH22_1_Variable_AmountDesc" xml:space="preserve">
    <value>Displays a list of components included in the amount averaging income list</value>
  </data>
  <data name="lblLeg_Brazil_NH22_1_Variable_AmountName" xml:space="preserve">
    <value>Variable Amount Income Report</value>
  </data>
  <data name="lblLeg_Brazil_NH22_BalancesDesc" xml:space="preserve">
    <value>Displays a list of components included in the unit averaging income list</value>
  </data>
  <data name="lblLeg_Brazil_NH22_BalancesName" xml:space="preserve">
    <value>Variable Units Income Report</value>
  </data>
  <data name="lblLeg_Brazil_NH62_1_VacationProvision_ReportDesc" xml:space="preserve">
    <value>Vacation Provision Analytical</value>
  </data>
  <data name="lblLeg_Brazil_NH62_1_VacationProvision_ReportName" xml:space="preserve">
    <value>Vacation Provision Analytical Report</value>
  </data>
  <data name="lblLeg_Brazil_NH62_4_13th_SalaryDesc" xml:space="preserve">
    <value>13th Salary Provision Summary Report</value>
  </data>
  <data name="lblLeg_Brazil_NH62_4_13th_SalaryName" xml:space="preserve">
    <value>13th Salary Provision Summary Report</value>
  </data>
  <data name="lblLeg_Brazil_NH7_Employee_Registration_FormDesc" xml:space="preserve">
    <value>Employee Registration Form</value>
  </data>
  <data name="lblLeg_Brazil_NH7_Employee_Registration_FormName" xml:space="preserve">
    <value>Employee Registration Form</value>
  </data>
  <data name="lblLeg_Brazil_NH91_StabilityDesc" xml:space="preserve">
    <value>Stability by Category Report</value>
  </data>
  <data name="lblLeg_Brazil_NH91_StabilityName" xml:space="preserve">
    <value>Employee Stability Report</value>
  </data>
  <data name="lblLeg_Brazil_NH92_SecurityDesc" xml:space="preserve">
    <value>Social Security Contribution Rates and Third Parties</value>
  </data>
  <data name="lblLeg_Brazil_NH92_SecurityName" xml:space="preserve">
    <value>Social Security Contribution Rates and Third Parties</value>
  </data>
  <data name="lblLeg_Brazil_NT36_2_THRCTDesc" xml:space="preserve">
    <value>Statement Form of Termination of Employment Contract</value>
  </data>
  <data name="lblLeg_Brazil_NT36_2_THRCTName" xml:space="preserve">
    <value>THRCT</value>
  </data>
  <data name="lblLeg_Brazil_NT36_Termination_PayslipDesc" xml:space="preserve">
    <value>Employment Contract Termination Form</value>
  </data>
  <data name="lblLeg_Brazil_NT36_Termination_PayslipName" xml:space="preserve">
    <value>TRCT</value>
  </data>
  <data name="lblLeg_Brazil_NT38_1_Company_Dismissing_EmpDesc" xml:space="preserve">
    <value>Company Dismissing Employee Worked</value>
  </data>
  <data name="lblLeg_Brazil_NT38_1_Company_Dismissing_EmpName" xml:space="preserve">
    <value>Company Dismissing Employee Worked</value>
  </data>
  <data name="lblLeg_Brazil_NT38Desc" xml:space="preserve">
    <value>Prior Notice Request</value>
  </data>
  <data name="lblLeg_Brazil_NT38Name" xml:space="preserve">
    <value>Prior Notice Request</value>
  </data>
  <data name="lblLeg_Burkina_Faso_Summary_DeclarationDesc" xml:space="preserve">
    <value>The summary declaration of employees report is a comprehensive Social Security report, that categorises the different types of employees and indicates the various contributions. If the company has at least 20 employees, the report and payment must be submitted quarterly, if more than 20 employees, the report must be submitted monthly.</value>
  </data>
  <data name="lblLeg_Burkina_Faso_Summary_DeclarationName" xml:space="preserve">
    <value>Summary declaration of employees</value>
  </data>
  <data name="lblLeg_Burundi_Monthly_Tax_Declaration_FormDesc" xml:space="preserve">
    <value>The monthly declaration form for Value Added Tax (VAT), IRE, deductions at source, and various flat-rate deductions. Only the heading section on page 1, page 4, and field 4 on page 6 will return payroll data. The report should be downloaded in Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_Burundi_Monthly_Tax_Declaration_FormName" xml:space="preserve">
    <value>Monthly Tax Declaration Form</value>
  </data>
  <data name="lblLeg_Cam_27ADesc" xml:space="preserve">
    <value>The DIPE Annual report - tab C1-NOTE27A. Reporting on IRPP, Credit Foncier du Cameroun (CFC), Local Development Tax, and Radiodiffusion Television Camerounaise (CRTV). Employers are required to submit the Annual DIPE as part of the Statistical and Tax Declaration (DSF). This report will be used to copy and paste information into the Dêclaration Stastiuque et de Fiscale (DSF) annual return on the C1-NOTE27A tab. The report should be downloaded in Excel (xlsx).</value>
  </data>
  <data name="lblLeg_Cam_27AName" xml:space="preserve">
    <value>DIPE Annual</value>
  </data>
  <data name="lblLeg_Canada_Advice_of_Debit_MonthlyDesc" xml:space="preserve">
    <value>A report that details the Remittance details to be paid to the relevant authorities per month.</value>
  </data>
  <data name="lblLeg_Canada_Advice_of_Debit_MonthlyName" xml:space="preserve">
    <value>Advice of Debit</value>
  </data>
  <data name="lblLeg_Canada_Advice_of_DebitDesc" xml:space="preserve">
    <value>A report that details the Remittance details to be paid to the relevant authorities.</value>
  </data>
  <data name="lblLeg_Canada_Annual_Filing_ConfirmationDesc" xml:space="preserve">
    <value>PDF document presented to EOR and GP clients after submission of T4 and RL-1 slips.</value>
  </data>
  <data name="lblLeg_Canada_Annual_Filing_ConfirmationName" xml:space="preserve">
    <value>Annual Filing Confirmation – T4 / RL-1</value>
  </data>
  <data name="lblLeg_Canada_CNT_WSDRF_Employee_DetailDesc" xml:space="preserve">
    <value>A report that details the employees that are subject to CNT and WSDRF ER Premiums for an employer.</value>
  </data>
  <data name="lblLeg_Canada_CNT_WSDRF_Employee_DetailName" xml:space="preserve">
    <value>Quebec CNT_WSDRF Employee Details Report</value>
  </data>
  <data name="lblLeg_Canada_CPP_Discrepancy_ReportDesc" xml:space="preserve">
    <value>Identify and track discrepancies between CPP/QPP expected calculations and actual payments for employees and employers.</value>
  </data>
  <data name="lblLeg_Canada_CPP_Discrepancy_ReportName" xml:space="preserve">
    <value>CPP/QPP Discrepancy Report</value>
  </data>
  <data name="lblLeg_Canada_EHT_FileDesc" xml:space="preserve">
    <value>Generates a .txt file formatted for British Columbia EHT bulk submission, including header, detail, and trailer lines. Supports filtering by date range and client inclusion.</value>
  </data>
  <data name="lblLeg_Canada_EHT_FileName" xml:space="preserve">
    <value>BC Employer Health Tax (EHT) Submission File</value>
  </data>
  <data name="lblLeg_Canada_Health_Tax_Summary_ReportDesc" xml:space="preserve">
    <value>The Health Tax Report provides a summary of employer health tax liabilities based on total payroll amounts, calculated in accordance with each province's applicable health tax rules. It helps employers monitor thresholds and contributions for accurate year-end reporting and compliance.</value>
  </data>
  <data name="lblLeg_Canada_Health_Tax_Summary_ReportName" xml:space="preserve">
    <value>Health Tax Summary Report</value>
  </data>
  <data name="lblLeg_Canada_Pay_Transparency_ReportDesc" xml:space="preserve">
    <value>A report that lists when the Advice of Debit reports should be paid</value>
  </data>
  <data name="lblLeg_Canada_Pay_Transparency_ReportName" xml:space="preserve">
    <value>Pay Transparency Report</value>
  </data>
  <data name="lblLeg_Canada_Payroll_ReconDesc" xml:space="preserve">
    <value>The report will be used to provide users employee as well as employer totals of the payroll components totals between a start and end date period.</value>
  </data>
  <data name="lblLeg_Canada_Payroll_ReconName" xml:space="preserve">
    <value>Canada Payroll Reconciliation Report</value>
  </data>
  <data name="lblLeg_Canada_RL1_Employee_CopyDesc" xml:space="preserve">
    <value>The RL-1 slip is a provincial year-end form that reports the earnings and statutory deductions of a Québec employee. If you file paper slips, you must distribute copy 2 of the slips to the employee either in person, by mail or by some other means.</value>
  </data>
  <data name="lblLeg_Canada_RL1_Employee_CopyName" xml:space="preserve">
    <value>RL-1 - Employee Copy</value>
  </data>
  <data name="lblLeg_Canada_RL1_Employer_CopyDesc" xml:space="preserve">
    <value>The RL-1 slip is a provincial year-end form that reports the earnings and statutory deductions of a Québec employee in a tax year. The employer copy provides a copy of each employee's RL-1 slip issued and a summary listing RL-1 company totals.</value>
  </data>
  <data name="lblLeg_Canada_RL1_Employer_CopyName" xml:space="preserve">
    <value>RL-1 - Employer Copy</value>
  </data>
  <data name="lblLeg_Canada_RL1_Master_FileDesc" xml:space="preserve">
    <value>The "Master Control" File for RL-1 employee data would need to be available on demand. This report would be utilized to: 1. Allow the employer the ability to review an employee's current state of their tax form data 2. Allow the employer to ensure their component is mapped to the correct tax form box(es) 3. Allow the employer to review the employee data to ensure it's accuracy before producing the forms</value>
  </data>
  <data name="lblLeg_Canada_RL1_Master_FileName" xml:space="preserve">
    <value>RL-1 Master Control</value>
  </data>
  <data name="lblLeg_Canada_ROEDesc" xml:space="preserve">
    <value>The ROE form is completed by the employer for employees receiving insurable earnings who stop working and experience an interruption of earnings.</value>
  </data>
  <data name="lblLeg_Canada_ROEName" xml:space="preserve">
    <value>Record of Employment (ROE)</value>
  </data>
  <data name="lblLeg_Canada_T4_Employee_CopyDesc" xml:space="preserve">
    <value>T4 Slip report is distributed to employees after submissions have been processed to the CRA</value>
  </data>
  <data name="lblLeg_Canada_T4_Employee_CopyName" xml:space="preserve">
    <value>T4 Slip - Employee Copy</value>
  </data>
  <data name="lblLeg_Canada_T4_ER_CopyDesc" xml:space="preserve">
    <value>T4 Slip report is used to accurately report income and taxes to the Canada Revenue Agency (CRA). This version of the report prints a copy of the T4 Slip, together with a T4 Summary.</value>
  </data>
  <data name="lblLeg_Canada_T4_ER_CopyName" xml:space="preserve">
    <value>T4 Slip - Employer Copy</value>
  </data>
  <data name="lblLeg_Canada_T4_Master_FileDesc" xml:space="preserve">
    <value>The "Master Control" File for T4 employee data would need to be available on demand.</value>
  </data>
  <data name="lblLeg_Canada_T4_Master_FileName" xml:space="preserve">
    <value>T4 - Master File</value>
  </data>
  <data name="lblLeg_Canada_Union_DuesDesc" xml:space="preserve">
    <value>The report lists all employees who have a union due deduction calculated for a specific period to remit.</value>
  </data>
  <data name="lblLeg_Canada_Union_DuesName" xml:space="preserve">
    <value>Union Dues</value>
  </data>
  <data name="lblLeg_Canada_Workers_Compensation_SummaryDesc" xml:space="preserve">
    <value>Workers Compensation Summary</value>
  </data>
  <data name="lblLeg_Canada_Workers_Compensation_SummaryName" xml:space="preserve">
    <value>Workers Compensation Summary</value>
  </data>
  <data name="lblLeg_Canada_Workers_CompensationDesc" xml:space="preserve">
    <value>The report lists all employees who have a workers agency rate assigned, and the amount calculated towards agency contributions.</value>
  </data>
  <data name="lblLeg_Canada_Workers_CompensationName" xml:space="preserve">
    <value>Workers Compensation</value>
  </data>
  <data name="lblLeg_CNSS_BNTS_ElectroniqueDesc" xml:space="preserve">
    <value>Monthly and quarterly CNSS electronic file.</value>
  </data>
  <data name="lblLeg_CNSS_BNTS_ElectroniqueName" xml:space="preserve">
    <value>CNSS BNTS Electronic</value>
  </data>
  <data name="lblLeg_Congo_CNSS_DetailDesc" xml:space="preserve">
    <value>This as a detailed report that indicates the various social contributions.</value>
  </data>
  <data name="lblLeg_Congo_CNSS_DetailName" xml:space="preserve">
    <value>Monthly or Quarterly Declaration of Salaries and Contributions</value>
  </data>
  <data name="lblLeg_Congo_Details_Tax_DeclarationDesc" xml:space="preserve">
    <value>A detailed report of the taxes (PIT, TUS, TOL and CAMU) deducted per employee. This report can be used in conjunction with the 'Bordereau Général de Versement' report.</value>
  </data>
  <data name="lblLeg_Congo_Details_Tax_DeclarationName" xml:space="preserve">
    <value>Details of the Tax Declaration</value>
  </data>
  <data name="lblLeg_Congo_Mon_CAMUDesc" xml:space="preserve">
    <value>A detailed report that indicates the employees monthly CAMU contribution.</value>
  </data>
  <data name="lblLeg_Congo_Mon_CAMUName" xml:space="preserve">
    <value>CAMU Monthly Statement</value>
  </data>
  <data name="lblLeg_DRC_Annual_Employee_Income_TaxDesc" xml:space="preserve">
    <value>Employee annual declaration of remuneration and taxes.</value>
  </data>
  <data name="lblLeg_DRC_Annual_Employee_Income_TaxName" xml:space="preserve">
    <value>Employee Annual Tax Certificate</value>
  </data>
  <data name="lblLeg_DRC_Declaration_MensuelleDesc" xml:space="preserve">
    <value>Monthly consolidated declaration of employee taxes, Social Security contributions and Employer Contributions</value>
  </data>
  <data name="lblLeg_DRC_Declaration_MensuelleName" xml:space="preserve">
    <value>Monthly Declaration of Taxes, Social Security Contribution and Employer Contributions</value>
  </data>
  <data name="lblLeg_DRC_Declaration_Mesuelle_ImpotDesc" xml:space="preserve">
    <value>A monthly report indicating the tax and the exceptional tax on remuneration, also indicating the payment of tax across the different provinces and employment categories.</value>
  </data>
  <data name="lblLeg_DRC_Declaration_Mesuelle_ImpotName" xml:space="preserve">
    <value>Monthly Declaration of Professional and Exceptional Tax on Remuneration</value>
  </data>
  <data name="lblLeg_DRC_DMFP_SummaryDesc" xml:space="preserve">
    <value>This file provides information relating to the CNSS contributions in order to complete the report called Déclaration Mensuelle de La Feuille de Paie (DMFP) through the electronic portal.</value>
  </data>
  <data name="lblLeg_DRC_DMFP_SummaryName" xml:space="preserve">
    <value>Monthly Payroll Declarations (DMFP) - The payslip template</value>
  </data>
  <data name="lblLeg_DRC_DMFPDesc" xml:space="preserve">
    <value>This is a file that provides the financial information relating to the CNSS contribution base to complete the report called Déclaration Mensuelle de La Feuille de Paie (DMFP) through the electronic portal.</value>
  </data>
  <data name="lblLeg_DRC_DMFPName" xml:space="preserve">
    <value>Monthly Payroll Declarations (DMFP) - The payslip detail template</value>
  </data>
  <data name="lblLeg_DRC_Fiche_IntercalaireName" xml:space="preserve">
    <value>Supplementary Annual Report</value>
  </data>
  <data name="lblLeg_Egypt_Monthly_Tax_ReturnDesc" xml:space="preserve">
    <value>This report must be electronically submitted to the Egyptian Tax Authority to declare monthly payroll information impacting taxes.</value>
  </data>
  <data name="lblLeg_Egypt_Monthly_Tax_ReturnName" xml:space="preserve">
    <value>Monthly Tax Return Form No. (2)</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_1Desc" xml:space="preserve">
    <value>This report must be submitted to the National Social Insurance Authority to register a new employee.</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_1Name" xml:space="preserve">
    <value>Social Security Form No. (1)</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_2Desc" xml:space="preserve">
    <value>This report must be submitted to the National Social Insurance Authority to report any changes.</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_2Name" xml:space="preserve">
    <value>Social Security Form No. (2)</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_6Desc" xml:space="preserve">
    <value>This report must be submitted to the National Social Insurance Authority when an employee is terminated.</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_6Name" xml:space="preserve">
    <value>Social Security Form No. (6)</value>
  </data>
  <data name="lblLeg_Eswatini_PAYE_ReconDesc" xml:space="preserve">
    <value>The report is used to reconcile employees' remuneration, allowable deductions, tax deducted and other relevant information for a specific month.</value>
  </data>
  <data name="lblLeg_Eswatini_PAYE_ReconName" xml:space="preserve">
    <value>PAYE Reconciliation Monthly Input</value>
  </data>
  <data name="lblLeg_Eswatini_Tax_CertificateDesc" xml:space="preserve">
    <value>Annual Tax Certificate issued to employees.</value>
  </data>
  <data name="lblLeg_Eswatini_Tax_CertificateName" xml:space="preserve">
    <value>PAYE05 Employee Tax Certificate</value>
  </data>
  <data name="lblLeg_GAB_CNSSDesc" xml:space="preserve">
    <value>Quarterly return of wages for CNSS. The Déclaration trimestrielle des salaires (DTS) returns on the first tab, and the Avis de declaration employeur (AVIS) returns on the second tab. This report should be extracted in an Excel (xlsx) format. If the 'Company Start Date' or 'Company Effective Date' is not the same as the 'Company Creation Date' on the Basic Company Information screen, then make use of the override dates on the report parameters.</value>
  </data>
  <data name="lblLeg_GAB_CNSSName" xml:space="preserve">
    <value>Déclaration trimestrielle des salaires CNSS</value>
  </data>
  <data name="lblLeg_Gabon_ID19Desc" xml:space="preserve">
    <value>Justification form for salaries, wages, pensions and life annuities ID19.</value>
  </data>
  <data name="lblLeg_Gabon_ID19Name" xml:space="preserve">
    <value>Employee Tax year-end certificate</value>
  </data>
  <data name="lblLeg_Gabon_ID21_Salaries_PaidDesc" xml:space="preserve">
    <value>An annual detailed report indicating the remuneration earned and the various taxes deducted. This report should be extracted in an Adobe (pdf) format.</value>
  </data>
  <data name="lblLeg_Gabon_ID21_Salaries_PaidName" xml:space="preserve">
    <value>Detailed report of salaries paid to employees ID21</value>
  </data>
  <data name="lblLeg_Gabon_ID22_Summary_StatementDesc" xml:space="preserve">
    <value>An annual summary report indicating the remuneration earned and the various taxes deducted. The report returns the page totals from the ID21 report. This report should be extracted in an Adobe (pdf) format.</value>
  </data>
  <data name="lblLeg_Gabon_ID22_Summary_StatementName" xml:space="preserve">
    <value>Summary Statement ID22</value>
  </data>
  <data name="lblLeg_Gabon_Payroll_ID20Desc" xml:space="preserve">
    <value>A summary report indicating the total number of employees and remuneration paid for the tax year.</value>
  </data>
  <data name="lblLeg_Gabon_Payroll_ID20Name" xml:space="preserve">
    <value>Payroll Statement ID20</value>
  </data>
  <data name="lblLeg_Gambia_NPF3_FormDesc" xml:space="preserve">
    <value>Monthly schedule for the National Provident Fund contributions.</value>
  </data>
  <data name="lblLeg_Gambia_NPF3_FormName" xml:space="preserve">
    <value>Social Security and Housing Finance Corporation Remittance Advice Form (NPF3)</value>
  </data>
  <data name="lblLeg_Guinea_Annual_DeclarationDesc" xml:space="preserve">
    <value>Under Article 76 of the General Tax Code, companies must submit an annual declaration to tax authorities to ensure accurate tax assessment.</value>
  </data>
  <data name="lblLeg_Guinea_Annual_DeclarationName" xml:space="preserve">
    <value>Annual Declaration Article 76 of the General Tax Code</value>
  </data>
  <data name="lblLeg_India_Annexure_1Desc" xml:space="preserve">
    <value>This report provides the TDS monthly values for employees for a selected quarter.</value>
  </data>
  <data name="lblLeg_India_Annexure_1Name" xml:space="preserve">
    <value>Annexure 1 Report</value>
  </data>
  <data name="lblLeg_India_Annexure_IIDesc" xml:space="preserve">
    <value>The Annexure II Report</value>
  </data>
  <data name="lblLeg_India_Annexure_IIName" xml:space="preserve">
    <value>Annexure II Report</value>
  </data>
  <data name="lblLeg_India_AnnexureBDesc" xml:space="preserve">
    <value>Provides the the Income Tax Details pertaining to Section 10 exemption and chapter VIA.</value>
  </data>
  <data name="lblLeg_India_AnnexureBName" xml:space="preserve">
    <value>Annexure B</value>
  </data>
  <data name="lblLeg_India_Arrear_PFDesc" xml:space="preserve">
    <value>The text file format required for uploading PF to the PF portal when arrears are applicable.</value>
  </data>
  <data name="lblLeg_India_Arrear_PFName" xml:space="preserve">
    <value>Arrear PF ECR Format</value>
  </data>
  <data name="lblLeg_India_Bank_Transfer_StatementDesc" xml:space="preserve">
    <value>This report provides information on the monthly salary credited to employee bank account.</value>
  </data>
  <data name="lblLeg_India_Bank_Transfer_StatementName" xml:space="preserve">
    <value>Bank Transfer Statement</value>
  </data>
  <data name="lblLeg_India_CTC_BreakupDesc" xml:space="preserve">
    <value>This report details the CTC salary breakdown for each employee, and including employees with revised and unchanged salaries.</value>
  </data>
  <data name="lblLeg_India_CTC_BreakupName" xml:space="preserve">
    <value>CTC Breakup Report</value>
  </data>
  <data name="lblLeg_India_Form_16Desc" xml:space="preserve">
    <value>Cover letter for the Form 16 certificate</value>
  </data>
  <data name="lblLeg_India_Form_16Name" xml:space="preserve">
    <value>Form 16 Cover Letter</value>
  </data>
  <data name="lblLeg_India_Form_24QDesc_" xml:space="preserve">
    <value>Form 24Q is a TDS (Tax Deducted at Source) return form used in India for reporting TDS on salary payments. It is specifically used by employers to report the TDS deductions made from salaries paid to employees. This form is required to be filed quarterly.  Filing Frequency: The form must be filed on a quarterly basis. Each quarter corresponds to a financial year quarter:  Q1: April to June Q2: July to September Q3: October to December Q4: January to March</value>
  </data>
  <data name="lblLeg_India_Form_24QDescription" xml:space="preserve">
    <value>TDS (Tax Deducted at Source) return form used in India for reporting TDS on salary payments.</value>
  </data>
  <data name="lblLeg_India_Form_24QN" xml:space="preserve">
    <value>Form 24Q</value>
  </data>
  <data name="lblLeg_India_Form_24QName_" xml:space="preserve">
    <value>Form 24Q</value>
  </data>
  <data name="lblLeg_India_Form_CDesc" xml:space="preserve">
    <value>It is a consolidated bonus paid register maintained by companies.</value>
  </data>
  <data name="lblLeg_India_Form_CName" xml:space="preserve">
    <value>Form C</value>
  </data>
  <data name="lblLeg_India_Form_Ddescr" xml:space="preserve">
    <value>This is a consolidated record of the bonus extended to employees in financial year.</value>
  </data>
  <data name="lblLeg_India_Form_Dname" xml:space="preserve">
    <value>Form D</value>
  </data>
  <data name="lblLeg_India_Form12BDesc" xml:space="preserve">
    <value>Form 12B is used to provide details about the income and tax deductions of an employee who has changed jobs during the financial year. It is used for income tax purposes to ensure that the income earned from multiple employers is properly accounted for when filing income tax returns.</value>
  </data>
  <data name="lblLeg_India_Form12BName" xml:space="preserve">
    <value>Form 12B</value>
  </data>
  <data name="lblLeg_India_Form16_Verification_LetterDesc" xml:space="preserve">
    <value>Provides the perquisites and other benefits given to employees, serving as a supplement to Form 16.</value>
  </data>
  <data name="lblLeg_India_Form16_Verification_LetterName" xml:space="preserve">
    <value>Form 16 - Verification Letter (with Form 12BA)</value>
  </data>
  <data name="lblLeg_India_Income_Tax_ConsolidatedDesc" xml:space="preserve">
    <value>This report provides details of salary, IT declaration data for the selected financial year in a consolidated format for all / select employees.</value>
  </data>
  <data name="lblLeg_India_Income_Tax_ConsolidatedName" xml:space="preserve">
    <value>Income Tax Consolidated Report</value>
  </data>
  <data name="lblLeg_India_Income_Tax_Monthly_StatementDesc" xml:space="preserve">
    <value>Provides details of taxation details for any selected month for all/select employees.</value>
  </data>
  <data name="lblLeg_India_Income_Tax_Monthly_StatementName" xml:space="preserve">
    <value>Income Tax Monthly Statement Report</value>
  </data>
  <data name="lblLeg_India_Income_Tax_WorksheetDesc" xml:space="preserve">
    <value>This report provides details of salary, IT declaration data for the selected month year in a consolidated format for all / select employees.</value>
  </data>
  <data name="lblLeg_India_Income_Tax_WorksheetName" xml:space="preserve">
    <value>Income Tax Worksheet</value>
  </data>
  <data name="lblLeg_India_IT_Savings_Chapter_VI_DeductionsDesc" xml:space="preserve">
    <value>Provides information on actual IT declaration applicable for Chapter VIA.</value>
  </data>
  <data name="lblLeg_India_IT_Savings_Chapter_VI_DeductionsName" xml:space="preserve">
    <value>IT Savings Report For Chapter-VI Deductions</value>
  </data>
  <data name="lblLeg_India_LWF_Monthly_StatementDesc" xml:space="preserve">
    <value>This report provides employee and employer contributions towards Labour Welfare fund.</value>
  </data>
  <data name="lblLeg_India_LWF_Monthly_StatementName" xml:space="preserve">
    <value>LWF Monthly Statement</value>
  </data>
  <data name="lblLeg_India_PayslipDesc" xml:space="preserve">
    <value>Standard payslip that displays all the values of the current period for employees.</value>
  </data>
  <data name="lblLeg_India_PayslipName" xml:space="preserve">
    <value>Payslip</value>
  </data>
  <data name="lblLeg_India_PF_ArrearsDesc" xml:space="preserve">
    <value>Provides details of provident fund arrears processed for employee(s) if any.</value>
  </data>
  <data name="lblLeg_India_PF_ArrearsName" xml:space="preserve">
    <value>PF Arrear Report</value>
  </data>
  <data name="lblLeg_India_PF_ECR_International_WorkersDesc" xml:space="preserve">
    <value>Provides the required text file format for PF returns filing specific to international employees, excluding regular PF contribution submissions.</value>
  </data>
  <data name="lblLeg_India_PF_ECR_International_WorkersName" xml:space="preserve">
    <value>PF ECR For International Workers</value>
  </data>
  <data name="lblLeg_India_PF_Form3ADesc" xml:space="preserve">
    <value>PF Form 3A is a document used in India related to the Employees' Provident Fund (EPF). It is used to record the details of employee contributions and employer contributions to the Employees' Provident Fund (EPF) on a monthly basis.</value>
  </data>
  <data name="lblLeg_India_PF_Form3AName" xml:space="preserve">
    <value>PF Form 3A</value>
  </data>
  <data name="lblLeg_India_PF_MonthlyDesc" xml:space="preserve">
    <value>This report provides details of the salary and provident fund contribution from both employee and employer for the month.</value>
  </data>
  <data name="lblLeg_India_PF_MonthlyName" xml:space="preserve">
    <value>PF Monthly Statement Report</value>
  </data>
  <data name="lblLeg_India_PFDesc" xml:space="preserve">
    <value>The text file format required for uploading to the PF portal.</value>
  </data>
  <data name="lblLeg_India_PFName" xml:space="preserve">
    <value>PF ECR Format</value>
  </data>
  <data name="lblLeg_India_Proof_Of_Investment_DeclarationDesc" xml:space="preserve">
    <value>Provides details of all investment declared, submitted and approved (status) for an employee for the financial year.</value>
  </data>
  <data name="lblLeg_India_Proof_Of_Investment_DeclarationName" xml:space="preserve">
    <value>Proof Of Investment Declaration</value>
  </data>
  <data name="lblLeg_India_PT_StatementDesc" xml:space="preserve">
    <value>This report provides details of the professional tax deducted for all employees. It is generated per state (or all states).</value>
  </data>
  <data name="lblLeg_India_PT_StatementName" xml:space="preserve">
    <value>Professional Tax Monthly Statement</value>
  </data>
  <data name="lblLeg_India_Salary_Revision_Historydescr" xml:space="preserve">
    <value>This report provides comparison of the latest salary with the one previous salary of an employee as applicable.</value>
  </data>
  <data name="lblLeg_India_Salary_Revision_Historyname" xml:space="preserve">
    <value>Salary Revision History</value>
  </data>
  <data name="lblLeg_India_Settlement_PayslipDesc" xml:space="preserve">
    <value>Provides a detailed summary of the settlement/resettlement information of employees leaving the company.</value>
  </data>
  <data name="lblLeg_India_Settlement_PayslipName" xml:space="preserve">
    <value>Settlement Payslip</value>
  </data>
  <data name="lblLeg_India_SettlementDesc" xml:space="preserve">
    <value>This report provides employee separation details and amount extended during full and final settlement (FnF).</value>
  </data>
  <data name="lblLeg_India_SettlementName" xml:space="preserve">
    <value>Settlement Report</value>
  </data>
  <data name="lblLeg_IVOR_FDFP_MonthlyDesc" xml:space="preserve">
    <value>Monthly employer training taxes report.</value>
  </data>
  <data name="lblLeg_IVOR_FDFP_MonthlyName" xml:space="preserve">
    <value>Apprenticeship tax and additional tax for continuing education (FDFP)</value>
  </data>
  <data name="lblLeg_IVOR_IVCDesc" xml:space="preserve">
    <value>Monthly declaration that indicates the monthly Taxes paid over to the revenue authorities. The report should be downloaded in Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_IVOR_IVCName" xml:space="preserve">
    <value>Monthly declaration of Taxes</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Annual_CNPS_DISADesc" xml:space="preserve">
    <value>A detailed annual declaration for CNPS.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Annual_CNPS_DISAName" xml:space="preserve">
    <value>Individual Annual Salary Declaration (D.I.S.A)</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Electronic_CNPS_MonthlyDesc" xml:space="preserve">
    <value>A monthly electronic file to import social security contributions.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Electronic_CNPS_MonthlyName" xml:space="preserve">
    <value>CNPS Monthly Electronic Report</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ETAT301_DetailDesc" xml:space="preserve">
    <value>Statement 301 is a statement of salaries and related remunerations paid during the year, used by employers in the private sector. This report will be used to copy and paste into the EDI macro. Copy columns C to R, T, V, W, Y, AA to AB and paste these onto the template. Columns S, U, X, Z will be calculated by the template. Columns S, U, X, Z are still returned in the PaySpace report for reconciliation purposes.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ETAT301_DetailName" xml:space="preserve">
    <value>ETAT301 Detail (EDI)</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ETAT301_SummaryDesc" xml:space="preserve">
    <value>ETAT 301 is an annual tax declaration. It provides a summary report of all salaries, wages, and related payments (such as bonuses and benefits) made by an employer to its employees over the course of a tax year.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ETAT301_SummaryName" xml:space="preserve">
    <value>ETAT301 Summary</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITS_FDFPDesc" xml:space="preserve">
    <value>Summary report of the employee and employer taxes (ITS, CE &amp; CN), including the Apprenticeship tax and the Training Tax.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITS_FDFPName" xml:space="preserve">
    <value>Monthly ITS-FDFP (Declaration of tax on salaries, wages and related contributions)</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSDesc" xml:space="preserve">
    <value>A detailed monthly statement indicating a breakdown of gross remuneration, family situation and the tax due per month. This report will be used to copy and paste into the EDI macro. Copy columns C to R, T, Y , AA to AB and paste these onto the template. Columns S, U to X, Z will be calculated by the template. Columns S, U to X, Z are still returned in the PaySpace report for reconciliation purposes.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSName" xml:space="preserve">
    <value>Individual Statement of Employees' Gross Monthly Pay</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSREGULDesc" xml:space="preserve">
    <value>Summary report of the employee and employer taxes (ITS, CE &amp; CN), including the Apprenticeship Tax and the Training Tax.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSREGULName" xml:space="preserve">
    <value>ITS REGUL</value>
  </data>
  <data name="lblLeg_Kenya_SHIFCDesc" xml:space="preserve">
    <value>This report is submitted monthly via the Social Health Authority’s website and contains information on employee Social Health Insurance Fund contributions.</value>
  </data>
  <data name="lblLeg_Kenya_SHIFCName" xml:space="preserve">
    <value>SHIF Contributions Report</value>
  </data>
  <data name="lblLeg_Lesotho_FBT_ReturnDesc" xml:space="preserve">
    <value>Quarterly report reflecting all benefits granted under the FBT regime.</value>
  </data>
  <data name="lblLeg_Lesotho_FBT_ReturnName" xml:space="preserve">
    <value>Fringe Benefits Tax (FBT) Return</value>
  </data>
  <data name="lblLeg_Lesotho_Monthly_PAYEDesc" xml:space="preserve">
    <value>Monthly PAYE schedule for efiling (download the report in Excel).</value>
  </data>
  <data name="lblLeg_Lesotho_Monthly_PAYEName" xml:space="preserve">
    <value>PAYE schedule</value>
  </data>
  <data name="lblLeg_Mad_MGA_USD_Bordereau_IRSA_ReportDesc" xml:space="preserve">
    <value>Monthly report for Tax On Salary And Similar Income.</value>
  </data>
  <data name="lblLeg_Mad_MGA_USD_Bordereau_IRSA_ReportName" xml:space="preserve">
    <value>Monthly report for Tax On Salary And Similar Income (IRSA)</value>
  </data>
  <data name="lblLeg_Mada_Etat_Nominatif_IRSA_ReportDesc" xml:space="preserve">
    <value>Statement of salaries, wages and similar income.</value>
  </data>
  <data name="lblLeg_Mada_Etat_Nominatif_IRSA_ReportName" xml:space="preserve">
    <value>Statement of salaries, wages and similar income (IRSA) Report</value>
  </data>
  <data name="lblLeg_Mada_Health_Org_ReportDesc" xml:space="preserve">
    <value>Provides the reports required for the Health Organisation contributions. It is recommended that the report is exported in MS Excel-format.</value>
  </data>
  <data name="lblLeg_Mada_Health_Org_ReportName" xml:space="preserve">
    <value>Health Organisation Contribution</value>
  </data>
  <data name="lblLeg_Mada_Mon_eHetra_ReportDesc" xml:space="preserve">
    <value>This schedule contains monthly employee IRSA information. Download the report in Excel and copy it into the macro file which can be submitted via the eHetra portal.</value>
  </data>
  <data name="lblLeg_Mada_Mon_eHetra_ReportName" xml:space="preserve">
    <value>Monthly eHetra IRSA Schedule</value>
  </data>
  <data name="lblLeg_Malaysia_ASNBDesc" xml:space="preserve">
    <value>Amanah Saham Nasional Berhad (ASNB) Deductions</value>
  </data>
  <data name="lblLeg_Malaysia_ASNBName" xml:space="preserve">
    <value>ASNB (Amanah Saham Nasional Berhad)</value>
  </data>
  <data name="lblLeg_Malaysia_CP21Desc" xml:space="preserve">
    <value>The CP21 form in Malaysia refers to a "Notification of Change in Employment" issued by the Inland Revenue Board of Malaysia (Lembaga Hasil Dalam Negeri, or LHDN). This form is primarily used by employers to inform LHDN when an employee's employment status changes, particularly in cases where an employee stops working for the company, such as due to termination, resignation, or retirement.</value>
  </data>
  <data name="lblLeg_Malaysia_CP21Name" xml:space="preserve">
    <value>CP21</value>
  </data>
  <data name="lblLeg_Malaysia_CP22_txtDesc" xml:space="preserve">
    <value>Reporting of new joiners to LHDN</value>
  </data>
  <data name="lblLeg_Malaysia_CP22_txtName" xml:space="preserve">
    <value>CP22 Text File</value>
  </data>
  <data name="lblLeg_Malaysia_CP22A_txtDesc" xml:space="preserve">
    <value>The CP22A form itself is typically submitted by employers when an employee leaves employment during the year.</value>
  </data>
  <data name="lblLeg_Malaysia_CP22A_txtName" xml:space="preserve">
    <value>CP22A Text File</value>
  </data>
  <data name="lblLeg_Malaysia_CP22ADesc" xml:space="preserve">
    <value>CP22A is another form submitted by employers to notify LHDN on their employees' termination of employment, retirement, or permanent overseas migration.</value>
  </data>
  <data name="lblLeg_Malaysia_CP22AName" xml:space="preserve">
    <value>CP22A</value>
  </data>
  <data name="lblLeg_Malaysia_CP22Desc" xml:space="preserve">
    <value>CP22 form is used in the context of payroll and tax administration to notify the Inland Revenue Board (LHDN) of a new employee or changes related to an existing employee.</value>
  </data>
  <data name="lblLeg_Malaysia_CP22Name" xml:space="preserve">
    <value>CP22</value>
  </data>
  <data name="lblLeg_Malaysia_CP39Desc" xml:space="preserve">
    <value>CP39 is a statement of monthly tax deduction (PCB) need to submit to LHDN Malaysia every month by every employer, effective 01 Sept 2019 IRB Malaysia no longer accept a manual CP39 Form, employer is required to submit through e-PCB, e-Data-PCB or e-CP39.</value>
  </data>
  <data name="lblLeg_Malaysia_CP39Name" xml:space="preserve">
    <value>MTD Form CP39</value>
  </data>
  <data name="lblLeg_Malaysia_CP8D_FormDesc" xml:space="preserve">
    <value>CP8D Form is the CP8D employee report in a PDF and EXCEL formats</value>
  </data>
  <data name="lblLeg_Malaysia_CP8D_FormName" xml:space="preserve">
    <value>CP8D Form</value>
  </data>
  <data name="lblLeg_Malaysia_CP8D_txtDesc" xml:space="preserve">
    <value>The CP8D TXT file Prefil and Praisi are features related to the CP8D Employer Report in Malaysia.</value>
  </data>
  <data name="lblLeg_Malaysia_CP8D_txtName" xml:space="preserve">
    <value>CP8D Prefil/ Praisi TXT File</value>
  </data>
  <data name="lblLeg_Malaysia_EIS_Text_FileDesc" xml:space="preserve">
    <value>The Employment Insurance System (EIS) is a financial aid scheme for employees who have lost their jobs until they find new employment. The contributions are reported on a monthly basis via the EIS Montly Text File.</value>
  </data>
  <data name="lblLeg_Malaysia_EIS_Text_FileName" xml:space="preserve">
    <value>EIS Monthly Text File</value>
  </data>
  <data name="lblLeg_Malaysia_EPF_Form_A_Text_FileDesc" xml:space="preserve">
    <value>EPF stands for Employees Provident Fund and also common known as Kumpulan Wang Simpanan Pekerja. Companies are required to contribute EPF on behalf of their employees and to remit the contribution sum to KWSP before the 15th day of the following month</value>
  </data>
  <data name="lblLeg_Malaysia_EPF_Form_A_Text_FileName" xml:space="preserve">
    <value>EPF Monthly Text File</value>
  </data>
  <data name="lblLeg_Malaysia_EPF_FORM_ADesc" xml:space="preserve">
    <value>Provides the monthly details for EPF contributions made by the employee and employer.</value>
  </data>
  <data name="lblLeg_Malaysia_EPF_FORM_AName" xml:space="preserve">
    <value>EPF Monthly Form A</value>
  </data>
  <data name="lblLeg_Malaysia_Form_EDesc" xml:space="preserve">
    <value>Form E is an essential document for employers in Malaysia to report the wages, allowances, and tax deductions for all their employees to the Inland Revenue Board (LHDN) annually. It ensures the proper reporting and compliance with the country’s income tax regulations.</value>
  </data>
  <data name="lblLeg_Malaysia_Form_EName" xml:space="preserve">
    <value>FORM E</value>
  </data>
  <data name="lblLeg_Malaysia_HRDF_ListingDesc" xml:space="preserve">
    <value>The HRDF Listing Report serves multiple purposes related to the management and compliance of HRDF contributions. It displays the total levy considered for HRDF calculation and the HRDF contribution of an employee for the month</value>
  </data>
  <data name="lblLeg_Malaysia_HRDF_ListingName" xml:space="preserve">
    <value>HRDF Listing Report</value>
  </data>
  <data name="lblLeg_Malaysia_HRDF_ReportDesc" xml:space="preserve">
    <value>The HRDF is an agency under the Ministry of Human Resources in Malaysia, established to encourage and support the development of Malaysian workforce through training and upskilling initiatives. The HRDF Report typically includes: Financial Statements: Details on the fund's financial performance, including income, expenditure, and reserves. Training Initiatives: Overview of training programs funded by HRDF during the year.</value>
  </data>
  <data name="lblLeg_Malaysia_HRDF_ReportName" xml:space="preserve">
    <value>HRDF Report</value>
  </data>
  <data name="lblLeg_Malaysia_Lampiran_1Desc" xml:space="preserve">
    <value>The Lampiran 1 (EIS Manual Form) report allows you to generate a report of those who contributed for EIS</value>
  </data>
  <data name="lblLeg_Malaysia_Lampiran_1Name" xml:space="preserve">
    <value>EIS Monthly Lampiran 1</value>
  </data>
  <data name="lblLeg_Malaysia_LHDN_ReportDesc" xml:space="preserve">
    <value>MTD (Monthly Tax Deduction) text file is a digital file used to submit Monthly Tax Deduction (MTD) information to the Inland Revenue Board of Malaysia (LHDN). This file format helps streamline the process of reporting and paying tax deductions on employees' salaries.</value>
  </data>
  <data name="lblLeg_Malaysia_LHDN_ReportName" xml:space="preserve">
    <value>MTD Text File</value>
  </data>
  <data name="lblLeg_Malaysia_Monthly_PCB_StatementDesc" xml:space="preserve">
    <value>This gives the month wise renumeration data for an employee in detail.</value>
  </data>
  <data name="lblLeg_Malaysia_Monthly_PCB_StatementName" xml:space="preserve">
    <value>Monthly PCB Statement</value>
  </data>
  <data name="lblLeg_Malaysia_SOCSO_8ADesc" xml:space="preserve">
    <value>SOCSO BORANG 8A provides information on SOCSO monthly contributions made for all employees. SOCSO BORANG 8A form for completed payroll runs must be submitted to Perkeso online or at the branch/counter by the 15th of the month following the contribution.</value>
  </data>
  <data name="lblLeg_Malaysia_SOCSO_8AName" xml:space="preserve">
    <value>SOCSO Monthly Borang 8A</value>
  </data>
  <data name="lblLeg_Malaysia_SOCSO_EIS_fileDesc" xml:space="preserve">
    <value>SOCSO (Social Security Organization) and EIS (Employment Insurance System) are part of the country’s statutory social security and insurance schemes. Employers are required to make contributions to these programs on behalf of their employees. The SOCSO + EIS text file is a file format used for submitting the contributions</value>
  </data>
  <data name="lblLeg_Malaysia_SOCSO_EIS_fileName" xml:space="preserve">
    <value>SOCSO &amp; EIS Text file</value>
  </data>
  <data name="lblLeg_Malaysia_Zakat_KedahDesc" xml:space="preserve">
    <value>The Zakat Kedah report typically refers to a periodic report issued by the Zakat collection and distribution agency in the state of Kedah, Malaysia.</value>
  </data>
  <data name="lblLeg_Malaysia_Zakat_KedahName" xml:space="preserve">
    <value>Zakat Kedah</value>
  </data>
  <data name="lblLeg_Malaysia_Zakat_PahangDesc" xml:space="preserve">
    <value>A Zakat form is used by individuals or organizations to systematically calculate their Zakat liability based on Islamic principles. It helps in documenting the assets, deductions, and the calculated Zakat amount, ensuring compliance with religious obligations. For selected region only.</value>
  </data>
  <data name="lblLeg_Malaysia_Zakat_PahangName" xml:space="preserve">
    <value>Zakat Pahang, Perlis &amp; Negeri Sembilan</value>
  </data>
  <data name="lblLeg_Mali_Declaration_Monthly_INPSDesc" xml:space="preserve">
    <value>Detailed report indicating the various contributions for INPS. To ensure correct reporting, all employees must be linked to an 'Employee Category'.</value>
  </data>
  <data name="lblLeg_Mali_Declaration_Monthly_INPSName" xml:space="preserve">
    <value>Nominative declaration of payment of contributions</value>
  </data>
  <data name="lblLeg_Mali_Monthly_Tax_Declaration_Form_ReportDesc" xml:space="preserve">
    <value>A monthly declaration illustrating the various contributions owed to the DGI. Submissions and reports must be submitted by the 15th of the month following the deduction.</value>
  </data>
  <data name="lblLeg_Mali_Monthly_Tax_Declaration_Form_ReportName" xml:space="preserve">
    <value>Monthly Tax Declaration Form</value>
  </data>
  <data name="lblLeg_Mauritania_Ann_DeclarationDesc" xml:space="preserve">
    <value>The annual salary declaration is a document used to report social security contributions and taxes on salaries and wages. This declaration is part of the requirements for the General Tax Return.</value>
  </data>
  <data name="lblLeg_Mauritania_Ann_DeclarationName" xml:space="preserve">
    <value>D31.DADS Annual salary declaration (DAS) General Tax Return</value>
  </data>
  <data name="lblLeg_Mauritania_DAS_AnnualDesc" xml:space="preserve">
    <value>Annual salary declaration used to submit the social security contributions and tax on salaries and wages.</value>
  </data>
  <data name="lblLeg_Mauritania_DAS_AnnualName" xml:space="preserve">
    <value>Annual salary declaration (DAS)</value>
  </data>
  <data name="lblLeg_Mauritania_Declaration_MenDesc" xml:space="preserve">
    <value>Monthly declaration used to submit the tax on wages and salaries.  This report must be extracted in PDF Format.</value>
  </data>
  <data name="lblLeg_Mauritania_Declaration_MenName" xml:space="preserve">
    <value>Monthly PAYE report</value>
  </data>
  <data name="lblLeg_Mon_Togo_CNSS_DeclarationDesc" xml:space="preserve">
    <value>The social contribution declaration form allows the employer to make monthly declarations to the CNSS.</value>
  </data>
  <data name="lblLeg_Mon_Togo_CNSS_DeclarationName" xml:space="preserve">
    <value>Declaration of Remuneration and Contributions (DRC)</value>
  </data>
  <data name="lblLeg_Mon_Togo_Tax_reportDesc" xml:space="preserve">
    <value>Monthly declaration for payroll taxes and employee tax</value>
  </data>
  <data name="lblLeg_Mon_Togo_Tax_reportName" xml:space="preserve">
    <value>Declaration of Payment of Payroll Tax and Tax Deductions</value>
  </data>
  <data name="lblLeg_MOZ_INSS_EDIDesc" xml:space="preserve">
    <value>Electronic submission file for monthly declaration of Social Security contributions uploaded via SISSMO (txt format).</value>
  </data>
  <data name="lblLeg_MOZ_INSS_EDIName" xml:space="preserve">
    <value>INSS Electronic Submission Upload File (txt format)</value>
  </data>
  <data name="lblLeg_MOZ_INSSDesc" xml:space="preserve">
    <value>Electronic submission file for monthly declaration of Social Security contributions uploaded via SISSMO (xlsx format).</value>
  </data>
  <data name="lblLeg_MOZ_INSSName" xml:space="preserve">
    <value>INSS Electronic Submission Upload File (xlsx format)</value>
  </data>
  <data name="lblLeg_MOZ_M19_ReturnDesc" xml:space="preserve">
    <value>Monthly payment return for tax (IRPS).</value>
  </data>
  <data name="lblLeg_MOZ_M19_ReturnName" xml:space="preserve">
    <value>M/19 IRPS Return</value>
  </data>
  <data name="lblLeg_Niger_Ann_ITSDesc" xml:space="preserve">
    <value>This is an annual summary report on the taxes calculated on Salaries, wages, similar income, and life annuities. This report should be downloaded in Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_Niger_Ann_ITSName" xml:space="preserve">
    <value>Annual Summary Declaration of Tax Deductions</value>
  </data>
  <data name="lblLeg_Niger_Mon_ITSDesc" xml:space="preserve">
    <value>A monthly report on the taxes calculated on Salaries, wages, similar income, and life annuities. This report should be downloaded in Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_Niger_Mon_ITSName" xml:space="preserve">
    <value>Monthly Declaration of Tax Deductions</value>
  </data>
  <data name="lblLeg_Nigeria_NHFDesc" xml:space="preserve">
    <value>Monthly schedule for the Nigeria National Housing Fund contributions.</value>
  </data>
  <data name="lblLeg_Nigeria_NHFName" xml:space="preserve">
    <value>NHF Report</value>
  </data>
  <data name="lblLeg_Nigeria_NSITF_Employers_ScheduleDesc" xml:space="preserve">
    <value>Monthly report for employer's schedule of payments.</value>
  </data>
  <data name="lblLeg_Nigeria_NSITF_Employers_ScheduleName" xml:space="preserve">
    <value>NSITF Employers-Schedule-Of-Payments</value>
  </data>
  <data name="lblLeg_Qua_Togo_CNSSDesc" xml:space="preserve">
    <value>From tax year 2024, this Nextgen report will no longer be enhanced.</value>
  </data>
  <data name="lblLeg_Qua_Togo_CNSSName" xml:space="preserve">
    <value>Nominative Declaration of Remuneration</value>
  </data>
  <data name="lblLeg_Senegal_Annual_TaxDesc" xml:space="preserve">
    <value>Annual declaration reporting the taxes (IR, TRIMF, CFCE) charged on salaries paid to all employees.</value>
  </data>
  <data name="lblLeg_Senegal_Annual_TaxName" xml:space="preserve">
    <value>Annual summary statement of wages and salaries</value>
  </data>
  <data name="lblLeg_Senegal_Monthly_Tax_Desc" xml:space="preserve">
    <value>Monthly withholding on wages for Impot, TRIMF and CFCE</value>
  </data>
  <data name="lblLeg_Senegal_Monthly_Tax_Name" xml:space="preserve">
    <value>Monthly Tax Report</value>
  </data>
  <data name="lblLeg_Singapore_CPF_Desc" xml:space="preserve">
    <value>This report is run on an annual basis to identifiy CPF contribution variances.</value>
  </data>
  <data name="lblLeg_Singapore_CPF_Name" xml:space="preserve">
    <value>CPF Adjustment Listing</value>
  </data>
  <data name="lblLeg_Singapore_CPF_Payment_Advice_SummaryDesc" xml:space="preserve">
    <value>This is a monthly report which provides a breakdown summary of items payable to CPF and supporting employee details.</value>
  </data>
  <data name="lblLeg_Singapore_CPF_Payment_Advice_SummaryName" xml:space="preserve">
    <value>CPF Payment Advice Summary</value>
  </data>
  <data name="lblLeg_Singapore_CPF_TextDesc" xml:space="preserve">
    <value>This is a monthly file to be generated for upload into CPF Portal as Employer submission of Total CPF Contributions and SHG Contributions.</value>
  </data>
  <data name="lblLeg_Singapore_CPF_TextName" xml:space="preserve">
    <value>CPF EZPay (FTP) File</value>
  </data>
  <data name="lblLeg_Singapore_Form_Appendix8ADesc" xml:space="preserve">
    <value>This report is generated annually for reporting of employee remuneration for the year ending 31 Dec 2024</value>
  </data>
  <data name="lblLeg_Singapore_Form_Appendix8AName" xml:space="preserve">
    <value>FORM APPENDIX 8A</value>
  </data>
  <data name="lblLeg_Singapore_Form_Appendix8BDesc" xml:space="preserve">
    <value>This report is generated annually for reporting of employee remuneration for the year ending 31 Dec &lt;yyyy&gt;</value>
  </data>
  <data name="lblLeg_Singapore_Form_Appendix8BName" xml:space="preserve">
    <value>FORM APPENDIX 8B</value>
  </data>
  <data name="lblLeg_Singapore_Form_IR8ADesc" xml:space="preserve">
    <value>This report is generated annually for reporting of employee remuneration for the year ending 31 Dec 2024</value>
  </data>
  <data name="lblLeg_Singapore_Form_IR8AName" xml:space="preserve">
    <value>FORM IR8A</value>
  </data>
  <data name="lblLeg_Singapore_IR8SDesc" xml:space="preserve">
    <value>This report is generated annually for reporting of employee remuneration for the year ending 31 Dec &lt;yyyy&gt;</value>
  </data>
  <data name="lblLeg_Singapore_IR8SName" xml:space="preserve">
    <value>FORM IR8S</value>
  </data>
  <data name="lblLeg_Singapore_IRAS_ValidationDesc" xml:space="preserve">
    <value>This is an internal validation report listing all employees reported under IR8A, Appendix 8A, Appendix 8B, IR8S with summary totals relevant to each form</value>
  </data>
  <data name="lblLeg_Singapore_IRAS_ValidationName" xml:space="preserve">
    <value>IRAS Validation Report</value>
  </data>
  <data name="lblLeg_Singapore_ReconDesc" xml:space="preserve">
    <value>This report is used as an internal reference to validate the values reported to IRAS.</value>
  </data>
  <data name="lblLeg_Singapore_ReconName" xml:space="preserve">
    <value>IRAS Reconciliation Report</value>
  </data>
  <data name="lblLeg_South_Africa_BCEA5Desc" xml:space="preserve">
    <value>Certificate of service for terminated employees</value>
  </data>
  <data name="lblLeg_South_Africa_BCEA5Name" xml:space="preserve">
    <value>BCEA 5</value>
  </data>
  <data name="lblLeg_South_Africa_UI_2_7Desc" xml:space="preserve">
    <value>Form issued to employees who are still in employment but unable to work due to maternity leave, illness, adoption leave, parental leave, commissioning parental leave, or reduced working time.</value>
  </data>
  <data name="lblLeg_South_Africa_UI_2_7Name" xml:space="preserve">
    <value>UI-2.7</value>
  </data>
  <data name="lblLeg_Spain_Average_HeadcountDesc" xml:space="preserve">
    <value>The Average Headcount per Company report is an operational report used by Compliance/HR team to report the average headcount of a company in a given timeframe.</value>
  </data>
  <data name="lblLeg_Spain_Average_HeadcountName" xml:space="preserve">
    <value>Average Headcount per Workplace</value>
  </data>
  <data name="lblLeg_Spain_Company_CertificateDesc" xml:space="preserve">
    <value>The Company Certificate is used to document the termination of the employment relationship between the employer and the employee on a specific date. It provides evidence of the cause or reason for that termination.</value>
  </data>
  <data name="lblLeg_Spain_Company_CertificateName" xml:space="preserve">
    <value>Company Certificate</value>
  </data>
  <data name="lblLeg_Spain_IRPF_ReconDesc" xml:space="preserve">
    <value>The "Cuadre de Cuotas" report ensures compliance with legal withholding requirements when an employee is terminated. It compares the estimated withholdings versus the actual amounts withheld. If a deficit is found (a negative difference), a higher tax rate must be applied to meet the legal minimum withholding based on the employee's total compensation. This report is the final calculation of the taxes. It compares our calculation with the Tax agency calculator.</value>
  </data>
  <data name="lblLeg_Spain_IRPF_ReconName" xml:space="preserve">
    <value>IRPF Tax Reconciliation File</value>
  </data>
  <data name="lblLeg_Spain_Tax_Form_111_ReportDesc" xml:space="preserve">
    <value>The Modelo 111 form in Spain is used by businesses and self-employed individuals to declare and pay withholding taxes on income earned by employees, freelancers, and professionals. It covers withholdings for salaries, professional services, and certain other payments. Filed quarterly or monthly, it ensures that taxes withheld from payments are reported and transferred to the Spanish Tax Agency (Agencia Tributaria).</value>
  </data>
  <data name="lblLeg_Spain_Tax_Form_111_ReportName" xml:space="preserve">
    <value>Tax Form 111 National State</value>
  </data>
  <data name="lblLeg_Tanzania_SDL_Return_FormDesc" xml:space="preserve">
    <value>Monthly SDL electronic report used in the macro sheet for online submission.</value>
  </data>
  <data name="lblLeg_Tanzania_SDL_Return_FormName" xml:space="preserve">
    <value>SDL Return Form ITX.215.03.E</value>
  </data>
  <data name="lblLeg_Togo_Anuual_Tax_DeclarationDesc" xml:space="preserve">
    <value>Annual declaration used to submit tax on salaries, wages, pension and life annuities.</value>
  </data>
  <data name="lblLeg_Togo_Anuual_Tax_DeclarationName" xml:space="preserve">
    <value>Annual Tax Declaration</value>
  </data>
  <data name="lblLeg_Tunisia_Annual_CAVISDesc" xml:space="preserve">
    <value>The report is used to declare the annual CAVIS contribution salary in text format on the online platform electronically.</value>
  </data>
  <data name="lblLeg_Tunisia_Annual_CAVISName" xml:space="preserve">
    <value>Annual CAVIS Declaration Report (txt format)</value>
  </data>
  <data name="lblLeg_UAE_DIFC_Employee_Workplace_SavingsDesc" xml:space="preserve">
    <value>This report populates the data of the employees and their DEWS contribution values. The file can be used for initial enrolment and on an ongoing basis in line with the frequency of your internal payroll processes (weekly, monthly etc.). The file is uploaded in CSV format to the DEWS portal.</value>
  </data>
  <data name="lblLeg_UAE_DIFC_Employee_Workplace_SavingsName" xml:space="preserve">
    <value>UAE DIFC Employee Workplace Savings (DEWS) Plan Upload File</value>
  </data>
  <data name="lblLeg_UGA_LST_MonthlyDesc" xml:space="preserve">
    <value>A generic monthly LST return. This return must be completed and submitted to the local government authorities for local services tax (LST).</value>
  </data>
  <data name="lblLeg_UGA_LST_MonthlyName" xml:space="preserve">
    <value>Local Service Tax (LST) Return</value>
  </data>
  <data name="lblLeg_UGA_PAYE_Return_Schedule3Desc" xml:space="preserve">
    <value>Schedule 3 of the DT-2008 PAYE Monthly Return. This report will be used to copy and paste into the DT-2008 PAYE Monthly Return template on the Schedule 3 tab. Copy columns A to G, I, J, M and O and paste these onto the template. Columns H, K, L, N, P and Q will be calculated by the template. Columns H, K, L, N, P and Q are still returned in the PaySpace report for reconciliation purposes.</value>
  </data>
  <data name="lblLeg_UGA_PAYE_Return_Schedule3Name" xml:space="preserve">
    <value>PAYE Monthly Return Schedule 3</value>
  </data>
  <data name="lblLeg_UGA_PAYE_Return_Schedule4Desc" xml:space="preserve">
    <value>Schedule 4 of the DT-2008 PAYE Monthly Return. This report will be used to copy and paste into the DT-2008 PAYE Monthly Return template on the Schedule 4 tab. Copy columns A to G, J and L and paste these onto the template. Columns H, I, K, M and N will be calculated by the template. Columns H, I, K, M and N are still returned in the PaySpace report for reconciliation purposes.</value>
  </data>
  <data name="lblLeg_UGA_PAYE_Return_Schedule4Name" xml:space="preserve">
    <value>PAYE Monthly Return Schedule 4</value>
  </data>
  <data name="lblLeg_UGA_PAYE_ReturnDesc" xml:space="preserve">
    <value>Schedule 1 of the DT-2008 PAYE Monthly Return. This report will be used to copy and paste into the DT-2008 PAYE Monthly Return template on the Schedule 1 tab. Copy columns A to O, Q, S, T and V and paste these onto the template. Columns P, R, and U will be calculated by the template. Columns P and R are still returned in the PaySpace report for reconciliation purposes. The report should be downloaded in excel (xlsx).</value>
  </data>
  <data name="lblLeg_UGA_PAYE_ReturnName" xml:space="preserve">
    <value>PAYE Monthly Return Schedule 1</value>
  </data>
  <data name="lblLeg_Uganda_NSSF_MonthlyDesc" xml:space="preserve">
    <value>Monthly NSSF declaration for Uganda, which can be uploaded on the NSSF portal.</value>
  </data>
  <data name="lblLeg_Uganda_NSSF_MonthlyName" xml:space="preserve">
    <value>National Social Security Fund Monthly Declaration Schedule</value>
  </data>
  <data name="lblLeg_Uganda_NSSF_SpecialDesc" xml:space="preserve">
    <value>Monthly NSSF declaration for Uganda for Special Contributions, which can be uploaded on the NSSF portal.</value>
  </data>
  <data name="lblLeg_Uganda_NSSF_SpecialName" xml:space="preserve">
    <value>Monthly NSSF Declaration - Special Contributions</value>
  </data>
  <data name="lblLeg_UK_P45_Termination_CertificateDesc" xml:space="preserve">
    <value>Certificate issued to terminated employees</value>
  </data>
  <data name="lblLeg_UK_P45_Termination_CertificateName" xml:space="preserve">
    <value>P45 Employee Termination Certificate</value>
  </data>
  <data name="lblLeg_Zambia_WCF_ElectronicDesc" xml:space="preserve">
    <value>Annual return for submission of a CSV file via the Workers Compensation Fund Control Boards website.</value>
  </data>
  <data name="lblLeg_Zambia_WCF_ElectronicName" xml:space="preserve">
    <value>Zambia WCF Electronic File</value>
  </data>
  <data name="lblLeg_Zim_Form_P2_PAYEDesc" xml:space="preserve">
    <value>Monthly return for the remittance of PAYE.</value>
  </data>
  <data name="lblLeg_Zim_Form_P2_PAYEName" xml:space="preserve">
    <value>Form P2</value>
  </data>
  <data name="lblLeg_Zim_ManPower_ReturnDesc" xml:space="preserve">
    <value>Monthly report to declare contributions to the Zimbabwe Manpower Development Fund.</value>
  </data>
  <data name="lblLeg_Zim_ManPower_ReturnName" xml:space="preserve">
    <value>Zimbabwe Manpower Development Fund Declaration Form</value>
  </data>
  <data name="lblLeg_Zim_NSSA_P4ADesc" xml:space="preserve">
    <value>Form P4A is a monthly remittance advice for payment to the National Social Security Authority.</value>
  </data>
  <data name="lblLeg_Zim_NSSA_P4AName" xml:space="preserve">
    <value>Form P4A NSSA</value>
  </data>
  <data name="lblLeg_Zim_NSSA_P4Desc" xml:space="preserve">
    <value>Employers are instructed to visit the portal and to download the P4 template for editing. Once downloaded, employers should use this NSSA P4 Excel report to edit columns E  and K to N.</value>
  </data>
  <data name="lblLeg_Zim_NSSA_P4Name" xml:space="preserve">
    <value>NSSA P4</value>
  </data>
  <data name="lblLeg_Zim_PAYE_Return_USDDesc" xml:space="preserve">
    <value>Monthly PAYE return for submission to ZIMRA via the online platform, TaRMS.</value>
  </data>
  <data name="lblLeg_Zim_PAYE_Return_USDName" xml:space="preserve">
    <value>PAYE Return</value>
  </data>
  <data name="lblLeg_Zim_PAYE_ReturnDesc" xml:space="preserve">
    <value>This report will not be enhanced in the future. Refer to the PAYE Template - Employee Earnings Upload.</value>
  </data>
  <data name="lblLeg_Zim_PAYE_ReturnDesc_USD" xml:space="preserve">
    <value>This report will not be enhanced in the future. Refer to the PAYE Template - Employee Earnings Upload.</value>
  </data>
  <data name="lblLeg_Zim_PAYE_ReturnName" xml:space="preserve">
    <value>PAYE Return</value>
  </data>
  <data name="lblLeg_Zim_PAYE_TemplateDesc" xml:space="preserve">
    <value>This template is used to upload employees' earnings to the Zimbabwe TaRMs portal for PAYE submission. For this report to return in a different currency, the Currency Exchange Rates screen has to be configured.</value>
  </data>
  <data name="lblLeg_Zim_PAYE_TemplateName" xml:space="preserve">
    <value>PAYE Template - Employees Earnings Upload</value>
  </data>
  <data name="lblLeg_Zim_SDL_DeclarationDesc" xml:space="preserve">
    <value>Quarterly report to declare the Standards Development Levy.</value>
  </data>
  <data name="lblLeg_Zim_SDL_DeclarationName" xml:space="preserve">
    <value>Standards Development Levy Report</value>
  </data>
  <data name="lblLegRSASalaryScheduleDesc" xml:space="preserve">
    <value>Issued to employees who are submitting claims to the Department of Labour.</value>
  </data>
  <data name="lblLegRSASalaryScheduleName" xml:space="preserve">
    <value>Salary Schedule</value>
  </data>
  <data name="lblLegRSASalaryScheduleNote" xml:space="preserve">
    <value>This report will need to be completed by the user. Due to the nature of the report, employers are required to fill in the employee's UIF remuneration received during the pay periods as requested by the 'Department of Labour'.</value>
  </data>
  <data name="lblLevel" xml:space="preserve">
    <value>Organisation Level</value>
  </data>
  <data name="lblLevelParamNameKey" xml:space="preserve">
    <value>Organisation level</value>
  </data>
  <data name="lblLoansReportDesc" xml:space="preserve">
    <value>Provides a listing of the employees Loans deductions for a chosen run.</value>
  </data>
  <data name="lblLoansReportName" xml:space="preserve">
    <value>Loans</value>
  </data>
  <data name="lblMalaysia_SOCSO_txt_fileDesc" xml:space="preserve">
    <value>SOCSO (Social Security Organization) contribution refers to the mandatory contributions made by both employers and employees in Malaysia to fund social security benefits provided by SOCSO. These contributions are primarily aimed at providing financial assistance and benefits to employees and their dependents in case of work-related injuries, disabilities, illnesses, or death.</value>
  </data>
  <data name="lblMalaysia_SOCSO_txt_fileName" xml:space="preserve">
    <value>SOCSO Monthly Text file</value>
  </data>
  <data name="lblMedicalReportDesc" xml:space="preserve">
    <value>Provides a listing of the employees Medical contributions for a chosen period.</value>
  </data>
  <data name="lblMedicalReportName" xml:space="preserve">
    <value>Medical</value>
  </data>
  <data name="lblMonthConditionalParamNameKey" xml:space="preserve">
    <value>Termination or suspension period</value>
  </data>
  <data name="lblMonthSelect" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="lblMTDOrCurrentParamNameKey" xml:space="preserve">
    <value>Run/Month</value>
  </data>
  <data name="lblMultipleActions" xml:space="preserve">
    <value>Multiple Actions</value>
  </data>
  <data name="lblMultipleCompanies" xml:space="preserve">
    <value>Multiple Companies</value>
  </data>
  <data name="lblMultipleUnits" xml:space="preserve">
    <value>Multiple Units</value>
  </data>
  <data name="lblNetPay" xml:space="preserve">
    <value>Include zero net pay payslips</value>
  </data>
  <data name="lblNetPaymentListingName" xml:space="preserve">
    <value>Net Payment Listing</value>
  </data>
  <data name="lblNewEngagementsandTerminationsDesc" xml:space="preserve">
    <value>Provides a listing of new engagements and terminated employees for a chosen period.</value>
  </data>
  <data name="lblNewEngagementsandTerminationsName" xml:space="preserve">
    <value>New Engagements and Terminations</value>
  </data>
  <data name="lblNig_Ann_Alt_LIRSDesc" xml:space="preserve">
    <value>This is the annual PAYE declaration form that employers submit to the revenue authorities. This report is not state specific and can be used by employers for states that do not have a specific H1 form template.</value>
  </data>
  <data name="lblNig_Ann_Alt_LIRSName" xml:space="preserve">
    <value>Alternative H1 Annual report (All States)</value>
  </data>
  <data name="lblNig_Ann_LIRS_PAYEDesc" xml:space="preserve">
    <value>Annual Tax report file for submission to the Lagos State Internal Revenue Service (LIRS) E-Tax portal. The file must be exported as a CSV file for import into the E-Tax portal.</value>
  </data>
  <data name="lblNig_Ann_LIRS_PAYEName" xml:space="preserve">
    <value>LIRS Annual Tax</value>
  </data>
  <data name="lblNIG_ITF_Form_5_Annual_ReturnDesc" xml:space="preserve">
    <value>Annual return for submission to the Industrial Training Fund by 1 April, reporting on the previous tax year.</value>
  </data>
  <data name="lblNIG_ITF_Form_5_Annual_ReturnName" xml:space="preserve">
    <value>ITF Form 5A</value>
  </data>
  <data name="lblNIG_OGIRS_PAYEDesc" xml:space="preserve">
    <value>Monthly tax report file for submission to the Ogun State Internal Revenue Service (OGIRS) E-Tax portal. The file must be exported to Excel for import into the E-Tax portal.</value>
  </data>
  <data name="lblNIG_OGIRS_PAYEName" xml:space="preserve">
    <value>OGIRS Monthly Tax</value>
  </data>
  <data name="lblNIG_Pension_Fund_NewDesc" xml:space="preserve">
    <value>Monthly report to declare contributions to the National Pension Scheme through the NIBSS online portal. The file must be exported to Excel. The first sheet will display the grand total for all employees and the separate sheets will display the subtotals per PFA Code.</value>
  </data>
  <data name="lblNIG_Pension_Fund_NewName" xml:space="preserve">
    <value>Generic Pension Fund Report</value>
  </data>
  <data name="lblNigeria_ABUJA_FCT_IRS_PAYEDesc" xml:space="preserve">
    <value>Monthly tax report for submission to FCT-IRS. The file must be exported to Excel into the E-Services.</value>
  </data>
  <data name="lblNigeria_ABUJA_FCT_IRS_PAYEName" xml:space="preserve">
    <value>FCT-IRS Monthly PAYE Schedule</value>
  </data>
  <data name="lblNigeria_Annual_OGIRS_PAYEDesc" xml:space="preserve">
    <value>Annual Tax report file for submission to the Ogun State Internal Revenue Service (OGIRS) E-Tax portal. The file must be exported to Excel for Import into the E-Tax portal.</value>
  </data>
  <data name="lblNigeria_Annual_OGIRS_PAYEName" xml:space="preserve">
    <value>OGIRS Annual Tax</value>
  </data>
  <data name="lblNigeria_FCT-IRS Form_H1_AnnDesc" xml:space="preserve">
    <value>Annual tax report for submission to FCT-IRS. The file must be exported to Excel into the E-Services.</value>
  </data>
  <data name="lblNigeria_FCT-IRS Form_H1_AnnName" xml:space="preserve">
    <value>FCT-IRS Form - H1- Employer’s Annual Declaration and Certificate</value>
  </data>
  <data name="lblNigeria_LIRS_PAYEDesc" xml:space="preserve">
    <value>Monthly tax report file for submission to the Lagos State Internal Revenue Service (LIRS) E-Tax portal. The file must be exported as a CSV file for import into the E-Tax portal.</value>
  </data>
  <data name="lblNigeria_LIRS_PAYEName" xml:space="preserve">
    <value>LIRS Monthly Tax</value>
  </data>
  <data name="lblNoGLNumberParamNameKey" xml:space="preserve">
    <value>Only show components that have a GL code defined</value>
  </data>
  <data name="lblNonFormattedParamNameKey" xml:space="preserve">
    <value>Tick here if you want to view this report without formatting</value>
  </data>
  <data name="lblNonFormattedSelect" xml:space="preserve">
    <value>Non formatted</value>
  </data>
  <data name="lblNoReport" xml:space="preserve">
    <value>No Report Found</value>
  </data>
  <data name="lblNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="lblNoTrainingParamNameKey" xml:space="preserve">
    <value>Only display employees that did not attend any training courses in the above period?</value>
  </data>
  <data name="lblNT35_Unemployment_Insurance_FileDesc" xml:space="preserve">
    <value>The report displays the information and values related to employee terminations within a specific period.</value>
  </data>
  <data name="lblNT35_Unemployment_Insurance_FileName" xml:space="preserve">
    <value>Unemployment Insurance File</value>
  </data>
  <data name="lblOrdinaryDays" xml:space="preserve">
    <value>Ordinary Days</value>
  </data>
  <data name="lblOrdinaryHours" xml:space="preserve">
    <value>Ordinary Hours</value>
  </data>
  <data name="lblOrgUnitId" xml:space="preserve">
    <value>Org Unit</value>
  </data>
  <data name="lblOrgUnitIDParamNameKey" xml:space="preserve">
    <value>Organisation units</value>
  </data>
  <data name="lblOrgUnitIds" xml:space="preserve">
    <value>Org Unit</value>
  </data>
  <data name="lblOrgUnitParamNameKey" xml:space="preserve">
    <value>Organisation units</value>
  </data>
  <data name="lblOrgUnitsParamNameKey" xml:space="preserve">
    <value>Organisation units</value>
  </data>
  <data name="lblOrgUnitTotalsfor" xml:space="preserve">
    <value>Org. Unit Totals for</value>
  </data>
  <data name="lblotalsTypeParamNameKey" xml:space="preserve">
    <value>Run report with</value>
  </data>
  <data name="lblP10_Simplifed_ReturnDesc" xml:space="preserve">
    <value>Produces a monthly iTax CSV file to be imported into the monthly iTax macro file.</value>
  </data>
  <data name="lblP10_Simplifed_ReturnName" xml:space="preserve">
    <value>P10 Simplified Return</value>
  </data>
  <data name="lblPackageStructureBreakDown" xml:space="preserve">
    <value>Package Structure Breakdown</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="lblPaid" xml:space="preserve">
    <value>Paid?</value>
  </data>
  <data name="lblPaidParamNameKey" xml:space="preserve">
    <value>Exclude payslips for employees marked as paid? This applies to the run selected above.</value>
  </data>
  <data name="lblParentReport" xml:space="preserve">
    <value>Parent report</value>
  </data>
  <data name="lblPaymentDate" xml:space="preserve">
    <value>Payment Date</value>
  </data>
  <data name="lblPaymentDay" xml:space="preserve">
    <value>Payment Day</value>
  </data>
  <data name="lblPayMethodParamNameKey" xml:space="preserve">
    <value>Payment method</value>
  </data>
  <data name="lblPayPoint" xml:space="preserve">
    <value>Pay Point</value>
  </data>
  <data name="lblPayRate" xml:space="preserve">
    <value>Pay Rate</value>
  </data>
  <data name="lblPayRatesReportDesc" xml:space="preserve">
    <value>Provides a listing of the employees package in historical order or just the latest.</value>
  </data>
  <data name="lblPayRatesReportName" xml:space="preserve">
    <value>Pay Rates</value>
  </data>
  <data name="lblPayrollConsolidatedReconciliationReportName" xml:space="preserve">
    <value>Payroll Consolidated Reconciliation</value>
  </data>
  <data name="lblPayrollReconciliationReportDesc" xml:space="preserve">
    <value>Provides current figures or MTD figures of all employees components on the payslips. Typically used for reconciliation purposes.</value>
  </data>
  <data name="lblPayrollReconciliationReportName" xml:space="preserve">
    <value>Payroll Reconciliation</value>
  </data>
  <data name="lblPayRollRegDesc" xml:space="preserve">
    <value>Provides a compact view of employees' payslips with multiple payslips per page.</value>
  </data>
  <data name="lblPayRollRegName" xml:space="preserve">
    <value>Payroll Register</value>
  </data>
  <data name="lblPayslipAction" xml:space="preserve">
    <value>Payslip Action</value>
  </data>
  <data name="lblPayslipActionParamNameKey" xml:space="preserve">
    <value>Payslip actions</value>
  </data>
  <data name="lblPayslipActions" xml:space="preserve">
    <value>Payslip Actions</value>
  </data>
  <data name="lblPayslipsDesc" xml:space="preserve">
    <value>Provides a listing of the employees payslips for a chosen run.</value>
  </data>
  <data name="lblPayslipSettingsParamNameKey" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="lblPayslipsName" xml:space="preserve">
    <value>Payslips</value>
  </data>
  <data name="lblPerformanceJournalDesc" xml:space="preserve">
    <value>Provides a listing of employee performance journals for a chosen date range.</value>
  </data>
  <data name="lblPerformanceJournalName" xml:space="preserve">
    <value>Performance Journal</value>
  </data>
  <data name="lblPeriodCode" xml:space="preserve">
    <value>Period Code</value>
  </data>
  <data name="lblPeriodCodeParamNameKey" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="lblPeriodEndDate" xml:space="preserve">
    <value>Period End Date</value>
  </data>
  <data name="lblPeriodStartDate" xml:space="preserve">
    <value>Period Start Date</value>
  </data>
  <data name="lblPersonal" xml:space="preserve">
    <value>Personal</value>
  </data>
  <data name="lblPhysAddress" xml:space="preserve">
    <value>Phys. Add</value>
  </data>
  <data name="lblPosition" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="lblPositionId" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="lblPositionIds" xml:space="preserve">
    <value>Positions</value>
  </data>
  <data name="lblPositionParamNameKey" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="lblpositionsParamNameKey" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="lblPrefName" xml:space="preserve">
    <value>Pref. Name</value>
  </data>
  <data name="lblPreviewParameters" xml:space="preserve">
    <value>Preview Parameters</value>
  </data>
  <data name="lblPrintOrgUnit" xml:space="preserve">
    <value>Print Org Unit</value>
  </data>
  <data name="lblProcessIDParamNameKey" xml:space="preserve">
    <value>Process</value>
  </data>
  <data name="lblProject" xml:space="preserve">
    <value>Project</value>
  </data>
  <data name="lblProjectDescription" xml:space="preserve">
    <value>Project Description</value>
  </data>
  <data name="lblProjectId" xml:space="preserve">
    <value>Project Costing</value>
  </data>
  <data name="lblProjectIds" xml:space="preserve">
    <value>Projects</value>
  </data>
  <data name="lblProjectParamNameKey" xml:space="preserve">
    <value>Project</value>
  </data>
  <data name="lblProjectTotalsfor" xml:space="preserve">
    <value>Project Totals for</value>
  </data>
  <data name="lblQty" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="lblRate" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="lblRegion" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="lblRegionId" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="lblRegionIds" xml:space="preserve">
    <value>Regions</value>
  </data>
  <data name="lblRegionParamNameKey" xml:space="preserve">
    <value>Region/location</value>
  </data>
  <data name="lblReport" xml:space="preserve">
    <value>Report</value>
  </data>
  <data name="lblReportAudit" xml:space="preserve">
    <value>ReportAudit</value>
  </data>
  <data name="lblReportAuditHeader" xml:space="preserve">
    <value>Report Audit Trail</value>
  </data>
  <data name="lblReportCategory" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="lblReportCategoryRequired" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="lblReportDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="lblReportDisableConfirmation" xml:space="preserve">
    <value>Are you sure you want to disable this report?</value>
  </data>
  <data name="lblReportDisabled" xml:space="preserve">
    <value>Disabled</value>
  </data>
  <data name="lblReportDisabledHasDependenies" xml:space="preserve">
    <value>Report has dependencies and has been marked as disabled.</value>
  </data>
  <data name="lblReportEditable" xml:space="preserve">
    <value>Editable</value>
  </data>
  <data name="lblReportEdited" xml:space="preserve">
    <value>Edited</value>
  </data>
  <data name="lblReportEnable" xml:space="preserve">
    <value>Enabled</value>
  </data>
  <data name="lblReportEnableConfirmation" xml:space="preserve">
    <value>Are you sure you want to enable this report?</value>
  </data>
  <data name="lblReportEnabled" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="lblReportHistoryHeader" xml:space="preserve">
    <value>My reports for the last 8 days</value>
  </data>
  <data name="lblReportLevel" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="lblReportList" xml:space="preserve">
    <value>Report List</value>
  </data>
  <data name="lblReportName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lblReportNameRequired" xml:space="preserve">
    <value>Please enter the name of the report.</value>
  </data>
  <data name="lblReportNotExists" xml:space="preserve">
    <value>Report does not exist</value>
  </data>
  <data name="lblReportPageTitle" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="lblReportParamTitle" xml:space="preserve">
    <value>Report Parameters</value>
  </data>
  <data name="lblReportPath" xml:space="preserve">
    <value>Report content format</value>
  </data>
  <data name="lblReportPeriodEnding" xml:space="preserve">
    <value>Report Period Ending:</value>
  </data>
  <data name="lblReportSaveChanges" xml:space="preserve">
    <value>Please save report changes first.</value>
  </data>
  <data name="lblReportsNoDataText" xml:space="preserve">
    <value>No reports have been made available for this category.</value>
  </data>
  <data name="lblReportSubCategory" xml:space="preserve">
    <value>Subcategory</value>
  </data>
  <data name="lblReportSubmitBtnText" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="lblReportType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="lblReportTypeActual" xml:space="preserve">
    <value>Actual</value>
  </data>
  <data name="lblReportTypeArchive" xml:space="preserve">
    <value>Archived</value>
  </data>
  <data name="lblReportTypeParamNameKey" xml:space="preserve">
    <value>Budget period</value>
  </data>
  <data name="lblReportTypeSnapShot" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="lblReportViewType" xml:space="preserve">
    <value>View Type</value>
  </data>
  <data name="lblResetMessage" xml:space="preserve">
    <value>Are you sure you want to reset?</value>
  </data>
  <data name="lblResetSuccessful" xml:space="preserve">
    <value>Report has been successfully reset</value>
  </data>
  <data name="lblrpt_sp_MOZ_M20_HDesc" xml:space="preserve">
    <value>Annual declaration of accounting and tax information.</value>
  </data>
  <data name="lblrpt_sp_MOZ_M20_HName" xml:space="preserve">
    <value>M/20 H</value>
  </data>
  <data name="lblRptDateRange" xml:space="preserve">
    <value>Date Range</value>
  </data>
  <data name="lblRun" xml:space="preserve">
    <value>Run</value>
  </data>
  <data name="lblRunAndPeriod" xml:space="preserve">
    <value>Run/Period</value>
  </data>
  <data name="lblRunId" xml:space="preserve">
    <value>Company Run</value>
  </data>
  <data name="lblRunIdParamNameKey" xml:space="preserve">
    <value>Run</value>
  </data>
  <data name="lblRunOn" xml:space="preserve">
    <value>Run on</value>
  </data>
  <data name="lblRunOrMonthOrYtd" xml:space="preserve">
    <value>Run/Month/YTD</value>
  </data>
  <data name="lblRunSelect" xml:space="preserve">
    <value>Run</value>
  </data>
  <data name="lblSA_UI_19Desc" xml:space="preserve">
    <value>UIF declaration form issued to employees on termination of service (New engagements and changes are automatically declared on a monthly bases).</value>
  </data>
  <data name="lblSA_UI_19Name" xml:space="preserve">
    <value>UI-19</value>
  </data>
  <data name="lblSaveDescription" xml:space="preserve">
    <value>Please enter a description of what changes were made.</value>
  </data>
  <data name="lblSaveMessage" xml:space="preserve">
    <value>Are you sure you want to save?</value>
  </data>
  <data name="lblSavingsReportDesc" xml:space="preserve">
    <value>Provides a listing of the employees Savings deductions for a chosen run.</value>
  </data>
  <data name="lblSavingsReportName" xml:space="preserve">
    <value>Savings</value>
  </data>
  <data name="lblScoresHistoryDesc" xml:space="preserve">
    <value>Returns all rating details and total scores for selected process</value>
  </data>
  <data name="lblScoresHistoryName" xml:space="preserve">
    <value>Scores History</value>
  </data>
  <data name="lblSearchPlaceHolderText" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="lblSecurityReportName" xml:space="preserve">
    <value>Security Report</value>
  </data>
  <data name="lblSelect" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="lblSelectExtract" xml:space="preserve">
    <value>Select Extract</value>
  </data>
  <data name="lblSettings" xml:space="preserve">
    <value>Selection options</value>
  </data>
  <data name="lblShowCDiffParamNameKey" xml:space="preserve">
    <value>Show differences only</value>
  </data>
  <data name="lblShowColumnsParamKey" xml:space="preserve">
    <value>Additional report fields</value>
  </data>
  <data name="lblShowDifferenceDiff" xml:space="preserve">
    <value>Difference (available for monthly runs only)</value>
  </data>
  <data name="lblShowExtraColParamNameKey" xml:space="preserve">
    <value>Additional report fields</value>
  </data>
  <data name="lblShowOpenPeriodsOnly" xml:space="preserve">
    <value>Show Only Open Periods</value>
  </data>
  <data name="lblShowSumDifferenceSum" xml:space="preserve">
    <value>Sum</value>
  </data>
  <data name="lblShowSumDiffParamNameKey" xml:space="preserve">
    <value>Sum/Difference</value>
  </data>
  <data name="lblShowTotalsPerNameKey" xml:space="preserve">
    <value>Show totals per</value>
  </data>
  <data name="lblSnapshotIDParamNameKey" xml:space="preserve">
    <value>Select budget snapshot</value>
  </data>
  <data name="lblSnapshotParamNameKey" xml:space="preserve">
    <value>Select budget snapshot</value>
  </data>
  <data name="lblSortEmpLastName" xml:space="preserve">
    <value>Employee Last Name</value>
  </data>
  <data name="lblSortEmpName" xml:space="preserve">
    <value>Employee Name</value>
  </data>
  <data name="lblSortEmpNumber" xml:space="preserve">
    <value>Employee Number</value>
  </data>
  <data name="lblSortOrder" xml:space="preserve">
    <value>Sort Order</value>
  </data>
  <data name="lblSortOrderParamNameKey" xml:space="preserve">
    <value>Sort order</value>
  </data>
  <data name="lblSpain_Average_Headcount_ReportDesc" xml:space="preserve">
    <value>The Average Headcount per Company report is an operational report used by Compliance/HR team to report the average headcount of a company in a given timeframe.</value>
  </data>
  <data name="lblSpain_Average_Headcount_ReportName" xml:space="preserve">
    <value>Average Headcount per Workplace</value>
  </data>
  <data name="lblSpain_Leaves_ReportDesc" xml:space="preserve">
    <value>The Leaves Report provides a comprehensive list of all employee leaves recorded in the platform. It includes key details such as the start date, end date, type of leave, and total duration. The report is particularly useful for identifying sick leaves exceeding 30 days, as HR needs to determine which cases require salary top-ups.</value>
  </data>
  <data name="lblSpain_Leaves_ReportName" xml:space="preserve">
    <value>Leaves report</value>
  </data>
  <data name="lblStandardReportTabName" xml:space="preserve">
    <value>Standard</value>
  </data>
  <data name="lblStartDateParamNameKey" xml:space="preserve">
    <value>Start date</value>
  </data>
  <data name="lblStatistics_SA_BreakdownDesc" xml:space="preserve">
    <value>Statistics SA Breakdown</value>
  </data>
  <data name="lblStatistics_SA_BreakdownName" xml:space="preserve">
    <value>Statistics SA Breakdown</value>
  </data>
  <data name="lblTaxCode" xml:space="preserve">
    <value>Tax Code</value>
  </data>
  <data name="lblTaxCodeName" xml:space="preserve">
    <value>Tax Code</value>
  </data>
  <data name="lblTaxCountries" xml:space="preserve">
    <value>Tax Countries</value>
  </data>
  <data name="lblTaxIDParamNameKey" xml:space="preserve">
    <value>Tax ID</value>
  </data>
  <data name="lblTaxRefNumber" xml:space="preserve">
    <value>Tax Ref. Number</value>
  </data>
  <data name="lblTaxStatus" xml:space="preserve">
    <value>Tax Status</value>
  </data>
  <data name="lblTemplateDisplayName" xml:space="preserve">
    <value>Please enter a template display name.</value>
  </data>
  <data name="lblTemplateDisplayNameInvalidFormat" xml:space="preserve">
    <value>Special characters not allowed in template display name</value>
  </data>
  <data name="lblTemplateDisplayNameRequired" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lblTerminatedFromDateParamKey" xml:space="preserve">
    <value>Include terminations from this date onwards</value>
  </data>
  <data name="lblTerminatedParamNameKey" xml:space="preserve">
    <value>Include terminations</value>
  </data>
  <data name="lblTerminationDate" xml:space="preserve">
    <value>Termination Date</value>
  </data>
  <data name="lblToDateNameKey" xml:space="preserve">
    <value>Include up to this date</value>
  </data>
  <data name="lblTotal" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="lblTotalCreditorParamKey" xml:space="preserve">
    <value>Show totals per creditor</value>
  </data>
  <data name="lblTotalEarnings" xml:space="preserve">
    <value>Total Earnings</value>
  </data>
  <data name="lblTotalsFundParamDescKey" xml:space="preserve">
    <value>show totals per fund?</value>
  </data>
  <data name="lblTotalsFundParamNameKey" xml:space="preserve">
    <value>Show totals per fund?</value>
  </data>
  <data name="lblTotalsParamNameKey" xml:space="preserve">
    <value>Show totals per scheme</value>
  </data>
  <data name="lblTotalsTypeParamNameKey" xml:space="preserve">
    <value>Show totals per leave type</value>
  </data>
  <data name="lblTrainingReportDesc" xml:space="preserve">
    <value>Provides a list of employees that have or have not attended training courses for a chosen period.</value>
  </data>
  <data name="lblTrainingReportName" xml:space="preserve">
    <value>Employee Training</value>
  </data>
  <data name="lblTransactionsReportDesc" xml:space="preserve">
    <value>Provides a list of all leave transactions for a specified period.</value>
  </data>
  <data name="lblTransactionsReportName" xml:space="preserve">
    <value>Leave Transactions</value>
  </data>
  <data name="lblTunisia_Annual_Individual_WHTDesc" xml:space="preserve">
    <value>Annual employee withholding tax certificate</value>
  </data>
  <data name="lblTunisia_Annual_Individual_WHTName" xml:space="preserve">
    <value>Employee Withholding Tax Certificate</value>
  </data>
  <data name="lblTunisiaEmployerAnnualTaxReturnReportDesc" xml:space="preserve">
    <value>The report is used to complete Annexure 1 of the employer annual return</value>
  </data>
  <data name="lblTunisiaEmployerAnnualTaxReturnReportName" xml:space="preserve">
    <value>Employer Annual Tax Return - Annexe 1</value>
  </data>
  <data name="lblTypeFigures" xml:space="preserve">
    <value>Figures</value>
  </data>
  <data name="lblTypeHeadCount" xml:space="preserve">
    <value>Head Count</value>
  </data>
  <data name="lblUI19ReportDesc" xml:space="preserve">
    <value>UIF declaration form issued to employees on termination of service (New engagements and changes are automatically declared electronically on a monthly basis).</value>
  </data>
  <data name="lblUI19ReportName" xml:space="preserve">
    <value>UI-19</value>
  </data>
  <data name="lblUIFNumber" xml:space="preserve">
    <value>UIF Number</value>
  </data>
  <data name="lblUIFSalaryScheduleDesc" xml:space="preserve">
    <value>The Salary Schedule may be requested by the UIF to verify or update an employee’s history information.</value>
  </data>
  <data name="lblUIFSalaryScheduleName" xml:space="preserve">
    <value>UIF Salary Schedule</value>
  </data>
  <data name="lblUK_Ann_P60Desc" xml:space="preserve">
    <value>Give a P60 tax certificate to all employees still employed on the last day of the tax year (5 April). The P60 is an annual summary of payroll information required to file a tax return.</value>
  </data>
  <data name="lblUK_Ann_P60Name" xml:space="preserve">
    <value>P60 Employee Tax Certificate</value>
  </data>
  <data name="lblUK_Apprenticeship_Levy_DetailDesc" xml:space="preserve">
    <value>This report displays the total amounts used to calculate the Apprenticeship Levy per month for the selected employer.</value>
  </data>
  <data name="lblUK_Apprenticeship_Levy_DetailName" xml:space="preserve">
    <value>Apprenticeship Levy Monthly Detail</value>
  </data>
  <data name="lblUK_Apprenticeship_Levy_QuarterlyDesc" xml:space="preserve">
    <value>This report displays the total amounts used to calculate the Apprenticeship Levy per quarter for the selected employer.</value>
  </data>
  <data name="lblUK_Apprenticeship_Levy_QuarterlyName" xml:space="preserve">
    <value>Apprenticeship Levy Quarterly Detail</value>
  </data>
  <data name="lblUK_P11DDesc" xml:space="preserve">
    <value>Annual benefits in kind report for submission to HM Revenue and Customs ( HMRC )</value>
  </data>
  <data name="lblUK_P11DName" xml:space="preserve">
    <value>P11D - old version</value>
  </data>
  <data name="lblUK_P30_QuarterlyDesc" xml:space="preserve">
    <value>A summary of the total HMRC liability due for the selected quarter. Print the P30 at the end of the quarter after submitting the FPS and/or EPS.</value>
  </data>
  <data name="lblUK_P30_QuarterlyName" xml:space="preserve">
    <value>P30 Quarterly Employer Summary</value>
  </data>
  <data name="lblUK_P30Desc" xml:space="preserve">
    <value>A summary of the total HMRC liability due for the selected month. Print the P30 at the end of the month after submitting the FPS and/or EPS.</value>
  </data>
  <data name="lblUK_P30Name" xml:space="preserve">
    <value>P30 Monthly Employer Summary</value>
  </data>
  <data name="lblUK_P32_MonthlyDesc" xml:space="preserve">
    <value>The P32 Monthly Employer Payment Detail report is used by employers to report the details of their payroll activities to the HMRC on a monthly basis. It includes employee income tax deductions (PAYE), National Insurance contributions (NICs), and other relevant payroll details for the month.</value>
  </data>
  <data name="lblUK_P32_MonthlyName" xml:space="preserve">
    <value>P32 Monthly Employer Payment Detail</value>
  </data>
  <data name="lblUK_P32_QuarterlyDesc" xml:space="preserve">
    <value>The P32 Quarterly Employer Payment Detail report is a total summary of the amounts you have paid to the HMRC per quarter. It includes employee income tax deductions (PAYE), National Insurance contributions (NICs), and other relevant payroll details for the quarter.</value>
  </data>
  <data name="lblUK_P32_QuarterlyName" xml:space="preserve">
    <value>P32 Quarterly Employer Payment Detail</value>
  </data>
  <data name="lblUnit" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="lblUploadReport" xml:space="preserve">
    <value>Upload Report</value>
  </data>
  <data name="lblUploadReportHeader" xml:space="preserve">
    <value>Upload report</value>
  </data>
  <data name="lblUser" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="lblUsername" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="lblValueRequired" xml:space="preserve">
    <value>Value is required</value>
  </data>
  <data name="lblVersions" xml:space="preserve">
    <value>Versions</value>
  </data>
  <data name="lblViewDiffCurrencyParamNameKey" xml:space="preserve">
    <value>View in different currency</value>
  </data>
  <data name="lblViewEmpHomeCurrencyParamNameKey" xml:space="preserve">
    <value>View this report based on employees attached to a specific currency</value>
  </data>
  <data name="lblViewInHomeCurrency" xml:space="preserve">
    <value>Home Currency</value>
  </data>
  <data name="lblViewInHomeCurrencyCheckBoxNameKey" xml:space="preserve">
    <value>View in home currency</value>
  </data>
  <data name="lblWorkplaceParamNameKey" xml:space="preserve">
    <value>Workplace</value>
  </data>
  <data name="lblYTDAmount" xml:space="preserve">
    <value>YTD Amount</value>
  </data>
  <data name="lblYTDParamNameKey" xml:space="preserve">
    <value>Draw this report for the YTD for a specific run</value>
  </data>
  <data name="lblYtdSelect" xml:space="preserve">
    <value>Year to date</value>
  </data>
  <data name="Leg_Malaysia_EA_FormDesc" xml:space="preserve">
    <value>The "EA Form" is an important document used for tax purposes. It is an annual tax statement provided by employers to their employees, detailing the employee's earnings, allowances, deductions, and tax contributions for the year.</value>
  </data>
  <data name="Leg_Malaysia_EA_FormName" xml:space="preserve">
    <value>EA Form</value>
  </data>
  <data name="Leg_Malaysia_PCB2Desc" xml:space="preserve">
    <value>PCB2 (Potongan Cukai Berjadual 2) refers to the Scheduled Tax Deduction Form 2 in Malaysia. This form is used for the deduction of monthly tax (potongan cukai bulanan) from an employee's salary. PCB2 specifically applies to employees whose income is subject to scheduled tax deductions based on Malaysia's income tax laws.</value>
  </data>
  <data name="Leg_Malaysia_PCB2Name" xml:space="preserve">
    <value>PCB2</value>
  </data>
  <data name="Leg_Malaysia_Tabung_HajiDesc" xml:space="preserve">
    <value>Tabung Haji form refers to a form related to the payroll contributions made to the Lembaga Tabung Haji. This form is used by employers to facilitate the automatic deduction of employee contributions to their Tabung Haji accounts on a monthly basis.</value>
  </data>
  <data name="Leg_Malaysia_Tabung_HajiName" xml:space="preserve">
    <value>Tabung Haji</value>
  </data>
  <data name="Leg_Malaysia_Zakat_KL_ReportDesc" xml:space="preserve">
    <value>A Zakat form is used by individuals or organizations to systematically calculate their Zakat liability based on Islamic principles. It helps in documenting the assets, deductions, and the calculated Zakat amount, ensuring compliance with religious obligations. Kuala Lumpur Zakat region only.</value>
  </data>
  <data name="Leg_Malaysia_Zakat_KL_ReportName" xml:space="preserve">
    <value>Zakat KL</value>
  </data>
  <data name="Leg_Malaysia_Zakat_SelagnorDesc" xml:space="preserve">
    <value>This document is a payment form for the Berkat Scheme from the Selangor Zakat Board (MAIS). It includes details such as the employer's name and address, the number of employees, and the total payment amount to be made to MAIS before the 10th of each month. The form provides instructions for filling it out correctly, such as arranging employee names in alphabetical order and making the payment by checking payable details to MAIS.</value>
  </data>
  <data name="Leg_Malaysia_Zakat_SelagnorName" xml:space="preserve">
    <value>Zakat Selangor</value>
  </data>
  <data name="Leg_Tanzania_Electronic_Monthly_PAYEDesc" xml:space="preserve">
    <value>Monthly PAYE import file used to import into macro sheet, for online submission.</value>
  </data>
  <data name="Leg_Tanzania_Electronic_Monthly_PAYEName" xml:space="preserve">
    <value>Electronic Template for Monthly PAYE</value>
  </data>
  <data name="msgResetFail" xml:space="preserve">
    <value>Can't reset Bureau Reports</value>
  </data>
  <data name="PreparingReport" xml:space="preserve">
    <value>Preparing report.</value>
  </data>
  <data name="ReportDesigner" xml:space="preserve">
    <value>Report Designer</value>
  </data>
  <data name="reportDetails" xml:space="preserve">
    <value>Report details</value>
  </data>
  <data name="ReportHistory" xml:space="preserve">
    <value>Report History</value>
  </data>
  <data name="ReportUploadCancelText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ReportUploadDetails" xml:space="preserve">
    <value>Report details</value>
  </data>
  <data name="ReportUploadHeading" xml:space="preserve">
    <value>Report upload</value>
  </data>
  <data name="ReportUploadReportType" xml:space="preserve">
    <value>Report type</value>
  </data>
  <data name="ReportUploadSubheading" xml:space="preserve">
    <value>Report file</value>
  </data>
  <data name="ReportUploadText" xml:space="preserve">
    <value>Upload</value>
  </data>
</root>