<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AccessDenied" xml:space="preserve">
    <value>Vous n'avez pas l'autorisation de modifier le dossier</value>
  </data>
  <data name="AddressLine1" xml:space="preserve">
    <value>Nom de la rue / Numéro d'adresse postale</value>
  </data>
  <data name="AddressLine2" xml:space="preserve">
    <value>Banlieue ou district / Agence postale</value>
  </data>
  <data name="AddressLine3" xml:space="preserve">
    <value>Ville ou commune / Ville postale</value>
  </data>
  <data name="lblAddress1" xml:space="preserve">
    <value>Adresse Ligne 1</value>
  </data>
  <data name="lblAddress2" xml:space="preserve">
    <value>Adresse Ligne 2</value>
  </data>
  <data name="lblAddressDetails" xml:space="preserve">
    <value>Détails de l'adresse</value>
  </data>
  <data name="lblAddressPostal1" xml:space="preserve">
    <value>Code postale</value>
  </data>
  <data name="lblAddressPostal2" xml:space="preserve">
    <value>Poste</value>
  </data>
  <data name="lblAddressPostal3" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="lblAddressType" xml:space="preserve">
    <value>Type d'adresse</value>
  </data>
  <data name="lblCareofAddress" xml:space="preserve">
    <value>Soin de l'intermédiaire</value>
  </data>
  <data name="lblCityPostal" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="lblCityTown" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="lblCode" xml:space="preserve">
    <value>Code postal</value>
  </data>
  <data name="lblCodePostal" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lblCompanyImplantation" xml:space="preserve">
    <value>Implantation de l'entreprise</value>
  </data>
  <data name="lblComplexName" xml:space="preserve">
    <value>Nom du complexe</value>
  </data>
  <data name="lblCountry" xml:space="preserve">
    <value>Pays</value>
  </data>
  <data name="lblCountryPostal" xml:space="preserve">
    <value>Pays</value>
  </data>
  <data name="lblDistributionService" xml:space="preserve">
    <value>Service de distribution</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Numéro d'employé</value>
  </data>
  <data name="lblIsCareofAddress" xml:space="preserve">
    <value>Autre adresse?</value>
  </data>
  <data name="lblPhysicalAddress" xml:space="preserve">
    <value>Physique</value>
  </data>
  <data name="lblPostalAddress" xml:space="preserve">
    <value>Adresse Postal</value>
  </data>
  <data name="lblProvince" xml:space="preserve">
    <value>Département</value>
  </data>
  <data name="lblProvincePostal" xml:space="preserve">
    <value>Région</value>
  </data>
  <data name="lblSameAsPostal" xml:space="preserve">
    <value>L'adresse postale est-elle la même que l'adresse physique?</value>
  </data>
  <data name="lblSpecialServices" xml:space="preserve">
    <value>Services spéciaux</value>
  </data>
  <data name="lblStreetName" xml:space="preserve">
    <value>Nom de la rue</value>
  </data>
  <data name="lblStreetNum" xml:space="preserve">
    <value>Numéro de la rue</value>
  </data>
  <data name="lblSuburb" xml:space="preserve">
    <value>Quartier</value>
  </data>
  <data name="lblUnitNumber" xml:space="preserve">
    <value>N° App</value>
  </data>
</root>