<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ExtractFile" xml:space="preserve">
    <value>Extraire un fichier</value>
  </data>
  <data name="lblAltLanguageParamNameKey" xml:space="preserve">
    <value>Voulez-vous voir les noms des composants dans une autre langue ?</value>
  </data>
  <data name="lblArchiveIDParamNameKey" xml:space="preserve">
    <value>Sélectionner un budget archivé</value>
  </data>
  <data name="lblArchiveParamNameKey" xml:space="preserve">
    <value>Sélectionner un budget archivé</value>
  </data>
  <data name="lblBudgetReportDesc" xml:space="preserve">
    <value>Visualisez une seule période budgétaire à la fois. (instantané, archives ou chiffres réels) Aucune comparaison n'est faite dans ce rapport</value>
  </data>
  <data name="lblBudgetReportName" xml:space="preserve">
    <value>Rapport budgétaire</value>
  </data>
  <data name="lblBurkinaFasoDetailedFSPDesc" xml:space="preserve">
    <value>Un rapport détaillé qui indique l’assiette des cotisations ainsi que la cotisation due pour le fonds de soutien patriotique (FSP).</value>
  </data>
  <data name="lblBurkinaFasoDetailedFSPName" xml:space="preserve">
    <value>Etat annexe de la declaration de la retenue obligatoire sur les salaires des agents publics et des travailleurs du secteur prive</value>
  </data>
  <data name="lblBurkinaFasoSummaryFSPDesc" xml:space="preserve">
    <value>Un rapport récapitulatif de la contribution mensuelle au FSP.</value>
  </data>
  <data name="lblBurkinaFasoSummaryFSPName" xml:space="preserve">
    <value>Declaration de la retenue obligatoire sur les salaires des agents publics et des travailleurs du secteur prive</value>
  </data>
  <data name="lblCollapseAllBtn" xml:space="preserve">
    <value>Effondrement</value>
  </data>
  <data name="lblCompanyLeaveDetailsIDParamNameKey" xml:space="preserve">
    <value>Régime de congés</value>
  </data>
  <data name="lblComponentCodesParamNameKey" xml:space="preserve">
    <value>Liste des composants</value>
  </data>
  <data name="lblComponentIDParamNameKey" xml:space="preserve">
    <value>Composants</value>
  </data>
  <data name="lblComponentPostedUnitsDesc" xml:space="preserve">
    <value>Similaire au rapport de synthèse des composantes, ce rapport fournit toutefois des détails spécifiques concernant les unités/heures/jours affichés pour chaque composante</value>
  </data>
  <data name="lblComponentReportDesc" xml:space="preserve">
    <value>Ce rapport fournit une liste de chiffres par mois ou par cycle pour un employé, une ou plusieurs unités organisationnelles ou pour toutes les unités d'un niveau particulier.</value>
  </data>
  <data name="lblComponentReportName" xml:space="preserve">
    <value>Rapport sur les composantes</value>
  </data>
  <data name="lblComponentTotalsParamNameKey" xml:space="preserve">
    <value>Afficher les totaux par composant ?</value>
  </data>
  <data name="lblComponentVarianceReport-GoingAcrossDesc" xml:space="preserve">
    <value>Fournit une liste verticale comparative, mois par mois ou en continu, de tous les chiffres des composants pour une période sélectionnée par employé.</value>
  </data>
  <data name="lblComponentVarianceReport-GoingAcrossName" xml:space="preserve">
    <value>Rapport sur les écarts de composants - Traverser</value>
  </data>
  <data name="lblComponentVarianceReportDesc" xml:space="preserve">
    <value>Fournit une liste comparative, d'un mois à l'autre ou d'une exécution à l'autre, de tous les chiffres des composants pour une période sélectionnée par employé et comprend une colonne de différence.</value>
  </data>
  <data name="lblComponentVarianceReportName" xml:space="preserve">
    <value>Rapport sur les écarts de composants</value>
  </data>
  <data name="lblComponentVarianceTotalsReportDesc" xml:space="preserve">
    <value>Fournit une liste comparative, d'un mois à l'autre ou d'une exécution à l'autre, côte à côte de tous les chiffres des composants pour une période sélectionnée.</value>
  </data>
  <data name="lblComponentVarianceTotalsReportName" xml:space="preserve">
    <value>Rapport sur les totaux d'écarts des composants</value>
  </data>
  <data name="lblConsolidatedPayrollReconciliationReportDesc" xml:space="preserve">
    <value>Fournit les chiffres mensuels de toutes les composantes des employés pour les entreprises sélectionnées au sein d'un groupe.</value>
  </data>
  <data name="lblConsolidatedPayrollReconciliationReportName" xml:space="preserve">
    <value>Rapport consolidé de rapprochement des salaires</value>
  </data>
  <data name="lblCostingPayrollReconcilliationReportDesc" xml:space="preserve">
    <value>Fournit un rapport de réconciliation de la paie avec les détails de chaque centre de coûts par employé.</value>
  </data>
  <data name="lblCostingPayrollReconcilliationReportName" xml:space="preserve">
    <value>Rapport de rapprochement des coûts de la masse salariale</value>
  </data>
  <data name="lblCurrencyIDParamNameKey" xml:space="preserve">
    <value>Extraire des fichiers bancaires configurés dans des devises spécifiques</value>
  </data>
  <data name="lblDefaultCurrencyParamNameKey" xml:space="preserve">
    <value>Extraire des fichiers bancaires configurés dans des devises spécifiques</value>
  </data>
  <data name="lblDivisionParamNameKey" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="lblDontGroupByTotals" xml:space="preserve">
    <value>Pas de totaux</value>
  </data>
  <data name="lblDynamicEmployeeDetailsDesc" xml:space="preserve">
    <value>Fournit une liste de divers champs prédéfinis pour les employés qui peuvent être sélectionnés par un utilisateur.</value>
  </data>
  <data name="lblEmployeeListingDesc" xml:space="preserve">
    <value>Fournit une liste des employés.</value>
  </data>
  <data name="lblEmployeePayslipName" xml:space="preserve">
    <value>Fiches de paie</value>
  </data>
  <data name="lblEmpNoParamNameKey" xml:space="preserve">
    <value>Numéro d'employé</value>
  </data>
  <data name="lblEndDateParamNameKey" xml:space="preserve">
    <value>Date de fin</value>
  </data>
  <data name="lblExclDefSortParamNameKey" xml:space="preserve">
    <value>Exclure le tri par unité org., point de paiement et projet (ne trier que sur l'option sélectionnée ci-dessus)</value>
  </data>
  <data name="lblExclOrgUnitGroupingParamNameKey" xml:space="preserve">
    <value>Exclure le regroupement d'unités organisationnelles ?</value>
  </data>
  <data name="lblExcludeEESParamNameKey" xml:space="preserve">
    <value>Inclure uniquement les employés qui n'ont PAS d'adresse électronique ?</value>
  </data>
  <data name="lblFavouriteReportTabName" xml:space="preserve">
    <value>Favoris</value>
  </data>
  <data name="lblFilledCountTypeParamNameKey" xml:space="preserve">
    <value>Rempli comptent comme</value>
  </data>
  <data name="lblFilledCurrentCount" xml:space="preserve">
    <value>Remplir le courant</value>
  </data>
  <data name="lblFilledEndOfPeriodCount" xml:space="preserve">
    <value>Remplir la fin de la période</value>
  </data>
  <data name="lblFirstName" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="lblfkReportIDParamNameKey" xml:space="preserve">
    <value>Ne visualiser que les employés qui sont attachés à cette fiche de paie</value>
  </data>
  <data name="lblForeingCurrencyParamNameKey" xml:space="preserve">
    <value>Monnaie</value>
  </data>
  <data name="lblFormatPerLine" xml:space="preserve">
    <value>Par ligne</value>
  </data>
  <data name="lblFormatPivotHorizontally" xml:space="preserve">
    <value>Pivoter horizontalement</value>
  </data>
  <data name="lblFormatSelectionNotSupported" xml:space="preserve">
    <value>Cette option n'est pas prise en charge</value>
  </data>
  <data name="lblFromDateNameKey" xml:space="preserve">
    <value>Inclure les résiliations à partir de cette date</value>
  </data>
  <data name="lblGeneralLedgerReportName" xml:space="preserve">
    <value>Le rapport 'Grand livre général'</value>
  </data>
  <data name="lblGroupByComponent" xml:space="preserve">
    <value>Composant</value>
  </data>
  <data name="lblHeadcountComparisonDesc" xml:space="preserve">
    <value>Planifier la future période budgétaire en comparant le budget approuvé précédent (archivé) avec le budget actuel tel quel et les changements planifiés / prévus (nouvelle période)</value>
  </data>
  <data name="lblHeadcountComparisonName" xml:space="preserve">
    <value>Comparaison des effectifs</value>
  </data>
  <data name="lblHideDetailParamNameKey" xml:space="preserve">
    <value>Cacher les détails?</value>
  </data>
  <data name="lblhideRateParamNameKey" xml:space="preserve">
    <value>Masquer le noteur</value>
  </data>
  <data name="lblHistory" xml:space="preserve">
    <value>Historique</value>
  </data>
  <data name="lblIncludeEngTerm_InThisPeriodParamNameKey" xml:space="preserve">
    <value>Inclure les nouveaux engagements et les résiliations saisis au cours de cette période</value>
  </data>
  <data name="lblIncludeZeroNetPayParamNameKey" xml:space="preserve">
    <value>Inclure les fiches de paie à salaire net zéro ?</value>
  </data>
  <data name="lblIsTotalsParamNameKey" xml:space="preserve">
    <value>Exécuter le rapport</value>
  </data>
  <data name="lblIsTotalsPerEmp" xml:space="preserve">
    <value>Totaux par employé</value>
  </data>
  <data name="lblIsTotalsPerOrgUnit" xml:space="preserve">
    <value>Totaux par unité d'organisation</value>
  </data>
  <data name="lblLeg_Benin_Monthly_IRPP_TSDesc" xml:space="preserve">
    <value>Un rapport mensuel représentant la valeur mensuelle de l'ITS.</value>
  </data>
  <data name="lblLeg_Benin_Monthly_IRPP_TSName" xml:space="preserve">
    <value>Declaration IRPP - TS</value>
  </data>
  <data name="lblLeg_Benin_Monthly_VPSDesc" xml:space="preserve">
    <value>Une déclaration récapitulative des contributions VPS.</value>
  </data>
  <data name="lblLeg_Benin_Monthly_VPSName" xml:space="preserve">
    <value>Fiche de Declaration de VPS</value>
  </data>
  <data name="lblLeg_Burkina_Faso_Summary_DeclarationDesc" xml:space="preserve">
    <value>La déclaration récapitulative des salariés est un rapport complet de la sécurité sociale, qui classe les différents types de salariés et indique les différentes cotisations. Si l'entreprise compte moins de 20 salariés, la déclaration et le paiement doivent être remis trimestriellement, si elle compte plus de 20 salariés, la déclaration est remise mensuellement.</value>
  </data>
  <data name="lblLeg_Burkina_Faso_Summary_DeclarationName" xml:space="preserve">
    <value>Déclaration récapitulative des salaires</value>
  </data>
  <data name="lblLeg_Burundi_Monthly_Tax_Declaration_FormDesc" xml:space="preserve">
    <value>Formulaire de declaration mensuelle a TVA, IRE, retenues a la source et divers prelevements forfaitaires. Seules la section d’en-tête à la page 1, la page 4 et le champ 4 à la page 6 renverront les données de paie. Le rapport doit être téléchargé en Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_Burundi_Monthly_Tax_Declaration_FormName" xml:space="preserve">
    <value>Formulaire de déclaration d’impôt mensuel</value>
  </data>
  <data name="lblLeg_Cam_27ADesc" xml:space="preserve">
    <value>Le rapport annuel du DIPE - onglet C1-NOTE27A. Reportage sur l'IRPP, le Crédit Foncier du Cameroun (CFC), la Taxe de Développement Local et la Radiodiffusion Télévision Camerounaise (CRTV). Les employeurs sont tenus de déposer le DIPE annuel dans le cadre de la Déclaration Statistique et Fiscale (DSF). Ce rapport sera utilisé pour copier et coller des informations dans la déclaration annuelle Dêclaration Stastiuque et de Fiscale (DSF) sur l'onglet C1-NOTE27A. Le rapport doit être téléchargé au format Excel (xlsx).</value>
  </data>
  <data name="lblLeg_Cam_27AName" xml:space="preserve">
    <value>D Information sur le Pesoneel Employe (DIPE) Annuelle</value>
  </data>
  <data name="lblLeg_CNSS_BNTS_ElectroniqueDesc" xml:space="preserve">
    <value>Déclaration électronique CNSS mensuelle et trimestrielle.</value>
  </data>
  <data name="lblLeg_CNSS_BNTS_ElectroniqueName" xml:space="preserve">
    <value>CNSS BNTS Electronique</value>
  </data>
  <data name="lblLeg_Congo_CNSS_DetailDesc" xml:space="preserve">
    <value>Un Rapport Récapitulatif Des Cotisations Sociales.</value>
  </data>
  <data name="lblLeg_Congo_CNSS_DetailName" xml:space="preserve">
    <value>Declaration Nominative Mensuelle ou Trimestrielle Des Salaires Et Des Cotisations</value>
  </data>
  <data name="lblLeg_Congo_Details_Tax_DeclarationDesc" xml:space="preserve">
    <value>Un rapport détaillé des impôts (PIT, TUS, TOL et CAMU) déduits par employé, utilisé conjointement avec le rapport Bordereau Général de Versement.</value>
  </data>
  <data name="lblLeg_Congo_Details_Tax_DeclarationName" xml:space="preserve">
    <value>Details Déclaration Des Impôts</value>
  </data>
  <data name="lblLeg_Congo_Mon_CAMUDesc" xml:space="preserve">
    <value>Un rapport détaillé indiquant la déduction mensuelle du CAMU de l'employés.</value>
  </data>
  <data name="lblLeg_Congo_Mon_CAMUName" xml:space="preserve">
    <value>Etat De Liquidation CAMU/Salaires</value>
  </data>
  <data name="lblLeg_DRC_Annual_Employee_Income_TaxDesc" xml:space="preserve">
    <value>Déclaration annuelle des rémunérations et des impôts des employés.</value>
  </data>
  <data name="lblLeg_DRC_Annual_Employee_Income_TaxName" xml:space="preserve">
    <value>Déclaration recapitulative annuelle de l’impot professionnel sur les remunerations des personnes physiques</value>
  </data>
  <data name="lblLeg_DRC_Declaration_MensuelleDesc" xml:space="preserve">
    <value>Déclaration mensuelle consolidée des charges sociales, cotisations sociales et cotisations patronales</value>
  </data>
  <data name="lblLeg_DRC_Declaration_MensuelleName" xml:space="preserve">
    <value>Declaration Mensuelle Unique Des Impots, Cotisation Social et Cotisation et Contributions Patronales</value>
  </data>
  <data name="lblLeg_DRC_Declaration_Mesuelle_ImpotDesc" xml:space="preserve">
    <value>Un rapport mensuel indiquant l'impôt et l'impôt exceptionnel sur les rémunérations, indiquant également le paiement de l'impôt dans les différentes provinces et catégories d'emploi.</value>
  </data>
  <data name="lblLeg_DRC_Declaration_Mesuelle_ImpotName" xml:space="preserve">
    <value>Declaration Mensuelle Des Impot Professionnel Et Exceptionnel Sur Les Remunerations</value>
  </data>
  <data name="lblLeg_DRC_DMFP_SummaryDesc" xml:space="preserve">
    <value>Ce fichier fournit des informations relatives aux contributions de la CNSS afin de compléter le rapport appelé Déclaration Mensuelle de La Feuille de Paie (DMFP) via le portail électronique.</value>
  </data>
  <data name="lblLeg_DRC_DMFP_SummaryName" xml:space="preserve">
    <value>Déclarations Mensuelle de La Feuille de Paie (DMFP) – Le modèle de fiche de paie</value>
  </data>
  <data name="lblLeg_DRC_DMFPDesc" xml:space="preserve">
    <value>Ce fichier fournit les informations financières relatives à l'assiette des cotisations de la CNSS pour compléter le rapport intitulé Déclaration Mensuelle de La Feuille de Paie (DMFP) via le portail électronique.</value>
  </data>
  <data name="lblLeg_DRC_DMFPName" xml:space="preserve">
    <value>Déclarations Mensuelle de La Feuille de Paie (DMFP) – Le modèle détaillé de la fiche de paie</value>
  </data>
  <data name="lblLeg_DRC_Fiche_IntercalaireDesc" xml:space="preserve">
    <value>Cette déclaration récapitule les rémunérations brutes imposables versées à chaque salarié ainsi que l'IPR (impôt sur le revenu des personnes physiques) de l'année fiscale. Elle est à déposer en même temps que le dépôt de la dernière déclaration mensuelle de l'année concernée.</value>
  </data>
  <data name="lblLeg_DRC_Fiche_IntercalaireName" xml:space="preserve">
    <value>Fiche Intercalaire</value>
  </data>
  <data name="lblLeg_GAB_CNSSDesc" xml:space="preserve">
    <value>Déclaration trimestrielle des salaires pour la CNSS. La Déclaration trimestrielle des salaires (DTS) revient sur le premier onglet, et l'Avis de déclaration employeur (AVIS) revient sur le deuxième onglet. Ce rapport doit être extrait au format Excel (xlsx). Si la date du 'Début de service' ou 'Effet' n'est pas la même que la date de la 'création de l'entreprise' qui est indiquée sur l'onglet d'Informations de l'entreprise, alors utilisez les dates de remplacement dans les paramètres du rapport.</value>
  </data>
  <data name="lblLeg_GAB_CNSSName" xml:space="preserve">
    <value>Déclaration trimestrielle des salaires CNSS</value>
  </data>
  <data name="lblLeg_Gabon_ID19Desc" xml:space="preserve">
    <value>Bulletin de justification traitements, salaires, pensions et rentes viageres ID19.</value>
  </data>
  <data name="lblLeg_Gabon_ID19Name" xml:space="preserve">
    <value>Bulletin de fin de l'annee</value>
  </data>
  <data name="lblLeg_Gabon_ID21_Salaries_PaidDesc" xml:space="preserve">
    <value>Un rapport annuel détaillé indiquant la rémunération gagnée et les différents impôts retenus. Ce rapport doit être extrait au format Adobe (pdf).</value>
  </data>
  <data name="lblLeg_Gabon_ID21_Salaries_PaidName" xml:space="preserve">
    <value>Bordereau détaillé des salaires versés aux employés ID21</value>
  </data>
  <data name="lblLeg_Gabon_ID22_Summary_StatementDesc" xml:space="preserve">
    <value>Un rapport annuel sommaire indiquant la rémunération gagnée ainsi que les différents impôts retenus. Le rapport renvoie les totaux de pages du rapport ID21. Ce rapport doit être extrait au format Adobe (pdf).</value>
  </data>
  <data name="lblLeg_Gabon_ID22_Summary_StatementName" xml:space="preserve">
    <value>Bordereau Recapitulatif ID22</value>
  </data>
  <data name="lblLeg_Gabon_Payroll_ID20Desc" xml:space="preserve">
    <value>Un état récapitulatif des salaires indiquant le nombre total de salariés et les rémunérations versées pour l'année fiscale.</value>
  </data>
  <data name="lblLeg_Gabon_Payroll_ID20Name" xml:space="preserve">
    <value>Etat de la masse salariale ID20</value>
  </data>
  <data name="lblLeg_Guinea_Annual_DeclarationDesc" xml:space="preserve">
    <value>Conformément à l'article 76 du Code général des impôts, les entreprises doivent soumettre une déclaration annuelle aux autorités fiscales afin de garantir une évaluation précise de l'impôt.</value>
  </data>
  <data name="lblLeg_Guinea_Annual_DeclarationName" xml:space="preserve">
    <value>Déclaration annuelle Article 76 du Code général des impôts</value>
  </data>
  <data name="lblLeg_IVOR_FDFP_MonthlyDesc" xml:space="preserve">
    <value>Rapport mensuel des taxes de formation de l'employeur.</value>
  </data>
  <data name="lblLeg_IVOR_FDFP_MonthlyName" xml:space="preserve">
    <value>Taxe d' apprentissage et taxe additionnelle a la formation continue (FDFP)</value>
  </data>
  <data name="lblLeg_IVOR_IVCDesc" xml:space="preserve">
    <value>Déclaration mensuelle qui indique les Impôts mensuels versés aux autorités fiscales. Le rapport doit être téléchargé en Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_IVOR_IVCName" xml:space="preserve">
    <value>Déclaration des impots sur les traitements, salaires, pensions et rentes viageres</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Annual_CNPS_DISADesc" xml:space="preserve">
    <value>Une déclaration annuelle détaillée pour la CNPS.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Annual_CNPS_DISAName" xml:space="preserve">
    <value>Declaration Individuelle Des Salaires Annuels (D.I.S.A)</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Electronic_CNPS_MonthlyDesc" xml:space="preserve">
    <value>Un fichier électronique mensuel pour importer les cotisations de sécurité sociale.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Electronic_CNPS_MonthlyName" xml:space="preserve">
    <value>Rapport électronique mensuel du CNPS</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ETAT301_DetailDesc" xml:space="preserve">
    <value>L'etat 301 est un état des salaires et des rémunérations assimilées versés au cours de l’année, utilisé par les employeurs du secteur privé. Copiez les colonnes C vers R, T, V, W, Y, AA vers AB et collez-les sur le modèle. Les colonnes S, U, X, Z seront calculées par le modèle. Les colonnes S, U, X, Z sont toujours renvoyées dans le rapport PaySpace à des fins de rapprochement.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ETAT301_DetailName" xml:space="preserve">
    <value>ETAT301 Detail (EDI)</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ETAT301_SummaryDesc" xml:space="preserve">
    <value>L’ÉTAT 301 est une déclaration fiscale annuelle. Il fournit un rapport récapitulatif de l’ensemble des salaires, traitements et paiements assimilés (tels que les primes et avantages) versés par un employeur à ses employés au cours de l’année fiscale.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ETAT301_SummaryName" xml:space="preserve">
    <value>ETAT301 Summary</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITS_FDFPDesc" xml:space="preserve">
    <value>Un rapport récapitulatif des taxes salariales et patronales (ITS, CE &amp; CN), y compris la taxe d'apprentissage et la taxe de formation.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITS_FDFPName" xml:space="preserve">
    <value>ITS-FDFP Mensuelle (Declaration des impots sur les traitements, salaires et contributions annexes)</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSDesc" xml:space="preserve">
    <value>Une déclaration mensuelle indiquant le montant de la rémunération brute, la situation familiale et l'impôt dû par mois. Ce rapport sera utilisé pour copier et coller dans la macro EDI. Copiez les colonnes C vers R, T, Y, AA vers AB et collez-les sur le modèle. Les colonnes S, U à X, Z seront calculées par le modèle. Les colonnes S, U à X, Z sont toujours renvoyées dans le rapport PaySpace à des fins de rapprochement.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSName" xml:space="preserve">
    <value>Etat Individuel des Remunerations Brutes Mensuelles des Salaries</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSREGULDesc" xml:space="preserve">
    <value>Etat Des Salaires, Et Des Remunerations Asimilees Payees Durant L’Annee.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSREGULName" xml:space="preserve">
    <value>ITS REGUL</value>
  </data>
  <data name="lblLeg_Mad_MGA_USD_Bordereau_IRSA_ReportDesc" xml:space="preserve">
    <value>Rapport mensuel de l'impôt sur les salaires et revenus assimilés.</value>
  </data>
  <data name="lblLeg_Mad_MGA_USD_Bordereau_IRSA_ReportName" xml:space="preserve">
    <value>Bordereau De Versement De L'impot Sur Les Revenus Salariaux Et Assimiles (IRSA)</value>
  </data>
  <data name="lblLeg_Mada_Etat_Nominatif_IRSA_ReportDesc" xml:space="preserve">
    <value>Rapport mensuel des salaires, traitements et revenus assimilés.</value>
  </data>
  <data name="lblLeg_Mada_Etat_Nominatif_IRSA_ReportName" xml:space="preserve">
    <value>Etat Nominatif des Traitements, Salaires et Assimiles (IRSA) Report</value>
  </data>
  <data name="lblLeg_Mada_Mon_eHetra_ReportDesc" xml:space="preserve">
    <value>Ce tableau contient des informations mensuelles sur l'IRSA des employés. Veuillez télécharger le fichier Excel. Copier le fichier téléchargé dans le fichier macro qui sera soumis via le portail eHetra.</value>
  </data>
  <data name="lblLeg_Mada_Mon_eHetra_ReportName" xml:space="preserve">
    <value>Programme mensuel eHetra IRSA</value>
  </data>
  <data name="lblLeg_Mali_Declaration_Monthly_INPSDesc" xml:space="preserve">
    <value>Un rapport détaillé montrant les différentes contributions à l’INPS, afin que le rapport reflète la réalité de l’entreprise, tous les employés doivent être liés à une "catégorie d’employés".</value>
  </data>
  <data name="lblLeg_Mali_Declaration_Monthly_INPSName" xml:space="preserve">
    <value>Declaration nominative de versement des cotisations.</value>
  </data>
  <data name="lblLeg_Mali_Monthly_Tax_Declaration_Form_ReportDesc" xml:space="preserve">
    <value>Une déclaration mensuelle illustrant les différentes contributions dues à la DGI. Les soumissions et les rapports doivent être présentés au plus tard le 15 du mois suivant la déduction.</value>
  </data>
  <data name="lblLeg_Mali_Monthly_Tax_Declaration_Form_ReportName" xml:space="preserve">
    <value>Fiche de Déclaration des Impôts Mensuels</value>
  </data>
  <data name="lblLeg_Mauritania_Ann_DeclarationDesc" xml:space="preserve">
    <value>La déclaration annuelle des salaires est un document utilisé pour déclarer les cotisations de sécurité sociale et les impôts sur les salaires. Cette déclaration fait partie des exigences de la déclaration générale des impôts.</value>
  </data>
  <data name="lblLeg_Mauritania_Ann_DeclarationName" xml:space="preserve">
    <value>D31.DADS Déclaration annuelle des salaires (DAS) Liasse fiscale generale</value>
  </data>
  <data name="lblLeg_Mauritania_DAS_AnnualDesc" xml:space="preserve">
    <value>Déclaration annuelle des salaires utilisée pour soumettre les cotisations de sécurité sociale et l'impôt sur les salaires et traitements.</value>
  </data>
  <data name="lblLeg_Mauritania_DAS_AnnualName" xml:space="preserve">
    <value>Déclaration annuelle des salaires (DAS)</value>
  </data>
  <data name="lblLeg_Mauritania_Declaration_MenDesc" xml:space="preserve">
    <value>Déclaration mensuelle utilisée pour déclarer l’impôt sur les salaires et traitements. Ce rapport doit être extrait au format PDF.</value>
  </data>
  <data name="lblLeg_Mauritania_Declaration_MenName" xml:space="preserve">
    <value>Déclaration mensuelle d’ITS</value>
  </data>
  <data name="lblLeg_Mon_Togo_CNSS_DeclarationDesc" xml:space="preserve">
    <value>Le formulaire de déclaration de cotisations sociales permet à l'employeur d'effectuer mensuellement les déclarations de cotisations à la CNSS.</value>
  </data>
  <data name="lblLeg_Mon_Togo_CNSS_DeclarationName" xml:space="preserve">
    <value>Déclaration et rémunération et cotisations (DRC)</value>
  </data>
  <data name="lblLeg_Mon_Togo_Tax_reportDesc" xml:space="preserve">
    <value>Déclaration mensuelle de charges sociales et retenues d’impôts</value>
  </data>
  <data name="lblLeg_Mon_Togo_Tax_reportName" xml:space="preserve">
    <value>Declaration De Versement Des Taxe sur les salaires et Retenues d’impôts</value>
  </data>
  <data name="lblLeg_Niger_Ann_ITSDesc" xml:space="preserve">
    <value>Un rapport annuel récapitulatif des impôts calculés sur les traitements, salaires, revenus assimilés et rentes viagères. Ce rapport doit être téléchargé dans Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_Niger_Ann_ITSName" xml:space="preserve">
    <value>Declaration Annuelle Recapitulative des Retenues pratiquees au titre de L’impot</value>
  </data>
  <data name="lblLeg_Niger_Mon_ITSDesc" xml:space="preserve">
    <value>Un rapport mensuel qui calcule les impôts calculés sur les traitements, traitements, revenus assimilés et rentes viagères. Ce rapport doit être téléchargé dans Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_Niger_Mon_ITSName" xml:space="preserve">
    <value>Declaration Mensuelle de L’impot</value>
  </data>
  <data name="lblLeg_Qua_Togo_CNSSDesc" xml:space="preserve">
    <value>Il s'agit de la déclaration trimestrielle CNSS utilisée pour le téléchargement en linge.</value>
  </data>
  <data name="lblLeg_Qua_Togo_CNSSName" xml:space="preserve">
    <value>Déclaration Nominative Des Rémunération (DNR)</value>
  </data>
  <data name="lblLeg_Senegal_Annual_TaxDesc" xml:space="preserve">
    <value>Déclaration annuelle qui déclare les impôts (IR, TRIMF, CFCE) prélevés sur les salaires versés aux salariés.</value>
  </data>
  <data name="lblLeg_Senegal_Annual_TaxName" xml:space="preserve">
    <value>Etat recapitulatif des traitements de salaires</value>
  </data>
  <data name="lblLeg_Senegal_Monthly_Tax_Desc" xml:space="preserve">
    <value>Retenue mensuelle sur les salaires pour l'impôt, le TRIMF et le CFCE</value>
  </data>
  <data name="lblLeg_Senegal_Monthly_Tax_Name" xml:space="preserve">
    <value>Declaration des retenues a la source sur les salaires</value>
  </data>
  <data name="lblLeg_Togo_Anuual_Tax_DeclarationDesc" xml:space="preserve">
    <value>Déclaration annuelle servant à la soumission de l'impôt sur les traitements, salaires, pensions et rentes viagères.</value>
  </data>
  <data name="lblLeg_Togo_Anuual_Tax_DeclarationName" xml:space="preserve">
    <value>Déclaration Annuelle des Salaries (DAS)</value>
  </data>
  <data name="lblLeg_Tunisia_Annual_CAVISDesc" xml:space="preserve">
    <value>Le rapport est utilisé pour déclarer le salaire annuel de cotisation CAVIS au format texte sur la plateforme en ligne par voie électronique.</value>
  </data>
  <data name="lblLeg_Tunisia_Annual_CAVISName" xml:space="preserve">
    <value>Rapport Annuel De Déclaration Cavis (format txt)</value>
  </data>
  <data name="lblMonthSelect" xml:space="preserve">
    <value>Mois</value>
  </data>
  <data name="lblNetPaymentListingName" xml:space="preserve">
    <value>Liste des paiements nets</value>
  </data>
  <data name="lblNewEngagementsandTerminationsDesc" xml:space="preserve">
    <value>Fournit une liste des nouveaux engagements et des employés licenciés pour une période choisie.</value>
  </data>
  <data name="lblNoGLNumberParamNameKey" xml:space="preserve">
    <value>N'afficher que les composants ayant un code de grand livre général défini?</value>
  </data>
  <data name="lblNonFormattedParamNameKey" xml:space="preserve">
    <value>Cochez ici si vous voulez voir ce rapport sans formatage</value>
  </data>
  <data name="lblOrgUnitIDParamNameKey" xml:space="preserve">
    <value>Unités d'organisation</value>
  </data>
  <data name="lblOrgUnitParamNameKey" xml:space="preserve">
    <value>Unités d'organisation</value>
  </data>
  <data name="lblOrgUnitsParamNameKey" xml:space="preserve">
    <value>Unités d'organisation</value>
  </data>
  <data name="lblotalsTypeParamNameKey" xml:space="preserve">
    <value>Exécuter le rapport avec</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Rapports</value>
  </data>
  <data name="lblPaidParamNameKey" xml:space="preserve">
    <value>Exclure les fiches de paie des employés marqués comme payés? Cela s'applique au cycle sélectionné ci-dessus.</value>
  </data>
  <data name="lblPayMethodParamNameKey" xml:space="preserve">
    <value>Filtrer les employés rémunérés via</value>
  </data>
  <data name="lblPayRatesReportDesc" xml:space="preserve">
    <value>Fournit une liste du package des employés dans l'ordre historique ou tout simplement le plus récent.</value>
  </data>
  <data name="lblPayRatesReportName" xml:space="preserve">
    <value>Rapport sur les taux de rémunération</value>
  </data>
  <data name="lblPayrollReconciliationReportDesc" xml:space="preserve">
    <value>Fournit les chiffres actuels ou les chiffres cumulatifs mensuels de tous les employés sur les fiches de paie. Généralement utilisé à des fins de rapprochement.</value>
  </data>
  <data name="lblPayrollReconciliationReportName" xml:space="preserve">
    <value>Rapport de rapprochement des salaires</value>
  </data>
  <data name="lblPayRollRegName" xml:space="preserve">
    <value>Rapport sur le registre des salaires</value>
  </data>
  <data name="lblPayslipActionParamNameKey" xml:space="preserve">
    <value>Actions sur les fiches de paie</value>
  </data>
  <data name="lblPayslipsDesc" xml:space="preserve">
    <value>Fournit une liste des fiches de paie des employés pour un cycle choisi.</value>
  </data>
  <data name="lblPayslipsName" xml:space="preserve">
    <value>Fiches de paie</value>
  </data>
  <data name="lblPositionParamNameKey" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="lblpositionsParamNameKey" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="lblProcessIDParamNameKey" xml:space="preserve">
    <value>Processus</value>
  </data>
  <data name="lblRegionParamNameKey" xml:space="preserve">
    <value>Région/localisation</value>
  </data>
  <data name="lblReport" xml:space="preserve">
    <value>Ajouter un rapport</value>
  </data>
  <data name="lblReportPageTitle" xml:space="preserve">
    <value>Rapports</value>
  </data>
  <data name="lblReportsNoDataText" xml:space="preserve">
    <value>Aucun rapport n'est disponible pour cette catégorie.</value>
  </data>
  <data name="lblReportSubmitBtnText" xml:space="preserve">
    <value>Soumettre</value>
  </data>
  <data name="lblReportTypeActual" xml:space="preserve">
    <value>Réel</value>
  </data>
  <data name="lblReportTypeArchive" xml:space="preserve">
    <value>Archivé</value>
  </data>
  <data name="lblReportTypeParamNameKey" xml:space="preserve">
    <value>Période budgétaire</value>
  </data>
  <data name="lblReportTypeSnapShot" xml:space="preserve">
    <value>Précédent</value>
  </data>
  <data name="lblRunSelect" xml:space="preserve">
    <value>Lancer</value>
  </data>
  <data name="lblSaveDescription" xml:space="preserve">
    <value>Veuillez entrer une description des modifications apportées.</value>
  </data>
  <data name="lblSaveMessage" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir enregistrer ?</value>
  </data>
  <data name="lblScoresHistoryDesc" xml:space="preserve">
    <value>Renvoie tous les détails de notation et les notes totales pour le processus sélectionné</value>
  </data>
  <data name="lblScoresHistoryName" xml:space="preserve">
    <value>Historique des scores</value>
  </data>
  <data name="lblSearchPlaceHolderText" xml:space="preserve">
    <value>Recherchez</value>
  </data>
  <data name="lblShowColumnsParamKey" xml:space="preserve">
    <value>Champs de rapport</value>
  </data>
  <data name="lblShowExtraColParamNameKey" xml:space="preserve">
    <value>Champs de rapport</value>
  </data>
  <data name="lblShowSumDifferenceSum" xml:space="preserve">
    <value>Somme</value>
  </data>
  <data name="lblShowTotalsPerNameKey" xml:space="preserve">
    <value>Indiquer les totaux par</value>
  </data>
  <data name="lblSnapshotIDParamNameKey" xml:space="preserve">
    <value>Sélectionner un aperçu du budget</value>
  </data>
  <data name="lblSnapshotParamNameKey" xml:space="preserve">
    <value>Sélectionner un aperçu du budget</value>
  </data>
  <data name="lblSortEmpName" xml:space="preserve">
    <value>Nom de l'employé</value>
  </data>
  <data name="lblSortEmpNumber" xml:space="preserve">
    <value>Numéro d'employé</value>
  </data>
  <data name="lblSortOrderParamNameKey" xml:space="preserve">
    <value>Ordre de tri</value>
  </data>
  <data name="lblStartDateParamNameKey" xml:space="preserve">
    <value>Date de début</value>
  </data>
  <data name="lblTerminatedFromDateParamKey" xml:space="preserve">
    <value>Inclure les résiliations à partir de cette date</value>
  </data>
  <data name="lblToDateNameKey" xml:space="preserve">
    <value>Inclure jusqu'à cette date</value>
  </data>
  <data name="lblTotalsParamNameKey" xml:space="preserve">
    <value>Montrer les totaux par régime ?</value>
  </data>
  <data name="lblTotalsTypeParamNameKey" xml:space="preserve">
    <value>Montrer les totaux par type de congé ?</value>
  </data>
  <data name="lblTrainingReportDesc" xml:space="preserve">
    <value>Fournit une liste des employés qui ont ou n'ont pas suivi de cours de formation pendant une période choisie.</value>
  </data>
  <data name="lblTrainingReportName" xml:space="preserve">
    <value>Rapport sur la formation</value>
  </data>
  <data name="lblTransactionsReportDesc" xml:space="preserve">
    <value>Fournit une liste de toutes les opérations de congé pour une période déterminée.</value>
  </data>
  <data name="lblTransactionsReportName" xml:space="preserve">
    <value>Rapport sur les transactions</value>
  </data>
  <data name="lblTunisia_Annual_Individual_WHTDesc" xml:space="preserve">
    <value>Certificat d'impôt qui divulgue le salaire, les traitements, la pension et les rentes viagères pour l'année d'imposition de l'employé</value>
  </data>
  <data name="lblTunisia_Annual_Individual_WHTName" xml:space="preserve">
    <value>Certificat de retenue d'impots sur le revenu au titre des traitements, salaires, pensions et rentes viageres</value>
  </data>
  <data name="lblTunisiaEmployerAnnualTaxReturnReportDesc" xml:space="preserve">
    <value>Le rapport est utilisé pour remplir l'annexe 1 de la déclaration annuelle de l'employeur</value>
  </data>
  <data name="lblTunisiaEmployerAnnualTaxReturnReportName" xml:space="preserve">
    <value>Declaration Employeur Annexe 1</value>
  </data>
  <data name="lblTypeFigures" xml:space="preserve">
    <value>Chiffres</value>
  </data>
  <data name="lblTypeHeadCount" xml:space="preserve">
    <value>Nombre de personnes</value>
  </data>
</root>