<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ComponentBureau" xml:space="preserve">
    <value>Components</value>
  </data>
  <data name="Components" xml:space="preserve">
    <value>Components</value>
  </data>
  <data name="ElementCodes" xml:space="preserve">
    <value>Element codes</value>
  </data>
  <data name="errDetailDependencies" xml:space="preserve">
    <value>Cannot delete this record due to linked dependencies.</value>
  </data>
  <data name="errDuplicateDetailCodes" xml:space="preserve">
    <value>There is already a Element with this code.</value>
  </data>
  <data name="errEffectiveDateRequired" xml:space="preserve">
    <value>Effective date is required</value>
  </data>
  <data name="errElementCodeRequired" xml:space="preserve">
    <value>Element Code is required</value>
  </data>
  <data name="errElementNameRequired" xml:space="preserve">
    <value>Element Name is required</value>
  </data>
  <data name="lblDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="lblElementDetailOffcanvasTitle" xml:space="preserve">
    <value>Link components to element codes</value>
  </data>
  <data name="ReportingElementHeaderName" xml:space="preserve">
    <value>Element</value>
  </data>
  <data name="ReportingElementsDetailCode" xml:space="preserve">
    <value>Element codes</value>
  </data>
  <data name="ReportingElementsDetailEffectiveDate" xml:space="preserve">
    <value>Effective date</value>
  </data>
  <data name="ReportingElementsDetailInactiveDate" xml:space="preserve">
    <value>Inactive date</value>
  </data>
</root>