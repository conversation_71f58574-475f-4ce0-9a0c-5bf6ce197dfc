<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActAsBasePayForOnceOffComponents" xml:space="preserve">
    <value>Act as Base Pay (for once-off components only)</value>
  </data>
  <data name="active" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="addComponent" xml:space="preserve">
    <value>Component</value>
  </data>
  <data name="AddComponentToAllEmployees" xml:space="preserve">
    <value>Add component to all employees</value>
  </data>
  <data name="aliasDescription" xml:space="preserve">
    <value>Component name</value>
  </data>
  <data name="AllocationType" xml:space="preserve">
    <value>Allocation type</value>
  </data>
  <data name="AllowToBePartOfPackage" xml:space="preserve">
    <value>Allow to be part of package</value>
  </data>
  <data name="AlternateComponentName" xml:space="preserve">
    <value>Alternative component name</value>
  </data>
  <data name="AlwaysCalculateIfPayslipExists" xml:space="preserve">
    <value>Always calculate if there is a payslip regardless of basic being present on payslip</value>
  </data>
  <data name="AutoRecoveryRule" xml:space="preserve">
    <value>Recovery rule</value>
  </data>
  <data name="CarVATPercentage" xml:space="preserve">
    <value>Car VAT percentage</value>
  </data>
  <data name="CompanyLeaveSetup" xml:space="preserve">
    <value>Leave bucket</value>
  </data>
  <data name="ComponentCode" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="componentDetails" xml:space="preserve">
    <value>Component details</value>
  </data>
  <data name="ComponentDetails" xml:space="preserve">
    <value>Component details</value>
  </data>
  <data name="ComponentName" xml:space="preserve">
    <value>Component name</value>
  </data>
  <data name="componentType" xml:space="preserve">
    <value>Component type</value>
  </data>
  <data name="ConvertResultToHostCurrency" xml:space="preserve">
    <value>Convert calculated result from this selection to host currency</value>
  </data>
  <data name="customNumbering" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="CustomNumbering" xml:space="preserve">
    <value>Order number</value>
  </data>
  <data name="DecimalPlaces" xml:space="preserve">
    <value>Decimal place value</value>
  </data>
  <data name="DoNotCalculateOnRetroactivePayslip" xml:space="preserve">
    <value>Do not calculate this component on the retroactive payslip</value>
  </data>
  <data name="DoNotConvertToDifferentCurrencyForReports" xml:space="preserve">
    <value>Do not convert to different currency for reports</value>
  </data>
  <data name="DoNotShowOnPayslip" xml:space="preserve">
    <value>Do not show on payslip</value>
  </data>
  <data name="errDuplicateComponentCode" xml:space="preserve">
    <value>Cannot have duplicate component codes.</value>
  </data>
  <data name="errDuplicateDescription" xml:space="preserve">
    <value>Cannot have duplicate descriptions.</value>
  </data>
  <data name="ExcludeFromRetrospectiveCalculation" xml:space="preserve">
    <value>Exclude component from retrospective calculation, calculate as per normal on retro payslip</value>
  </data>
  <data name="ExtraOptions" xml:space="preserve">
    <value>Extra options</value>
  </data>
  <data name="formula" xml:space="preserve">
    <value>Formula</value>
  </data>
  <data name="Formula" xml:space="preserve">
    <value>Formula</value>
  </data>
  <data name="HideCommentsOnPayslip" xml:space="preserve">
    <value>Hide comments on payslip</value>
  </data>
  <data name="InActive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="IndicatorDescription" xml:space="preserve">
    <value>Indicator Description</value>
  </data>
  <data name="IndicatorLineDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="IndicatorLineValue" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="inPackage" xml:space="preserve">
    <value>Allow to be part of package</value>
  </data>
  <data name="InPackage" xml:space="preserve">
    <value>Allow inclusion in package</value>
  </data>
  <data name="IsBonusTaxSpread" xml:space="preserve">
    <value>Is bonus tax spread</value>
  </data>
  <data name="IsSelected" xml:space="preserve">
    <value>Selected</value>
  </data>
  <data name="lblActive" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="lblAddOccurance" xml:space="preserve">
    <value>Add new occurrence</value>
  </data>
  <data name="lblAutomatic" xml:space="preserve">
    <value>Automatic</value>
  </data>
  <data name="lblBonusProvision" xml:space="preserve">
    <value>Bonus provision</value>
  </data>
  <data name="lblCalculator" xml:space="preserve">
    <value>Calculator</value>
  </data>
  <data name="lblCompanyCarVAT" xml:space="preserve">
    <value>Company ca VAT</value>
  </data>
  <data name="lblComponentFilters" xml:space="preserve">
    <value>Component filters</value>
  </data>
  <data name="lblComponentValues" xml:space="preserve">
    <value>Component values</value>
  </data>
  <data name="lblDownloadReport" xml:space="preserve">
    <value>Download report</value>
  </data>
  <data name="lblHasSubCodes" xml:space="preserve">
    <value>Taxability subcode</value>
  </data>
  <data name="lblInactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="lblIndicators" xml:space="preserve">
    <value>Indicators</value>
  </data>
  <data name="lblLeaveBuy" xml:space="preserve">
    <value>Leave buy</value>
  </data>
  <data name="lblOverrideTooltip" xml:space="preserve">
    <value>Overridden Code : {0} - {1}</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Component Taxability</value>
  </data>
  <data name="lblStatutory" xml:space="preserve">
    <value>Statutory</value>
  </data>
  <data name="lblSubCode" xml:space="preserve">
    <value>Component Calculation Exceptions</value>
  </data>
  <data name="lblUnion" xml:space="preserve">
    <value>Union</value>
  </data>
  <data name="MaxRatioOfRecurringValue" xml:space="preserve">
    <value>Max ratio of recurring value to recover per period</value>
  </data>
  <data name="MaxValue" xml:space="preserve">
    <value>Maximum value</value>
  </data>
  <data name="MinValue" xml:space="preserve">
    <value>Minimum value</value>
  </data>
  <data name="MultiplyByComponentCompanyId" xml:space="preserve">
    <value>Multiply payslip result by component</value>
  </data>
  <data name="onceOff" xml:space="preserve">
    <value>Once Off</value>
  </data>
  <data name="OnlyEnableFromMonth" xml:space="preserve">
    <value>Only enable from month</value>
  </data>
  <data name="OnlyEnableToMonth" xml:space="preserve">
    <value>Only enable to month</value>
  </data>
  <data name="order" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="OverrideTaxCode" xml:space="preserve">
    <value>Override tax code</value>
  </data>
  <data name="PartOfCostToCompany" xml:space="preserve">
    <value>Part of cost to company</value>
  </data>
  <data name="PayslipMsg" xml:space="preserve">
    <value>Payslip message</value>
  </data>
  <data name="PayslipName" xml:space="preserve">
    <value>Payslip name</value>
  </data>
  <data name="PayslipOptions" xml:space="preserve">
    <value>Payslip options</value>
  </data>
  <data name="payslipRecurrence" xml:space="preserve">
    <value>Payslip recurrence</value>
  </data>
  <data name="ProRataAsPerBasicPay" xml:space="preserve">
    <value>Pro rata component as per basic pay</value>
  </data>
  <data name="RecoverInNextPeriod" xml:space="preserve">
    <value>Recover in next period</value>
  </data>
  <data name="recurringAndOnceOff" xml:space="preserve">
    <value>Recurring &amp; Once Off</value>
  </data>
  <data name="RunId" xml:space="preserve">
    <value>Period from</value>
  </data>
  <data name="RunsToCalculateComponent" xml:space="preserve">
    <value>Runs to calculate the component in</value>
  </data>
  <data name="SelectPayrollComponent" xml:space="preserve">
    <value>Select The Payroll Component</value>
  </data>
  <data name="ShowInactive" xml:space="preserve">
    <value>Include inactive components</value>
  </data>
  <data name="ShowOnMockPayslipOnly" xml:space="preserve">
    <value>Show on mock payslip only</value>
  </data>
  <data name="taxCode" xml:space="preserve">
    <value>Tax code</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="WeeklyStartDay" xml:space="preserve">
    <value>Weekly start day</value>
  </data>
</root>