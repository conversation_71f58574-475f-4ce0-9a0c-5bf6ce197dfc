<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AccessDenied" xml:space="preserve">
    <value>You don’t have permission to edit the record</value>
  </data>
  <data name="AddressLine1" xml:space="preserve">
    <value>Street name</value>
  </data>
  <data name="AddressLine2" xml:space="preserve">
    <value>Suburb</value>
  </data>
  <data name="AddressLine3" xml:space="preserve">
    <value>City or Town / Postal City</value>
  </data>
  <data name="errAddressCodeMaxLength" xml:space="preserve">
    <value>The field Code must be a string with a maximum length of 12.</value>
  </data>
  <data name="errInvalidFranceAddressCode" xml:space="preserve">
    <value>Invalid selection for Address Code</value>
  </data>
  <data name="InvalidFrequency" xml:space="preserve">
    <value>Invalid Frequency</value>
  </data>
  <data name="lblAddress1" xml:space="preserve">
    <value>Address Line 1</value>
  </data>
  <data name="lblAddress2" xml:space="preserve">
    <value>Address Line 2</value>
  </data>
  <data name="lblAddress3" xml:space="preserve">
    <value>City / Address Line 3</value>
  </data>
  <data name="lblAddressDetails" xml:space="preserve">
    <value>Address Details</value>
  </data>
  <data name="lblAddressPostal1" xml:space="preserve">
    <value>Postal Address Number</value>
  </data>
  <data name="lblAddressPostal2" xml:space="preserve">
    <value>Postal Agency</value>
  </data>
  <data name="lblAddressPostal3" xml:space="preserve">
    <value>Suburb</value>
  </data>
  <data name="lblAddressStreetType" xml:space="preserve">
    <value>Type of Address</value>
  </data>
  <data name="lblAddressType" xml:space="preserve">
    <value>Address Type</value>
  </data>
  <data name="lblApartment" xml:space="preserve">
    <value>Apartment</value>
  </data>
  <data name="lblBlock" xml:space="preserve">
    <value>Block</value>
  </data>
  <data name="lblCareofAddress" xml:space="preserve">
    <value>Care of Intermediary</value>
  </data>
  <data name="lblCityPostal" xml:space="preserve">
    <value>Suburb</value>
  </data>
  <data name="lblCityTown" xml:space="preserve">
    <value>City / Town</value>
  </data>
  <data name="lblCode" xml:space="preserve">
    <value>Postcode</value>
  </data>
  <data name="lblCodePostal" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lblCompanyImplantation" xml:space="preserve">
    <value>Company Implantation</value>
  </data>
  <data name="lblComplexName" xml:space="preserve">
    <value>Complex Name</value>
  </data>
  <data name="lblCountry" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="lblCountryPostal" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="lblDistributionCodeAbroad" xml:space="preserve">
    <value>Distribution code abroad</value>
  </data>
  <data name="lblDistributionService" xml:space="preserve">
    <value>Distribution Service</value>
  </data>
  <data name="lblDoor" xml:space="preserve">
    <value>Door</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Employee Number</value>
  </data>
  <data name="lblEntrance" xml:space="preserve">
    <value>Entrance</value>
  </data>
  <data name="lblEntryCode" xml:space="preserve">
    <value>Entry Code</value>
  </data>
  <data name="lblFloor" xml:space="preserve">
    <value>Floor</value>
  </data>
  <data name="lblIsCareofAddress" xml:space="preserve">
    <value>Is the postal address a care of address?</value>
  </data>
  <data name="lblMunicipality" xml:space="preserve">
    <value>Municipality Code</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Employee Address</value>
  </data>
  <data name="lblPhysicalAddress" xml:space="preserve">
    <value>Physical</value>
  </data>
  <data name="lblPostalAddress" xml:space="preserve">
    <value>Postal</value>
  </data>
  <data name="lblProvince" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="lblProvincePostal" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="lblSameAsPostal" xml:space="preserve">
    <value>Is the postal address the same as physical address?</value>
  </data>
  <data name="lblSpecialServices" xml:space="preserve">
    <value>Special services</value>
  </data>
  <data name="lblStaircase" xml:space="preserve">
    <value>Staircase</value>
  </data>
  <data name="lblStreetName" xml:space="preserve">
    <value>Address Line 2</value>
  </data>
  <data name="lblStreetNum" xml:space="preserve">
    <value>Street number</value>
  </data>
  <data name="lblSuburb" xml:space="preserve">
    <value>Suburb</value>
  </data>
  <data name="lblUkEmpNumber" xml:space="preserve">
    <value>Employee Number / Payroll ID</value>
  </data>
  <data name="lblUkPostCode" xml:space="preserve">
    <value>Code / Postcode</value>
  </data>
  <data name="lblUkProvince" xml:space="preserve">
    <value>Province / County</value>
  </data>
  <data name="lblUkStreetName" xml:space="preserve">
    <value>Address LIne 2</value>
  </data>
  <data name="lblUkStreetNumber" xml:space="preserve">
    <value>Address Line 1</value>
  </data>
  <data name="lblUnitNumber" xml:space="preserve">
    <value>Unit Number</value>
  </data>
</root>