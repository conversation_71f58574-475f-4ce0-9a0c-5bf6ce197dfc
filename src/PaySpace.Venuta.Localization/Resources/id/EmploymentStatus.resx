<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="0001" xml:space="preserve">
    <value>Cannot Have TaxDirectiveNumber If TaxStatus Is Not Directive.</value>
  </data>
  <data name="0002" xml:space="preserve">
    <value>Cannot Have Percentage / Amount If TaxStatus Is Not Directive Or Director With DeemedRecoveryMonthly As True.</value>
  </data>
  <data name="0003" xml:space="preserve">
    <value>Cannot Have Percentage If Percentage / Amount Is Not Percentage.</value>
  </data>
  <data name="0004" xml:space="preserve">
    <value>Cannot Have DeemedMonthlyRemuneration If Deemed75Indicator Is True.</value>
  </data>
  <data name="0005" xml:space="preserve">
    <value>Cannot Have Deemed75Indicator If TaxStatus Is Not Director.</value>
  </data>
  <data name="0006" xml:space="preserve">
    <value>Cannot Have DeemedRecoveryMonthly If Deemed75Indicator Is True.</value>
  </data>
  <data name="0007" xml:space="preserve">
    <value>Cannot Have Irp30 If TaxStatus Is Not Labour.</value>
  </data>
  <data name="0008" xml:space="preserve">
    <value>The Selected TaxStatus Does Not Match The Nature Of Person.</value>
  </data>
  <data name="0009" xml:space="preserve">
    <value>TerminationCompanyRun Required When Terminating.</value>
  </data>
  <data name="0010" xml:space="preserve">
    <value>Termination Date Required When Terminating.</value>
  </data>
  <data name="0011" xml:space="preserve">
    <value>Termination Reason Required When Terminating.</value>
  </data>
  <data name="0012" xml:space="preserve">
    <value>Encash leave Required When Terminating.</value>
  </data>
  <data name="0013" xml:space="preserve">
    <value>Cannot Have TerminationCompanyRun When Not Terminating.</value>
  </data>
  <data name="0014" xml:space="preserve">
    <value>Cannot Have TerminationDate When Not Terminating.</value>
  </data>
  <data name="0015" xml:space="preserve">
    <value>Cannot Have TerminationReason When Not Terminating.</value>
  </data>
  <data name="0016" xml:space="preserve">
    <value>Cannot Have EncashLeave When Not Terminating.</value>
  </data>
  <data name="0017" xml:space="preserve">
    <value>Employment Status Already Exists, Please Modify Existing Records Accordingly.</value>
  </data>
  <data name="0018" xml:space="preserve">
    <value>Termination Date Must Be After Employment Date.</value>
  </data>
  <data name="0019" xml:space="preserve">
    <value>Employment Date Must Be After Group Join Date.</value>
  </data>
  <data name="0020" xml:space="preserve">
    <value>Employment Date Must Not Overlap Existing Employment Status.</value>
  </data>
  <data name="0021" xml:space="preserve">
    <value>Group Join Date Cannot Be Before Any Existing Records.</value>
  </data>
  <data name="0022" xml:space="preserve">
    <value>A re-instatement with a BREAK in service is not possible when payslips in an open period have been captured against the old record, delete those records and try again.</value>
  </data>
  <data name="0023" xml:space="preserve">
    <value>Closed runs exist in the current tax year. Please adjust the termination date to be after the start of the current tax year.</value>
  </data>
  <data name="0024" xml:space="preserve">
    <value>Termination Date Cannot Be Before The Last Day Of The Previous Tax Year.</value>
  </data>
  <data name="0025" xml:space="preserve">
    <value>AdditionalDate Must Be Null If The Company Setting Is Off.</value>
  </data>
  <data name="0026" xml:space="preserve">
    <value>AdditionalDate1 Must Be Null If The Company Setting Is Off.</value>
  </data>
  <data name="0027" xml:space="preserve">
    <value>Additional Date Is Required</value>
  </data>
  <data name="0028" xml:space="preserve">
    <value>AdditionalDate1 Is Required.</value>
  </data>
  <data name="0029" xml:space="preserve">
    <value>PassportCountry Required If IdentityType Is Not Identity.</value>
  </data>
  <data name="0030" xml:space="preserve">
    <value>PassportCountry Must Be Null If IdentityType Is ID.</value>
  </data>
  <data name="0031" xml:space="preserve">
    <value>Passport Or ID Number Is Required.</value>
  </data>
  <data name="0032" xml:space="preserve">
    <value>TempWorker Required When The Company Setting Is On.</value>
  </data>
  <data name="0033" xml:space="preserve">
    <value>TempWorker Must Be Null When The Company Setting Is Off.</value>
  </data>
  <data name="0034" xml:space="preserve">
    <value>TaxDirectiveNumber Required If TaxStatus Is Directive.</value>
  </data>
  <data name="0035" xml:space="preserve">
    <value>Percentage / Amount Is Required.</value>
  </data>
  <data name="0036" xml:space="preserve">
    <value>Percentage Required If Percentage / Amount Is Percentage.</value>
  </data>
  <data name="0037" xml:space="preserve">
    <value>DeemedMonthlyRemuneration Required If Deemed75Indicator Is False.</value>
  </data>
  <data name="0038" xml:space="preserve">
    <value>Deemed75Indicator Required If TaxStatus Is Director.</value>
  </data>
  <data name="0039" xml:space="preserve">
    <value>DeemedRecoveryMonthly Required If Deemed75Indicator Is False.</value>
  </data>
  <data name="0040" xml:space="preserve">
    <value>Irp30 Required If TaxStatus Is Labour.</value>
  </data>
  <data name="0041" xml:space="preserve">
    <value>Cannot Have Amount If Percentage / Amount Is Not Amount.</value>
  </data>
  <data name="0042" xml:space="preserve">
    <value>Amount Required If Percentage / Amount Is Amount.</value>
  </data>
  <data name="0043" xml:space="preserve">
    <value>OldEmployeeId can't be the same as Employee's id</value>
  </data>
  <data name="0044" xml:space="preserve">
    <value>OldEmployeeId must be in the same company group</value>
  </data>
  <data name="0045" xml:space="preserve">
    <value>Tax Reference Number Must Be 10 Numeric Characters Long.</value>
  </data>
  <data name="0046" xml:space="preserve">
    <value>Please Add The Leave Pay Component To Your Company Components Before You Encash An Employee's Leave.</value>
  </data>
  <data name="0047" xml:space="preserve">
    <value>A closed termination run is not allowed</value>
  </data>
  <data name="0048" xml:space="preserve">
    <value>You cannot reinstate a tax profile that was not terminated.</value>
  </data>
  <data name="0049" xml:space="preserve">
    <value>Tax Reference Number Required</value>
  </data>
  <data name="0051" xml:space="preserve">
    <value>The passport issue country must be completed.</value>
  </data>
  <data name="0052" xml:space="preserve">
    <value>Duplicate Employee Number {0} on rows {1}</value>
  </data>
  <data name="0053" xml:space="preserve">
    <value>{0} cannot exceed dates older than {1} years</value>
  </data>
  <data name="0054" xml:space="preserve">
    <value>Employment Date may not be after pay rate effective date or position effective date.</value>
  </data>
  <data name="AcceptedStability" xml:space="preserve">
    <value>Accepted Stability</value>
  </data>
  <data name="AccessDenied" xml:space="preserve">
    <value>You don’t have permission to edit the record</value>
  </data>
  <data name="AccessDeniedForEmp" xml:space="preserve">
    <value>You have invalid permissions to modify employee with number {0}</value>
  </data>
  <data name="AccessDeniedForOrgUnit" xml:space="preserve">
    <value>You have invalid security organisation unit permissions to modify employee with number {0}</value>
  </data>
  <data name="btnCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="btnContinue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="btnNo" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="btnProceed" xml:space="preserve">
    <value>Proceed</value>
  </data>
  <data name="btnSkip" xml:space="preserve">
    <value>Skip Reassignment</value>
  </data>
  <data name="btnYes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="CompanyRunId" xml:space="preserve">
    <value>Company run</value>
  </data>
  <data name="Deemed75Indicator" xml:space="preserve">
    <value>Does the deemed fixed monthly remuneration make up 75% or more of the annual remuneration</value>
  </data>
  <data name="DeemedMonthlyRemuneration" xml:space="preserve">
    <value>Deemed monthly remuneration</value>
  </data>
  <data name="DeemedRecoveryMonthly" xml:space="preserve">
    <value>Will the company recover any tax on the notional income from the director</value>
  </data>
  <data name="DentalBenefitId" xml:space="preserve">
    <value>Employer Offered Dental Benefits</value>
  </data>
  <data name="DirectorshipId" xml:space="preserve">
    <value>Directorship</value>
  </data>
  <data name="EmployeeNumber" xml:space="preserve">
    <value>Employee Number</value>
  </data>
  <data name="EmploymentAction" xml:space="preserve">
    <value>Employment Action</value>
  </data>
  <data name="EmploymentDate" xml:space="preserve">
    <value>Employment date</value>
  </data>
  <data name="EmploymentDate.tooltip" xml:space="preserve">
    <value>Refers to the employee's start date within the company. This is the employee's most recent start date.</value>
  </data>
  <data name="EmploymentIdentifier" xml:space="preserve">
    <value>Employment ID</value>
  </data>
  <data name="EncashLeave" xml:space="preserve">
    <value>Encash Leave</value>
  </data>
  <data name="errAadharNumberDigitsOnly" xml:space="preserve">
    <value>The Aadhar Number field is a numeric field and should have no spaces.</value>
  </data>
  <data name="errAsylumIdNumberRequired" xml:space="preserve">
    <value>Asylum Identity No. is required if Nature of Person is Asylum Seeker.</value>
  </data>
  <data name="errBrazilInvalidSocialSecurityFormat" xml:space="preserve">
    <value>Invalid format for social security identity no</value>
  </data>
  <data name="errBrazilInvalidSocialSecurityNumber" xml:space="preserve">
    <value>Invalid Social Security Number</value>
  </data>
  <data name="errDuplicateEmploymentIdentifierInTaxYear" xml:space="preserve">
    <value>Duplicate Employment ID in tax year</value>
  </data>
  <data name="errDuplicateIdNumber" xml:space="preserve">
    <value>Duplicate id numbers exists</value>
  </data>
  <data name="errEmploymentDate" xml:space="preserve">
    <value>Employment date cannot be after employee's effective date.</value>
  </data>
  <data name="errEmploymentIdentifierMaxLength" xml:space="preserve">
    <value>Employment ID may not exceed 100 characters</value>
  </data>
  <data name="errEmploymentIdentifierRequired" xml:space="preserve">
    <value>Employment ID is required</value>
  </data>
  <data name="errEmploymentStatusMayNotBeNew" xml:space="preserve">
    <value>This Employee has been Terminated and therefore has to be Re-Instated Resuming or Starting A New Tax record.</value>
  </data>
  <data name="errEmploymentStatusNewNotAllowed" xml:space="preserve">
    <value>The employee employment status may not be New.</value>
  </data>
  <data name="errGroupJoinDate" xml:space="preserve">
    <value>Group join date cannot be after employee's effective date.</value>
  </data>
  <data name="errIdNumberAndIdentifierNotUnique" xml:space="preserve">
    <value>This PPS number and Employment ID combination is already in use. The PPS number and Employment ID must be unique for each employment.</value>
  </data>
  <data name="errIdNumberPPSDuplicate" xml:space="preserve">
    <value>This PPS number is already in use. The PPS number must be unique for each employee record.</value>
  </data>
  <data name="errIdNumbersOnly" xml:space="preserve">
    <value>The Social Insurance No. field is a numeric field and should have no spaces.</value>
  </data>
  <data name="errInvalidCanadianIdLength" xml:space="preserve">
    <value>ID number must be exactly 9 digits long.</value>
  </data>
  <data name="errInvalidIndiaIdLength" xml:space="preserve">
    <value>Aadhar Number must be exactly 12 digits long.</value>
  </data>
  <data name="errInvalidSocialSecurityNumber" xml:space="preserve">
    <value>Invalid social security number</value>
  </data>
  <data name="errInvalidTerminationReason" xml:space="preserve">
    <value>Invalid termination reason for selected employee tax status</value>
  </data>
  <data name="errLinkedToPayrate" xml:space="preserve">
    <value>Cannot delete record as it has payrates linked to it.</value>
  </data>
  <data name="errMustHaveActiveFrequency" xml:space="preserve">
    <value>Employee cannot be reinstated in a frequency that is inactive.</value>
  </data>
  <data name="errObsoleteTaxStatusId" xml:space="preserve">
    <value>The selected tax status is obsolete and no longer valid.</value>
  </data>
  <data name="errPaySeveranceRequired" xml:space="preserve">
    <value>Pay severance is required</value>
  </data>
  <data name="errReinstateWithBreakNotAllowed" xml:space="preserve">
    <value>Employee cannot be reinstated due to a break in service. Create a new payroll ID for the employee.</value>
  </data>
  <data name="errSeveranceDaysRequired" xml:space="preserve">
    <value>Severance days is required</value>
  </data>
  <data name="errTaxDirectiveNumberRequired" xml:space="preserve">
    <value>Tax directive number is required when 'Annuitant with multiple income sources Par 2(2B)' is not activated</value>
  </data>
  <data name="errTaxReferenceNumberAssigned" xml:space="preserve">
    <value>An Employee With The Entered PAN No. Already Exists. - ({0})</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Full Name</value>
  </data>
  <data name="GroupJoinDate" xml:space="preserve">
    <value>Group join date</value>
  </data>
  <data name="GroupJoinDate.tooltip" xml:space="preserve">
    <value>Refers to the employee's original start date within the group.</value>
  </data>
  <data name="IdentityType" xml:space="preserve">
    <value>Identity Type</value>
  </data>
  <data name="IdentityTypeId" xml:space="preserve">
    <value>Identification Type</value>
  </data>
  <data name="IdNumber" xml:space="preserve">
    <value>National Identification Number</value>
  </data>
  <data name="InvalidFrequency" xml:space="preserve">
    <value>Invalid Frequency</value>
  </data>
  <data name="InvalidNumericValues" xml:space="preserve">
    <value>Exactly {0} numeric values should be applied to {1}.</value>
  </data>
  <data name="InvalidTaxReferenceNumberLength" xml:space="preserve">
    <value>Tax reference Number must contain 14 characters</value>
  </data>
  <data name="IR21ReminderTitle" xml:space="preserve">
    <value>IR21</value>
  </data>
  <data name="IR21ReminderWarningMessage" xml:space="preserve">
    <value>IR21 needs to be submitted for this employee ({0}).</value>
  </data>
  <data name="Irp30" xml:space="preserve">
    <value>Has an IRP30 certificate been issued?</value>
  </data>
  <data name="lbCannotChangeEmploymentDateExistingRunsError" xml:space="preserve">
    <value>Committed transactions exist in the current records tax year, you may not change the employment date</value>
  </data>
  <data name="lblActionsGroup" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="lblActiveLeaveApplicationsOnTerminationWarning" xml:space="preserve">
    <value>This employee has pending leave transaction(s), and/or approved leave transaction(s) with dates that fall after the selected termination date. Are you sure you want to continue?</value>
  </data>
  <data name="lblAmount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="lblArrearsComponentsOnTerminationWarning" xml:space="preserve">
    <value>This employee has outstanding arrears on one or more components. Please ensure this is recovered accordingly.</value>
  </data>
  <data name="lblArrearsComponentsWarning" xml:space="preserve">
    <value>Employee is linked to arrears components. Balance adjustment advised.</value>
  </data>
  <data name="lblAttention" xml:space="preserve">
    <value>Attention</value>
  </data>
  <data name="lblCannotChangeIfReInstatingWithNewRecord" xml:space="preserve">
    <value>Cannot Change {0} While Reinstating This Employee Starting A New Tax Record.</value>
  </data>
  <data name="lblCannotChangeIfReInstatingWithSameRecord" xml:space="preserve">
    <value>Cannot Change {0} While Reinstating This Employee Resuming This Tax Record.</value>
  </data>
  <data name="lblCannotChangeIfTerminated" xml:space="preserve">
    <value>Cannot Change {0} While Employee Is Terminated</value>
  </data>
  <data name="lblCannotChangeIfTerminating" xml:space="preserve">
    <value>Cannot Change {0} While Terminating An Employee.</value>
  </data>
  <data name="lblCannotReinstateIfNotLatest" xml:space="preserve">
    <value>Can't Reinstate If Record Is Not The Latest By Employement Date</value>
  </data>
  <data name="lblCannotTerminatedIfNotLatest" xml:space="preserve">
    <value>Can'T Terminate If Record Is Not The Latest By Employement Date</value>
  </data>
  <data name="lblCitizanshipNotMatchingIdNumber" xml:space="preserve">
    <value>The Id number does not match the employee citizenship.</value>
  </data>
  <data name="lblCompanyRun" xml:space="preserve">
    <value>Company run</value>
  </data>
  <data name="lblCompanyTrustIdType" xml:space="preserve">
    <value>CC/Company/Trust</value>
  </data>
  <data name="lblCustomDate1" xml:space="preserve">
    <value>First custom date</value>
  </data>
  <data name="lblCustomDate2" xml:space="preserve">
    <value>Second custom date</value>
  </data>
  <data name="lblDateOfBirthNotMatchingIdNumber" xml:space="preserve">
    <value>Id number does not match employee birth date.</value>
  </data>
  <data name="lblDeleteExistingPayslipsError" xml:space="preserve">
    <value>Cannot delete record as it has payslips linked to it.
Most recent payslips:
{0}</value>
  </data>
  <data name="lblDeleteNotLatestError" xml:space="preserve">
    <value>Cannot delete record as there are newer records</value>
  </data>
  <data name="lblDetail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="lblDirectorship" xml:space="preserve">
    <value>Directorship</value>
  </data>
  <data name="lblDistinctEmployeeEmployementActions" xml:space="preserve">
    <value>Multiple transactions for the same Employee Number / Employment Action detected.</value>
  </data>
  <data name="lblDownload" xml:space="preserve">
    <value>P45 Termination Certificate</value>
  </data>
  <data name="lblDownloadLeaveErrors" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="lblDuplicateEmploymentDate" xml:space="preserve">
    <value>Duplicate/overlapping transactions exist.</value>
  </data>
  <data name="lblDuplicateGroupEmployeeIdNumberWarning" xml:space="preserve">
    <value>This ID number already exists within the below group of companies</value>
  </data>
  <data name="lblDuplicateTerminationDate" xml:space="preserve">
    <value>Duplicate/overlapping transactions exist.</value>
  </data>
  <data name="lblEdit" xml:space="preserve">
    <value>Edit record</value>
  </data>
  <data name="lblEmployeeFrequencyRequired" xml:space="preserve">
    <value>Employee frequency is required</value>
  </data>
  <data name="lblEmployeeTerminationHeader" xml:space="preserve">
    <value>Terminate and Reinstate</value>
  </data>
  <data name="lblEmployment" xml:space="preserve">
    <value>Employment</value>
  </data>
  <data name="lblEmploymentActionMustBeNullOnCreation" xml:space="preserve">
    <value>Employment Action Must Be Null On Creation</value>
  </data>
  <data name="lblEmploymentDateBeforeJoinDate" xml:space="preserve">
    <value>Employment date cannot be before Group join date</value>
  </data>
  <data name="lblEmploymentDateCantBeBeforeAnyTerminationDate" xml:space="preserve">
    <value>Employment Date Cannot Be Before A Previous Termination Date</value>
  </data>
  <data name="lblEmploymentDateRequired" xml:space="preserve">
    <value>Employment Date Required</value>
  </data>
  <data name="lblEmploymentStatusDeleted" xml:space="preserve">
    <value>Tax Profile Successfully Removed</value>
  </data>
  <data name="lblEmploymentStatusSaved" xml:space="preserve">
    <value>Tax Profile Successfully Saved</value>
  </data>
  <data name="lblExistingEmploymentDate" xml:space="preserve">
    <value>You cannot have multiple Tax profiles with the same employment date.</value>
  </data>
  <data name="lblFinalizeTaxInvalidReferenceError" xml:space="preserve">
    <value>Finalise and issue the tax certificate cannot be checked when the company tax certificate number is invalid</value>
  </data>
  <data name="lblFullName" xml:space="preserve">
    <value>Full Name</value>
  </data>
  <data name="lblGenderNotMatchingIdNumber" xml:space="preserve">
    <value>Id number does not match employee gender.</value>
  </data>
  <data name="lblHasLeaveOverlap" xml:space="preserve">
    <value>The employee has an approved leave application with overlapping dates.</value>
  </data>
  <data name="lblHasPayslipBeforeEmploymentDate" xml:space="preserve">
    <value>Committed transactions exist, you may not change the employment date to a future date</value>
  </data>
  <data name="lblHistory" xml:space="preserve">
    <value>History</value>
  </data>
  <data name="lblIdentificationGroup" xml:space="preserve">
    <value>Identification</value>
  </data>
  <data name="lblIDIdType" xml:space="preserve">
    <value>Social Security</value>
  </data>
  <data name="lblIdNumber" xml:space="preserve">
    <value>National Identification Number</value>
  </data>
  <data name="lblIdNumberDuplicate" xml:space="preserve">
    <value>An Employee With The Entered Id Number Already Exists - {0}</value>
  </data>
  <data name="lblIdNumberExpiryDate" xml:space="preserve">
    <value>Social Insurance Number Expiry Date</value>
  </data>
  <data name="lblIdNumberExpiryDateRequired" xml:space="preserve">
    <value>Social Insurance Number Expiry Date is required</value>
  </data>
  <data name="lblIdNumberInvalid" xml:space="preserve">
    <value>Id number is invalid.</value>
  </data>
  <data name="lblIdNumberMaxLengthExceeded" xml:space="preserve">
    <value>Id Number cannot be more that 30 characters</value>
  </data>
  <data name="lblIdNumberMustBeNull" xml:space="preserve">
    <value>ID number must be null when Nature of person is B</value>
  </data>
  <data name="lblIdNumberNotCorrectLength" xml:space="preserve">
    <value>Id number must contain 13 digits.</value>
  </data>
  <data name="lblInvalidDniNieNumber" xml:space="preserve">
    <value>Invalid DNI/NIE Identity Number.</value>
  </data>
  <data name="lblInvalidEmploymentDate" xml:space="preserve">
    <value>Invalid Employment Date</value>
  </data>
  <data name="lblInvalidIdentityType" xml:space="preserve">
    <value>IdentityType is invalid for the selected nature of person.</value>
  </data>
  <data name="lblInvalidIdNumberFormat" xml:space="preserve">
    <value>When Nature of person is either E, G, or H, Identity No. should have the format CCYY/NNNNNN/NN</value>
  </data>
  <data name="lblInvalidNAF" xml:space="preserve">
    <value>NAF number is invalid.</value>
  </data>
  <data name="lblInvalidPassportMaximumNumber" xml:space="preserve">
    <value>Passport number can be no longer than {0} characters.</value>
  </data>
  <data name="lblInvalidPassportNumber" xml:space="preserve">
    <value>Passport number must be at least {0} characters long.</value>
  </data>
  <data name="lblInvalidPercentageError" xml:space="preserve">
    <value>Percentage cannot be greater than 100</value>
  </data>
  <data name="lblInvalidTaxReferenceNumber" xml:space="preserve">
    <value>The employee's Tax Reference Number is invalid</value>
  </data>
  <data name="lblIsBreakInService" xml:space="preserve">
    <value>Has there been a break in service?</value>
  </data>
  <data name="lblJobActiveWarning" xml:space="preserve">
    <value>This employee is linked to job ({0}), that has been assigned to another employee ({1}). Please create a new position record with a new job number.</value>
  </data>
  <data name="lblKenyaEmployeePIN" xml:space="preserve">
    <value>Employee PIN</value>
  </data>
  <data name="lblLeaveBalance" xml:space="preserve">
    <value>Leave balance</value>
  </data>
  <data name="lblLeaveBalanceValue" xml:space="preserve">
    <value>Leave value</value>
  </data>
  <data name="lblLeaveErrorOnTerminationWarning" xml:space="preserve">
    <value>The selected termination date has resulted in leave applications that require attention</value>
  </data>
  <data name="lblMustBeAfterSuspensions" xml:space="preserve">
    <value>Employee has a suspension record after the termination date - please edit/delete the suspension first</value>
  </data>
  <data name="lblMustReInstateNewRecord" xml:space="preserve">
    <value>This Employee Has Been Terminated In A Previous Tax Year And Therefore Has To Be Re-Instated With A Break In Service.</value>
  </data>
  <data name="lblNatureOfPersonRequired" xml:space="preserve">
    <value>The Nature Of Person field is required</value>
  </data>
  <data name="lblNotReEmployableWarning" xml:space="preserve">
    <value>The employee you are trying to reinstate has been flagged as Not Re-employable</value>
  </data>
  <data name="lblNoVacantJob" xml:space="preserve">
    <value>No vacant jobs are available. Create a new job in order to reinstate this employee.</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Tax Profile</value>
  </data>
  <data name="lblPassportExpiryBeforeIssueDate" xml:space="preserve">
    <value>Passport expiry date Cannot be Before Passport Issue Date</value>
  </data>
  <data name="lblPassportExpiryDateRequired" xml:space="preserve">
    <value>PassportExpiry is Required</value>
  </data>
  <data name="lblPassportLengthError" xml:space="preserve">
    <value>Passport number should be atleast 7 characters long</value>
  </data>
  <data name="lblPassportNumberIsRequired" xml:space="preserve">
    <value>Passport Number Is Required When Selecting Work Permit/Passport As The Identification Type.</value>
  </data>
  <data name="lblPendingInboxes" xml:space="preserve">
    <value>This employee has pending inbox entries</value>
  </data>
  <data name="lblPercent" xml:space="preserve">
    <value>Percent</value>
  </data>
  <data name="lblPermitExpiryBeforeIssueDate" xml:space="preserve">
    <value>Permit expiry date Cannot be Before Permit Issue Date</value>
  </data>
  <data name="lblPermitExpiryDateRequired" xml:space="preserve">
    <value>PermitExpiry is Required</value>
  </data>
  <data name="lblRedirectInstructions" xml:space="preserve">
    <value>Please note that the terminated employee has subordinates that will be affected. To reassign the reporting for these employees, click 'Proceed' and you will be redirected to the Bulk Reporting To Change screen.</value>
  </data>
  <data name="lblReInstateNewRecord" xml:space="preserve">
    <value>Reinstate Starting A New Record</value>
  </data>
  <data name="lblReInstateOldRecord" xml:space="preserve">
    <value>Reinstate Resuming This Record</value>
  </data>
  <data name="lblReinstateWithBreakNotAllowed" xml:space="preserve">
    <value>If the employee has been with another employer after termination, the employee should not be reinstated resuming this record. Create a new payroll ID for the employee</value>
  </data>
  <data name="lblRpnHistory" xml:space="preserve">
    <value>RPN</value>
  </data>
  <data name="lblSave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="lblSelectRunBeforeDisplayingBalance" xml:space="preserve">
    <value>Please select a company run in order to view the balance</value>
  </data>
  <data name="lblShowLeaveBalance" xml:space="preserve">
    <value>Show leave balance and value</value>
  </data>
  <data name="lblStabilityWarningAreYouSure" xml:space="preserve">
    <value>Are you sure that you want to make this change?</value>
  </data>
  <data name="lblStabilityWarningHeading" xml:space="preserve">
    <value>This employee is protected by stability and terminating the employee may result in fines.</value>
  </data>
  <data name="lblSubmit" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="lblTaxGroup" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="lblTaxStatusRequired" xml:space="preserve">
    <value>The Tax Status field is required</value>
  </data>
  <data name="lblTerminated" xml:space="preserve">
    <value>Reporting Line Changes</value>
  </data>
  <data name="lblTermination" xml:space="preserve">
    <value>Terminate employee</value>
  </data>
  <data name="lblTerminationGroup" xml:space="preserve">
    <value>Termination</value>
  </data>
  <data name="lblWorkIdType" xml:space="preserve">
    <value>Work Permit</value>
  </data>
  <data name="lblZambiaTaxReferenceNumber" xml:space="preserve">
    <value>Taxpayer Identification Number (TPIN)</value>
  </data>
  <data name="MyKadExpiry" xml:space="preserve">
    <value>MyKad expiry date</value>
  </data>
  <data name="MyKadIssued" xml:space="preserve">
    <value>MyKad issue date</value>
  </data>
  <data name="NatureOfPerson" xml:space="preserve">
    <value>Nature Of Person</value>
  </data>
  <data name="NatureOfPersonId" xml:space="preserve">
    <value>Nature of person</value>
  </data>
  <data name="NotReEmployable" xml:space="preserve">
    <value>Not re-employable</value>
  </data>
  <data name="OtherExpiry" xml:space="preserve">
    <value>Others expiry date</value>
  </data>
  <data name="OtherIssued" xml:space="preserve">
    <value>Others issue date</value>
  </data>
  <data name="PassportCountryId" xml:space="preserve">
    <value>Passport issuing country</value>
  </data>
  <data name="PassportExpiry" xml:space="preserve">
    <value>Passport expiry date</value>
  </data>
  <data name="PassportIssued" xml:space="preserve">
    <value>Passport issue date</value>
  </data>
  <data name="PassportNumber" xml:space="preserve">
    <value>Passport number</value>
  </data>
  <data name="Percentage" xml:space="preserve">
    <value>Percentage / Amount value</value>
  </data>
  <data name="PercentageAmount" xml:space="preserve">
    <value>Percentage / Amount</value>
  </data>
  <data name="PermitExpiry" xml:space="preserve">
    <value>Permit expiry date</value>
  </data>
  <data name="PermitIssued" xml:space="preserve">
    <value>Permit issue date</value>
  </data>
  <data name="ReferenceNumber" xml:space="preserve">
    <value>KITAS Number</value>
  </data>
  <data name="SeveranceDay" xml:space="preserve">
    <value>Severance days</value>
  </data>
  <data name="SeveranceDayId" xml:space="preserve">
    <value>Severance days</value>
  </data>
  <data name="ShadowPayroll" xml:space="preserve">
    <value>Shadow payroll</value>
  </data>
  <data name="ShadowPayroll.tooltip" xml:space="preserve">
    <value>Employee is paid under a 'shadow payroll' arrangement</value>
  </data>
  <data name="SocialSecurityNumberRequired" xml:space="preserve">
    <value>Social security number is required.</value>
  </data>
  <data name="TaxDirectiveNumber" xml:space="preserve">
    <value>Tax directive number</value>
  </data>
  <data name="TaxReferenceNumber" xml:space="preserve">
    <value>NIK/NPWP Number</value>
  </data>
  <data name="TaxReferenceNumberValidationMessage" xml:space="preserve">
    <value>must be 10 numeric characters long</value>
  </data>
  <data name="TaxStatus" xml:space="preserve">
    <value>Tax Status</value>
  </data>
  <data name="TaxStatusId" xml:space="preserve">
    <value>Tax status</value>
  </data>
  <data name="TempWorker" xml:space="preserve">
    <value>Accumulate periods worked as a casual worker</value>
  </data>
  <data name="TerminationCompanyRun" xml:space="preserve">
    <value>Termination Run</value>
  </data>
  <data name="TerminationDate" xml:space="preserve">
    <value>Termination date</value>
  </data>
  <data name="TerminationLeave" xml:space="preserve">
    <value>Encash leave</value>
  </data>
  <data name="TerminationPaySeverance" xml:space="preserve">
    <value>Pay severance</value>
  </data>
  <data name="TerminationReason" xml:space="preserve">
    <value>Termination reason</value>
  </data>
  <data name="TerminationReasonId" xml:space="preserve">
    <value>Termination reason</value>
  </data>
</root>