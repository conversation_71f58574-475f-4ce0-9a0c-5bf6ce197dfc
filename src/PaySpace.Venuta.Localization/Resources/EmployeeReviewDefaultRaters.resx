<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AccessJournal" xml:space="preserve">
    <value>Access journal</value>
  </data>
  <data name="DefaultTemplate" xml:space="preserve">
    <value>Default template</value>
  </data>
  <data name="DirectReportsTo" xml:space="preserve">
    <value>Direct Reports to</value>
  </data>
  <data name="EmployeeNumber" xml:space="preserve">
    <value>Employee Number</value>
  </data>
  <data name="errDefRaterIdGreaterThanZero" xml:space="preserve">
    <value>DefRaterId should be greater than zero</value>
  </data>
  <data name="errEmployeeHasNoManagerOrDirectReportInHierarchy" xml:space="preserve">
    <value>Cannot have Inherit checkbox selection as TRUE when Directly reports to field on the employee Position screen is blank</value>
  </data>
  <data name="errEmployeeIdRequired" xml:space="preserve">
    <value>EmployeeId is required</value>
  </data>
  <data name="errInvalidRaterTypeSelection" xml:space="preserve">
    <value>Invalid Rater Type Selection</value>
  </data>
  <data name="errMoreThanOneRaterSelectedInheritRaterFromPosition" xml:space="preserve">
    <value>Cannot have more than one rater selected for Inherit rater from position</value>
  </data>
  <data name="errProcessTypeMoreThanOneDefaultTemplate" xml:space="preserve">
    <value>Process type cannot have more than one Default Templates linked</value>
  </data>
  <data name="errProcessTypeRequired" xml:space="preserve">
    <value>Process Type is required</value>
  </data>
  <data name="errRaterEmpNumberDoesNotMatchRaterTypeAndInheritSelection" xml:space="preserve">
    <value>Rater Emp Number does not match Rater Type and Inherit selection</value>
  </data>
  <data name="errRaterEmpNumberDoesNotMatchRaterTypeSelection" xml:space="preserve">
    <value>Rater Emp Number does not match Rater Type selection</value>
  </data>
  <data name="errRaterEmpNumberRequired" xml:space="preserve">
    <value>Rater Emp number is required</value>
  </data>
  <data name="errRaterIdAlreadyLinked" xml:space="preserve">
    <value>This Rater Emp number is already linked to this employee</value>
  </data>
  <data name="errRaterTypeAlreadyLinked" xml:space="preserve">
    <value>Rater type is already linked to the Process type for this employee</value>
  </data>
  <data name="errRaterTypeRequired" xml:space="preserve">
    <value>Rater type is required</value>
  </data>
  <data name="errRaterWeightningsMust100" xml:space="preserve">
    <value>The Rater weightings must all add up to 100%</value>
  </data>
  <data name="errTemplateIdRequired" xml:space="preserve">
    <value>TemplateId is required</value>
  </data>
  <data name="errWeightingValidationError" xml:space="preserve">
    <value>Weightning is required and must be between 0 and 100</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Full Name</value>
  </data>
  <data name="IncludeScore" xml:space="preserve">
    <value>Include score</value>
  </data>
  <data name="InheritRaterFromPosition" xml:space="preserve">
    <value>Inherit rater from position</value>
  </data>
  <data name="lblEmployeeReviewDefaultRatersHeader" xml:space="preserve">
    <value>Rater Setup</value>
  </data>
  <data name="ProcessType" xml:space="preserve">
    <value>Process Type</value>
  </data>
  <data name="RaterEmpNumber" xml:space="preserve">
    <value>Rater Emp Number</value>
  </data>
  <data name="RaterName" xml:space="preserve">
    <value>Rater Name</value>
  </data>
  <data name="RaterType" xml:space="preserve">
    <value>Rater type</value>
  </data>
  <data name="ShowWeighting" xml:space="preserve">
    <value>Show weighting</value>
  </data>
  <data name="Weighting" xml:space="preserve">
    <value>Weighting %</value>
  </data>
</root>