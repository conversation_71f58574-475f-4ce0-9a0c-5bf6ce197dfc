<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="0007" xml:space="preserve">
    <value>Email Already Exists In This Company</value>
  </data>
  <data name="0008" xml:space="preserve">
    <value>The Legal Age For This Company's Tax Country Is 14 Years Old</value>
  </data>
  <data name="0009" xml:space="preserve">
    <value>The Legal Age For This Company's Tax Country Is 15 Years Old</value>
  </data>
  <data name="0010" xml:space="preserve">
    <value>The Legal Age For This Company's Tax Country Is 16 Years Old</value>
  </data>
  <data name="0011" xml:space="preserve">
    <value>Province is required</value>
  </data>
  <data name="0012" xml:space="preserve">
    <value>Country is required</value>
  </data>
  <data name="0013" xml:space="preserve">
    <value>Address code is required</value>
  </data>
  <data name="0014" xml:space="preserve">
    <value>Employee Number Already Exists In This Company</value>
  </data>
  <data name="0015" xml:space="preserve">
    <value>Street Name / Postal address number is required</value>
  </data>
  <data name="0016" xml:space="preserve">
    <value>Physical address type can't be modified</value>
  </data>
  <data name="0017" xml:space="preserve">
    <value>Physical address required</value>
  </data>
  <data name="0018" xml:space="preserve">
    <value>Duplicate address types not allowed</value>
  </data>
  <data name="0019" xml:space="preserve">
    <value>Initials Required</value>
  </data>
  <data name="0020" xml:space="preserve">
    <value>Invalid email format</value>
  </data>
  <data name="0021" xml:space="preserve">
    <value>Invalid province for given country</value>
  </data>
  <data name="0022" xml:space="preserve">
    <value>Employee number required</value>
  </data>
  <data name="0023" xml:space="preserve">
    <value>Invalid work number, only numbers allowed</value>
  </data>
  <data name="0024" xml:space="preserve">
    <value>Initals can't be more than {0} characters</value>
  </data>
  <data name="0025" xml:space="preserve">
    <value>Work extention can't be more than {0} characters</value>
  </data>
  <data name="0026" xml:space="preserve">
    <value>Email already registered</value>
  </data>
  <data name="0027" xml:space="preserve">
    <value>Marital status required</value>
  </data>
  <data name="0028" xml:space="preserve">
    <value>Birthday required</value>
  </data>
  <data name="0029" xml:space="preserve">
    <value>Gender required</value>
  </data>
  <data name="0030" xml:space="preserve">
    <value>Date created can't be set or changed</value>
  </data>
  <data name="0031" xml:space="preserve">
    <value>Address Line 3 is required</value>
  </data>
  <data name="0032" xml:space="preserve">
    <value>Address code must be between {0} and {1} characters</value>
  </data>
  <data name="0033" xml:space="preserve">
    <value>Address code can only contain numbers</value>
  </data>
  <data name="0034" xml:space="preserve">
    <value>Unit number can't be more than {0} characters</value>
  </data>
  <data name="0035" xml:space="preserve">
    <value>Complex can't be more than {0} characters</value>
  </data>
  <data name="0036" xml:space="preserve">
    <value>Street Name / Postal address number can't be more than {0} characters</value>
  </data>
  <data name="0037" xml:space="preserve">
    <value>Same as physical can't be set on Physical address line</value>
  </data>
  <data name="0038" xml:space="preserve">
    <value>Special services must be empty</value>
  </data>
  <data name="0039" xml:space="preserve">
    <value>Is care of address can't be set</value>
  </data>
  <data name="0040" xml:space="preserve">
    <value>Care of intermediary can't be set</value>
  </data>
  <data name="0041" xml:space="preserve">
    <value>Care of intermediary required</value>
  </data>
  <data name="0042" xml:space="preserve">
    <value>Citizenship required</value>
  </data>
  <data name="0043" xml:space="preserve">
    <value>City or Town / Postal city can't be more than {0} characters</value>
  </data>
  <data name="0044" xml:space="preserve">
    <value>Suburb or District / Postal agency can't be more than {0} characters</value>
  </data>
  <data name="0045" xml:space="preserve">
    <value>Complex not allowed for address type PO Box or Private bag</value>
  </data>
  <data name="0046" xml:space="preserve">
    <value>Unit number not allowed for address type PO Box or Private bag</value>
  </data>
  <data name="0047" xml:space="preserve">
    <value>EtiExempt only allowed for South Africa</value>
  </data>
  <data name="0048" xml:space="preserve">
    <value>One contact number required</value>
  </data>
  <data name="0050" xml:space="preserve">
    <value>The employee can't be deleted</value>
  </data>
  <data name="0051" xml:space="preserve">
    <value>Employee Number can't be more than {0} characters</value>
  </data>
  <data name="0052" xml:space="preserve">
    <value>Postal Code must match this pattern NNNNN-NNN</value>
  </data>
  <data name="0053" xml:space="preserve">
    <value>First Name cannot contain numeric characters.</value>
  </data>
  <data name="0054" xml:space="preserve">
    <value>Middle Name cannot contain numeric characters.</value>
  </data>
  <data name="0055" xml:space="preserve">
    <value>Last Name cannot contain numeric characters.</value>
  </data>
  <data name="0056" xml:space="preserve">
    <value>Initials cannot contain special characters.</value>
  </data>
  <data name="0057" xml:space="preserve">
    <value>{0} number must be at least 10 digits long</value>
  </data>
  <data name="0058" xml:space="preserve">
    <value>Email address does not meet SARS tax submission requirements due to invalid special characters</value>
  </data>
  <data name="0059" xml:space="preserve">
    <value>{0} number does not meet SARS tax submission requirements due to special character '+'. National numbers must start with a 0 and International numbers must start with 00</value>
  </data>
  <data name="0060" xml:space="preserve">
    <value>{0} number does not meet SARS tax submission requirements due to containing whitespaces or non-numeric characters</value>
  </data>
  <data name="0061" xml:space="preserve">
    <value>Invalid GenderEnum for Company Country. Refer to Metadata for the selected tax country</value>
  </data>
  <data name="0062" xml:space="preserve">
    <value>Either the City / Town field or the Suburb / District field is required.</value>
  </data>
  <data name="AccessDenied" xml:space="preserve">
    <value>You don’t have permission to edit the record</value>
  </data>
  <data name="AccessDeniedForEmp" xml:space="preserve">
    <value>You have invalid permissions to modify employee with number {0}</value>
  </data>
  <data name="AccessDeniedForOrgUnit" xml:space="preserve">
    <value>You have invalid security organisation unit permissions to modify employee with number {0}</value>
  </data>
  <data name="AddNewEmployeePageHeader" xml:space="preserve">
    <value>Add New Employee</value>
  </data>
  <data name="AddressLine2Required" xml:space="preserve">
    <value>Suburb is required.</value>
  </data>
  <data name="AustraliaAddressCodeFormatError" xml:space="preserve">
    <value>Physical or Postal address is restricted to values between 0200 and 9999</value>
  </data>
  <data name="AustraliaAddressCodeLengthError" xml:space="preserve">
    <value>Physical or Postal address is restricted to 4 characters.</value>
  </data>
  <data name="BlockMaxLengthError" xml:space="preserve">
    <value>Block cannot exceed {0} characters.</value>
  </data>
  <data name="btnSave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="CompanyFrequencyId" xml:space="preserve">
    <value>Company frequency</value>
  </data>
  <data name="CreateAccessDenied" xml:space="preserve">
    <value>You don't have permission to create the record.</value>
  </data>
  <data name="deleteMessage" xml:space="preserve">
    <value>The employee will be deleted entirely, are you sure you wish to proceed?</value>
  </data>
  <data name="deleteMessageNo" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="deleteMessageYes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="DenyEmployeeCompanyChange" xml:space="preserve">
    <value>An employee's company cannot be changed</value>
  </data>
  <data name="DoorMaxLengthError" xml:space="preserve">
    <value>Door cannot exceed {0} characters.</value>
  </data>
  <data name="EffectiveDate" xml:space="preserve">
    <value>Effective Date</value>
  </data>
  <data name="EmailAddressContainsSpace" xml:space="preserve">
    <value>Email address cannot contain spaces. Please enter a valid email address.</value>
  </data>
  <data name="EntranceMaxLengthError" xml:space="preserve">
    <value>Entrance cannot exceed {0} characters.</value>
  </data>
  <data name="errAddressCodeLength" xml:space="preserve">
    <value>Address code must be {0} characters.</value>
  </data>
  <data name="errAddressCodeMaxLength" xml:space="preserve">
    <value>The field Code must be a string with a maximum length of 12.</value>
  </data>
  <data name="errAddressCodeZeros" xml:space="preserve">
    <value>The {0} address postal code cannot be 0000.</value>
  </data>
  <data name="errAddressStreetTypeRequired" xml:space="preserve">
    <value>Address Street Type is Required</value>
  </data>
  <data name="errBirthDateUnder14" xml:space="preserve">
    <value>The employee is younger than 14 years old</value>
  </data>
  <data name="errDateOfBirthInFuture" xml:space="preserve">
    <value>Birth Date cannot be today or in the future.</value>
  </data>
  <data name="errEffectiveDateInvalid" xml:space="preserve">
    <value>Effective date must be unique to each record.</value>
  </data>
  <data name="errEffectiveDateRequired" xml:space="preserve">
    <value>Effective date required.</value>
  </data>
  <data name="errEmailAddressMaxLength" xml:space="preserve">
    <value>Employee's Email Address exceeds the {0} character limit</value>
  </data>
  <data name="errEmploymentDate" xml:space="preserve">
    <value>Effective date cannot be before the employee's employment date.</value>
  </data>
  <data name="errFrequencyInactive" xml:space="preserve">
    <value>Employee cannot be linked to an inactive pay frequency</value>
  </data>
  <data name="errFullNameMaxLength" xml:space="preserve">
    <value>Full Name must be a string with a maximum length of {0}</value>
  </data>
  <data name="errFullNameRequired" xml:space="preserve">
    <value>Full name field is required</value>
  </data>
  <data name="errGroupJoinDate" xml:space="preserve">
    <value>Effective date cannot be before the employee's group join date.</value>
  </data>
  <data name="errInvalidCanadaAddressCode" xml:space="preserve">
    <value>Invalid Format for Address Code</value>
  </data>
  <data name="errInvalidCharacterInAddressFields" xml:space="preserve">
    <value>Employee address contains an invalid character</value>
  </data>
  <data name="errInvalidFranceAddressCode" xml:space="preserve">
    <value>Invalid selection for Address Code</value>
  </data>
  <data name="errInvalidUKAddressCode" xml:space="preserve">
    <value>Postcode must be in the valid format</value>
  </data>
  <data name="errLanguageRequired" xml:space="preserve">
    <value>Language is required</value>
  </data>
  <data name="errMaxEffectiveDate" xml:space="preserve">
    <value>Effective Date cannot be after {0}</value>
  </data>
  <data name="errMaximumAgeExceeded" xml:space="preserve">
    <value>The date of birth entered exceeds the maximum age allowed.</value>
  </data>
  <data name="errMinimumAgeRequired" xml:space="preserve">
    <value>The date of birth entered is below the minimum age allowed.</value>
  </data>
  <data name="errMunicipalityRequired" xml:space="preserve">
    <value>Municipality code is required if the address country is Brazil.</value>
  </data>
  <data name="errPreferredNameMaxLength" xml:space="preserve">
    <value>The field Preferred Name must be a string with a maximum length of {0}</value>
  </data>
  <data name="errRaceRequired" xml:space="preserve">
    <value>The Race field is required.</value>
  </data>
  <data name="errStreeNumberRequired" xml:space="preserve">
    <value>The street number field is required</value>
  </data>
  <data name="FloorMaxLengthError" xml:space="preserve">
    <value>Piso no puede exceder {0} caracteres.</value>
  </data>
  <data name="IndiaAddressCodeFormatError" xml:space="preserve">
    <value>Invalid Format for Pin Code</value>
  </data>
  <data name="IndiaAddressCodeMaxLengthError" xml:space="preserve">
    <value>Pin Code must be 6 digits long</value>
  </data>
  <data name="InvalidFrequency" xml:space="preserve">
    <value>Invalid Frequency</value>
  </data>
  <data name="InvalidNamesLengthError" xml:space="preserve">
    <value>Name fields must contain between {0} and {1} characters.</value>
  </data>
  <data name="IrelandAddressCodeFormatError" xml:space="preserve">
    <value>The Eircode provided is not valid. The code must be a seven-character alpha-numeric code, made up of two parts.</value>
  </data>
  <data name="lblAddressTypePhysical" xml:space="preserve">
    <value>Physical</value>
  </data>
  <data name="lblAddressTypePostalBox" xml:space="preserve">
    <value>PO Box</value>
  </data>
  <data name="lblAddressTypePrivateBag" xml:space="preserve">
    <value>Private Bag</value>
  </data>
  <data name="lblAddressTypeStreet" xml:space="preserve">
    <value>Street</value>
  </data>
  <data name="lblApartment" xml:space="preserve">
    <value>Apartment</value>
  </data>
  <data name="lblBeninSdlExemption" xml:space="preserve">
    <value>VPS exemption reason</value>
  </data>
  <data name="lblBirthdate" xml:space="preserve">
    <value>Birth Date</value>
  </data>
  <data name="lblBirthdayWarning" xml:space="preserve">
    <value>The legal age for this company's tax country is 15 years old</value>
  </data>
  <data name="lblBlock" xml:space="preserve">
    <value>Block</value>
  </data>
  <data name="lblCellNumber" xml:space="preserve">
    <value>Cellphone Number</value>
  </data>
  <data name="lblCitiz" xml:space="preserve">
    <value>Citizenship</value>
  </data>
  <data name="lblCompanyImplantation" xml:space="preserve">
    <value>Company Implantation</value>
  </data>
  <data name="lblContactDetHeader" xml:space="preserve">
    <value>Contact Details</value>
  </data>
  <data name="lblDefault" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="lblDetailsHeader" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="lblDialCode" xml:space="preserve">
    <value>Dial Code</value>
  </data>
  <data name="lblDisabledT" xml:space="preserve">
    <value>Disabled Type</value>
  </data>
  <data name="lblDistributionCodeAbroad" xml:space="preserve">
    <value>Distribution code abroad</value>
  </data>
  <data name="lblDistributionService" xml:space="preserve">
    <value>Distribution Service</value>
  </data>
  <data name="lblDoor" xml:space="preserve">
    <value>Door</value>
  </data>
  <data name="lblEditSuccessful" xml:space="preserve">
    <value>Changes successfully saved.</value>
  </data>
  <data name="lblEmailAdd" xml:space="preserve">
    <value>Email Address</value>
  </data>
  <data name="lblEmailAddressBusyProcessing" xml:space="preserve">
    <value>Email will be validated within 15 minutes (emails will still be sent until then)</value>
  </data>
  <data name="lblEmailAddressConfirmStatusUpdate" xml:space="preserve">
    <value>Are you sure you want to validate the email address again?</value>
  </data>
  <data name="lblEmailAddressInvalid" xml:space="preserve">
    <value>Invalid email address - emails will not be sent</value>
  </data>
  <data name="lblEmailAddressRefresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="lblEmailAddressUnverified" xml:space="preserve">
    <value>The email address is pending validation</value>
  </data>
  <data name="lblEmailAddressValid" xml:space="preserve">
    <value>The email address is valid</value>
  </data>
  <data name="lblEmailLinkedToUser" xml:space="preserve">
    <value>Email cannot be removed as it is linked to a user</value>
  </data>
  <data name="lblEmergContactDetHeader" xml:space="preserve">
    <value>Emergency Contact Details</value>
  </data>
  <data name="lblEmergContAddress" xml:space="preserve">
    <value>Emergency contact address</value>
  </data>
  <data name="lblEmergContName" xml:space="preserve">
    <value>Emergency contact name</value>
  </data>
  <data name="lblEmergContNumber" xml:space="preserve">
    <value>Emergency contact number</value>
  </data>
  <data name="lblEmpDetailsHeading" xml:space="preserve">
    <value>Employee Details</value>
  </data>
  <data name="lblEmpIDNumber" xml:space="preserve">
    <value>Aadhar Number</value>
  </data>
  <data name="lblEmpIDNumber.tooltip" xml:space="preserve">
    <value>Aadhar Number must be 12 digits.</value>
  </data>
  <data name="lblEmployeeLegalWorkAgeWarning" xml:space="preserve">
    <value>The legal age for this company's tax country is {0} years old.</value>
  </data>
  <data name="lblEmployeeNumberChangeNotAllowed" xml:space="preserve">
    <value>Changing of the Employee Number not allowed</value>
  </data>
  <data name="lblEmployeeNumberGenerated" xml:space="preserve">
    <value>Will be generated</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Employee Number</value>
  </data>
  <data name="lblEntrance" xml:space="preserve">
    <value>Entrance</value>
  </data>
  <data name="lblEntryCode" xml:space="preserve">
    <value>Entry Code</value>
  </data>
  <data name="lblEssRegistration" xml:space="preserve">
    <value>Please note that an ESS registration email will be sent to this email address when captured the first time.</value>
  </data>
  <data name="lblEtiExempt" xml:space="preserve">
    <value>Does Not Qualify For Employment Tax Incentive</value>
  </data>
  <data name="lblFirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="lblFirstNameMaxLength" xml:space="preserve">
    <value>First Name cannot exceed {0} characters</value>
  </data>
  <data name="lblFloor" xml:space="preserve">
    <value>Floor</value>
  </data>
  <data name="lblForeignNat" xml:space="preserve">
    <value>International Employee</value>
  </data>
  <data name="lblFormGroupCaption" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="lblGender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="lblHistory" xml:space="preserve">
    <value>History</value>
  </data>
  <data name="lblHomeNumber" xml:space="preserve">
    <value>Home Number</value>
  </data>
  <data name="lblImageDeletionMessage" xml:space="preserve">
    <value>Are you sure you want to delete this image?</value>
  </data>
  <data name="lblInheritFromCompany" xml:space="preserve">
    <value>Inherit From Company</value>
  </data>
  <data name="lblInitial" xml:space="preserve">
    <value>Initials</value>
  </data>
  <data name="lblInvalidBirthday" xml:space="preserve">
    <value>Invalid birth date</value>
  </data>
  <data name="lblInvalidCellNumber" xml:space="preserve">
    <value>Invalid cellphone number, only numbers allowed</value>
  </data>
  <data name="lblInvalidHomeNumber" xml:space="preserve">
    <value>Invalid home number, only numbers allowed</value>
  </data>
  <data name="lblIsCareofAddress" xml:space="preserve">
    <value>Is the postal address a care of address?</value>
  </data>
  <data name="lblIsEmpRetired" xml:space="preserve">
    <value>Is this employee retired?</value>
  </data>
  <data name="lblIso2DigitCode" xml:space="preserve">
    <value>ISO 2 Digit Code</value>
  </data>
  <data name="lblIso3DigitCode" xml:space="preserve">
    <value>ISO 3 Digit Code</value>
  </data>
  <data name="lblIsRetiredWarningPart1" xml:space="preserve">
    <value>This tick option is used for the Pension/Provident/RA calculation for legally retired employees.</value>
  </data>
  <data name="lblIsRetiredWarningPart2" xml:space="preserve">
    <value>If an Employer contributes to any of these Funds on the Employee's behalf, no Fringe benefit will calculate.</value>
  </data>
  <data name="lblKenyaSdlexemption" xml:space="preserve">
    <value>NITA Exemption Reason</value>
  </data>
  <data name="lblLanguage" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="lblLastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="lblLastNameMaxLength" xml:space="preserve">
    <value>Last Name cannot exceed {0} characters</value>
  </data>
  <data name="lblMaidenName" xml:space="preserve">
    <value>Maiden Name</value>
  </data>
  <data name="lblMaritalStat" xml:space="preserve">
    <value>Marital Status</value>
  </data>
  <data name="lblMiddleName" xml:space="preserve">
    <value>Middle Name</value>
  </data>
  <data name="lblMiddleNameMaxLength" xml:space="preserve">
    <value>Middle Name cannot exceed {0} characters</value>
  </data>
  <data name="lblMissingAddress" xml:space="preserve">
    <value>Missing primary address on employee.</value>
  </data>
  <data name="lblName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lblNamibiaSdlExemption" xml:space="preserve">
    <value>Vet levy exemption reason</value>
  </data>
  <data name="lblNationality" xml:space="preserve">
    <value>Nationality</value>
  </data>
  <data name="lblNationalityDescription" xml:space="preserve">
    <value>Nationality Description</value>
  </data>
  <data name="lblNewEmployeeHistory" xml:space="preserve">
    <value>Basic Profile</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Basic Profile</value>
  </data>
  <data name="lblPhysicalAddress" xml:space="preserve">
    <value>Permanent Address</value>
  </data>
  <data name="lblPostalAddress" xml:space="preserve">
    <value>Present Address</value>
  </data>
  <data name="lblPostalAddressNumberMaxError" xml:space="preserve">
    <value>The field Street Name must be a string with a maximum length of {0}</value>
  </data>
  <data name="lblPreferredNameMaxLength" xml:space="preserve">
    <value>Preferred Name cannot exceed {0} characters</value>
  </data>
  <data name="lblPrefName" xml:space="preserve">
    <value>Full Name</value>
  </data>
  <data name="lblQuickAdd" xml:space="preserve">
    <value>Quick Add</value>
  </data>
  <data name="lblRace" xml:space="preserve">
    <value>Race</value>
  </data>
  <data name="lblRemoveImage" xml:space="preserve">
    <value>Remove image</value>
  </data>
  <data name="lblReportId" xml:space="preserve">
    <value>Default Payslip</value>
  </data>
  <data name="lblSameAsPostal" xml:space="preserve">
    <value>Is Present Address same as Permanent?</value>
  </data>
  <data name="lblSDLExceptionRequired" xml:space="preserve">
    <value>SDL exception reason required</value>
  </data>
  <data name="lblSdlexemption" xml:space="preserve">
    <value>SDL exemption reason</value>
  </data>
  <data name="lblSelfDeregister" xml:space="preserve">
    <value>Self-Deregister:</value>
  </data>
  <data name="lblSenegalSdlExemption" xml:space="preserve">
    <value>CFCE exemption reason</value>
  </data>
  <data name="lblStaircase" xml:space="preserve">
    <value>Staircase</value>
  </data>
  <data name="lblStandardIndustryCodeHeader" xml:space="preserve">
    <value>Standard Industry Code Group</value>
  </data>
  <data name="lblStreetNameMaxError" xml:space="preserve">
    <value>The field Street Name must be a string with a maximum length of {0}</value>
  </data>
  <data name="lblSubmit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="lblSubStandardIndustryCodeId" xml:space="preserve">
    <value>Standard Industry Code</value>
  </data>
  <data name="lblTitle" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="lblUIFExceptionRequired" xml:space="preserve">
    <value>UIF exception reason required</value>
  </data>
  <data name="lblUifexemption" xml:space="preserve">
    <value>UIF exemption reason</value>
  </data>
  <data name="lblUkEmpNumber" xml:space="preserve">
    <value>Employee Number / Payroll ID</value>
  </data>
  <data name="lblUkLastName" xml:space="preserve">
    <value>Last Name / Surname</value>
  </data>
  <data name="lblUkProvince" xml:space="preserve">
    <value>Code / Postcode</value>
  </data>
  <data name="lblWorkEx" xml:space="preserve">
    <value>Work Extension</value>
  </data>
  <data name="lblWorkNumber" xml:space="preserve">
    <value>Work Number</value>
  </data>
  <data name="OrganizationRegion" xml:space="preserve">
    <value>Organization Region</value>
  </data>
  <data name="StaircaseMaxLengthError" xml:space="preserve">
    <value>Staircase cannot exceed {0} characters.</value>
  </data>
  <data name="tabAddresses" xml:space="preserve">
    <value>Addresses</value>
  </data>
  <data name="tabContactDetails" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="tabEmployeeDetails" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="tabExemptionsAndOther" xml:space="preserve">
    <value>Exemptions and Other</value>
  </data>
  <data name="tabOnBehalfOf" xml:space="preserve">
    <value>Act on my behalf</value>
  </data>
  <data name="tabPersonal" xml:space="preserve">
    <value>Personal</value>
  </data>
  <data name="tabWorkflowSettings" xml:space="preserve">
    <value>Out of Office Settings</value>
  </data>
  <data name="txtimage" xml:space="preserve">
    <value>Employee photo</value>
  </data>
</root>