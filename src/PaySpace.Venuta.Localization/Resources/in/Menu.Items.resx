<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="100" xml:space="preserve">
    <value>Schedule Tasks</value>
  </data>
  <data name="101" xml:space="preserve">
    <value>General Ledger Parameters</value>
  </data>
  <data name="102" xml:space="preserve">
    <value>Work Flow</value>
  </data>
  <data name="10293" xml:space="preserve">
    <value>Company Report Configuration</value>
  </data>
  <data name="10294" xml:space="preserve">
    <value>Countries Report Configuration</value>
  </data>
  <data name="10295" xml:space="preserve">
    <value>French Payslip Fiscal Configuration</value>
  </data>
  <data name="10296" xml:space="preserve">
    <value>Third Party Payments Files</value>
  </data>
  <data name="103" xml:space="preserve">
    <value>Grades</value>
  </data>
  <data name="104" xml:space="preserve">
    <value>Create A Mock Payslip</value>
  </data>
  <data name="106" xml:space="preserve">
    <value>Organisation Hierarchy Units</value>
  </data>
  <data name="107" xml:space="preserve">
    <value>Position Management</value>
  </data>
  <data name="108" xml:space="preserve">
    <value>Position Details</value>
  </data>
  <data name="109" xml:space="preserve">
    <value>Audit Trail</value>
  </data>
  <data name="110" xml:space="preserve">
    <value>Banking Details</value>
  </data>
  <data name="111" xml:space="preserve">
    <value>Post Bulk Entries For Multiple Employees</value>
  </data>
  <data name="112" xml:space="preserve">
    <value>Organisation Structure</value>
  </data>
  <data name="115" xml:space="preserve">
    <value>Leave Administration</value>
  </data>
  <data name="116" xml:space="preserve">
    <value>Leave Scheme Setup</value>
  </data>
  <data name="117" xml:space="preserve">
    <value>Leave Scheme Parameters</value>
  </data>
  <data name="118" xml:space="preserve">
    <value>Leave Adjustments</value>
  </data>
  <data name="119" xml:space="preserve">
    <value>Leave Administration</value>
  </data>
  <data name="12" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="120" xml:space="preserve">
    <value>Leave Application</value>
  </data>
  <data name="122" xml:space="preserve">
    <value>Document Work Flow Setup</value>
  </data>
  <data name="123" xml:space="preserve">
    <value>Leave Setup</value>
  </data>
  <data name="125" xml:space="preserve">
    <value>Administer Payroll Funds</value>
  </data>
  <data name="127" xml:space="preserve">
    <value>Organisation Structure Overview</value>
  </data>
  <data name="128" xml:space="preserve">
    <value>Organisation Hierarchy Levels</value>
  </data>
  <data name="129" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="131" xml:space="preserve">
    <value>Quick Links</value>
  </data>
  <data name="133" xml:space="preserve">
    <value>Pricing</value>
  </data>
  <data name="134" xml:space="preserve">
    <value>Company Billing Settings</value>
  </data>
  <data name="135" xml:space="preserve">
    <value>Company Payments</value>
  </data>
  <data name="136" xml:space="preserve">
    <value>Company Pricing</value>
  </data>
  <data name="137" xml:space="preserve">
    <value>Maintain Pay Calendars and Frequencies</value>
  </data>
  <data name="138" xml:space="preserve">
    <value>Create an Interim Run</value>
  </data>
  <data name="140" xml:space="preserve">
    <value>Allowances / Deductions / Fringe Benefits / Contributions</value>
  </data>
  <data name="141" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="142" xml:space="preserve">
    <value>Payroll Information</value>
  </data>
  <data name="143" xml:space="preserve">
    <value>Payslips / Tax Certificates / YTD's</value>
  </data>
  <data name="144" xml:space="preserve">
    <value>Payslips</value>
  </data>
  <data name="145" xml:space="preserve">
    <value>Terminate / Reinstate or Suspend Employment</value>
  </data>
  <data name="146" xml:space="preserve">
    <value>Terminate / Reinstate</value>
  </data>
  <data name="15" xml:space="preserve">
    <value>Creditors</value>
  </data>
  <data name="151" xml:space="preserve">
    <value>Leave Averaging Income Setup</value>
  </data>
  <data name="152" xml:space="preserve">
    <value>Manage Business Partners</value>
  </data>
  <data name="153" xml:space="preserve">
    <value>Leave Balances</value>
  </data>
  <data name="154" xml:space="preserve">
    <value>Add Organisational Structure</value>
  </data>
  <data name="157" xml:space="preserve">
    <value>Financial Documents</value>
  </data>
  <data name="158" xml:space="preserve">
    <value>Invoices and Receipts</value>
  </data>
  <data name="159" xml:space="preserve">
    <value>Income Protection / PHI</value>
  </data>
  <data name="160" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="162" xml:space="preserve">
    <value>Debit Order Process</value>
  </data>
  <data name="163" xml:space="preserve">
    <value>Reminder Contact Details</value>
  </data>
  <data name="164" xml:space="preserve">
    <value>Tax Certificate Run</value>
  </data>
  <data name="165" xml:space="preserve">
    <value>Financial</value>
  </data>
  <data name="166" xml:space="preserve">
    <value>Banking Details</value>
  </data>
  <data name="167" xml:space="preserve">
    <value>Security</value>
  </data>
  <data name="168" xml:space="preserve">
    <value>User Profiles</value>
  </data>
  <data name="169" xml:space="preserve">
    <value>Security Roles</value>
  </data>
  <data name="171" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="172" xml:space="preserve">
    <value>Disciplinary Record</value>
  </data>
  <data name="173" xml:space="preserve">
    <value>Training / Skills / Qualifications Administration</value>
  </data>
  <data name="174" xml:space="preserve">
    <value>Training Courses</value>
  </data>
  <data name="175" xml:space="preserve">
    <value>Training Records</value>
  </data>
  <data name="177" xml:space="preserve">
    <value>Skills</value>
  </data>
  <data name="178" xml:space="preserve">
    <value>Skills</value>
  </data>
  <data name="179" xml:space="preserve">
    <value>PaySpace Debit Orders</value>
  </data>
  <data name="18" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="184" xml:space="preserve">
    <value>Costing</value>
  </data>
  <data name="185" xml:space="preserve">
    <value>Project Costing</value>
  </data>
  <data name="186" xml:space="preserve">
    <value>Activity Costing</value>
  </data>
  <data name="188" xml:space="preserve">
    <value>User Organisation Permissions</value>
  </data>
  <data name="190" xml:space="preserve">
    <value>Terms and Conditions</value>
  </data>
  <data name="191" xml:space="preserve">
    <value>Additional Fees</value>
  </data>
  <data name="192" xml:space="preserve">
    <value>Leave Schedule</value>
  </data>
  <data name="196" xml:space="preserve">
    <value>Salary Payment Files</value>
  </data>
  <data name="197" xml:space="preserve">
    <value>Leave Overview</value>
  </data>
  <data name="198" xml:space="preserve">
    <value>Performance Evaluation Administration</value>
  </data>
  <data name="199" xml:space="preserve">
    <value>Template Definition</value>
  </data>
  <data name="2" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="200" xml:space="preserve">
    <value>Scale Definitions</value>
  </data>
  <data name="201" xml:space="preserve">
    <value>Scale Options</value>
  </data>
  <data name="202" xml:space="preserve">
    <value>KPI Setup</value>
  </data>
  <data name="203" xml:space="preserve">
    <value>Evaluation Processes</value>
  </data>
  <data name="204" xml:space="preserve">
    <value>Performance Evaluations</value>
  </data>
  <data name="205" xml:space="preserve">
    <value>Evaluation History</value>
  </data>
  <data name="206" xml:space="preserve">
    <value>Performance Journal</value>
  </data>
  <data name="207" xml:space="preserve">
    <value>Pricing</value>
  </data>
  <data name="208" xml:space="preserve">
    <value>KPI Targets</value>
  </data>
  <data name="209" xml:space="preserve">
    <value>KPI Scorecard</value>
  </data>
  <data name="210" xml:space="preserve">
    <value>Evaluation Overview</value>
  </data>
  <data name="211" xml:space="preserve">
    <value>Calendar</value>
  </data>
  <data name="212" xml:space="preserve">
    <value>Leave Summary</value>
  </data>
  <data name="215" xml:space="preserve">
    <value>Project Details</value>
  </data>
  <data name="217" xml:space="preserve">
    <value>Attachments</value>
  </data>
  <data name="219" xml:space="preserve">
    <value>Skills Categories</value>
  </data>
  <data name="22" xml:space="preserve">
    <value>Recurring Payroll Components</value>
  </data>
  <data name="220" xml:space="preserve">
    <value>Qualifications</value>
  </data>
  <data name="221" xml:space="preserve">
    <value>Skills / Training / Qualifications</value>
  </data>
  <data name="222" xml:space="preserve">
    <value>Qualifications</value>
  </data>
  <data name="224" xml:space="preserve">
    <value>Transfer</value>
  </data>
  <data name="225" xml:space="preserve">
    <value>Mass Capture</value>
  </data>
  <data name="226" xml:space="preserve">
    <value>Claims Manager Capture</value>
  </data>
  <data name="227" xml:space="preserve">
    <value>Claims Admin Capture</value>
  </data>
  <data name="228" xml:space="preserve">
    <value>Claim Component Settings</value>
  </data>
  <data name="23" xml:space="preserve">
    <value>Pay Rate Details</value>
  </data>
  <data name="231" xml:space="preserve">
    <value>Configuration</value>
  </data>
  <data name="232" xml:space="preserve">
    <value>Company Settings</value>
  </data>
  <data name="235" xml:space="preserve">
    <value>Claims Batch Audit</value>
  </data>
  <data name="236" xml:space="preserve">
    <value>Out Of Office</value>
  </data>
  <data name="237" xml:space="preserve">
    <value>Payslip Message</value>
  </data>
  <data name="24" xml:space="preserve">
    <value>Basic Profile</value>
  </data>
  <data name="241" xml:space="preserve">
    <value>Registered Companies</value>
  </data>
  <data name="242" xml:space="preserve">
    <value>Recurring Templates</value>
  </data>
  <data name="243" xml:space="preserve">
    <value>Business Partner Details</value>
  </data>
  <data name="244" xml:space="preserve">
    <value>Statistics SA Config</value>
  </data>
  <data name="245" xml:space="preserve">
    <value>Employment Equity Config</value>
  </data>
  <data name="249" xml:space="preserve">
    <value>Recruitment Administration</value>
  </data>
  <data name="252" xml:space="preserve">
    <value>Workflow Configuration</value>
  </data>
  <data name="254" xml:space="preserve">
    <value>Extra</value>
  </data>
  <data name="255" xml:space="preserve">
    <value>Workflow Status</value>
  </data>
  <data name="256" xml:space="preserve">
    <value>Transfer History</value>
  </data>
  <data name="257" xml:space="preserve">
    <value>Mass Internal Transfers</value>
  </data>
  <data name="258" xml:space="preserve">
    <value>Asset Register</value>
  </data>
  <data name="259" xml:space="preserve">
    <value>Training Documents</value>
  </data>
  <data name="26" xml:space="preserve">
    <value>Payroll Components</value>
  </data>
  <data name="260" xml:space="preserve">
    <value>Company Training Material</value>
  </data>
  <data name="261" xml:space="preserve">
    <value>Workflow Roles</value>
  </data>
  <data name="267" xml:space="preserve">
    <value>Training Calendar</value>
  </data>
  <data name="268" xml:space="preserve">
    <value>Pastel Conversion Utility</value>
  </data>
  <data name="269" xml:space="preserve">
    <value>PaySpace Course Schedule</value>
  </data>
  <data name="27" xml:space="preserve">
    <value>Basic Company Information</value>
  </data>
  <data name="270" xml:space="preserve">
    <value>Take On Admin</value>
  </data>
  <data name="271" xml:space="preserve">
    <value>Notes / Reminders</value>
  </data>
  <data name="272" xml:space="preserve">
    <value>Currency Exchange Rates</value>
  </data>
  <data name="274" xml:space="preserve">
    <value>Onboarding / Termination Notifications</value>
  </data>
  <data name="275" xml:space="preserve">
    <value>Conversion Utility</value>
  </data>
  <data name="277" xml:space="preserve">
    <value>Pay Rate Categories</value>
  </data>
  <data name="28" xml:space="preserve">
    <value>Component</value>
  </data>
  <data name="281" xml:space="preserve">
    <value>Third Party Payments Configuration</value>
  </data>
  <data name="284" xml:space="preserve">
    <value>Component Threshhold Setup</value>
  </data>
  <data name="285" xml:space="preserve">
    <value>EFT Reports</value>
  </data>
  <data name="286" xml:space="preserve">
    <value>Company Group Link</value>
  </data>
  <data name="288" xml:space="preserve">
    <value>Project Rates</value>
  </data>
  <data name="289" xml:space="preserve">
    <value>Compare Payslips</value>
  </data>
  <data name="29" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="290" xml:space="preserve">
    <value>Inbox Management</value>
  </data>
  <data name="291" xml:space="preserve">
    <value>Company MIBFA Config</value>
  </data>
  <data name="292" xml:space="preserve">
    <value>Tax Certificate Configuration</value>
  </data>
  <data name="293" xml:space="preserve">
    <value>Manager View</value>
  </data>
  <data name="294" xml:space="preserve">
    <value>Manager Self Service Config</value>
  </data>
  <data name="30" xml:space="preserve">
    <value>Tax Code Details</value>
  </data>
  <data name="30298" xml:space="preserve">
    <value>Replicate User</value>
  </data>
  <data name="30300" xml:space="preserve">
    <value>Cloud Analytics</value>
  </data>
  <data name="30304" xml:space="preserve">
    <value>Evaluation Defaults</value>
  </data>
  <data name="30305" xml:space="preserve">
    <value>Employee KPA Definitions</value>
  </data>
  <data name="30307" xml:space="preserve">
    <value>Org. Structure Permissions</value>
  </data>
  <data name="31" xml:space="preserve">
    <value>Admin</value>
  </data>
  <data name="32" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="33" xml:space="preserve">
    <value>Security</value>
  </data>
  <data name="35" xml:space="preserve">
    <value>Bureau Security Roles</value>
  </data>
  <data name="4" xml:space="preserve">
    <value>Add A New Company</value>
  </data>
  <data name="40306" xml:space="preserve">
    <value>Incident Management</value>
  </data>
  <data name="40307" xml:space="preserve">
    <value>Succession Planning Configuration</value>
  </data>
  <data name="40309" xml:space="preserve">
    <value>Talent Pools</value>
  </data>
  <data name="40310" xml:space="preserve">
    <value>Mass Template Attachment Utility</value>
  </data>
  <data name="40311" xml:space="preserve">
    <value>Talent Pool</value>
  </data>
  <data name="40312" xml:space="preserve">
    <value>Hide/Show Payslip Items</value>
  </data>
  <data name="40313" xml:space="preserve">
    <value>Company Documents</value>
  </data>
  <data name="43" xml:space="preserve">
    <value>Manage Permissions</value>
  </data>
  <data name="45" xml:space="preserve">
    <value>Creditor Garnishee Transactions</value>
  </data>
  <data name="5" xml:space="preserve">
    <value>Bureau</value>
  </data>
  <data name="50313" xml:space="preserve">
    <value>Bureau Exchange Rates</value>
  </data>
  <data name="50315" xml:space="preserve">
    <value>Company Group Integration Fields</value>
  </data>
  <data name="50323" xml:space="preserve">
    <value>Other Enumerations</value>
  </data>
  <data name="50324" xml:space="preserve">
    <value>Company Configuration</value>
  </data>
  <data name="50326" xml:space="preserve">
    <value>Quote Management</value>
  </data>
  <data name="50327" xml:space="preserve">
    <value>Legislative Configurations</value>
  </data>
  <data name="50328" xml:space="preserve">
    <value>Company Enumerations</value>
  </data>
  <data name="50329" xml:space="preserve">
    <value>Company Related</value>
  </data>
  <data name="50330" xml:space="preserve">
    <value>Templates Administration</value>
  </data>
  <data name="50331" xml:space="preserve">
    <value>Arrears Report</value>
  </data>
  <data name="50332" xml:space="preserve">
    <value>Audit Trail Report</value>
  </data>
  <data name="50333" xml:space="preserve">
    <value>Company Car report</value>
  </data>
  <data name="50334" xml:space="preserve">
    <value>Component Costing Hours</value>
  </data>
  <data name="50335" xml:space="preserve">
    <value>Component File Extract</value>
  </data>
  <data name="50336" xml:space="preserve">
    <value>Component Input Variance Report</value>
  </data>
  <data name="50337" xml:space="preserve">
    <value>Component Once Off Adjustments Report</value>
  </data>
  <data name="50338" xml:space="preserve">
    <value>Component Posted Units</value>
  </data>
  <data name="50339" xml:space="preserve">
    <value>Component Report</value>
  </data>
  <data name="50340" xml:space="preserve">
    <value>Component ThreshHolds Report</value>
  </data>
  <data name="50341" xml:space="preserve">
    <value>Component Variance Report</value>
  </data>
  <data name="50342" xml:space="preserve">
    <value>Component Variance Report Going Across</value>
  </data>
  <data name="50343" xml:space="preserve">
    <value>Component Variance Totals Report</value>
  </data>
  <data name="50344" xml:space="preserve">
    <value>Consolidated Payroll Reconciliation Report</value>
  </data>
  <data name="50345" xml:space="preserve">
    <value>Cost To Company Report</value>
  </data>
  <data name="50346" xml:space="preserve">
    <value>Daily Register</value>
  </data>
  <data name="50347" xml:space="preserve">
    <value>Disability Report</value>
  </data>
  <data name="50348" xml:space="preserve">
    <value>Employee Financial House Payments Report</value>
  </data>
  <data name="50349" xml:space="preserve">
    <value>Employees With No Payslips</value>
  </data>
  <data name="50350" xml:space="preserve">
    <value>Expatriate Tax Certificate</value>
  </data>
  <data name="50351" xml:space="preserve">
    <value>Garnishee Report</value>
  </data>
  <data name="50352" xml:space="preserve">
    <value>Group Life Report</value>
  </data>
  <data name="50353" xml:space="preserve">
    <value>Income Protection Report</value>
  </data>
  <data name="50354" xml:space="preserve">
    <value>Increase Summary</value>
  </data>
  <data name="50355" xml:space="preserve">
    <value>Levies Report</value>
  </data>
  <data name="50356" xml:space="preserve">
    <value>Loans Report</value>
  </data>
  <data name="50357" xml:space="preserve">
    <value>Lump Sum Report</value>
  </data>
  <data name="50358" xml:space="preserve">
    <value>Medical Report</value>
  </data>
  <data name="50359" xml:space="preserve">
    <value>Net Payment Listing</value>
  </data>
  <data name="50360" xml:space="preserve">
    <value>Pay Rates Report</value>
  </data>
  <data name="50361" xml:space="preserve">
    <value>Payroll Reconciliation Report</value>
  </data>
  <data name="50362" xml:space="preserve">
    <value>Payroll Register Report</value>
  </data>
  <data name="50363" xml:space="preserve">
    <value>Payslips</value>
  </data>
  <data name="50364" xml:space="preserve">
    <value>Pension &amp; Provident  Report</value>
  </data>
  <data name="50365" xml:space="preserve">
    <value>Project Costing Report</value>
  </data>
  <data name="50366" xml:space="preserve">
    <value>Reimbursive Claims Billing Report</value>
  </data>
  <data name="50367" xml:space="preserve">
    <value>Savings Report</value>
  </data>
  <data name="50368" xml:space="preserve">
    <value>SMS Payslips</value>
  </data>
  <data name="50369" xml:space="preserve">
    <value>Tax Deductions Report</value>
  </data>
  <data name="50371" xml:space="preserve">
    <value>Trade Union  Report</value>
  </data>
  <data name="50372" xml:space="preserve">
    <value>Wage Batch Summary Report</value>
  </data>
  <data name="50373" xml:space="preserve">
    <value>Weekly Register</value>
  </data>
  <data name="50374" xml:space="preserve">
    <value>Asset Register Report</value>
  </data>
  <data name="50375" xml:space="preserve">
    <value>Consolidated Dynamic Employee Details</value>
  </data>
  <data name="50376" xml:space="preserve">
    <value>Dependants Listing</value>
  </data>
  <data name="50377" xml:space="preserve">
    <value>Disciplinary Report</value>
  </data>
  <data name="50378" xml:space="preserve">
    <value>Dynamic Employee Details</value>
  </data>
  <data name="50379" xml:space="preserve">
    <value>Employee Listing</value>
  </data>
  <data name="50380" xml:space="preserve">
    <value>Employee Projects</value>
  </data>
  <data name="50381" xml:space="preserve">
    <value>Incident Management Report</value>
  </data>
  <data name="50382" xml:space="preserve">
    <value>Balances</value>
  </data>
  <data name="50383" xml:space="preserve">
    <value>Provision Balancing Report</value>
  </data>
  <data name="50384" xml:space="preserve">
    <value>Transactions Report</value>
  </data>
  <data name="50385" xml:space="preserve">
    <value>New Engagements and Terminations</value>
  </data>
  <data name="50386" xml:space="preserve">
    <value>Org. Structure Budget Breakdown</value>
  </data>
  <data name="50387" xml:space="preserve">
    <value>Personnel Action Notice</value>
  </data>
  <data name="50388" xml:space="preserve">
    <value>Position Requirements Report</value>
  </data>
  <data name="50389" xml:space="preserve">
    <value>Recruitment Status'</value>
  </data>
  <data name="50390" xml:space="preserve">
    <value>Training Report</value>
  </data>
  <data name="50391" xml:space="preserve">
    <value>Costing Codes Report</value>
  </data>
  <data name="50392" xml:space="preserve">
    <value>Costing Payroll Reconcilliation Report</value>
  </data>
  <data name="50393" xml:space="preserve">
    <value>General Ledger Report</value>
  </data>
  <data name="50394" xml:space="preserve">
    <value>Project Costing Analysis</value>
  </data>
  <data name="50395" xml:space="preserve">
    <value>Coida Report</value>
  </data>
  <data name="50396" xml:space="preserve">
    <value>EEA2 Report</value>
  </data>
  <data name="50397" xml:space="preserve">
    <value>EEA4 Report</value>
  </data>
  <data name="50398" xml:space="preserve">
    <value>EMP201</value>
  </data>
  <data name="50399" xml:space="preserve">
    <value>EMP201 Breakdown</value>
  </data>
  <data name="50400" xml:space="preserve">
    <value>SDL Report</value>
  </data>
  <data name="50401" xml:space="preserve">
    <value>Statistics SA</value>
  </data>
  <data name="50402" xml:space="preserve">
    <value>UI-19 Report</value>
  </data>
  <data name="50403" xml:space="preserve">
    <value>UIF Declaration File</value>
  </data>
  <data name="50404" xml:space="preserve">
    <value>UIF Report</value>
  </data>
  <data name="50405" xml:space="preserve">
    <value>Qualifications Report</value>
  </data>
  <data name="50406" xml:space="preserve">
    <value>INSS Report</value>
  </data>
  <data name="50407" xml:space="preserve">
    <value>Employee Notes Report</value>
  </data>
  <data name="50408" xml:space="preserve">
    <value>Coinage Analysis</value>
  </data>
  <data name="50409" xml:space="preserve">
    <value>Medical Summary</value>
  </data>
  <data name="50410" xml:space="preserve">
    <value>Leave Bucket Mapping</value>
  </data>
  <data name="50411" xml:space="preserve">
    <value>National Social Security Fund Report</value>
  </data>
  <data name="50412" xml:space="preserve">
    <value>Parastal Pension Fund Report</value>
  </data>
  <data name="50413" xml:space="preserve">
    <value>National Pension Scheme Authority</value>
  </data>
  <data name="6" xml:space="preserve">
    <value>Formula</value>
  </data>
  <data name="60411" xml:space="preserve">
    <value>Country Legislative Static Fields</value>
  </data>
  <data name="60412" xml:space="preserve">
    <value>Company Static Defined Fields</value>
  </data>
  <data name="60413" xml:space="preserve">
    <value>Change Reporting Lines In Mass</value>
  </data>
  <data name="60414" xml:space="preserve">
    <value>Release Notifications Configuration</value>
  </data>
  <data name="60415" xml:space="preserve">
    <value>Newsletter Management</value>
  </data>
  <data name="60416" xml:space="preserve">
    <value>Claims</value>
  </data>
  <data name="60417" xml:space="preserve">
    <value>Bank File Definition Utility</value>
  </data>
  <data name="60418" xml:space="preserve">
    <value>Company Roster Schedules</value>
  </data>
  <data name="60419" xml:space="preserve">
    <value>Runs Configuration</value>
  </data>
  <data name="60420" xml:space="preserve">
    <value>Runs Configuration</value>
  </data>
  <data name="60421" xml:space="preserve">
    <value>Run Management</value>
  </data>
  <data name="60422" xml:space="preserve">
    <value>Kenya Tax Deduction Cards</value>
  </data>
  <data name="60423" xml:space="preserve">
    <value>NHIF Report</value>
  </data>
  <data name="60424" xml:space="preserve">
    <value>NSSF Report</value>
  </data>
  <data name="60425" xml:space="preserve">
    <value>Payroll Reconciliation Report in Home Currency</value>
  </data>
  <data name="60426" xml:space="preserve">
    <value>Kenya PAYE Supporting List P10A / P10D</value>
  </data>
  <data name="60427" xml:space="preserve">
    <value>EEA Reports</value>
  </data>
  <data name="60428" xml:space="preserve">
    <value>EMP501 Report</value>
  </data>
  <data name="60429" xml:space="preserve">
    <value>Employee KPA Summary and Status</value>
  </data>
  <data name="60430" xml:space="preserve">
    <value>Attendance Summary</value>
  </data>
  <data name="60431" xml:space="preserve">
    <value>M19 Return</value>
  </data>
  <data name="60432" xml:space="preserve">
    <value>M20/H</value>
  </data>
  <data name="60433" xml:space="preserve">
    <value>Relacao Nominal</value>
  </data>
  <data name="60434" xml:space="preserve">
    <value>Tax Certificates</value>
  </data>
  <data name="60435" xml:space="preserve">
    <value>Retenues à la source sur les salaires
État récapitulatif des traitements, 
salaires et rétributions de toute nature</value>
  </data>
  <data name="60436" xml:space="preserve">
    <value>Déclaration des retenues a la source sur les salaires.</value>
  </data>
  <data name="60438" xml:space="preserve">
    <value>Cloud Room</value>
  </data>
  <data name="60439" xml:space="preserve">
    <value>Payroll Process Management</value>
  </data>
  <data name="60440" xml:space="preserve">
    <value>Payroll Processes Definition</value>
  </data>
  <data name="60441" xml:space="preserve">
    <value>Process Progress</value>
  </data>
  <data name="60442" xml:space="preserve">
    <value>Period Processes</value>
  </data>
  <data name="60443" xml:space="preserve">
    <value>Workforce Planning</value>
  </data>
  <data name="60444" xml:space="preserve">
    <value>Budget Cost Groups</value>
  </data>
  <data name="60445" xml:space="preserve">
    <value>Cost Group Allocation</value>
  </data>
  <data name="60446" xml:space="preserve">
    <value>Budget Period Process</value>
  </data>
  <data name="60447" xml:space="preserve">
    <value>Budget Increase Amounts</value>
  </data>
  <data name="60448" xml:space="preserve">
    <value>Tax Countries Configuration</value>
  </data>
  <data name="60449" xml:space="preserve">
    <value>P.A.Y.E Statement And Payment Of Tax Withheld Report</value>
  </data>
  <data name="60450" xml:space="preserve">
    <value>Skills And Development Levy - Half Year Report</value>
  </data>
  <data name="60451" xml:space="preserve">
    <value>Skills And Development Levy - Monthly Report</value>
  </data>
  <data name="60452" xml:space="preserve">
    <value>Budget Detail Report.</value>
  </data>
  <data name="60453" xml:space="preserve">
    <value>Workforce Planning</value>
  </data>
  <data name="60454" xml:space="preserve">
    <value>Job Management</value>
  </data>
  <data name="60455" xml:space="preserve">
    <value>Workforce Snapshot</value>
  </data>
  <data name="60456" xml:space="preserve">
    <value>Archive Workforce Snapshot</value>
  </data>
  <data name="60457" xml:space="preserve">
    <value>Cost Breakdown</value>
  </data>
  <data name="60458" xml:space="preserve">
    <value>GL Report</value>
  </data>
  <data name="60459" xml:space="preserve">
    <value>ITW10 Report</value>
  </data>
  <data name="60460" xml:space="preserve">
    <value>ITW10A</value>
  </data>
  <data name="60461" xml:space="preserve">
    <value>ITW7A</value>
  </data>
  <data name="60462" xml:space="preserve">
    <value>ITW8 Report</value>
  </data>
  <data name="60463" xml:space="preserve">
    <value>REM - 2/2002</value>
  </data>
  <data name="60464" xml:space="preserve">
    <value>Kenya Central Bureau of Statistics Report</value>
  </data>
  <data name="60465" xml:space="preserve">
    <value>Kenya PAYE Employers Certificate P10</value>
  </data>
  <data name="60466" xml:space="preserve">
    <value>Declaration Mensuelle Des Impots Proffessionnel Et Exceptionnel Sur Les Remunerations</value>
  </data>
  <data name="60467" xml:space="preserve">
    <value>Ventilation des montant déclarés par Province de provenance</value>
  </data>
  <data name="60468" xml:space="preserve">
    <value>Institut National de Préparation Professionnelle</value>
  </data>
  <data name="60469" xml:space="preserve">
    <value>ONEM Declaration des Remunerations Mensuelles</value>
  </data>
  <data name="60470" xml:space="preserve">
    <value>Cameroon Legislative Reports</value>
  </data>
  <data name="60471" xml:space="preserve">
    <value>Return of Earnings</value>
  </data>
  <data name="60472" xml:space="preserve">
    <value>Statement Of Emoluments</value>
  </data>
  <data name="60473" xml:space="preserve">
    <value>Namibia PAYE 5 Tax Certificate</value>
  </data>
  <data name="60474" xml:space="preserve">
    <value>PAYE Monthly Recon</value>
  </data>
  <data name="60475" xml:space="preserve">
    <value>Social Security Return Forms</value>
  </data>
  <data name="60476" xml:space="preserve">
    <value>VET Monthly Return</value>
  </data>
  <data name="60477" xml:space="preserve">
    <value>IRP5 Reports</value>
  </data>
  <data name="60478" xml:space="preserve">
    <value>PAYE Monthly Remittance Report</value>
  </data>
  <data name="60479" xml:space="preserve">
    <value>Institut National de Securite Sociale</value>
  </data>
  <data name="60480" xml:space="preserve">
    <value>Zambia</value>
  </data>
  <data name="60481" xml:space="preserve">
    <value>Zambia</value>
  </data>
  <data name="60482" xml:space="preserve">
    <value>Zambia</value>
  </data>
  <data name="60483" xml:space="preserve">
    <value>ITF/P22</value>
  </data>
  <data name="60484" xml:space="preserve">
    <value>Zambia</value>
  </data>
  <data name="60485" xml:space="preserve">
    <value>Zambia</value>
  </data>
  <data name="60486" xml:space="preserve">
    <value>ITF/P18</value>
  </data>
  <data name="60487" xml:space="preserve">
    <value>ITF/P16</value>
  </data>
  <data name="60488" xml:space="preserve">
    <value>Monthly INSS Return</value>
  </data>
  <data name="60489" xml:space="preserve">
    <value>MRA P12A</value>
  </data>
  <data name="60490" xml:space="preserve">
    <value>MRA P4</value>
  </data>
  <data name="60491" xml:space="preserve">
    <value>MRA P5</value>
  </data>
  <data name="60492" xml:space="preserve">
    <value>MRA Quarterly Fringe Benefit Return</value>
  </data>
  <data name="60493" xml:space="preserve">
    <value>Form P2</value>
  </data>
  <data name="60494" xml:space="preserve">
    <value>Form P4A</value>
  </data>
  <data name="60495" xml:space="preserve">
    <value>ITF 16 Format</value>
  </data>
  <data name="60496" xml:space="preserve">
    <value>P6 Form</value>
  </data>
  <data name="60497" xml:space="preserve">
    <value>Standard Development Levy Declaration</value>
  </data>
  <data name="60498" xml:space="preserve">
    <value>Zimbabwe Manpower Development Fund</value>
  </data>
  <data name="60499" xml:space="preserve">
    <value>DT0103</value>
  </data>
  <data name="60500" xml:space="preserve">
    <value>DT0107</value>
  </data>
  <data name="60501" xml:space="preserve">
    <value>DT0107a</value>
  </data>
  <data name="60502" xml:space="preserve">
    <value>DT0107b</value>
  </data>
  <data name="60503" xml:space="preserve">
    <value>DT0107c</value>
  </data>
  <data name="60504" xml:space="preserve">
    <value>DT0108</value>
  </data>
  <data name="60505" xml:space="preserve">
    <value>DT0108a</value>
  </data>
  <data name="60506" xml:space="preserve">
    <value>DT0108b</value>
  </data>
  <data name="60507" xml:space="preserve">
    <value>SSNIT</value>
  </data>
  <data name="60508" xml:space="preserve">
    <value>Employee Suspension</value>
  </data>
  <data name="60509" xml:space="preserve">
    <value>MRA P12</value>
  </data>
  <data name="60510" xml:space="preserve">
    <value>MRA P9</value>
  </data>
  <data name="60511" xml:space="preserve">
    <value>Fringe benefits</value>
  </data>
  <data name="60512" xml:space="preserve">
    <value>P.19 Employees Tax (PAYE) Return</value>
  </data>
  <data name="60513" xml:space="preserve">
    <value>Nigeria Employers Annual Income and Tax Declaration Certificate H1</value>
  </data>
  <data name="60514" xml:space="preserve">
    <value>NHF Report</value>
  </data>
  <data name="60515" xml:space="preserve">
    <value>Nigeria PAYE Monthly Return</value>
  </data>
  <data name="60516" xml:space="preserve">
    <value>Nigeria Employee Annual Statement of Earnings</value>
  </data>
  <data name="60517" xml:space="preserve">
    <value>IRP5 / Lump Sum Certificates</value>
  </data>
  <data name="60518" xml:space="preserve">
    <value>WCP-1 Workers Compensation Fund (WCF)</value>
  </data>
  <data name="60519" xml:space="preserve">
    <value>Tanzania Statement of Income Per Company</value>
  </data>
  <data name="60520" xml:space="preserve">
    <value>Employee Request Audit</value>
  </data>
  <data name="60521" xml:space="preserve">
    <value>User Org Management</value>
  </data>
  <data name="60522" xml:space="preserve">
    <value>Bureau Pricing</value>
  </data>
  <data name="60523" xml:space="preserve">
    <value>Outstanding Payments</value>
  </data>
  <data name="60524" xml:space="preserve">
    <value>Company Group Invoice Setup</value>
  </data>
  <data name="60526" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="60527" xml:space="preserve">
    <value>Pension Contribution Form</value>
  </data>
  <data name="60528" xml:space="preserve">
    <value>Action Types</value>
  </data>
  <data name="60529" xml:space="preserve">
    <value>Action Type History</value>
  </data>
  <data name="60530" xml:space="preserve">
    <value>Documento de Liquidação de Impostos</value>
  </data>
  <data name="60531" xml:space="preserve">
    <value>Mapa de Liquidação de IRT</value>
  </data>
  <data name="60532" xml:space="preserve">
    <value>New INSS Monthly Return</value>
  </data>
  <data name="60533" xml:space="preserve">
    <value>Generic SSNIT Monthly Return</value>
  </data>
  <data name="60534" xml:space="preserve">
    <value>SSNIT Tier Monthly Return</value>
  </data>
  <data name="60535" xml:space="preserve">
    <value>NITA Levy Monthly Return</value>
  </data>
  <data name="60536" xml:space="preserve">
    <value>MRA P.16 Form</value>
  </data>
  <data name="60537" xml:space="preserve">
    <value>TEVETA Employers Data Form</value>
  </data>
  <data name="60538" xml:space="preserve">
    <value>PAYE-16 Recon</value>
  </data>
  <data name="60539" xml:space="preserve">
    <value>Detailed Evaluation History</value>
  </data>
  <data name="60540" xml:space="preserve">
    <value>Generic NSITF/ECS Return</value>
  </data>
  <data name="60541" xml:space="preserve">
    <value>Generic Pension Fund Report Classic</value>
  </data>
  <data name="60542" xml:space="preserve">
    <value>Monthly CNSS Return</value>
  </data>
  <data name="60543" xml:space="preserve">
    <value>Monthly IPTS and VPS Return</value>
  </data>
  <data name="60544" xml:space="preserve">
    <value>Orgchart Extract</value>
  </data>
  <data name="60545" xml:space="preserve">
    <value>Employee KPA Details</value>
  </data>
  <data name="60546" xml:space="preserve">
    <value>Scores History</value>
  </data>
  <data name="60547" xml:space="preserve">
    <value>Imposto Sobre Rendimento Do Trabalho</value>
  </data>
  <data name="60548" xml:space="preserve">
    <value>IPRES Payment advice</value>
  </data>
  <data name="60549" xml:space="preserve">
    <value>Budget Report</value>
  </data>
  <data name="60550" xml:space="preserve">
    <value>OrgChart</value>
  </data>
  <data name="60551" xml:space="preserve">
    <value>Budget vs Actuals</value>
  </data>
  <data name="60552" xml:space="preserve">
    <value>Appel De Cotisation Mensuel</value>
  </data>
  <data name="60553" xml:space="preserve">
    <value>Appel De Cotisation Trimestriel</value>
  </data>
  <data name="60554" xml:space="preserve">
    <value>Declaration Annuelle Des Salaires Et Des Cotisations</value>
  </data>
  <data name="60555" xml:space="preserve">
    <value>Declaration Des Impots Sur Les Salaires</value>
  </data>
  <data name="60556" xml:space="preserve">
    <value>BCEA5</value>
  </data>
  <data name="60557" xml:space="preserve">
    <value>UI 23</value>
  </data>
  <data name="60558" xml:space="preserve">
    <value>UI-2.7</value>
  </data>
  <data name="60559" xml:space="preserve">
    <value>Formula Details</value>
  </data>
  <data name="60560" xml:space="preserve">
    <value>Imposto sobre os Rendimentos do Trabalho</value>
  </data>
  <data name="60561" xml:space="preserve">
    <value>Single Monthly Declaration of Taxes, Social Contributions and Employer Contributions on Remuneration</value>
  </data>
  <data name="60562" xml:space="preserve">
    <value>Income Tax Report</value>
  </data>
  <data name="60563" xml:space="preserve">
    <value>Pension Fund / Social Security Report</value>
  </data>
  <data name="60564" xml:space="preserve">
    <value>iTax CSV File</value>
  </data>
  <data name="60565" xml:space="preserve">
    <value>Income Tax Return / IT2</value>
  </data>
  <data name="60566" xml:space="preserve">
    <value>P.A.Y.E - Employer's 6 Monthly Certificate - P10</value>
  </data>
  <data name="60567" xml:space="preserve">
    <value>Employment Taxes Payment Credit Slip</value>
  </data>
  <data name="60568" xml:space="preserve">
    <value>Order Form for Electronic Funds Transfer to Bank of Tanzania</value>
  </data>
  <data name="60569" xml:space="preserve">
    <value>P9 - Employee Tax Deduction Card</value>
  </data>
  <data name="60570" xml:space="preserve">
    <value>Custom Form Configuration</value>
  </data>
  <data name="60571" xml:space="preserve">
    <value>Country Legislative Historical Category Fields</value>
  </data>
  <data name="60572" xml:space="preserve">
    <value>Dynamic Historical Information</value>
  </data>
  <data name="60573" xml:space="preserve">
    <value>Dynamic Information</value>
  </data>
  <data name="60574" xml:space="preserve">
    <value>Custom Form Configuration</value>
  </data>
  <data name="60574s" xml:space="preserve">
    <value>Historical Data Categories</value>
  </data>
  <data name="60575" xml:space="preserve">
    <value>Historical Data Category Fields</value>
  </data>
  <data name="60576" xml:space="preserve">
    <value>Company Historical Information</value>
  </data>
  <data name="60577" xml:space="preserve">
    <value>Statistics Report</value>
  </data>
  <data name="60578" xml:space="preserve">
    <value>Tax Deduction Report</value>
  </data>
  <data name="60579" xml:space="preserve">
    <value>Formulaire Unique De Declaration et de Paiement des Impots et Taxes</value>
  </data>
  <data name="60580" xml:space="preserve">
    <value>Ivory Coast</value>
  </data>
  <data name="60581" xml:space="preserve">
    <value>Income Tax Report.</value>
  </data>
  <data name="60582" xml:space="preserve">
    <value>NASSIT Report / SS4A</value>
  </data>
  <data name="60583" xml:space="preserve">
    <value>NAPSA Remittance</value>
  </data>
  <data name="60584" xml:space="preserve">
    <value>Recurring Costing</value>
  </data>
  <data name="60585" xml:space="preserve">
    <value>Attachment Classification</value>
  </data>
  <data name="60586" xml:space="preserve">
    <value>Declaration annualle des Salaires</value>
  </data>
  <data name="60587" xml:space="preserve">
    <value>Appel Mensuel de Cotisation du Regime General</value>
  </data>
  <data name="60588" xml:space="preserve">
    <value>Declaration De Cotisations</value>
  </data>
  <data name="60589" xml:space="preserve">
    <value>Monthly PAYE import file</value>
  </data>
  <data name="60590" xml:space="preserve">
    <value>Salary Schedule</value>
  </data>
  <data name="60591" xml:space="preserve">
    <value>Evaluation Process Types</value>
  </data>
  <data name="60592" xml:space="preserve">
    <value>Custom Forms Report</value>
  </data>
  <data name="60593" xml:space="preserve">
    <value>Special Component Type</value>
  </data>
  <data name="60594" xml:space="preserve">
    <value>Annual Staff Salary Schedule</value>
  </data>
  <data name="60595" xml:space="preserve">
    <value>Retro Payslip Comparison</value>
  </data>
  <data name="60596" xml:space="preserve">
    <value>EFT Outbox File Settings</value>
  </data>
  <data name="60597" xml:space="preserve">
    <value>ITF/P13</value>
  </data>
  <data name="60598" xml:space="preserve">
    <value>ITF 1A - Tax Return to Income tax return for individuals self assessment form.</value>
  </data>
  <data name="60599" xml:space="preserve">
    <value>Edit Closed Runs Bank Details</value>
  </data>
  <data name="60601" xml:space="preserve">
    <value>PM - Evaluation Progress</value>
  </data>
  <data name="60602" xml:space="preserve">
    <value>Fisches Individuelles</value>
  </data>
  <data name="60603" xml:space="preserve">
    <value>Impot professionnel et exceptionnel sur les renumerations etat recapitulatif des elements imposables</value>
  </data>
  <data name="60605" xml:space="preserve">
    <value>Country Administration</value>
  </data>
  <data name="60606" xml:space="preserve">
    <value>Terminal Benefit Report</value>
  </data>
  <data name="60610" xml:space="preserve">
    <value>Activate Users</value>
  </data>
  <data name="60611" xml:space="preserve">
    <value>Debit Order Mandate</value>
  </data>
  <data name="60612" xml:space="preserve">
    <value>Escalated Leave &amp; Claims Workflow</value>
  </data>
  <data name="60613" xml:space="preserve">
    <value>Company Category Re Order</value>
  </data>
  <data name="60614" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="60615" xml:space="preserve">
    <value>Commission Admin Capture</value>
  </data>
  <data name="60616" xml:space="preserve">
    <value>Headcount Comparison</value>
  </data>
  <data name="60617" xml:space="preserve">
    <value>Botswana</value>
  </data>
  <data name="60618" xml:space="preserve">
    <value>Monthly NAPSA import file</value>
  </data>
  <data name="60619" xml:space="preserve">
    <value>NAPSA Electronic Return Report</value>
  </data>
  <data name="60620" xml:space="preserve">
    <value>Nigeria</value>
  </data>
  <data name="60621" xml:space="preserve">
    <value>Nigeria</value>
  </data>
  <data name="60622" xml:space="preserve">
    <value>Abolished Jobs</value>
  </data>
  <data name="60623" xml:space="preserve">
    <value>New Jobs</value>
  </data>
  <data name="60624" xml:space="preserve">
    <value>Zambia Workers' Compensation Fund Form - Form 14</value>
  </data>
  <data name="60625" xml:space="preserve">
    <value>Active Companies For Specified Country</value>
  </data>
  <data name="60626" xml:space="preserve">
    <value>Declaration annuelle des salaires et des cotisations (D.I.S.A)</value>
  </data>
  <data name="60627" xml:space="preserve">
    <value>PaySpace Training Delegates</value>
  </data>
  <data name="60628" xml:space="preserve">
    <value>BURS PAYE Monthly Import File</value>
  </data>
  <data name="60629" xml:space="preserve">
    <value>IRT Relatório Mensal</value>
  </data>
  <data name="60631" xml:space="preserve">
    <value>Bulk Capture Audit</value>
  </data>
  <data name="60632" xml:space="preserve">
    <value>Grade Costing Setup</value>
  </data>
  <data name="60633" xml:space="preserve">
    <value>Electronic Simplistic SSNIT Return</value>
  </data>
  <data name="60634" xml:space="preserve">
    <value>Bordereau Général de Versement</value>
  </data>
  <data name="60635" xml:space="preserve">
    <value>Bulletin individuel</value>
  </data>
  <data name="60636" xml:space="preserve">
    <value>Company Component Report</value>
  </data>
  <data name="60637" xml:space="preserve">
    <value>Declaration à joindre obligatoirement au paiement</value>
  </data>
  <data name="60638" xml:space="preserve">
    <value>Déclaration annuelle des salaires (DAS02)</value>
  </data>
  <data name="60639" xml:space="preserve">
    <value>Bulletin de justification traitements, salaires, pensions et rentes viageres (ID19)</value>
  </data>
  <data name="60640" xml:space="preserve">
    <value>External Quick Links</value>
  </data>
  <data name="60641" xml:space="preserve">
    <value>Agency</value>
  </data>
  <data name="60642" xml:space="preserve">
    <value>Edit Website</value>
  </data>
  <data name="60643" xml:space="preserve">
    <value>Business Partner Details</value>
  </data>
  <data name="60644" xml:space="preserve">
    <value>Agency Billing Report</value>
  </data>
  <data name="60645" xml:space="preserve">
    <value>Registered Companies</value>
  </data>
  <data name="60646" xml:space="preserve">
    <value>Form P.16</value>
  </data>
  <data name="60647" xml:space="preserve">
    <value>Form P.16 Summary Report</value>
  </data>
  <data name="60648" xml:space="preserve">
    <value>Employee Evaluation Defaults</value>
  </data>
  <data name="60649" xml:space="preserve">
    <value>WCR-3 Workers Compensation Fund (WCF)</value>
  </data>
  <data name="60650" xml:space="preserve">
    <value>IRP 5 electronic submission (excel spreadsheet)</value>
  </data>
  <data name="60651" xml:space="preserve">
    <value>Avis de declaration employeur</value>
  </data>
  <data name="60652" xml:space="preserve">
    <value>Déclaration des retenues à la source sur les salaires</value>
  </data>
  <data name="60653" xml:space="preserve">
    <value>Declaration Mensuelle de Salaires et des Cotisations</value>
  </data>
  <data name="60654" xml:space="preserve">
    <value>Declaration Trimestrielle de Salaires et des Cotisations French</value>
  </data>
  <data name="60655" xml:space="preserve">
    <value>Déclaration trimestrielle des salaires CNAMGS</value>
  </data>
  <data name="60656" xml:space="preserve">
    <value>Déclaration trimestrielle des salaires CNSS</value>
  </data>
  <data name="60657" xml:space="preserve">
    <value>Excel version of  Regime De Retraite Complentaire Des Cadre Etat du Personnel Et Des Salaires</value>
  </data>
  <data name="60658" xml:space="preserve">
    <value>Excel version of  Regime De Retraite Etat du Personnel Et Des Salaires</value>
  </data>
  <data name="60659" xml:space="preserve">
    <value>Regime De Retraite Complentaire Des Cadre Etat du Personnel Et Des Salaires</value>
  </data>
  <data name="60660" xml:space="preserve">
    <value>Regime De Retraite Etat du Personnel Et Des Salaires</value>
  </data>
  <data name="60661" xml:space="preserve">
    <value>uFiling Bulk Upload File</value>
  </data>
  <data name="60662" xml:space="preserve">
    <value>Déclaration annuelle des salaires</value>
  </data>
  <data name="60664" xml:space="preserve">
    <value>Employee Birthday Email</value>
  </data>
  <data name="60665" xml:space="preserve">
    <value>Payroll Components Blueprint</value>
  </data>
  <data name="60666" xml:space="preserve">
    <value>Bureau User Company Permissions</value>
  </data>
  <data name="60667" xml:space="preserve">
    <value>Personal levy declaration</value>
  </data>
  <data name="60668" xml:space="preserve">
    <value>Company Settings</value>
  </data>
  <data name="60669" xml:space="preserve">
    <value>Company Themes</value>
  </data>
  <data name="60670" xml:space="preserve">
    <value>Employee Return of earnings for Workmans compensation fund</value>
  </data>
  <data name="60671" xml:space="preserve">
    <value>Employee Tax certificate listing</value>
  </data>
  <data name="60672" xml:space="preserve">
    <value>Social Security Electronic File</value>
  </data>
  <data name="60673" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="60674" xml:space="preserve">
    <value>Employee Public Profile</value>
  </data>
  <data name="60675" xml:space="preserve">
    <value>Bordereau de Versement</value>
  </data>
  <data name="60676" xml:space="preserve">
    <value>Déclaration de versement des retenues de l'IUTS et de la TPA</value>
  </data>
  <data name="60677" xml:space="preserve">
    <value>Déclaration récapitulative des salaires</value>
  </data>
  <data name="60678" xml:space="preserve">
    <value>Etat des traitements,  salaires, commissions, honoraires, …versés à des tiers</value>
  </data>
  <data name="60679" xml:space="preserve">
    <value>Relevé détaillé du versement des retenues de l'IUTS et de la TPA</value>
  </data>
  <data name="60680" xml:space="preserve">
    <value>Etat Recapulative Des Prestations IPM</value>
  </data>
  <data name="60681" xml:space="preserve">
    <value>Déclaration de cotisations sociales</value>
  </data>
  <data name="60682" xml:space="preserve">
    <value>Declaration nominative de versement des cotisations</value>
  </data>
  <data name="60683" xml:space="preserve">
    <value>Caisse Nationale de Prévoyance Social - Déclaration des Salaires</value>
  </data>
  <data name="60684" xml:space="preserve">
    <value>Fiche de Declaration des Impots Mensuels</value>
  </data>
  <data name="60685" xml:space="preserve">
    <value>NSSF c-speed schedule</value>
  </data>
  <data name="60686" xml:space="preserve">
    <value>PAYE Return Form DT-2008</value>
  </data>
  <data name="60687" xml:space="preserve">
    <value>Change Request</value>
  </data>
  <data name="60688" xml:space="preserve">
    <value>On Behalf Of</value>
  </data>
  <data name="60689" xml:space="preserve">
    <value>Bureau Self Help</value>
  </data>
  <data name="60690" xml:space="preserve">
    <value>ETI Bi_weekly and Weekly Employee Detail Report</value>
  </data>
  <data name="67" xml:space="preserve">
    <value>Add New Employee</value>
  </data>
  <data name="70695" xml:space="preserve">
    <value>Employee KPIs</value>
  </data>
  <data name="70696" xml:space="preserve">
    <value>Employee Social Security Registration Form</value>
  </data>
  <data name="70697" xml:space="preserve">
    <value>Employee's Tax Deduction Form IT Form 51</value>
  </data>
  <data name="70698" xml:space="preserve">
    <value>Pension and Social Insurance</value>
  </data>
  <data name="70699" xml:space="preserve">
    <value>WPS Report</value>
  </data>
  <data name="70700" xml:space="preserve">
    <value>NEC Remittance</value>
  </data>
  <data name="70701" xml:space="preserve">
    <value>Annual statement of the wages</value>
  </data>
  <data name="70702" xml:space="preserve">
    <value>GEPF Pension Fund Report</value>
  </data>
  <data name="70703" xml:space="preserve">
    <value>LAPF Pension Fund Report</value>
  </data>
  <data name="70704" xml:space="preserve">
    <value>PSPF Pension Fund Report</value>
  </data>
  <data name="70705" xml:space="preserve">
    <value>Witholding Taxes Statement</value>
  </data>
  <data name="70706" xml:space="preserve">
    <value>Monthly PAYE,NPF,NSF CSV Return</value>
  </data>
  <data name="70707" xml:space="preserve">
    <value>Declaration Nominative Mensuelle Des Salaires</value>
  </data>
  <data name="70708" xml:space="preserve">
    <value>Declaration Nominative Mensuelle Des Salaires Cadres</value>
  </data>
  <data name="70709" xml:space="preserve">
    <value>Recapulatif Des Salaries Des Employes Locaux</value>
  </data>
  <data name="70710" xml:space="preserve">
    <value>NSSA Form P4</value>
  </data>
  <data name="70711" xml:space="preserve">
    <value>Declaração para Efeitos de Imposto sobre Rendimentos Pessoas Singulares</value>
  </data>
  <data name="70712" xml:space="preserve">
    <value>Imposto Pessoal Autárquico</value>
  </data>
  <data name="70713" xml:space="preserve">
    <value>Bordereau des versements</value>
  </data>
  <data name="70714" xml:space="preserve">
    <value>Caisse Nationale de Prévoyance Social - Déclaration des Salaires</value>
  </data>
  <data name="70715" xml:space="preserve">
    <value>HELP Remittance Form</value>
  </data>
  <data name="70716" xml:space="preserve">
    <value>Mensuel Paiement de la Cotisation Sociale</value>
  </data>
  <data name="70717" xml:space="preserve">
    <value>Paiement de la Contribution à la Formation Professionnelle Continue et à lapprentissage</value>
  </data>
  <data name="70718" xml:space="preserve">
    <value>Retenues sur traitements et salaires</value>
  </data>
  <data name="70719" xml:space="preserve">
    <value>Bordereau de Versement (Togo)</value>
  </data>
  <data name="70720" xml:space="preserve">
    <value>Bordereau de Versement.</value>
  </data>
  <data name="70721" xml:space="preserve">
    <value>Déclaration CNaPS</value>
  </data>
  <data name="70722" xml:space="preserve">
    <value>Declaration Nominative Des Salaires Verses - Sanitary Contributions Report</value>
  </data>
  <data name="70723" xml:space="preserve">
    <value>CNSS Appel De Cotisations</value>
  </data>
  <data name="70724" xml:space="preserve">
    <value>Déclaration Annuelle des Salaries (DAS)</value>
  </data>
  <data name="70725" xml:space="preserve">
    <value>PSSSF Pension fund declaration form</value>
  </data>
  <data name="70726" xml:space="preserve">
    <value>Declaration Mensuelle De Salaires</value>
  </data>
  <data name="70727" xml:space="preserve">
    <value>Trimestriel Paiement de la Cotisation Sociale</value>
  </data>
  <data name="70728" xml:space="preserve">
    <value>Fiche de declaration de VPS Classic</value>
  </data>
  <data name="70729" xml:space="preserve">
    <value>Impot Sur Les Revenus Salariaux Et Assimiles (R-tax)</value>
  </data>
  <data name="70730" xml:space="preserve">
    <value>Monthly PAYE Declaration form</value>
  </data>
  <data name="70731" xml:space="preserve">
    <value>Taxe d'Apprentissage et de formation professionnelle a la charge des Employeurs</value>
  </data>
  <data name="70732" xml:space="preserve">
    <value>Quarterly Report of Wages (Social Security)</value>
  </data>
  <data name="70733" xml:space="preserve">
    <value>PAYE Report in excel format</value>
  </data>
  <data name="70734" xml:space="preserve">
    <value>PAYE, Pension, Medical and Maternity eTax File</value>
  </data>
  <data name="70735" xml:space="preserve">
    <value>ITAS</value>
  </data>
  <data name="70736" xml:space="preserve">
    <value>ETI Summary Report</value>
  </data>
  <data name="70737" xml:space="preserve">
    <value>Contribution a La Formation Professionnelle</value>
  </data>
  <data name="70738" xml:space="preserve">
    <value>Quarterly PAYE Declaration form</value>
  </data>
  <data name="70739" xml:space="preserve">
    <value>Déclaration Annuelle des Salaries (DAS)</value>
  </data>
  <data name="70740" xml:space="preserve">
    <value>Monthly Social Security Declaration file</value>
  </data>
  <data name="70741" xml:space="preserve">
    <value>Withholding Tax report</value>
  </data>
  <data name="70742" xml:space="preserve">
    <value>Withholding Tax Report.</value>
  </data>
  <data name="70743" xml:space="preserve">
    <value>SYSCOHADA</value>
  </data>
  <data name="70744" xml:space="preserve">
    <value>Declaracion Mensual Sobre el Fondo de Proteccion al Trabajo y la Formacion Profesional</value>
  </data>
  <data name="70745" xml:space="preserve">
    <value>Impuesto Sobre La Renta De Las Personas Fisicas No Petrolera</value>
  </data>
  <data name="70746" xml:space="preserve">
    <value>Impuesto Sobre La Renta De Las Personas Fisicas Petrolera</value>
  </data>
  <data name="70747" xml:space="preserve">
    <value>E.As Employee Breakdown</value>
  </data>
  <data name="70748" xml:space="preserve">
    <value>CNaPS et FMFP Talon de Déclarations Nominative des Salaires.</value>
  </data>
  <data name="70749" xml:space="preserve">
    <value>Electronic ITF 16</value>
  </data>
  <data name="70750" xml:space="preserve">
    <value>Impuesto sobre el individuo (Cedula Personal)</value>
  </data>
  <data name="70751" xml:space="preserve">
    <value>What's new</value>
  </data>
  <data name="70752" xml:space="preserve">
    <value>National Provident Fund Report</value>
  </data>
  <data name="70753" xml:space="preserve">
    <value>Sub Tax Codes</value>
  </data>
  <data name="70754" xml:space="preserve">
    <value>Impots sur les traitement et salaires</value>
  </data>
  <data name="70755" xml:space="preserve">
    <value>Monthly and Quarterly Statement of Wages and Contributions for Social Security</value>
  </data>
  <data name="70756" xml:space="preserve">
    <value>Statement of the contributions for the mandatory health insurance</value>
  </data>
  <data name="70757" xml:space="preserve">
    <value>NHIS Employee Schedule</value>
  </data>
  <data name="70759" xml:space="preserve">
    <value>COVID-19 UIF application file</value>
  </data>
  <data name="70760" xml:space="preserve">
    <value>NSSF CSV Payroll Template</value>
  </data>
  <data name="70761" xml:space="preserve">
    <value>RSA - Force 3699 Equal Income</value>
  </data>
  <data name="70762" xml:space="preserve">
    <value>Portable Retirement Gratuity Fund contribution report</value>
  </data>
  <data name="70763" xml:space="preserve">
    <value>Regions</value>
  </data>
  <data name="70764" xml:space="preserve">
    <value>SSNIT Tier II Monthly Return</value>
  </data>
  <data name="70765" xml:space="preserve">
    <value>NHIIMA Contributions Online CSV file</value>
  </data>
  <data name="70766" xml:space="preserve">
    <value>Bulk Receipting</value>
  </data>
  <data name="70767" xml:space="preserve">
    <value>CMU Cotisation Nominative</value>
  </data>
  <data name="70768" xml:space="preserve">
    <value>Registo Nominal de Trabalharoes.</value>
  </data>
  <data name="70769" xml:space="preserve">
    <value>Declaracao Anual De Modelo 2 (Grupo A)</value>
  </data>
  <data name="70770" xml:space="preserve">
    <value>Declaracao Anual De Modelo 2 (Grupo A) - schedule.</value>
  </data>
  <data name="70771" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="70772" xml:space="preserve">
    <value>Déclaration Mensuelle des Impôts</value>
  </data>
  <data name="70773" xml:space="preserve">
    <value>UI-2.1P</value>
  </data>
  <data name="70774" xml:space="preserve">
    <value>UI-2.8</value>
  </data>
  <data name="70775" xml:space="preserve">
    <value>Declaration trimestrielle des salaries et des salaires</value>
  </data>
  <data name="70776" xml:space="preserve">
    <value>Etat Recapitulatif Des Cotisations</value>
  </data>
  <data name="70778" xml:space="preserve">
    <value>Rosters</value>
  </data>
  <data name="70779" xml:space="preserve">
    <value>Etat Des Salaires</value>
  </data>
  <data name="70780" xml:space="preserve">
    <value>ETI Take On</value>
  </data>
  <data name="70781" xml:space="preserve">
    <value>EMP501 ETI Breakdown</value>
  </data>
  <data name="70784" xml:space="preserve">
    <value>Integrations</value>
  </data>
  <data name="70785" xml:space="preserve">
    <value>General Ledger</value>
  </data>
  <data name="70786" xml:space="preserve">
    <value>Employers schedule of monthly PAYE deductions from Employees remuneration (GRA RET 5</value>
  </data>
  <data name="70787" xml:space="preserve">
    <value>Declaration des Revenues</value>
  </data>
  <data name="70788" xml:space="preserve">
    <value>Déclaration Nominative des salaires</value>
  </data>
  <data name="70789" xml:space="preserve">
    <value>Monthly PAYE Return</value>
  </data>
  <data name="70790" xml:space="preserve">
    <value>National Provident Fund Report (ENPF)</value>
  </data>
  <data name="70791" xml:space="preserve">
    <value>Payroll Reconciliation Report – Totals</value>
  </data>
  <data name="70792" xml:space="preserve">
    <value>eSocial Dashboard</value>
  </data>
  <data name="70793" xml:space="preserve">
    <value>Electronic Template for Monthly PAYE</value>
  </data>
  <data name="70794" xml:space="preserve">
    <value>WCF CSV Template</value>
  </data>
  <data name="70795" xml:space="preserve">
    <value>Industrial Injuries Compensation Fund Employer Registration Form (ICF1)</value>
  </data>
  <data name="70796" xml:space="preserve">
    <value>NHIMA CSV Template</value>
  </data>
  <data name="70797" xml:space="preserve">
    <value>Monthly Contribution Returns</value>
  </data>
  <data name="70798" xml:space="preserve">
    <value>UI-2.1</value>
  </data>
  <data name="70799" xml:space="preserve">
    <value>NPF Notice of Termination and Application for benefit form (NPF7B)</value>
  </data>
  <data name="70800" xml:space="preserve">
    <value>NPF2a</value>
  </data>
  <data name="70801" xml:space="preserve">
    <value>Social Security and Housing Finance Corporation Remittance Advice Form (IICF3)</value>
  </data>
  <data name="70802" xml:space="preserve">
    <value>Social Security and Housing Finance Corporation Remittance Advice Form (NPF3).</value>
  </data>
  <data name="70803" xml:space="preserve">
    <value>Social Security Fund Employer Registration (NPF1).</value>
  </data>
  <data name="70804" xml:space="preserve">
    <value>Workmans Registration Form (ICF2A) Form</value>
  </data>
  <data name="70805" xml:space="preserve">
    <value>Alternative Annual Report</value>
  </data>
  <data name="70899" xml:space="preserve">
    <value>Evaluations</value>
  </data>
  <data name="70900" xml:space="preserve">
    <value>Taxe d'apprentissage</value>
  </data>
  <data name="70901" xml:space="preserve">
    <value>Electronic SSNIT First Tier Contribution Report</value>
  </data>
  <data name="70902" xml:space="preserve">
    <value>VET Monthly Return</value>
  </data>
  <data name="70903" xml:space="preserve">
    <value>IPRES and Social Security déclaration</value>
  </data>
  <data name="70904" xml:space="preserve">
    <value>Directive Number Report</value>
  </data>
  <data name="70905" xml:space="preserve">
    <value>User Login Audit</value>
  </data>
  <data name="70906" xml:space="preserve">
    <value>Affirmative Action Report</value>
  </data>
  <data name="70907" xml:space="preserve">
    <value>Bordereau Unique Impots Nouveau</value>
  </data>
  <data name="70908" xml:space="preserve">
    <value>CIMR Quarterly Report</value>
  </data>
  <data name="70909" xml:space="preserve">
    <value>NAPSA Remittance Form (Form No. NPS31)</value>
  </data>
  <data name="70910" xml:space="preserve">
    <value>IRT A2.1 - Mapa de Remunerações</value>
  </data>
  <data name="70911" xml:space="preserve">
    <value>SDL Return Form ITX.215.03.E</value>
  </data>
  <data name="70912" xml:space="preserve">
    <value>WCF Monthly Report</value>
  </data>
  <data name="70913" xml:space="preserve">
    <value>Power BI</value>
  </data>
  <data name="70915" xml:space="preserve">
    <value>Monthly Electronic INSS Report</value>
  </data>
  <data name="70916" xml:space="preserve">
    <value>Quarterly CNSS text file</value>
  </data>
  <data name="70917" xml:space="preserve">
    <value>Timesheet Billing Report</value>
  </data>
  <data name="70918" xml:space="preserve">
    <value>Security Role Security Configuration</value>
  </data>
  <data name="70919" xml:space="preserve">
    <value>Taxe D'apprentissage</value>
  </data>
  <data name="70920" xml:space="preserve">
    <value>CNSS Monthly Report</value>
  </data>
  <data name="70921" xml:space="preserve">
    <value>Déclaration des traitements et salaires</value>
  </data>
  <data name="70922" xml:space="preserve">
    <value>Monthly Tax Report</value>
  </data>
  <data name="70923" xml:space="preserve">
    <value>Tax balance report</value>
  </data>
  <data name="70924" xml:space="preserve">
    <value>Year to date tax code breakdown</value>
  </data>
  <data name="70925" xml:space="preserve">
    <value>Year to date tax code drilldown</value>
  </data>
  <data name="70926" xml:space="preserve">
    <value>PAYE Return</value>
  </data>
  <data name="70927" xml:space="preserve">
    <value>Electronic WCR-3 Workers Compensation Fund (WCF)</value>
  </data>
  <data name="70928" xml:space="preserve">
    <value>NSSF Electronic Monthly Submission</value>
  </data>
  <data name="70929" xml:space="preserve">
    <value>Annual Employer Declaration</value>
  </data>
  <data name="70930" xml:space="preserve">
    <value>Detail des remunerations</value>
  </data>
  <data name="70931" xml:space="preserve">
    <value>CNPS Monthly Report</value>
  </data>
  <data name="70932" xml:space="preserve">
    <value>Electronic Monthly PAYE Report</value>
  </data>
  <data name="70933" xml:space="preserve">
    <value>Transactions Without Payslip Report</value>
  </data>
  <data name="70934" xml:space="preserve">
    <value>Table Builder Configuration</value>
  </data>
  <data name="70935" xml:space="preserve">
    <value>Component Tables Configuration</value>
  </data>
  <data name="70936" xml:space="preserve">
    <value>Formulaire de saisie et génération des fichiers XML our les annexes détaillées</value>
  </data>
  <data name="70937" xml:space="preserve">
    <value>NASSIT/SS4A</value>
  </data>
  <data name="70938" xml:space="preserve">
    <value>Health Organisation Contribution Report</value>
  </data>
  <data name="70939" xml:space="preserve">
    <value>DT0107a</value>
  </data>
  <data name="70940" xml:space="preserve">
    <value>LIRS Monthly Tax Report</value>
  </data>
  <data name="70941" xml:space="preserve">
    <value>OGIRS Monthly Tax Report</value>
  </data>
  <data name="70942" xml:space="preserve">
    <value>Nominative Declaration of Remuneration</value>
  </data>
  <data name="70943" xml:space="preserve">
    <value>LIRS Annual Tax Report</value>
  </data>
  <data name="70944" xml:space="preserve">
    <value>OGIRS Annual Tax report</value>
  </data>
  <data name="70945" xml:space="preserve">
    <value>PAYE Monthly Return Schedule 1</value>
  </data>
  <data name="70946" xml:space="preserve">
    <value>Employment Stability Rules</value>
  </data>
  <data name="70947" xml:space="preserve">
    <value>Employment Equity Setup</value>
  </data>
  <data name="70948" xml:space="preserve">
    <value>Employment Equity Plan</value>
  </data>
  <data name="70950" xml:space="preserve">
    <value>Employment Equity</value>
  </data>
  <data name="70952" xml:space="preserve">
    <value>Pacey Dashboard</value>
  </data>
  <data name="70953" xml:space="preserve">
    <value>Apprenticeship tax and additional tax for continuing education</value>
  </data>
  <data name="70954" xml:space="preserve">
    <value>CNSS BNTS Electronic</value>
  </data>
  <data name="70955" xml:space="preserve">
    <value>Declaration of Payment of Payroll Tax and Tax Deductions</value>
  </data>
  <data name="70956" xml:space="preserve">
    <value>Details of the Tax Declaration</value>
  </data>
  <data name="70957" xml:space="preserve">
    <value>DIPE Annual</value>
  </data>
  <data name="70958" xml:space="preserve">
    <value>Employee Withholding Tax Certificate</value>
  </data>
  <data name="70959" xml:space="preserve">
    <value>Employer Annual Tax Return - Annexe 1</value>
  </data>
  <data name="70960" xml:space="preserve">
    <value>FCT-IRS Monthly PAYE Schedule</value>
  </data>
  <data name="70961" xml:space="preserve">
    <value>Monthly Declaration of Taxes, Social Security Contribution and Employer Contributions</value>
  </data>
  <data name="70962" xml:space="preserve">
    <value>NSSA P4</value>
  </data>
  <data name="70963" xml:space="preserve">
    <value>PAYE Monthly Return Schedule 4</value>
  </data>
  <data name="70964" xml:space="preserve">
    <value>Simplified IRT - Compensation Map</value>
  </data>
  <data name="70965" xml:space="preserve">
    <value>Declaration de versement INSS</value>
  </data>
  <data name="70966" xml:space="preserve">
    <value>Formulaire de declaration mensuelle a la taxe sur la valeur ajoutee.</value>
  </data>
  <data name="70967" xml:space="preserve">
    <value>MSCOA</value>
  </data>
  <data name="71100" xml:space="preserve">
    <value>Email Validation</value>
  </data>
  <data name="71101" xml:space="preserve">
    <value>FCT-IRS Form - H1- Employer’s Annual Declaration and Certificate</value>
  </data>
  <data name="71102" xml:space="preserve">
    <value>Public Holidays</value>
  </data>
  <data name="71103" xml:space="preserve">
    <value>Public Holiday Categories</value>
  </data>
  <data name="71104" xml:space="preserve">
    <value>Generic Pension Fund Report</value>
  </data>
  <data name="71105" xml:space="preserve">
    <value>Leave Settings</value>
  </data>
  <data name="71106" xml:space="preserve">
    <value>Security Settings</value>
  </data>
  <data name="71107" xml:space="preserve">
    <value>General Settings</value>
  </data>
  <data name="71108" xml:space="preserve">
    <value>Performance Management Settings</value>
  </data>
  <data name="71109" xml:space="preserve">
    <value>Workforce Planning Settings</value>
  </data>
  <data name="71110" xml:space="preserve">
    <value>Org and Position Settings</value>
  </data>
  <data name="71111" xml:space="preserve">
    <value>Calculation Settings</value>
  </data>
  <data name="71112" xml:space="preserve">
    <value>Claim Settings</value>
  </data>
  <data name="71113" xml:space="preserve">
    <value>Costing Settings</value>
  </data>
  <data name="71114" xml:space="preserve">
    <value>Vacation Schedule</value>
  </data>
  <data name="71115" xml:space="preserve">
    <value>DARF (IRFF)</value>
  </data>
  <data name="71116" xml:space="preserve">
    <value>DARF Consolidado por Empresa</value>
  </data>
  <data name="71117" xml:space="preserve">
    <value>Analítico de Imposto de Renda</value>
  </data>
  <data name="71119" xml:space="preserve">
    <value>THRCT</value>
  </data>
  <data name="71120" xml:space="preserve">
    <value>TQRCT</value>
  </data>
  <data name="71121" xml:space="preserve">
    <value>Comunicação Aviso Prévio Trabalhado</value>
  </data>
  <data name="71122" xml:space="preserve">
    <value>Empregados com o Líquido Zero</value>
  </data>
  <data name="71123" xml:space="preserve">
    <value>Relação de Líquidos - Analítica</value>
  </data>
  <data name="71124" xml:space="preserve">
    <value>Aviso Prévio de Férias Report</value>
  </data>
  <data name="71125" xml:space="preserve">
    <value>Monthly PAYE report</value>
  </data>
  <data name="71126" xml:space="preserve">
    <value>Supplementary Annual Report</value>
  </data>
  <data name="71127" xml:space="preserve">
    <value>Employee Annual Tax Certificate</value>
  </data>
  <data name="71128" xml:space="preserve">
    <value>UIFSalarySchedule Nextgen Report</value>
  </data>
  <data name="71328" xml:space="preserve">
    <value>Dynamic Form Builder</value>
  </data>
  <data name="71329" xml:space="preserve">
    <value>Dynamic Form Builder</value>
  </data>
  <data name="71330" xml:space="preserve">
    <value>API</value>
  </data>
  <data name="71331" xml:space="preserve">
    <value>Company EFT File</value>
  </data>
  <data name="71332" xml:space="preserve">
    <value>Pacey Dashboard</value>
  </data>
  <data name="71339" xml:space="preserve">
    <value>Copy Configuration</value>
  </data>
  <data name="71340" xml:space="preserve">
    <value>Copy Configuration</value>
  </data>
  <data name="71341" xml:space="preserve">
    <value>ITF Form 5A</value>
  </data>
  <data name="71342" xml:space="preserve">
    <value>Detailed report of salaries paid to employees ID21</value>
  </data>
  <data name="71343" xml:space="preserve">
    <value>Summary Statement ID22</value>
  </data>
  <data name="71344" xml:space="preserve">
    <value>Annual summary statement of wages and salaries</value>
  </data>
  <data name="71345" xml:space="preserve">
    <value>SSNIT Contribution Report Tier 2</value>
  </data>
  <data name="71346" xml:space="preserve">
    <value>SSNIT Contribution Report Tier 1</value>
  </data>
  <data name="71347" xml:space="preserve">
    <value>Generic Tax Certificate</value>
  </data>
  <data name="71348" xml:space="preserve">
    <value>Annual Summary Declaration of Tax Deductions</value>
  </data>
  <data name="71349" xml:space="preserve">
    <value>Declaration de la retenue obligatoire sur les salaires des agents publics et des travailleurs du secteur prive</value>
  </data>
  <data name="71350" xml:space="preserve">
    <value>Etat annexe de la declaration de la retenue obligatoire sur les salaires des agents publics et des travailleurs du secteur prive</value>
  </data>
  <data name="71351" xml:space="preserve">
    <value>Variable Amount Income Report</value>
  </data>
  <data name="71352" xml:space="preserve">
    <value>Local Service Tax (LST) Return</value>
  </data>
  <data name="71353" xml:space="preserve">
    <value>Relação de Líquidos - Analítica - Pensionistas</value>
  </data>
  <data name="71354" xml:space="preserve">
    <value>RTI</value>
  </data>
  <data name="71355" xml:space="preserve">
    <value>Variable Units Income</value>
  </data>
  <data name="71356" xml:space="preserve">
    <value>Vacation Request Form</value>
  </data>
  <data name="71357" xml:space="preserve">
    <value>Vacation Notice and Receipt</value>
  </data>
  <data name="71358" xml:space="preserve">
    <value>Unpaid Value &amp; Days for Leave Vesting Period</value>
  </data>
  <data name="71359" xml:space="preserve">
    <value>Company Dismissing Employee Worked</value>
  </data>
  <data name="71360" xml:space="preserve">
    <value>Relatório Consolidado de Reconciliação da Folha de Pagamento por Estabelecimento</value>
  </data>
  <data name="71361" xml:space="preserve">
    <value>Zambia WCF Electronic File</value>
  </data>
  <data name="71362" xml:space="preserve">
    <value>Pension</value>
  </data>
  <data name="71363" xml:space="preserve">
    <value>Pension</value>
  </data>
  <data name="71364" xml:space="preserve">
    <value>Social Security Form No. (6)</value>
  </data>
  <data name="71365" xml:space="preserve">
    <value>DT0107 Monthly P.A.Y.E Deductions Return</value>
  </data>
  <data name="71366" xml:space="preserve">
    <value>Tanzania NSSF Electronic Monthly Submission</value>
  </data>
  <data name="71367" xml:space="preserve">
    <value>Nominative declaration of payment of contributions</value>
  </data>
  <data name="71368" xml:space="preserve">
    <value>Monthly Declaration of Tax Deductions</value>
  </data>
  <data name="71369" xml:space="preserve">
    <value>Fiche de Declaration de VPS</value>
  </data>
  <data name="71370" xml:space="preserve">
    <value>Social Security Form No. (1)</value>
  </data>
  <data name="71371" xml:space="preserve">
    <value>Social Security Form No. (2)</value>
  </data>
  <data name="71372" xml:space="preserve">
    <value>Workers Compensation Report</value>
  </data>
  <data name="71373" xml:space="preserve">
    <value>Component with Sub Codes Report</value>
  </data>
  <data name="71374" xml:space="preserve">
    <value>M/20 H</value>
  </data>
  <data name="71381" xml:space="preserve">
    <value>Tax Definition</value>
  </data>
  <data name="71393" xml:space="preserve">
    <value>Suspension</value>
  </data>
  <data name="71397" xml:space="preserve">
    <value>STP</value>
  </data>
  <data name="71400" xml:space="preserve">
    <value>IRAS</value>
  </data>
  <data name="71450" xml:space="preserve">
    <value>Record Of Employment Adjustments</value>
  </data>
  <data name="71458" xml:space="preserve">
    <value>Integrations</value>
  </data>
  <data name="71471" xml:space="preserve">
    <value>Pacey Message Template</value>
  </data>
  <data name="71506" xml:space="preserve">
    <value>Copy Payroll Components</value>
  </data>
  <data name="71635" xml:space="preserve">
    <value>Retro Trigger</value>
  </data>
  <data name="71661" xml:space="preserve">
    <value>Year End Reporting</value>
  </data>
  <data name="71665" xml:space="preserve">
    <value>Template Setup</value>
  </data>
  <data name="71666" xml:space="preserve">
    <value>Template Configuration</value>
  </data>
  <data name="71667" xml:space="preserve">
    <value>Template Setup</value>
  </data>
  <data name="71668" xml:space="preserve">
    <value>Template Setup</value>
  </data>
  <data name="71669" xml:space="preserve">
    <value>Template Configuration</value>
  </data>
  <data name="71670" xml:space="preserve">
    <value>Employee Templates</value>
  </data>
  <data name="71674" xml:space="preserve">
    <value>Year End Reporting</value>
  </data>
  <data name="72" xml:space="preserve">
    <value>Pay Frequencies</value>
  </data>
  <data name="72686" xml:space="preserve">
    <value>General Ledger Parameters</value>
  </data>
  <data name="73" xml:space="preserve">
    <value>Run Management</value>
  </data>
  <data name="75" xml:space="preserve">
    <value>Pension And Provident Setup</value>
  </data>
  <data name="76" xml:space="preserve">
    <value>Tables</value>
  </data>
  <data name="77" xml:space="preserve">
    <value>Pension</value>
  </data>
  <data name="80" xml:space="preserve">
    <value>Unions</value>
  </data>
  <data name="81" xml:space="preserve">
    <value>Medical Aid</value>
  </data>
  <data name="82" xml:space="preserve">
    <value>Medical Aid Setup</value>
  </data>
  <data name="83" xml:space="preserve">
    <value>Disability Lumpsum Setup</value>
  </data>
  <data name="84" xml:space="preserve">
    <value>Group Life Setup</value>
  </data>
  <data name="85" xml:space="preserve">
    <value>Once off Payslip Adjustments</value>
  </data>
  <data name="86" xml:space="preserve">
    <value>Payroll Engine</value>
  </data>
  <data name="88" xml:space="preserve">
    <value>Dependants</value>
  </data>
  <data name="89" xml:space="preserve">
    <value>Tax Profile</value>
  </data>
  <data name="90" xml:space="preserve">
    <value>Public Holidays</value>
  </data>
  <data name="91" xml:space="preserve">
    <value>Tax Year Details</value>
  </data>
  <data name="92" xml:space="preserve">
    <value>Suspension</value>
  </data>
  <data name="93" xml:space="preserve">
    <value>Tax Certificates / Historical Drill Down</value>
  </data>
  <data name="96" xml:space="preserve">
    <value>Take On Year To Date Figures</value>
  </data>
  <data name="98" xml:space="preserve">
    <value>Config Settings</value>
  </data>
  <data name="addnewemployee" xml:space="preserve">
    <value>Add New Employee</value>
  </data>
  <data name="apiMenu" xml:space="preserve">
    <value>API</value>
  </data>
  <data name="BasicProfileInfo" xml:space="preserve">
    <value>Basic Profile</value>
  </data>
  <data name="basicsettings" xml:space="preserve">
    <value>Basic Settings</value>
  </data>
  <data name="BulkReportingChanges" xml:space="preserve">
    <value>Bulk Reporting Changes</value>
  </data>
  <data name="bulkupload" xml:space="preserve">
    <value>Bulk Actions</value>
  </data>
  <data name="businesspartner" xml:space="preserve">
    <value>Business Partner</value>
  </data>
  <data name="companyconfig" xml:space="preserve">
    <value>Config</value>
  </data>
  <data name="companypayroll" xml:space="preserve">
    <value>Payroll</value>
  </data>
  <data name="companypayslip" xml:space="preserve">
    <value>Payslip</value>
  </data>
  <data name="CompanyPensionEnrolment" xml:space="preserve">
    <value>Pension Enrolment</value>
  </data>
  <data name="CompanyTableBuilder" xml:space="preserve">
    <value>Component Tables Configuration</value>
  </data>
  <data name="componentSubCodes" xml:space="preserve">
    <value>Component Taxability</value>
  </data>
  <data name="costing" xml:space="preserve">
    <value>Costing</value>
  </data>
  <data name="cstcomcapture" xml:space="preserve">
    <value>Custom Forms</value>
  </data>
  <data name="custfields" xml:space="preserve">
    <value>Custom Screens &amp; Fields</value>
  </data>
  <data name="custfields1" xml:space="preserve">
    <value>Custom Fields</value>
  </data>
  <data name="custominfo" xml:space="preserve">
    <value>Custom Forms</value>
  </data>
  <data name="custscrcat" xml:space="preserve">
    <value>Country Legislative Historical Categories</value>
  </data>
  <data name="editpayslip" xml:space="preserve">
    <value>Edit Payslip</value>
  </data>
  <data name="employee" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="EmploymentContract" xml:space="preserve">
    <value>Employment Contract</value>
  </data>
  <data name="evaluations" xml:space="preserve">
    <value>Evaluations</value>
  </data>
  <data name="evaluationsetup" xml:space="preserve">
    <value>Setup</value>
  </data>
  <data name="gencompany" xml:space="preserve">
    <value>General Company</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>History</value>
  </data>
  <data name="humanresources" xml:space="preserve">
    <value>Human Resources</value>
  </data>
  <data name="IRAS" xml:space="preserve">
    <value>IRAS</value>
  </data>
  <data name="kpis" xml:space="preserve">
    <value>KPI's</value>
  </data>
  <data name="lblBulkuploadOnly" xml:space="preserve">
    <value>Bulk Upload</value>
  </data>
  <data name="lblEmployeeProfile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="lblGettingStarted" xml:space="preserve">
    <value>Getting Started Guide</value>
  </data>
  <data name="lblHowCanWeHelp" xml:space="preserve">
    <value>How can we help?</value>
  </data>
  <data name="lblIdeasPortal" xml:space="preserve">
    <value>Ideas Portal</value>
  </data>
  <data name="lblOnBehalfOf" xml:space="preserve">
    <value>Act On My Behalf</value>
  </data>
  <data name="lblOutOfOffice" xml:space="preserve">
    <value>Out Of Office</value>
  </data>
  <data name="lblSettings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="lblSignOut" xml:space="preserve">
    <value>Sign Out</value>
  </data>
  <data name="lblSubordinates" xml:space="preserve">
    <value>Subordinates</value>
  </data>
  <data name="lblSwitchUserDashboard" xml:space="preserve">
    <value>Switch to</value>
  </data>
  <data name="lblUpdateEmployee" xml:space="preserve">
    <value>Update Employee Information</value>
  </data>
  <data name="Leave" xml:space="preserve">
    <value>Leave</value>
  </data>
  <data name="leavesetup" xml:space="preserve">
    <value>Setup</value>
  </data>
  <data name="listmanagement" xml:space="preserve">
    <value>Dropdown Management</value>
  </data>
  <data name="MenuBulkUpload" xml:space="preserve">
    <value>Bulk Actions</value>
  </data>
  <data name="MenuCompany" xml:space="preserve">
    <value>Config</value>
  </data>
  <data name="MenuEmployee" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="MenuIntegrations" xml:space="preserve">
    <value>Integrations</value>
  </data>
  <data name="MenuOnOffBoarding" xml:space="preserve">
    <value>On / Off boarding</value>
  </data>
  <data name="MenuOther" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="MenuPayrollCycle" xml:space="preserve">
    <value>Payroll Cycle</value>
  </data>
  <data name="MenuReport" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="miscelleneous" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="onBehalfOf" xml:space="preserve">
    <value>Act On My Behalf</value>
  </data>
  <data name="ONOffBoarding" xml:space="preserve">
    <value>On / Off Boarding</value>
  </data>
  <data name="othdropdowns" xml:space="preserve">
    <value>Other Dropdowns</value>
  </data>
  <data name="other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="PaceyMessageTemplate" xml:space="preserve">
    <value>Pacey Message Template</value>
  </data>
  <data name="paycycleset" xml:space="preserve">
    <value>Payroll Cycle Setup</value>
  </data>
  <data name="payrollconfig" xml:space="preserve">
    <value>Payroll Config</value>
  </data>
  <data name="PayrollCycle" xml:space="preserve">
    <value>Payroll Cycle</value>
  </data>
  <data name="payrollprocessing" xml:space="preserve">
    <value>Payroll Processing</value>
  </data>
  <data name="payrollresults" xml:space="preserve">
    <value>Payroll Results</value>
  </data>
  <data name="perfmanagement" xml:space="preserve">
    <value>Performance Management</value>
  </data>
  <data name="Performance" xml:space="preserve">
    <value>Performance</value>
  </data>
  <data name="performancehistory" xml:space="preserve">
    <value>Performance History</value>
  </data>
  <data name="PositionOrg" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>Regions</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="reportsexports" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="Rosters" xml:space="preserve">
    <value>Rosters</value>
  </data>
  <data name="RTI" xml:space="preserve">
    <value>RTI</value>
  </data>
  <data name="rtiRedirect" xml:space="preserve">
    <value>RTI</value>
  </data>
  <data name="securityreport" xml:space="preserve">
    <value>Security Report</value>
  </data>
  <data name="skill" xml:space="preserve">
    <value>Skills</value>
  </data>
  <data name="StabilityRules" xml:space="preserve">
    <value>Employment Stability Rules</value>
  </data>
  <data name="TableBuilderCategory" xml:space="preserve">
    <value>Table Builder Category</value>
  </data>
  <data name="TableBuilderCustomField" xml:space="preserve">
    <value>Table Builder Configuration</value>
  </data>
  <data name="takeOnRunAdmin" xml:space="preserve">
    <value>Take On Run Admin</value>
  </data>
  <data name="Terminate" xml:space="preserve">
    <value>Separate / Reinstate</value>
  </data>
  <data name="Upskilling" xml:space="preserve">
    <value>Upskilling</value>
  </data>
  <data name="WFPset" xml:space="preserve">
    <value>Workforce Planning Setup</value>
  </data>
</root>