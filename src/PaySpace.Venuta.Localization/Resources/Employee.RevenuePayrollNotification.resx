<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ApproverEmail" xml:space="preserve">
    <value>Approver Email</value>
  </data>
  <data name="BlobStorageId" xml:space="preserve">
    <value>Blob Storage Id</value>
  </data>
  <data name="current" xml:space="preserve">
    <value>Current</value>
  </data>
  <data name="EffectiveDate" xml:space="preserve">
    <value>Effective Date</value>
  </data>
  <data name="EmployeeId" xml:space="preserve">
    <value>Employee Id</value>
  </data>
  <data name="EmployeeIsExemptFromPrsiInIreland" xml:space="preserve">
    <value>Employee is exempt from PRSI in Ireland</value>
  </data>
  <data name="EmployeeNumber" xml:space="preserve">
    <value>Employee number</value>
  </data>
  <data name="EmployeePpsn" xml:space="preserve">
    <value>Employee PPSN</value>
  </data>
  <data name="EmployerReference" xml:space="preserve">
    <value>Employer Reference</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="errInvalidRpnIdMessage" xml:space="preserve">
    <value>Invalid RPN Id provided.</value>
  </data>
  <data name="errNumericField" xml:space="preserve">
    <value>Please enter a valid number</value>
  </data>
  <data name="errRPNBulkUploadOnlySupportsUpdate" xml:space="preserve">
    <value>Invalid employee rpn key, bulk operation only accepts update.</value>
  </data>
  <data name="ExclusionOrder" xml:space="preserve">
    <value>Exclusion Order</value>
  </data>
  <data name="ExemptionCase" xml:space="preserve">
    <value>Exemption Case</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Full name</value>
  </data>
  <data name="General" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="IncomeTaxCalculationBasis" xml:space="preserve">
    <value>Taxation</value>
  </data>
  <data name="IncomeTaxCalculationBasisRpn" xml:space="preserve">
    <value>Taxation</value>
  </data>
  <data name="IncomeTaxDeductedToDate" xml:space="preserve">
    <value>Income Tax Deducted to Date</value>
  </data>
  <data name="latest" xml:space="preserve">
    <value>Latest</value>
  </data>
  <data name="lblCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="lblConfirmationMessage" xml:space="preserve">
    <value>One or more selected employees have details that don’t match their latest RPN. Please update their info to match Revenue records before processing payroll.</value>
  </data>
  <data name="lblContinue" xml:space="preserve">
    <value>Continue anyway</value>
  </data>
  <data name="lblEmployeeRpn" xml:space="preserve">
    <value>Employee RPN</value>
  </data>
  <data name="lblEndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="lblLatestRosRpn" xml:space="preserve">
    <value>Latest ROS RPN</value>
  </data>
  <data name="lblRpnEffectiveDate" xml:space="preserve">
    <value>RPN Effective Date</value>
  </data>
  <data name="lblYearlyUscRate1CutOff" xml:space="preserve">
    <value>Yearly USC Rate 1 Cut-Off</value>
  </data>
  <data name="LptToBeDeducted" xml:space="preserve">
    <value>LPT to be Deducted</value>
  </data>
  <data name="PageHeader" xml:space="preserve">
    <value>Revenue Payroll Notification</value>
  </data>
  <data name="PayeSummary" xml:space="preserve">
    <value>PAYE Summary</value>
  </data>
  <data name="PayForIncomeTaxToDate" xml:space="preserve">
    <value>Pay for Income Tax to Date</value>
  </data>
  <data name="PayForUscToDate" xml:space="preserve">
    <value>Pay for USC to Date</value>
  </data>
  <data name="PreviousEmployeePpsn" xml:space="preserve">
    <value>Previous Employee PPSN</value>
  </data>
  <data name="PrsiClassAndSubclass" xml:space="preserve">
    <value>PRSI Class and Subclass</value>
  </data>
  <data name="RpnEffectiveDate" xml:space="preserve">
    <value>RPN Effective Date</value>
  </data>
  <data name="RpnEmploymentId" xml:space="preserve">
    <value>RPN Employment ID</value>
  </data>
  <data name="RpnFamilyName" xml:space="preserve">
    <value>Family name</value>
  </data>
  <data name="RpnFirstName" xml:space="preserve">
    <value>First name</value>
  </data>
  <data name="RpnIssueDate" xml:space="preserve">
    <value>RPN Issue Date</value>
  </data>
  <data name="RpnNumber" xml:space="preserve">
    <value>RPN Number</value>
  </data>
  <data name="SourceType" xml:space="preserve">
    <value>Source Type</value>
  </data>
  <data name="StatePensionContribution" xml:space="preserve">
    <value>State Pension Contribution</value>
  </data>
  <data name="successSaveMessage" xml:space="preserve">
    <value>Employee RPN successfully updated.</value>
  </data>
  <data name="Taxation" xml:space="preserve">
    <value>Taxation</value>
  </data>
  <data name="TaxationInformation" xml:space="preserve">
    <value>Taxation Information</value>
  </data>
  <data name="TaxRate1Percent" xml:space="preserve">
    <value>Tax Rate 1 Percent</value>
  </data>
  <data name="TaxRate2Percent" xml:space="preserve">
    <value>Tax Rate 2 Percent</value>
  </data>
  <data name="TaxYear" xml:space="preserve">
    <value>Tax Year</value>
  </data>
  <data name="Timestamp" xml:space="preserve">
    <value>Timestamp</value>
  </data>
  <data name="UscDeductedToDate" xml:space="preserve">
    <value>USC Deducted to Date</value>
  </data>
  <data name="UscRate1Percent" xml:space="preserve">
    <value>USC Rate 1 Percent</value>
  </data>
  <data name="UscRate2Percent" xml:space="preserve">
    <value>USC Rate 2 Percent</value>
  </data>
  <data name="UscRate3Percent" xml:space="preserve">
    <value>USC Rate 3 Percent</value>
  </data>
  <data name="UscRate4Percent" xml:space="preserve">
    <value>USC Rate 4 Percent</value>
  </data>
  <data name="UscStatus" xml:space="preserve">
    <value>USC Status</value>
  </data>
  <data name="UscStatusExempt" xml:space="preserve">
    <value>Exempt</value>
  </data>
  <data name="UscStatusOrdinary" xml:space="preserve">
    <value>Ordinary</value>
  </data>
  <data name="UscSummary" xml:space="preserve">
    <value>USC Summary</value>
  </data>
  <data name="YearlyRate1CutOff" xml:space="preserve">
    <value>Yearly Rate 1 Cut Off</value>
  </data>
  <data name="YearlyTaxCredit" xml:space="preserve">
    <value>Yearly Tax Credit</value>
  </data>
  <data name="YearlyUscRate1CutOff" xml:space="preserve">
    <value>Yearly USC Rate 1 Cut Off</value>
  </data>
  <data name="YearlyUscRate2CutOff" xml:space="preserve">
    <value>Yearly USC Rate 2 Cut Off</value>
  </data>
  <data name="YearlyUscRate3CutOff" xml:space="preserve">
    <value>Yearly USC Rate 3 Cut Off</value>
  </data>
  <data name="YearlyUscRate4CutOff" xml:space="preserve">
    <value>Yearly USC Rate 4 Cut Off</value>
  </data>
</root>