<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="btnIRASAmendmentGuide" xml:space="preserve">
    <value>IRAS Amendment Guide</value>
  </data>
  <data name="errAccommodationMutualExclusivityAnnualValue" xml:space="preserve">
    <value>Annual value of premises cannot be entered when rent paid to landlord is provided. Please use either annual value or rental.</value>
  </data>
  <data name="errAccommodationMutualExclusivityRent" xml:space="preserve">
    <value>Rent paid to landlord cannot be entered when annual value of premises is provided. Please use either annual value or rental .</value>
  </data>
  <data name="errAccommodationOptionRequiredError" xml:space="preserve">
    <value>An Accommodation option is required</value>
  </data>
  <data name="errAddressLine1Length" xml:space="preserve">
    <value>Address Line 1 is limited to 30 characters.</value>
  </data>
  <data name="errAddressLine2Length" xml:space="preserve">
    <value>Address Line 2 is limited to 30 characters.</value>
  </data>
  <data name="errAddressLine3Length" xml:space="preserve">
    <value>Address Line 3 is limited to 30 characters.</value>
  </data>
  <data name="errAnnualValuePremises" xml:space="preserve">
    <value>Annual Value (AV) of Premises must be 0 or greater.</value>
  </data>
  <data name="errCompanyNameLimit" xml:space="preserve">
    <value>Name of company is limited to 40 characters.</value>
  </data>
  <data name="errCompanyRegistrationNumberLimit" xml:space="preserve">
    <value>Company registration number is limited to 12 characters.</value>
  </data>
  <data name="errCostHotelAccommodation" xml:space="preserve">
    <value>Cost of Hotel Accommodation must be 0 or greater.</value>
  </data>
  <data name="errDependentBooleanField" xml:space="preserve">
    <value>{0} must be empty when {1} is false</value>
  </data>
  <data name="errDependentLookupField" xml:space="preserve">
    <value>{0} must be empty when {1} is {2}</value>
  </data>
  <data name="errDependentPrimitiveField" xml:space="preserve">
    <value>{0} must be empty when {1} is empty</value>
  </data>
  <data name="errDesignatedFundNameLength" xml:space="preserve">
    <value>The designated fund name for compulsory contributions description is limited to 60 characters.</value>
  </data>
  <data name="errDriverCosts" xml:space="preserve">
    <value>Driver costs must be 0 or greater.</value>
  </data>
  <data name="errExerciseDateRequired" xml:space="preserve">
    <value>Exercise date required</value>
  </data>
  <data name="errFurnitureFittingOptionRequired" xml:space="preserve">
    <value>A Furniture &amp; Fitting Option value is required.</value>
  </data>
  <data name="errGrantDateRequired" xml:space="preserve">
    <value>Grant date required</value>
  </data>
  <data name="errHotelAccommodationCountError" xml:space="preserve">
    <value>A maximum of 1 Hotel accommodation is allowed for this tax year.</value>
  </data>
  <data name="errHotelAccommodationCountErrorSheet" xml:space="preserve">
    <value>The selected base year already contains 1 hotel accommodation Appendix 8A record within the sheet</value>
  </data>
  <data name="errHotelAccommodationValueStipulation" xml:space="preserve">
    <value>{0} is associated with the Hotel Accommodation Type.</value>
  </data>
  <data name="errHotelAmountPaidByEmployee" xml:space="preserve">
    <value>Amount paid by employee for hotel accommodation must be 0 or greater.</value>
  </data>
  <data name="errIncomeTaxoptionRequired" xml:space="preserve">
    <value>Income tax option is required.</value>
  </data>
  <data name="errInvalidAmount" xml:space="preserve">
    <value>{0} must not exceed 9 digits (max 999,999,999)</value>
  </data>
  <data name="errInvalidAmountAccruedFrom1993" xml:space="preserve">
    <value>The amount accrued from 1993 must be greater than or equal to 0.</value>
  </data>
  <data name="errInvalidAmountAccruedUntill1992" xml:space="preserve">
    <value>The amount accrued until 1992 must be greater than or equal to 0.</value>
  </data>
  <data name="errInvalidBonusDeclarationDate" xml:space="preserve">
    <value>The bonus declaration date selected does not fall within the basis year.</value>
  </data>
  <data name="errInvalidDecimalAmount" xml:space="preserve">
    <value>{0} is limited to 9 digits and 2 decimals (max 999,999,999.99)</value>
  </data>
  <data name="errInvalidDirectorsFeesApprovalDate" xml:space="preserve">
    <value>The director's fees approval date selected does not fall within the basis year.</value>
  </data>
  <data name="errInvalidExerciseOpenMarketPricePerShare" xml:space="preserve">
    <value>Open market value cannot exceed maximum length of 9 digits.</value>
  </data>
  <data name="errInvalidExercisePrice" xml:space="preserve">
    <value>Exercise price cannot exceed maximum length of 9 digits.</value>
  </data>
  <data name="errInvalidIntegerAmount" xml:space="preserve">
    <value>{0} must be a whole number without decimals.</value>
  </data>
  <data name="errInvalidSharesAmount" xml:space="preserve">
    <value>Number of shares acquired cannot exceed maximum length of 9 digits.</value>
  </data>
  <data name="errInvalidTaxYear" xml:space="preserve">
    <value>Basis year can only be current tax year or one of the previous 4 tax years.</value>
  </data>
  <data name="errLumpSumPaymentBasisRequired" xml:space="preserve">
    <value>A Basis of arriving at the Payment is required.</value>
  </data>
  <data name="errLumpSumPaymentReasonLength" xml:space="preserve">
    <value>The reason for payment is limited to 60 characters.</value>
  </data>
  <data name="errMax11RecordsPerYear" xml:space="preserve">
    <value>No more than 11 records allowed for the selected Basis Year.</value>
  </data>
  <data name="errNumberOfEmployeesSharing" xml:space="preserve">
    <value>The No of employee(s) sharing the premises cannot exceed 99.</value>
  </data>
  <data name="errOccupationPeriodEndDateBasisYear" xml:space="preserve">
    <value>The occupation period end date selected does not fall within the selected basis year.</value>
  </data>
  <data name="errOccupationPeriodEndDateBeforeStart" xml:space="preserve">
    <value>The occupation period end date selected cannot precede the occupation period start date.</value>
  </data>
  <data name="errOccupationPeriodEndDateRequired" xml:space="preserve">
    <value>Occupation period end date is required.</value>
  </data>
  <data name="errOccupationPeriodStartDateBasisYear" xml:space="preserve">
    <value>The occupation period start date selected does not fall within the selected basis year.</value>
  </data>
  <data name="errOccupationPeriodStartDateRequired" xml:space="preserve">
    <value>Occupation period start date is required.</value>
  </data>
  <data name="errOnly5TaxYearsAllowed" xml:space="preserve">
    <value>Records can only be added/edited for the current open or the most recent 4 closed Tax Years.</value>
  </data>
  <data name="errOverseasPostingReasonRequired" xml:space="preserve">
    <value>Overseas posting selection is required.</value>
  </data>
  <data name="errPensionOrProvidentFundNameLength" xml:space="preserve">
    <value>The Pension/Provident fund is limited to 60 characters.</value>
  </data>
  <data name="errRemissionExemptIncomeReasonRequired" xml:space="preserve">
    <value>A Remission / Overseas posting / Exempt income reason is required.</value>
  </data>
  <data name="errRentPaidByEmployee" xml:space="preserve">
    <value>Total Rent paid by employee for Place of Residence must be 0 or greater.</value>
  </data>
  <data name="errRentPaidToLandlordRequired" xml:space="preserve">
    <value>Rent paid to landlord including rental of Furniture &amp; Fittings is required.</value>
  </data>
  <data name="errResidenceProvidedByEmployerCountError" xml:space="preserve">
    <value>More than 1 record is not allowed for this Tax year.</value>
  </data>
  <data name="errResidenceProvidedByEmployerCountErrorSheet" xml:space="preserve">
    <value>The selected base year already contains 10 Appendix 8A record within the sheet</value>
  </data>
  <data name="errResidenceProvidedByEmployerValueStipulation" xml:space="preserve">
    <value>{0} is associated with the Residence Provided by Employer Accommodation Type.</value>
  </data>
  <data name="errSectionALimit" xml:space="preserve">
    <value>Section A Employment Equity-Based Remuneration (EEBR) scheme is limited to 30 records.</value>
  </data>
  <data name="errSectionBLimit" xml:space="preserve">
    <value>Section B Equity Remuneration Incentive Scheme (ERIS) SME is limited to 15 records.</value>
  </data>
  <data name="errSectionCLimit" xml:space="preserve">
    <value>Section C Equity Remuneration Incentive Scheme (ERIS) All Corporations is limited to 15 records.</value>
  </data>
  <data name="errSectionDLimit" xml:space="preserve">
    <value>Section D Equity Remuneration Incentive Scheme (ERIS) Start-Ups is limited to 15 records.</value>
  </data>
  <data name="errServantCosts" xml:space="preserve">
    <value>Servant costs must be 0 or greater.</value>
  </data>
  <data name="errTaxableValueHotelAccommodation" xml:space="preserve">
    <value>Taxable value of hotel accommodation must be 0 or greater.</value>
  </data>
  <data name="errTaxableValuePlaceOfResidence" xml:space="preserve">
    <value>Taxable Value of Place of Residence must be 0 or greater.</value>
  </data>
  <data name="errTaxableValueUtilitiesHousekeeping" xml:space="preserve">
    <value>Taxable Value of Utilities and Housekeeping Costs must be 0 or greater.</value>
  </data>
  <data name="errTenRecordsPerTaxYear" xml:space="preserve">
    <value>Employee: {0} has reached the maximum of 10 records for the {1} basis year.</value>
  </data>
  <data name="errTotalTaxableValuePlaceOfResidence" xml:space="preserve">
    <value>Total Taxable Value of Place of Residence must be 0 or greater.</value>
  </data>
  <data name="errUtilitiesCosts" xml:space="preserve">
    <value>Utilities costs must be 0 or greater.</value>
  </data>
  <data name="errValueFurnitureFittingMismatch" xml:space="preserve">
    <value>Value of Furniture &amp; Fitting does not match the selected option and annual value.</value>
  </data>
  <data name="lblAccommodationDetails" xml:space="preserve">
    <value>Accommodation details</value>
  </data>
  <data name="lblAccommodationId" xml:space="preserve">
    <value>Accommodation ID</value>
  </data>
  <data name="lblAccommodationType" xml:space="preserve">
    <value>Accommodation type</value>
  </data>
  <data name="lblAccomodationAndRelatedBenefitsProvidedByEmployer" xml:space="preserve">
    <value>Accommodation and related benefits provided by employer for the employee</value>
  </data>
  <data name="lblAccomodationOption" xml:space="preserve">
    <value>Accomodation option</value>
  </data>
  <data name="lblAccomodationType" xml:space="preserve">
    <value>Accomodation type</value>
  </data>
  <data name="lblAddressLine1" xml:space="preserve">
    <value>Address Line 1</value>
  </data>
  <data name="lblAddressLine2" xml:space="preserve">
    <value>Address Line 2</value>
  </data>
  <data name="lblAddressLine3" xml:space="preserve">
    <value>Address Line 3</value>
  </data>
  <data name="lblAmountAccruedFrom1993" xml:space="preserve">
    <value>Amount accrued from 1993</value>
  </data>
  <data name="lblAmountAccruedUntill1992" xml:space="preserve">
    <value>Amount accrued up to 31 Dec 1992</value>
  </data>
  <data name="lblAnnualValuePremises" xml:space="preserve">
    <value>Annual value (AV) of premises</value>
  </data>
  <data name="lblAppendix8A" xml:space="preserve">
    <value>Appendix 8A</value>
  </data>
  <data name="lblAppendix8B" xml:space="preserve">
    <value>Appendix 8B</value>
  </data>
  <data name="lblBasisYear" xml:space="preserve">
    <value>Basis year</value>
  </data>
  <data name="lblBonusDeclarationDate" xml:space="preserve">
    <value>Bonus declaration date</value>
  </data>
  <data name="lblCompanyDefaultsCaption" xml:space="preserve">
    <value>Company defaults override</value>
  </data>
  <data name="lblCompanyName" xml:space="preserve">
    <value>Name of company</value>
  </data>
  <data name="lblCompanyOrganizationType" xml:space="preserve">
    <value>Company ID Type</value>
  </data>
  <data name="lblCompanyRegistrationNumber" xml:space="preserve">
    <value>Company registration number</value>
  </data>
  <data name="lblCostHotelAccommodation" xml:space="preserve">
    <value>Cost of hotel accommodation</value>
  </data>
  <data name="lblCtfRefundDetailsCaption" xml:space="preserve">
    <value>CTF refund details</value>
  </data>
  <data name="lblDesignatedFundName" xml:space="preserve">
    <value>Designated Fund name for compulsory contributions</value>
  </data>
  <data name="lblDirectorsFeesApprovalDate" xml:space="preserve">
    <value>Director's fees approval date</value>
  </data>
  <data name="lblDriverCosts" xml:space="preserve">
    <value>Driver costs</value>
  </data>
  <data name="lblEditAppendix8APageHeader" xml:space="preserve">
    <value>Employee Appendix 8A</value>
  </data>
  <data name="lblEmployeeCpfRefundClaimed" xml:space="preserve">
    <value>Actual YTD Employee CPF Refund Claimed</value>
  </data>
  <data name="lblEmployeeEquityBasedRemuneration" xml:space="preserve">
    <value>EEBR ID</value>
  </data>
  <data name="lblEmployeeLiabilityAmount" xml:space="preserve">
    <value>Fixed amount of liability borne by employee</value>
  </data>
  <data name="lblEmployeeNumber" xml:space="preserve">
    <value>Employee number</value>
  </data>
  <data name="lblEmployeeRefundInterest" xml:space="preserve">
    <value>Employee refund interest</value>
  </data>
  <data name="lblEmployerCpfRefundClaimed" xml:space="preserve">
    <value>Actual YTD employer CPF refund claimed</value>
  </data>
  <data name="lblEmployerIncomeTaxAmount" xml:space="preserve">
    <value>Employment income that tax is borne by employer</value>
  </data>
  <data name="lblEmployerRefundInterest" xml:space="preserve">
    <value>Employer refund interest</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Employee number</value>
  </data>
  <data name="lblExeciseDate" xml:space="preserve">
    <value>Date of exercise of ESOP/vesting of ESCOW</value>
  </data>
  <data name="lblExerciseOpenMarketPricePerShare" xml:space="preserve">
    <value>Open market value per share at date of exercise/vesting</value>
  </data>
  <data name="lblExercisePrice" xml:space="preserve">
    <value>Exercise price of ESOP/ price paid/ payable per share</value>
  </data>
  <data name="lblFurnitureFittingOption" xml:space="preserve">
    <value>Furniture &amp; fitting option</value>
  </data>
  <data name="lblGainsGrossAmount" xml:space="preserve">
    <value>Gross amount of gains from ESOP/ESOW plans</value>
  </data>
  <data name="lblGeneralDetailsCaption" xml:space="preserve">
    <value>General details</value>
  </data>
  <data name="lblGrandTotal" xml:space="preserve">
    <value>Grand total</value>
  </data>
  <data name="lblGrantDate" xml:space="preserve">
    <value>Date of grant</value>
  </data>
  <data name="lblHotelAccommodationProvided" xml:space="preserve">
    <value>Hotel accommodation provided</value>
  </data>
  <data name="lblHotelAmountPaidByEmployee" xml:space="preserve">
    <value>Amount paid by employee for hotel accommodation</value>
  </data>
  <data name="lblIncomeTaxOption" xml:space="preserve">
    <value>Income tax options</value>
  </data>
  <data name="lblIR8A" xml:space="preserve">
    <value>IR8A</value>
  </data>
  <data name="lblIsDeclarationByAgent" xml:space="preserve">
    <value>Declarations are prepared by third party agents</value>
  </data>
  <data name="lblIsExemptRemissionIncomeApplicable" xml:space="preserve">
    <value>Remission / Overseas posting / Exempt income applicable</value>
  </data>
  <data name="lblIsIncomeTaxBorneByEmployer" xml:space="preserve">
    <value>Income tax borne by the employer</value>
  </data>
  <data name="lblIsIR21Submitted" xml:space="preserve">
    <value>An IR21 has been submitted</value>
  </data>
  <data name="lblIsSection45Applicable" xml:space="preserve">
    <value>Section 45 is applicable</value>
  </data>
  <data name="lblLumpsumCaption" xml:space="preserve">
    <value>Lumpsum payment (Section 4)</value>
  </data>
  <data name="lblLumpSumPaymentBasis" xml:space="preserve">
    <value>Basis of arriving at the payment</value>
  </data>
  <data name="lblLumpSumPaymentReason" xml:space="preserve">
    <value>Reason for payment</value>
  </data>
  <data name="lblNoSelection" xml:space="preserve">
    <value>empty</value>
  </data>
  <data name="lblNumberOfDays" xml:space="preserve">
    <value>Number of days</value>
  </data>
  <data name="lblNumberOfEmployeesSharing" xml:space="preserve">
    <value>No. of employees sharing premises</value>
  </data>
  <data name="lblOccupationPeriodEndDate" xml:space="preserve">
    <value>Occupation period end date</value>
  </data>
  <data name="lblOccupationPeriodStartDate" xml:space="preserve">
    <value>Occupation period start date</value>
  </data>
  <data name="lblOverseasPostingReason" xml:space="preserve">
    <value>Overseas posting</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Year End Reporting</value>
  </data>
  <data name="lblPensionOrProvidentFundName" xml:space="preserve">
    <value>Pension/Provident fund</value>
  </data>
  <data name="lblPlaceOfResidenceProvidedByEmployer" xml:space="preserve">
    <value>Place of residence provided by employer</value>
  </data>
  <data name="lblPlanType" xml:space="preserve">
    <value>Type of plan</value>
  </data>
  <data name="lblRemissionExemptIncomeAmount" xml:space="preserve">
    <value>Remission / Overseas posting / Exempt income amount</value>
  </data>
  <data name="lblRemissionExemptIncomeReason" xml:space="preserve">
    <value>Remission / Overseas posting / Exempt income reasons</value>
  </data>
  <data name="lblRemissionIncomeAmount" xml:space="preserve">
    <value>Remission / Overseas Posting / Amount of Income</value>
  </data>
  <data name="lblRentPaidByEmployee" xml:space="preserve">
    <value>Rent paid by employee</value>
  </data>
  <data name="lblRentPaidToLandlord" xml:space="preserve">
    <value>Rent paid to landlord</value>
  </data>
  <data name="lblRetirementCaption" xml:space="preserve">
    <value>Retirement benefits</value>
  </data>
  <data name="lblServantCosts" xml:space="preserve">
    <value>Servant costs</value>
  </data>
  <data name="lblSharesAmount" xml:space="preserve">
    <value>Number of shares acquired</value>
  </data>
  <data name="lblShareType" xml:space="preserve">
    <value>Share type</value>
  </data>
  <data name="lblTaxableValueHotelAccommodation" xml:space="preserve">
    <value>Taxable value of hotel accommodation</value>
  </data>
  <data name="lblTaxableValuePlaceOfResidence" xml:space="preserve">
    <value>Taxable value of place of residence</value>
  </data>
  <data name="lblTaxableValueUtilitiesHousekeeping" xml:space="preserve">
    <value>Taxable value of utilities and housekeeping costs</value>
  </data>
  <data name="lblTotalAccommodationBenefit" xml:space="preserve">
    <value>Total accommodation benefit</value>
  </data>
  <data name="lblTotalGainsGrossAmountESOPAndESOWAfter2003" xml:space="preserve">
    <value>Total gross amount of gains from ESOP/ESOW plans for ESOP granted on or after 2003 and ESOW</value>
  </data>
  <data name="lblTotalGainsGrossAmountESOPBefore2003" xml:space="preserve">
    <value>Total gross amount of gains from ESOP/ESOW plans for ESOP granted before 2003</value>
  </data>
  <data name="lblTotalRecordCountExceeds30" xml:space="preserve">
    <value>The selected base year already contains 30 IR8B records.</value>
  </data>
  <data name="lblTotalTaxableValuePlaceOfResidence" xml:space="preserve">
    <value>Total taxable value of place of residence</value>
  </data>
  <data name="lblUtilitiesCosts" xml:space="preserve">
    <value>Utilities costs</value>
  </data>
  <data name="lblValueFurnitureFitting" xml:space="preserve">
    <value>Value of furniture &amp; fitting</value>
  </data>
  <data name="maxRecordMessage" xml:space="preserve">
    <value>Maximum record limit reached. No more entries can be added.</value>
  </data>
</root>