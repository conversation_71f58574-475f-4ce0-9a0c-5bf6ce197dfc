<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="CodeDuplicate" xml:space="preserve">
    <value>The code already exists.</value>
  </data>
  <data name="ComponentVariableCode" xml:space="preserve">
    <value>Variable name</value>
  </data>
  <data name="ComponentVariableId" xml:space="preserve">
    <value>Component Variable</value>
  </data>
  <data name="ComponentVariables" xml:space="preserve">
    <value>Component variables</value>
  </data>
  <data name="EffectiveDate" xml:space="preserve">
    <value>Effective date</value>
  </data>
  <data name="EffectiveDateDuplicate" xml:space="preserve">
    <value>An entry with the same effective date already exists.</value>
  </data>
  <data name="EffectiveDateRequired" xml:space="preserve">
    <value>Effective date is required.</value>
  </data>
  <data name="errDuplicateTemplate" xml:space="preserve">
    <value>Duplicate Template</value>
  </data>
  <data name="HasDependencies" xml:space="preserve">
    <value>This item has dependent records and cannot be removed.</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>History</value>
  </data>
  <data name="InvalidDeleteOnLevel" xml:space="preserve">
    <value>Deletion is not allowed at this level.</value>
  </data>
  <data name="IsOverride" xml:space="preserve">
    <value>Custom value</value>
  </data>
  <data name="PageHeader" xml:space="preserve">
    <value>Template Configuration</value>
  </data>
  <data name="SubTemplateId" xml:space="preserve">
    <value>Sub Template</value>
  </data>
  <data name="TemplateCode" xml:space="preserve">
    <value>Template Configuration</value>
  </data>
  <data name="TemplateConfigurationId" xml:space="preserve">
    <value>Template Configuration</value>
  </data>
  <data name="TemplateDuplicate" xml:space="preserve">
    <value>This template already exists.</value>
  </data>
  <data name="TemplateId" xml:space="preserve">
    <value>Template</value>
  </data>
  <data name="TemplateNotExist" xml:space="preserve">
    <value>The selected template does not exist.</value>
  </data>
  <data name="TemplateRequired" xml:space="preserve">
    <value>Template selection is required.</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
</root>