<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="allowedFileExtensionsMessage" xml:space="preserve">
    <value>Allowed file extensions: .csv</value>
  </data>
  <data name="AssociatedWorksNumber" xml:space="preserve">
    <value>Associated works number</value>
  </data>
  <data name="DownloadRpnTemplate" xml:space="preserve">
    <value>RPN template</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="EmploymentId" xml:space="preserve">
    <value>Employment Id</value>
  </data>
  <data name="errIntegrationErrorBadRequestMessage" xml:space="preserve">
    <value>The information you provided is invalid. Please check your details and try again.</value>
  </data>
  <data name="errIntegrationErrorInternalServerErrorMessage" xml:space="preserve">
    <value>An unexpected error occurred</value>
  </data>
  <data name="errIntegrationErrorUnauthorizedMessage" xml:space="preserve">
    <value>Access denied. Please check your permissions.</value>
  </data>
  <data name="ExistingValue" xml:space="preserve">
    <value>Existing value</value>
  </data>
  <data name="FamilyName" xml:space="preserve">
    <value>Family name</value>
  </data>
  <data name="Field" xml:space="preserve">
    <value>Field</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>First name</value>
  </data>
  <data name="LastDownloadedAt" xml:space="preserve">
    <value>Last downloaded at:</value>
  </data>
  <data name="LatestRosRpnMatch" xml:space="preserve">
    <value>Latest ROS RPN match</value>
  </data>
  <data name="NewValue" xml:space="preserve">
    <value>New value</value>
  </data>
  <data name="PageHeader" xml:space="preserve">
    <value>Revenue Payroll Notification</value>
  </data>
  <data name="PayFrequency" xml:space="preserve">
    <value>Pay frequency</value>
  </data>
  <data name="PendingUpdates" xml:space="preserve">
    <value>Pending Updates</value>
  </data>
  <data name="PPSNumber" xml:space="preserve">
    <value>PPS number</value>
  </data>
  <data name="RetrieveRpn" xml:space="preserve">
    <value>Retrieve</value>
  </data>
  <data name="RpnIssueDate" xml:space="preserve">
    <value>RPN issue date</value>
  </data>
  <data name="UnmatchedRecords" xml:space="preserve">
    <value>Unmatched Records</value>
  </data>
  <data name="UpdatedRpn" xml:space="preserve">
    <value>Updated RPNs</value>
  </data>
  <data name="UpdateEmployee" xml:space="preserve">
    <value>Update Employee</value>
  </data>
  <data name="UploadCsvHeader" xml:space="preserve">
    <value>Upload RPN csv file</value>
  </data>
  <data name="UploadRpn" xml:space="preserve">
    <value>Upload</value>
  </data>
</root>