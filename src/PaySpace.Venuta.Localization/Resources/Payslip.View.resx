<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="0001" xml:space="preserve">
    <value>Payslip successfully emailed.</value>
  </data>
  <data name="0003" xml:space="preserve">
    <value>You cannot create a payslip in this tax year as this employee was terminated in a previous tax year, please re-instate this employee and then return to this screen to create a payslip</value>
  </data>
  <data name="Allowances" xml:space="preserve">
    <value>Allowances</value>
  </data>
  <data name="Company Contributions" xml:space="preserve">
    <value>Company contributions</value>
  </data>
  <data name="Deductions" xml:space="preserve">
    <value>Deductions</value>
  </data>
  <data name="Fringe Benefits" xml:space="preserve">
    <value>Fringe benefits</value>
  </data>
  <data name="lblAllowancesTitle" xml:space="preserve">
    <value>Allowances vs Deductions</value>
  </data>
  <data name="lblAltLanguage" xml:space="preserve">
    <value>View alternative component names</value>
  </data>
  <data name="lblBrowsePageHeader" xml:space="preserve">
    <value>Payslips</value>
  </data>
  <data name="lblCreateConfirmationMessage" xml:space="preserve">
    <value>Are you sure you want to create this payslip?</value>
  </data>
  <data name="lblCurrency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="lblDetail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="lblDownload" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="lblEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="lblNetPayText" xml:space="preserve">
    <value>Net Pay</value>
  </data>
  <data name="lblNo" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Payslip View</value>
  </data>
  <data name="lblPayDate" xml:space="preserve">
    <value>Pay Date</value>
  </data>
  <data name="lblPayslip" xml:space="preserve">
    <value>Payslip</value>
  </data>
  <data name="lblPeriod" xml:space="preserve">
    <value>Period</value>
  </data>
  <data name="lblPeriodEndDate" xml:space="preserve">
    <value>Period End Date</value>
  </data>
  <data name="lblPeriodStartDate" xml:space="preserve">
    <value>Period Start Date</value>
  </data>
  <data name="lblRegularTax" xml:space="preserve">
    <value>Regular Tax - View</value>
  </data>
  <data name="lblRun" xml:space="preserve">
    <value>Run</value>
  </data>
  <data name="lblRunType" xml:space="preserve">
    <value>Run Type</value>
  </data>
  <data name="lblTaxBreakdown" xml:space="preserve">
    <value>Tax Breakdown</value>
  </data>
  <data name="lblTotalNetPay" xml:space="preserve">
    <value>Total Net Pay</value>
  </data>
  <data name="lblTotalText" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="lblView" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="lblViewInHomeCurrency" xml:space="preserve">
    <value>View in home currency</value>
  </data>
  <data name="lblYes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="NotifySuccessTitle" xml:space="preserve">
    <value>success</value>
  </data>
  <data name="OtherActions" xml:space="preserve">
    <value>Other Actions</value>
  </data>
  <data name="PeriodCodeDate" xml:space="preserve">
    <value>Period</value>
  </data>
  <data name="Personals" xml:space="preserve">
    <value>Personals</value>
  </data>
  <data name="RunDescription" xml:space="preserve">
    <value>Run</value>
  </data>
  <data name="RunType" xml:space="preserve">
    <value>Run Type</value>
  </data>
  <data name="Totals" xml:space="preserve">
    <value>Totals</value>
  </data>
</root>