<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="2NDPAYRATE" xml:space="preserve">
    <value>Afficher un deuxième champ de taux de rémunération sur l'écran des détails de rémunération de l'employé</value>
  </data>
  <data name="ADDDATE1" xml:space="preserve">
    <value>Afficher une deuxième date supplémentaire sur l'écran du profil fiscal de l'employé avec le nom suivant</value>
  </data>
  <data name="ADDTAXREFNO" xml:space="preserve">
    <value>Profil fiscal de l'employé : Numéro fiscal</value>
  </data>
  <data name="ALERTEMPCUT" xml:space="preserve">
    <value>Avertir l'employé en cas de modification de bulletin de paie entre la date de clôture et la date de paie</value>
  </data>
  <data name="BANKDETEMAIL" xml:space="preserve">
    <value>Ne pas envoyer de notification par e-mail à l'employé lors de la mise à jour des coordonnées bancaires</value>
  </data>
  <data name="CONSOL" xml:space="preserve">
    <value>Inclure toutes les trains de paie dans le dossier fiscal consolidé de fin d'année</value>
  </data>
  <data name="CUSTPAYSLIP" xml:space="preserve">
    <value>Utiliser le rapport de paie personnalisé comme valeur par défaut sur les bulletins de paie des employés</value>
  </data>
  <data name="EARCLOSE" xml:space="preserve">
    <value>Clôturer les run ce nombre de jours avant la date de paie</value>
  </data>
  <data name="EMPDELNOT" xml:space="preserve">
    <value>Avertir le contact référent lorsqu'un employé est supprimé</value>
  </data>
  <data name="ENROLLTRAINLINK" xml:space="preserve">
    <value>Désactiver le lien « S'inscrire maintenant » dans le calendrier des formations au niveau de l'employé</value>
  </data>
  <data name="General.Settings.Employee" xml:space="preserve">
    <value>Paramètres des salariés</value>
  </data>
  <data name="General.Settings.EmployeeRequired" xml:space="preserve">
    <value>Champs obligatoires pour l'employé</value>
  </data>
  <data name="General.Settings.Payroll" xml:space="preserve">
    <value>Paramètres de paie</value>
  </data>
  <data name="HIDEPAYSLIPEMAIL" xml:space="preserve">
    <value>Masquer l'option d'envoi des bulletins de paie par courriel sur l'écran des bulletins de paie de cette entreprise</value>
  </data>
  <data name="IDNUMCHECK" xml:space="preserve">
    <value>Vérifier les doublons de matricule lors de l'embauche d'un nouvel employé</value>
  </data>
  <data name="INBOXREM" xml:space="preserve">
    <value>Envoyer des e-mails de rappel pour les éléments dans la boîte de réception après ce nombre de jours</value>
  </data>
  <data name="MaritalStatus" xml:space="preserve">
    <value>Profil de base de l'employé : État civil</value>
  </data>
  <data name="MEDREF" xml:space="preserve">
    <value>Éléments de paie récurrents: Numéro de référence d'assurance maladie</value>
  </data>
  <data name="PAYRATECUR" xml:space="preserve">
    <value>Taux de rémunération de l'employé : Devise</value>
  </data>
  <data name="PayRateReason" xml:space="preserve">
    <value>Taux de rémunération de l'employé : Motif de l'augmentation</value>
  </data>
  <data name="PAYSLIPACCESS" xml:space="preserve">
    <value>Permettre aux employés d'accéder à leurs bulletins de paie entre la date de clôture et la date de paie</value>
  </data>
  <data name="SALCLOSE" xml:space="preserve">
    <value>Si la fonction EFT est utilisée, clôturer le run ce nombre de jours avant la date de paie</value>
  </data>
  <data name="SEARCH" xml:space="preserve">
    <value>Activer la recherche des employés de cette entreprise depuis n'importe quelle entreprise du groupe</value>
  </data>
  <data name="SHOWNOTES" xml:space="preserve">
    <value>Afficher les éléments de la partie "Notes" sur le bulletin de paie d'un employé</value>
  </data>
  <data name="SHOWYTD" xml:space="preserve">
    <value>Afficher la valeur cumulée de l'année, quelle que soit la valeur actuelle</value>
  </data>
</root>