<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="errCddReasonRequired" xml:space="preserve">
    <value>Reason for CDD (temporary contract) is required</value>
  </data>
  <data name="errContractEndDateBeforeContractExtensionDate" xml:space="preserve">
    <value>Contract end date cannot be before contract extension effective date</value>
  </data>
  <data name="errContractEndDateBeforeContractModDate" xml:space="preserve">
    <value>Contract end date cannot be before contract modification effective date</value>
  </data>
  <data name="errContractEndDateBeforeContractStartDate" xml:space="preserve">
    <value>Contract end date cannot be before contract start date</value>
  </data>
  <data name="errContractEndDateRequired" xml:space="preserve">
    <value>Contract end date is required</value>
  </data>
  <data name="errContractEndReasonRequired" xml:space="preserve">
    <value>Contract end reason is required</value>
  </data>
  <data name="errContractModificationDateRequired" xml:space="preserve">
    <value>Contact modification effective date is required</value>
  </data>
  <data name="errContractNumberExistsForEmployee" xml:space="preserve">
    <value>A contract with this number already exists for this employee</value>
  </data>
  <data name="errContractNumberInvalidCharacters" xml:space="preserve">
    <value>Contract number cannot contain special characters.</value>
  </data>
  <data name="errContractNumberMaxLength" xml:space="preserve">
    <value>Contract number limited to a maximum of 20 characters</value>
  </data>
  <data name="errContractNumberMinLength" xml:space="preserve">
    <value>Contract number requires a minimum of 5 characters</value>
  </data>
  <data name="errContractStartDateAfterContractEndDate" xml:space="preserve">
    <value>Contract start date cannot be after Contract end date</value>
  </data>
  <data name="errContractStartDateAfterEmploymentStartDate" xml:space="preserve">
    <value>Contract start cannot be before Employment start date</value>
  </data>
  <data name="errContractStartDateRequired" xml:space="preserve">
    <value>Contract start date is required</value>
  </data>
  <data name="errContractTypeGroupRequired" xml:space="preserve">
    <value>Contract type group is required</value>
  </data>
  <data name="errContractTypeRequired" xml:space="preserve">
    <value>Contract type is required</value>
  </data>
  <data name="errEffectiveDateBeforeContractExtensionDate" xml:space="preserve">
    <value>Effective date must be after Contract extension effective date</value>
  </data>
  <data name="errEffectiveDateBeforeContractModificationDate" xml:space="preserve">
    <value>Effective date must be after Contract modification date</value>
  </data>
  <data name="errEffectiveDateBeforeContractStartDate" xml:space="preserve">
    <value>Effective date cannot be before Contract start date</value>
  </data>
  <data name="errEffectiveDateRequired" xml:space="preserve">
    <value>Effective date is required</value>
  </data>
  <data name="errEmploymentStartDateAfterContractStartDate" xml:space="preserve">
    <value>Employment start date cannot be after Contract start date</value>
  </data>
  <data name="errEmploymentStartDateBeforePreviousContractDate" xml:space="preserve">
    <value>Employment start date must be after the previous record's contract end date</value>
  </data>
  <data name="errEmploymentStartDateRequired" xml:space="preserve">
    <value>Employment start date is required</value>
  </data>
  <data name="errExtensionDateAfterEndDate" xml:space="preserve">
    <value>Contract extension effective date must be before contract end date</value>
  </data>
  <data name="errExtensionDateBeforeStartDate" xml:space="preserve">
    <value>Contract extension effective date must be after contract start date</value>
  </data>
  <data name="errExtensionDateRequired" xml:space="preserve">
    <value>Contract extension effective date is required</value>
  </data>
  <data name="errHighestEducationLevelRequired" xml:space="preserve">
    <value>Highest level of education is required</value>
  </data>
  <data name="errInProgressEducationLevelRequired" xml:space="preserve">
    <value>Education level in progress is required</value>
  </data>
  <data name="errModificationDateAfterEndDate" xml:space="preserve">
    <value>Contact modification must be before contract end date</value>
  </data>
  <data name="errModificationDateBeforeStartDate" xml:space="preserve">
    <value>Contract modification effective date must be after contract start date</value>
  </data>
  <data name="errProvEndDateBeforeContractStartDate" xml:space="preserve">
    <value>Provisional contract end date cannot be before contract start date</value>
  </data>
  <data name="errProvEndDateBeforeExtensionDate" xml:space="preserve">
    <value>Provisional contract end date must be after Contract extension effective date</value>
  </data>
  <data name="errProvEndDateBeforeModDate" xml:space="preserve">
    <value>Provisional contract end date must be after Contract modification effective date</value>
  </data>
  <data name="errProvEndDateRequired" xml:space="preserve">
    <value>Provisional contract end date is required</value>
  </data>
  <data name="errReplacedEmployeeNumberRequired" xml:space="preserve">
    <value>Replaced employee number is required</value>
  </data>
  <data name="errRunRequired" xml:space="preserve">
    <value>Company run is required</value>
  </data>
  <data name="errSpecialSponsoredContractRequired" xml:space="preserve">
    <value>Special sponsored contract is required</value>
  </data>
  <data name="errWorkplaceRequired" xml:space="preserve">
    <value>Workplace is required</value>
  </data>
  <data name="errWorkplaceSiretRequired" xml:space="preserve">
    <value>Workplace SIRET is required</value>
  </data>
  <data name="lblCddReason" xml:space="preserve">
    <value>CDD reason</value>
  </data>
  <data name="lblContractEndDate" xml:space="preserve">
    <value>Contract end date</value>
  </data>
  <data name="lblContractEndReason" xml:space="preserve">
    <value>Contract end reason</value>
  </data>
  <data name="lblContractModificationDate" xml:space="preserve">
    <value>Contract modification date</value>
  </data>
  <data name="lblContractNumber" xml:space="preserve">
    <value>Contract number</value>
  </data>
  <data name="lblContractStartDate" xml:space="preserve">
    <value>Contract start date</value>
  </data>
  <data name="lblContractType" xml:space="preserve">
    <value>Contract type</value>
  </data>
  <data name="lblContractTypeGroup" xml:space="preserve">
    <value>Contract type group</value>
  </data>
  <data name="lblEffectiveDate" xml:space="preserve">
    <value>Effective date</value>
  </data>
  <data name="lblEmploymentStartDate" xml:space="preserve">
    <value>Employment start date</value>
  </data>
  <data name="lblEncashLeave" xml:space="preserve">
    <value>Encash leave</value>
  </data>
  <data name="lblExtendContract" xml:space="preserve">
    <value>Extend contract</value>
  </data>
  <data name="lblHighestEducationLevel" xml:space="preserve">
    <value>Highest education level</value>
  </data>
  <data name="lblInProgressEducationLevel" xml:space="preserve">
    <value>Progress education level</value>
  </data>
  <data name="lblLeaveBalance" xml:space="preserve">
    <value>Leave balance</value>
  </data>
  <data name="lblLeaveValue" xml:space="preserve">
    <value>Leave value</value>
  </data>
  <data name="lblModifyContract" xml:space="preserve">
    <value>Modify contract</value>
  </data>
  <data name="lblNotReEmployable" xml:space="preserve">
    <value>Not re-employable</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Employment Contract</value>
  </data>
  <data name="lblProvisionalContractEndDate" xml:space="preserve">
    <value>Provisional contract end date</value>
  </data>
  <data name="lblReinstateContract" xml:space="preserve">
    <value>Reinstate contract</value>
  </data>
  <data name="lblReplacedEmployeeNumber" xml:space="preserve">
    <value>Replaced employee number</value>
  </data>
  <data name="lblRunId" xml:space="preserve">
    <value>Run id</value>
  </data>
  <data name="lblSpecialSponsoredContract" xml:space="preserve">
    <value>Special sponsored contract</value>
  </data>
  <data name="lblTerminateContract" xml:space="preserve">
    <value>Terminate contract</value>
  </data>
  <data name="lblWorkplace" xml:space="preserve">
    <value>Workplace</value>
  </data>
  <data name="lblWorkplaceSIRET" xml:space="preserve">
    <value>Workplace SIRET</value>
  </data>
</root>