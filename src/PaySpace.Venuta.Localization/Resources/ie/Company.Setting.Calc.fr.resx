<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BLKAUTOPAY" xml:space="preserve">
    <value>Autoriser les téléversements groupés de bulletins de paie vers les composantes statutaires et automatiques.</value>
  </data>
  <data name="BLKBASAUT" xml:space="preserve">
    <value>Ne pas autoriser la mise à jour en masse d'éléments de salaire pour un employé dont l'indicateur de paie automatique est coché.</value>
  </data>
  <data name="Calc.Settings.General" xml:space="preserve">
    <value>Calculs généraux</value>
  </data>
  <data name="Calc.Settings.Proration" xml:space="preserve">
    <value>Proratisation</value>
  </data>
  <data name="CEPSS" xml:space="preserve">
    <value>Afficher la case « statut temporaire » pour cumuler les périodes travaillées en tant qu'intérimaire.</value>
  </data>
  <data name="COMORD" xml:space="preserve">
    <value>Afficher les numéros d'ordre de calcul sur l'écran des composants de paie de l'entreprise.</value>
  </data>
  <data name="PAYRATEROSTER" xml:space="preserve">
    <value>Afficher la planning des employés sur l'écran de paie plutôt que le poste.</value>
  </data>
  <data name="POSITIONERR" xml:space="preserve">
    <value>Tout employé non rattaché à un poste doit générer une erreur et le bulletin de paie ne doit pas être calculé.</value>
  </data>
  <data name="PRORATEPER" xml:space="preserve">
    <value>Arrondir systématiquement les périodes pour cumuler une période travaillée lors du calcul des périodes travaillées.</value>
  </data>
  <data name="PRORATEPKG" xml:space="preserve">
    <value>Payer rétroactivement et automatiquement les nouveaux employés qui ont manqué leur premier bulletin de paie, jusqu'à ce nombre de jours.</value>
  </data>
  <data name="PROU" xml:space="preserve">
    <value>Proratiser en utilisant</value>
  </data>
  <data name="TERMCALC" xml:space="preserve">
    <value>Ne pas calculer les bulletins des employés des périodes postérieures à leur date de cessation d'emploi.</value>
  </data>
  <data name="TERMCALC1" xml:space="preserve">
    <value>Ne pas calculer les bulletins des employés ayant quitté leur emploi depuis deux mois ou plus</value>
  </data>
</root>