<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="0015" xml:space="preserve">
    <value>Nom de la rue / Numéro d'adresse postale est requis</value>
  </data>
  <data name="0016" xml:space="preserve">
    <value>Le type d'adresse physique ne peut pas être modifié</value>
  </data>
  <data name="0017" xml:space="preserve">
    <value>Adresse physique requise</value>
  </data>
  <data name="0031" xml:space="preserve">
    <value>Ville ou village / ville postale est obligatoire</value>
  </data>
  <data name="0036" xml:space="preserve">
    <value>Nom de la rue / Numéro d'adresse postale ne peut pas dépasser {0} caractères</value>
  </data>
  <data name="0037" xml:space="preserve">
    <value>Tout comme physique ne peut pas être mis sur la ligne Adresse physique</value>
  </data>
  <data name="0043" xml:space="preserve">
    <value>Ville ou village / Ville postale ne peut pas dépasser {0} caractères</value>
  </data>
  <data name="0044" xml:space="preserve">
    <value>Banlieue ou District / Agence postale ne peut pas dépasser {0} caractères</value>
  </data>
  <data name="0048" xml:space="preserve">
    <value>Un seul numéro de contact requis</value>
  </data>
  <data name="0050" xml:space="preserve">
    <value>L'employé ne peut pas être supprimé</value>
  </data>
  <data name="10" xml:space="preserve">
    <value>L'âge légal pour le pays d'imposition de cette société est de 16 ans</value>
  </data>
  <data name="11" xml:space="preserve">
    <value>Le champ de Province est obligatoire</value>
  </data>
  <data name="12" xml:space="preserve">
    <value>Le champ Pays est obligatoire</value>
  </data>
  <data name="13" xml:space="preserve">
    <value>Champ de code d'adresse obligatoire</value>
  </data>
  <data name="14" xml:space="preserve">
    <value>Le numéro d'employé existe déjà dans cette société</value>
  </data>
  <data name="15" xml:space="preserve">
    <value>Ligne D'Adresse1 requis</value>
  </data>
  <data name="16" xml:space="preserve">
    <value>Deux adresses sont requises</value>
  </data>
  <data name="17" xml:space="preserve">
    <value>La première adresse doit être l'adresse physique</value>
  </data>
  <data name="18" xml:space="preserve">
    <value>Types d'adresses en double non autorisés</value>
  </data>
  <data name="19" xml:space="preserve">
    <value>Initiales requises</value>
  </data>
  <data name="20" xml:space="preserve">
    <value>Format de courriel invalide</value>
  </data>
  <data name="21" xml:space="preserve">
    <value>Province non valide pour un pays donné</value>
  </data>
  <data name="22" xml:space="preserve">
    <value>Numéro d'employé requis</value>
  </data>
  <data name="23" xml:space="preserve">
    <value>Numéro de travail invalide, seuls les chiffres sont autorisés</value>
  </data>
  <data name="24" xml:space="preserve">
    <value>Les initals ne peuvent pas être plus de {0} caractères</value>
  </data>
  <data name="25" xml:space="preserve">
    <value>L'extension de travail ne peut pas dépasser {0} caractères</value>
  </data>
  <data name="26" xml:space="preserve">
    <value>Email déjà enregistré</value>
  </data>
  <data name="27" xml:space="preserve">
    <value>État civil requis</value>
  </data>
  <data name="28" xml:space="preserve">
    <value>Anniversaire requis</value>
  </data>
  <data name="29" xml:space="preserve">
    <value>Sexe requis</value>
  </data>
  <data name="30" xml:space="preserve">
    <value>La date de création ne peut pas être définie ou modifiée</value>
  </data>
  <data name="31" xml:space="preserve">
    <value>Ligne D'Adresse3 requis</value>
  </data>
  <data name="32" xml:space="preserve">
    <value>Le code d'adresse doit être compris entre {0} et {1} caractères.</value>
  </data>
  <data name="33" xml:space="preserve">
    <value>Le code d'adresse ne peut contenir que des numéros</value>
  </data>
  <data name="34" xml:space="preserve">
    <value>Le numéro d'unité ne peut pas dépasser {0} caractères.</value>
  </data>
  <data name="35" xml:space="preserve">
    <value>Le complexe ne peut pas être plus de {0} caractères</value>
  </data>
  <data name="36" xml:space="preserve">
    <value>La ligne d'adresse1 ne peut pas contenir plus de {0} caractères</value>
  </data>
  <data name="37" xml:space="preserve">
    <value>Même que physique ne peut pas être défini</value>
  </data>
  <data name="38" xml:space="preserve">
    <value>Les services spéciaux doivent être vides</value>
  </data>
  <data name="39" xml:space="preserve">
    <value>Le soin de l'adresse ne peut pas être réglé</value>
  </data>
  <data name="40" xml:space="preserve">
    <value>Le soin de l'intermédiaire ne peut pas être fixé</value>
  </data>
  <data name="41" xml:space="preserve">
    <value>Soins de l'intermédiaire requis</value>
  </data>
  <data name="42" xml:space="preserve">
    <value>Citoyenneté requise</value>
  </data>
  <data name="43" xml:space="preserve">
    <value>La ligne d'adresse3 ne peut pas contenir plus de {0} caractères</value>
  </data>
  <data name="44" xml:space="preserve">
    <value>La ligne d'adresse2 ne peut pas contenir plus de {0} caractères</value>
  </data>
  <data name="45" xml:space="preserve">
    <value>Complexe non autorisé pour le type d'adresse Boîte postale ou Sac privé</value>
  </data>
  <data name="46" xml:space="preserve">
    <value>Numéro d'unité non autorisé pour le type d'adresse Boîte postale ou Sac privé</value>
  </data>
  <data name="47" xml:space="preserve">
    <value>EtiExempt autorisé uniquement pour l'Afrique du Sud</value>
  </data>
  <data name="7" xml:space="preserve">
    <value>Le courrier électronique existe déjà dans cette entreprise</value>
  </data>
  <data name="8" xml:space="preserve">
    <value>L'âge légal pour le pays d'imposition de cette société est de 14 ans</value>
  </data>
  <data name="9" xml:space="preserve">
    <value>L'âge légal pour le pays d'imposition de cette société est de 15 ans</value>
  </data>
  <data name="AccessDenied" xml:space="preserve">
    <value>Vous n'avez pas l'autorisation de modifier le dossier</value>
  </data>
  <data name="btnSave" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="CompanyFrequencyId" xml:space="preserve">
    <value>Fréquence</value>
  </data>
  <data name="deleteMessage" xml:space="preserve">
    <value>L'employé sera entièrement supprimé, êtes-vous sûr de vouloir continuer ?</value>
  </data>
  <data name="deleteMessageNo" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="deleteMessageYes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="lblBirthdate" xml:space="preserve">
    <value>Date de naissance</value>
  </data>
  <data name="lblBirthdayWarning" xml:space="preserve">
    <value>Veuillez noter que vous avez choisi l'âge de l'employé comme étant inférieur à 16 ans</value>
  </data>
  <data name="lblCellNumber" xml:space="preserve">
    <value>Numéro de téléphone portable</value>
  </data>
  <data name="lblCitiz" xml:space="preserve">
    <value>Citoyenneté</value>
  </data>
  <data name="lblContactDetHeader" xml:space="preserve">
    <value>Détails du contact</value>
  </data>
  <data name="lblDetailsHeader" xml:space="preserve">
    <value>Détails</value>
  </data>
  <data name="lblDisabledT" xml:space="preserve">
    <value>Type d'invalidité</value>
  </data>
  <data name="lblDistributionService" xml:space="preserve">
    <value>Service de distribution</value>
  </data>
  <data name="lblEditSuccessful" xml:space="preserve">
    <value>Modifications enregistrées avec succès.</value>
  </data>
  <data name="lblEmailAdd" xml:space="preserve">
    <value>Adresse e-mail</value>
  </data>
  <data name="lblEmergContactDetHeader" xml:space="preserve">
    <value>Contacts d'urgence</value>
  </data>
  <data name="lblEmergContAddress" xml:space="preserve">
    <value>Adresse du contact d'urgence</value>
  </data>
  <data name="lblEmergContName" xml:space="preserve">
    <value>Personne à contacter en cas d'urgence</value>
  </data>
  <data name="lblEmergContNumber" xml:space="preserve">
    <value>Numéro du contact en cas d'urgence</value>
  </data>
  <data name="lblEmpDetailsHeading" xml:space="preserve">
    <value>Détails de l'employé</value>
  </data>
  <data name="lblEmpIDNumber" xml:space="preserve">
    <value>Numéro d'identification</value>
  </data>
  <data name="lblEmployeeNumberGenerated" xml:space="preserve">
    <value>Seront générés</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Matricule</value>
  </data>
  <data name="lblEtiExempt" xml:space="preserve">
    <value>N'est pas admissible aux incitatifs fiscaux à l'emploi</value>
  </data>
  <data name="lblFirstName" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="lblForeignNat" xml:space="preserve">
    <value>Travailleur étranger</value>
  </data>
  <data name="lblFormGroupCaption" xml:space="preserve">
    <value>Détails</value>
  </data>
  <data name="lblGender" xml:space="preserve">
    <value>genre</value>
  </data>
  <data name="lblHomeNumber" xml:space="preserve">
    <value>Numéro de téléphone à domicile</value>
  </data>
  <data name="lblInheritFromCompany" xml:space="preserve">
    <value>Héritage de l'entreprise</value>
  </data>
  <data name="lblInitial" xml:space="preserve">
    <value>Initiales</value>
  </data>
  <data name="lblInvalidCellNumber" xml:space="preserve">
    <value>Numéro de téléphone portable non valide, seuls les numéros autorisés</value>
  </data>
  <data name="lblInvalidHomeNumber" xml:space="preserve">
    <value>Numéro de domicile invalide, seuls les numéros autorisés</value>
  </data>
  <data name="lblIsCareofAddress" xml:space="preserve">
    <value>Autre adresse?</value>
  </data>
  <data name="lblIsEmpRetired" xml:space="preserve">
    <value>Cet employé est-il à la retraite?</value>
  </data>
  <data name="lblKenyaSdlexemption" xml:space="preserve">
    <value>Raison de l'exemption de la NITA</value>
  </data>
  <data name="lblLanguage" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="lblLastName" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="lblMaidenName" xml:space="preserve">
    <value>Nom d'usage</value>
  </data>
  <data name="lblMaritalStat" xml:space="preserve">
    <value>État civil</value>
  </data>
  <data name="lblMiddleName" xml:space="preserve">
    <value>Deuxième prénom</value>
  </data>
  <data name="lblName" xml:space="preserve">
    <value>prénom</value>
  </data>
  <data name="lblNationality" xml:space="preserve">
    <value>Nationalité</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Gestion Salarié</value>
  </data>
  <data name="lblPhysicalAddress" xml:space="preserve">
    <value>Physique</value>
  </data>
  <data name="lblPostalAddress" xml:space="preserve">
    <value>Adresse Postal</value>
  </data>
  <data name="lblPrefName" xml:space="preserve">
    <value>Nom préféré</value>
  </data>
  <data name="lblRace" xml:space="preserve">
    <value>race</value>
  </data>
  <data name="lblReportId" xml:space="preserve">
    <value>Fiche de paie par défaut</value>
  </data>
  <data name="lblSameAsPostal" xml:space="preserve">
    <value>L'adresse postale est-elle la même que l'adresse physique?</value>
  </data>
  <data name="lblSdlexemption" xml:space="preserve">
    <value>Raison de l'exemption de la SDL</value>
  </data>
  <data name="lblStandardIndustryCodeHeader" xml:space="preserve">
    <value>Groupe de codes industriels standard</value>
  </data>
  <data name="lblSubmit" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="lblSubStandardIndustryCodeId" xml:space="preserve">
    <value>Code standard de l'industrie</value>
  </data>
  <data name="lblTitle" xml:space="preserve">
    <value>Titre</value>
  </data>
  <data name="lblUifexemption" xml:space="preserve">
    <value>Raison de l'exemption des UIF</value>
  </data>
  <data name="lblWorkEx" xml:space="preserve">
    <value>Numéro de poste</value>
  </data>
  <data name="lblWorkNumber" xml:space="preserve">
    <value>Numéro de téléphone au travail</value>
  </data>
  <data name="tabAddresses" xml:space="preserve">
    <value>Adresses</value>
  </data>
  <data name="tabContactDetails" xml:space="preserve">
    <value>Coordonnées</value>
  </data>
  <data name="tabEmployeeDetails" xml:space="preserve">
    <value>Détails de Salarié</value>
  </data>
  <data name="tabExemptionsAndOther" xml:space="preserve">
    <value>Exemptions et autres</value>
  </data>
  <data name="tabOnBehalfOf" xml:space="preserve">
    <value>Agir en mon nom</value>
  </data>
  <data name="tabPersonal" xml:space="preserve">
    <value>Renseignements personnels</value>
  </data>
  <data name="tabWorkflowSettings" xml:space="preserve">
    <value>Paramètres d'absence du bureau</value>
  </data>
  <data name="txtimage" xml:space="preserve">
    <value>Photo du Salarié</value>
  </data>
</root>