<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="errApplicationCutoffDatePassed" xml:space="preserve">
    <value>The application cut-off date has already passed</value>
  </data>
  <data name="errDelegateAlreadyExists" xml:space="preserve">
    <value>{0} has already been added to the dated schedule</value>
  </data>
  <data name="errDescriptionDuplicateExists" xml:space="preserve">
    <value>A course with this description already exists.</value>
  </data>
  <data name="errEmployeeTrainingCourseExists" xml:space="preserve">
    <value>This training course cannot be deleted as it has employees linked to it.</value>
  </data>
  <data name="errMaxNoOfDelegatesReached" xml:space="preserve">
    <value>The maximum number of delegates has been reached</value>
  </data>
  <data name="errScheduleDelegateExists" xml:space="preserve">
    <value>This course schedule cannot be deleted as it has delegates linked to it</value>
  </data>
  <data name="errTrainingCourseScheduleExists" xml:space="preserve">
    <value>This training course cannot be deleted as it has schedules linked to it</value>
  </data>
  <data name="lblApplicationCutOffDate" xml:space="preserve">
    <value>Application cut-off date</value>
  </data>
  <data name="lblComments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="lblCoordinatorEmail" xml:space="preserve">
    <value>Co-ordinator email</value>
  </data>
  <data name="lblCoordinatorName" xml:space="preserve">
    <value>Co-ordinator name</value>
  </data>
  <data name="lblCoordinatorNumber" xml:space="preserve">
    <value>Co-ordinator contact no</value>
  </data>
  <data name="lblCoordinatorType" xml:space="preserve">
    <value>Co-ordinator type</value>
  </data>
  <data name="lblCourseCostNotes" xml:space="preserve">
    <value>Direct course cost notes</value>
  </data>
  <data name="lblCourseDescription" xml:space="preserve">
    <value>Course Description</value>
  </data>
  <data name="lblCourseDuration" xml:space="preserve">
    <value>Course Duration</value>
  </data>
  <data name="lblCourseDurationHours" xml:space="preserve">
    <value>Course duration in hours</value>
  </data>
  <data name="lblCourseOverview" xml:space="preserve">
    <value>Course Overview</value>
  </data>
  <data name="lblCourseRequirements" xml:space="preserve">
    <value>Course Requirements</value>
  </data>
  <data name="lblCourseTitle" xml:space="preserve">
    <value>Course title</value>
  </data>
  <data name="lblCourseType" xml:space="preserve">
    <value>Course Type</value>
  </data>
  <data name="lblCredits" xml:space="preserve">
    <value>Credits</value>
  </data>
  <data name="lblDelegateEmailAddress" xml:space="preserve">
    <value>Delegate email</value>
  </data>
  <data name="lblDelegateName" xml:space="preserve">
    <value>Delegate name</value>
  </data>
  <data name="lblDelegateType" xml:space="preserve">
    <value>External delegate</value>
  </data>
  <data name="lblDirectCourseCost" xml:space="preserve">
    <value>Direct course cost</value>
  </data>
  <data name="lblEffectiveDate" xml:space="preserve">
    <value>Effective Date</value>
  </data>
  <data name="lblEndDate" xml:space="preserve">
    <value>End date</value>
  </data>
  <data name="lblInHouseOutsideProvider" xml:space="preserve">
    <value>Provider</value>
  </data>
  <data name="lblInstitution" xml:space="preserve">
    <value>Institution</value>
  </data>
  <data name="lblIsEquityTraining" xml:space="preserve">
    <value>Equity training</value>
  </data>
  <data name="lblMaxCandidates" xml:space="preserve">
    <value>Maximum candidates</value>
  </data>
  <data name="lblMaxNoCandidates" xml:space="preserve">
    <value>Max delegate capacity</value>
  </data>
  <data name="lblMinCandidates" xml:space="preserve">
    <value>Minimum candidates</value>
  </data>
  <data name="lblNoCandidates" xml:space="preserve">
    <value>No. of delegates</value>
  </data>
  <data name="lblNqfLevel" xml:space="preserve">
    <value>ABET/NQF level</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Training Courses</value>
  </data>
  <data name="lblProgramCategory" xml:space="preserve">
    <value>Program category</value>
  </data>
  <data name="lblSaqaIdNumber" xml:space="preserve">
    <value>SAQA ID number</value>
  </data>
  <data name="lblSaqaRegistered" xml:space="preserve">
    <value>SAQA registered?</value>
  </data>
  <data name="lblSkillsPriority" xml:space="preserve">
    <value>Skills priority</value>
  </data>
  <data name="lblStartDate" xml:space="preserve">
    <value>Start date</value>
  </data>
  <data name="lblStopDate" xml:space="preserve">
    <value>Expiry date</value>
  </data>
  <data name="lblVenue" xml:space="preserve">
    <value>Venue</value>
  </data>
  <data name="lblVenueAddress" xml:space="preserve">
    <value>Venue address</value>
  </data>
  <data name="schedule" xml:space="preserve">
    <value>Schedule</value>
  </data>
</root>