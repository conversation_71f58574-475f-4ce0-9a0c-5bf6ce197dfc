namespace PaySpace.Venuta.Data.Models.Validation.Employees
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees.SG;

    public class EmployeeIR8AValidator : AbstractValidator<EmployeeIR8A>
    {
        private static readonly string[] OverseasPostingRequiredCodes = new string[] { "IOE", "OVERSEASEMPLPENFUND" };
        private static readonly string[] EmployerIncomeTaxAmountRequiredCodes = new string[] { "P" };
        private static readonly string[] EmployeeLiabilityAmountRequiredCodes = new string[] { "H" };
        private static readonly string[] RemissionExemptIncomeRequiredCodes = new string[] { "OCLA", "SEAMAN", "EXMPT", "PENFUNDTAXCONC" };

        private readonly ISingaporeEmployeeIR8AService employeeIR8AService;
        private readonly ICountryTaxYearService countryTaxYearService;
        private readonly IEmployeeService employeeService;
        private readonly IStringLocalizer localizer;
        private readonly IEnumService enumService;

        public EmployeeIR8AValidator(
            ISingaporeEmployeeIR8AService employeeIR8AService,
            ICountryTaxYearService countryTaxYearService,
            IEmployeeService employeeService,
            IStringLocalizer<EmployeeIR8A> localizer,
            IEnumService enumService)
        {
            this.localizer = localizer;
            this.enumService = enumService;
            this.employeeService = employeeService;
            this.employeeIR8AService = employeeIR8AService;
            this.countryTaxYearService = countryTaxYearService;

            this.RuleSet(RuleSetNames.Create, this.CreateValidation);
            this.RuleSet(RuleSetNames.Update, this.UpdateValidation);
            this.RuleSet(RuleSetNames.Delete, this.DeleteValidation);
        }

        private void DeleteValidation()
        {
            this.RuleFor(_ => _.TaxYearId)
                .MustAsync(this.IsAbleToEditOrCreateForTaxYearAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidTaxYearError));
        }

        private void CreateValidation()
        {
            this.RuleFor(_ => _.EmployeeId)
                .MustAsync(this.HasNotReachedMaxRecordsAsync)
                .WithMessage("{ValidationMessage}");

            this.UpdateValidation();
        }

        private void UpdateValidation()
        {
            this.RuleFor(_ => _.TaxYearId)
                .MustAsync(this.IsAbleToEditOrCreateForTaxYearAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidTaxYearError));

            this.RuleFor(_ => _.IncomeTaxOptionId)
                .Empty()
                .When(_ => !_.IsIncomeTaxBorneByEmployer)
                .WithMessage(string.Format(
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.DependentBooleanFieldError),
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.IncomeTaxOption),
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.IsIncomeTaxBorneByEmployer)));

            this.RuleFor(_ => _.IncomeTaxOptionId)
                .NotEmpty()
                .When(_ => _.IsIncomeTaxBorneByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.IncomeTaxoptionRequiredError));

            this.RuleFor(_ => _.RemissionExemptIncomeReasonId)
                .Empty()
                .When(_ => !_.IsExemptRemissionIncomeApplicable)
                .WithMessage(string.Format(
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.DependentBooleanFieldError),
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeReason),
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.IsExemptRemissionIncomeApplicable)));

            this.RuleFor(_ => _.RemissionExemptIncomeReasonId)
                .NotEmpty()
                .When(_ => _.IsExemptRemissionIncomeApplicable)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeReasonRequiredError));

            this.RuleFor(_ => _.OverseasPostingReasonId)
                .Empty()
                .When((_, context) => !this.OverseasPostingReasonRequired(_, context))
                .WithMessage("{ValidationMessage}");

            this.RuleFor(_ => _.OverseasPostingReasonId)
                .NotEmpty()
                .When(this.OverseasPostingReasonRequired)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.OverseasPostingReasonRequiredError));

            this.RuleFor(_ => _.EmployeeLiabilityAmount)
                .Empty()
                .When((_, context) => !this.EmployeeLiabilityAmountRequired(_, context))
                .WithMessage("{ValidationMessage}");

            this.RuleFor(_ => _.EmployeeLiabilityAmount)
                .GreaterThanOrEqualTo(0)
                .LessThanOrEqualTo(999_999_999)
                .WithMessage(string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidAmount), this.localizer.GetString(SystemAreas.YearEndReporting.Keys.EmployeeLiabilityAmount)));

            this.RuleFor(_ => _.EmployerIncomeTaxAmount)
                .Empty()
                .When((_, context) => !this.EmployerIncomeTaxAmountRequired(_, context))
                .WithMessage("{ValidationMessage}");

            this.RuleFor(_ => _.EmployerIncomeTaxAmount)
                .GreaterThanOrEqualTo(0)
                .LessThanOrEqualTo(999_999_999)
                .WithMessage(string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidAmount), this.localizer.GetString(SystemAreas.YearEndReporting.Keys.EmployerIncomeTaxAmount)));

            this.RuleFor(_ => _.RemissionIncomeAmount)
                .GreaterThanOrEqualTo(0)
                .LessThanOrEqualTo(999_999_999)
                .WithMessage(string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidAmount), this.localizer.GetString(SystemAreas.YearEndReporting.Keys.RemissionIncomeAmount)));

            this.RuleFor(_ => _.RemissionExemptIncomeAmount)
                .GreaterThanOrEqualTo(0)
                .LessThanOrEqualTo(999_999_999)
                .WithMessage(string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidAmount), this.localizer.GetString(SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeAmount)));

            this.RuleFor(_ => _.RemissionExemptIncomeAmount)
                .Empty()
                .When((_, context) => !this.RemissionExemptIncomeAmountRequired(_, context))
                .WithMessage("{ValidationMessage}");

            this.RuleFor(_ => _.DesignatedFundName)
                .MaximumLength(60)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.DesignatedFundNameLengthError));

            this.RuleFor(_ => _.LumpSumPaymentReason)
                .MaximumLength(60)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.LumpSumPaymentReasonLengthError));

            this.RuleFor(_ => _.LumpSumPaymentBasis)
                .Must(string.IsNullOrWhiteSpace)
                .When(_ => string.IsNullOrWhiteSpace(_.LumpSumPaymentReason))
                .WithMessage(string.Format(
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.DependentPrimitiveFieldError),
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.LumpSumPaymentBasis),
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.LumpSumPaymentReason)));

            this.RuleFor(_ => _.LumpSumPaymentBasis)
                .Must(_ => !string.IsNullOrWhiteSpace(_))
                .When(_ => !string.IsNullOrWhiteSpace(_.LumpSumPaymentReason))
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.LumpSumPaymentBasisRequiredError));

            this.RuleFor(_ => _.PensionOrProvidentFundName)
                .MaximumLength(60)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.PensionOrProvidentFundNameLengthError));

            this.RuleFor(_ => _.AmountAccruedFrom1993)
                .Empty()
                .When(_ => string.IsNullOrWhiteSpace(_.PensionOrProvidentFundName))
                .WithMessage(string.Format(
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.DependentPrimitiveFieldError),
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AmountAccruedFrom1993),
                    this.localizer.GetString(SystemAreas.YearEndReporting.Keys.PensionOrProvidentFundName)))
                .GreaterThanOrEqualTo(0)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AmountAccruedFrom1993))
                .PrecisionScale(11, 2, true)
                .WithMessage(string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidDecimalAmount), this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AmountAccruedFrom1993)));

            this.RuleFor(_ => _.BonusDeclarationDate)
                .MustAsync(this.IsWithinSelectedBasisYearAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidBonusDeclarationDateError));

            this.RuleFor(_ => _.DirectorsFeesApprovalDate)
                .MustAsync(this.IsWithinSelectedBasisYearAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidDirectorsFeesApprovalDateError));

            this.RuleFor(_ => _.EmployerCpfRefundClaimed)
                .GreaterThanOrEqualTo(0)
                .LessThanOrEqualTo(999_999_999)
                .WithMessage(string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidAmount), this.localizer.GetString(SystemAreas.YearEndReporting.Keys.EmployerCpfRefundClaimed)));

            this.RuleFor(_ => _.EmployeeCpfRefundClaimed)
                .GreaterThanOrEqualTo(0)
                .LessThanOrEqualTo(999_999_999)
                .WithMessage(string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidAmount), this.localizer.GetString(SystemAreas.YearEndReporting.Keys.EmployeeCpfRefundClaimed)));

            this.RuleFor(_ => _.EmployerRefundInterest)
                .GreaterThanOrEqualTo(0)
                .LessThanOrEqualTo(999_999_999)
                .WithMessage(string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidAmount), this.localizer.GetString(SystemAreas.YearEndReporting.Keys.EmployerRefundInterest)));

            this.RuleFor(_ => _.EmployeeRefundInterest)
                .GreaterThanOrEqualTo(0)
                .LessThanOrEqualTo(999_999_999)
                .WithMessage(string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.InvalidAmount), this.localizer.GetString(SystemAreas.YearEndReporting.Keys.EmployeeRefundInterest)));
        }

        private async Task<bool> IsWithinSelectedBasisYearAsync(EmployeeIR8A model, DateTime date, CancellationToken token)
        {
            var taxCountryId = await this.employeeService.GetTaxCountryIdAsync(model.EmployeeId);
            var taxYear = await this.countryTaxYearService.GetCountryTaxYearAsync(model.TaxYearId, taxCountryId);

            return date >= taxYear.YearStartDate && date <= taxYear.YearEndDate;
        }

        private async Task<bool> IsAbleToEditOrCreateForTaxYearAsync(EmployeeIR8A model, int basisYearId, CancellationToken token)
        {
            var taxCountryId = await this.employeeService.GetTaxCountryIdAsync(model.EmployeeId);
            return await this.employeeIR8AService.CanEditOrAddRecordForTaxYearAsync(taxCountryId, basisYearId);
        }

        private async Task<bool> HasNotReachedMaxRecordsAsync(EmployeeIR8A model, long employeeId, ValidationContext<EmployeeIR8A> context, CancellationToken token)
        {
            var isMaxRecords = await this.employeeIR8AService.IsMaxRecordCountForTaxYearAsync(employeeId, model.TaxYearId);

            if (isMaxRecords)
            {
                var taxCountryId = await this.employeeService.GetTaxCountryIdAsync(employeeId);
                var taxYear = await this.countryTaxYearService.GetCountryTaxYearAsync(model.TaxYearId, taxCountryId);
                context.MessageFormatter.AppendArgument(
                    "ValidationMessage",
                    string.Format(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.TenRecordsPerTaxYearError), this.employeeService.GetEmployeeNumber(employeeId), taxYear.YearStartDate.Year));
            }

            return !isMaxRecords;
        }

        private bool OverseasPostingReasonRequired(EmployeeIR8A model, ValidationContext<EmployeeIR8A> context)
        {
            var options = this.enumService.GetRemissionExemptIncomeReasons();
            return this.ValidateLookupDependencyRequirement(
                context,
                options,
                _ => _.ExemptIncomeReasonCode,
                _ => _.ExemptIncomeReasonDescription,
                model.OverseasPostingReasonId ?? 0,
                OverseasPostingRequiredCodes,
                SystemAreas.YearEndReporting.Keys.OverseasPostingReason,
                SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeReason,
                _ => _.ExemptIncomeReasonId == model.RemissionExemptIncomeReasonId
            );
        }

        private bool EmployerIncomeTaxAmountRequired(EmployeeIR8A model, ValidationContext<EmployeeIR8A> context)
        {
            var options = this.enumService.GetIncomeTaxOptions();
            return this.ValidateLookupDependencyRequirement(
                context,
                options,
                _ => _.TaxOptionCode,
                _ => _.TaxOptionDescription,
                model.EmployerIncomeTaxAmount,
                EmployerIncomeTaxAmountRequiredCodes,
                SystemAreas.YearEndReporting.Keys.EmployerIncomeTaxAmount,
                SystemAreas.YearEndReporting.Keys.IncomeTaxOption,
                _ => _.TaxOptionId == model.IncomeTaxOptionId
            );
        }

        private bool EmployeeLiabilityAmountRequired(EmployeeIR8A model, ValidationContext<EmployeeIR8A> context)
        {
            var options = this.enumService.GetIncomeTaxOptions();
            return this.ValidateLookupDependencyRequirement(
                context,
                options,
                _ => _.TaxOptionCode,
                _ => _.TaxOptionDescription,
                model.EmployeeLiabilityAmount,
                EmployeeLiabilityAmountRequiredCodes,
                SystemAreas.YearEndReporting.Keys.EmployeeLiabilityAmount,
                SystemAreas.YearEndReporting.Keys.IncomeTaxOption,
                _ => _.TaxOptionId == model.IncomeTaxOptionId
            );
        }

        private bool RemissionExemptIncomeAmountRequired(EmployeeIR8A model, ValidationContext<EmployeeIR8A> context)
        {
            var options = this.enumService.GetRemissionExemptIncomeReasons();
            return this.ValidateLookupDependencyRequirement(
                context,
                options,
                _ => _.ExemptIncomeReasonCode,
                _ => _.ExemptIncomeReasonDescription,
                model.RemissionExemptIncomeAmount,
                RemissionExemptIncomeRequiredCodes,
                SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeAmount,
                SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeReason,
                _ => _.ExemptIncomeReasonId == model.RemissionExemptIncomeReasonId
            );
        }

        private bool ValidateLookupDependencyRequirement<T>(
            ValidationContext<EmployeeIR8A> context,
            IEnumerable<T> options,
            Func<T, string> getCode,
            Func<T, string> getDescription,
            decimal amountValue,
            string[] requiredCodes,
            string fieldKey,
            string lookupKey,
            Func<T, bool> matchFunc)
        {
            var isRequired = options.Any(_ => matchFunc(_) && requiredCodes.Contains(getCode(_)));

            if (!isRequired && amountValue > 0)
            {
                var matchedOption = options.SingleOrDefault(_ => matchFunc(_));
                context.MessageFormatter.AppendArgument(
                    "ValidationMessage",
                    string.Format(
                        this.localizer.GetString(SystemAreas.YearEndReporting.Keys.DependentLookupFieldError),
                        this.localizer.GetString(fieldKey),
                        this.localizer.GetString(lookupKey),
                        matchedOption != null ? getDescription(matchedOption) : this.localizer.GetString("lblNoSelection")));
            }

            return isRequired;
        }
    }
}
