namespace PaySpace.Venuta.Data.Models.Validation.Employees
{
    using System;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation.Company;
    using PaySpace.Venuta.Data.Models.Validation.Extensions;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Services.Organization;

    public class EmployeePayRateValidator : CompanyRunValidationEntityValidator<EmployeePayRate, EmployeePayRateCustomFieldValue>
    {
        private readonly ApplicationContext applicationContext;
        private readonly ICompanySettingService companySettingService;
        private readonly IPayRateService payRateService;
        private readonly ICompanyService companyService;
        private readonly ICompanyGroupExchangeRateService companyGroupExchangeRateService;
        private readonly ICompanyRunService companyRunService;
        private readonly IEmploymentStatusService employmentStatusService;
        private readonly ICompanyFrequencyService companyFrequencyService;
        private readonly IIncreaseReasonService increaseReasonService;
        private readonly IOrganizationCategoryService organizationCategoryService;
        private readonly IScopedCache scopedCache;
        private readonly IEmployeeService employeeService;
        private readonly ITenantProvider tenantProvider;

        public EmployeePayRateValidator(
            ApplicationContext applicationContext,
            IStringLocalizerFactory stringLocalizerFactory,
            ICompanySettingService companySettingService,
            IPayRateService payRateService,
            ICompanyService companyService,
            ICompanyGroupExchangeRateService companyGroupExchangeRateService,
            ICompanyRunService companyRunService,
            ICompanyRunEntityService companyRunEntityService,
            IEmploymentStatusService employmentStatusService,
            IEmployeeService employeeService,
            ICustomFieldService customFieldService,
            ITenantProvider tenantProvider,
            ICompanyFrequencyService companyFrequencyService,
            IIncreaseReasonService increaseReasonService,
            IOrganizationCategoryService organizationCategoryService,
            IScopedCache scopedCache,
            ICountryServiceFactory countryServiceFactory)
            : base(
                stringLocalizerFactory,
                employeeService,
                companyRunEntityService,
                customFieldService,
                tenantProvider,
                countryServiceFactory)
        {
            this.applicationContext = applicationContext;
            this.companySettingService = companySettingService;
            this.payRateService = payRateService;
            this.companyService = companyService;
            this.companyGroupExchangeRateService = companyGroupExchangeRateService;
            this.companyRunService = companyRunService;
            this.employmentStatusService = employmentStatusService;
            this.companyFrequencyService = companyFrequencyService;
            this.increaseReasonService = increaseReasonService;
            this.organizationCategoryService = organizationCategoryService;
            this.scopedCache = scopedCache;
            this.employeeService = employeeService;
            this.tenantProvider = tenantProvider;
            this.increaseReasonService = increaseReasonService;
        }

        protected override void CreateRules()
        {
            this.RuleFor(r => r)
                .MustAsync(this.HasEmploymentStatus)
                .DependentRules(() =>
                {
                    this.RuleFor(r => r.CompanyFrequencyId)
                        .NotEmpty()
                        .WhenAsync(this.IsPayRateRequiredAsync)
                        .DependentRules(this.AdditionalCreationRules)
                        .WithMessage(this.Localizer.GetString("lblCompanyFrequencyRequired"));
                })
                .WithMessage(this.Localizer.GetString("lblCannotAddPayRateWithoutExistingEmploymentStatus"));

            this.RuleFor(r => r.CompanyFrequencyId)
                .MustAsync(this.IsFrequencyActiveAsync)
                .WithMessage(this.Localizer.GetString("errFrequencyInactive"));

            this.RuleFor(_ => _.CompanyFrequencyId)
                .MustAsync(this.ValidateMismatchedFrequencyAsync)
                .WithMessage("{ValidationMessage}");
        }

        protected override void DeleteExtraRules()
        {
            this.RuleFor(r => r)
                .MustAsync(this.NotEarliestPayRate)
                .WithMessage(this.Localizer.GetString("lblDeleteFirstRecord"));
        }

        protected override void UpdateRules()
        {
            base.UpdateRules();

            this.RuleFor(r => r.EffectiveDate)
                .MustAsync((m, v, c) => this.RecordCanBeModified(m, c))
                .When(m => this.ShouldValidate(nameof(m.EffectiveDate), m))
                .WithErrorCode("ModifyEffectiveDate");
        }

        /// <inheritdoc/>
        protected override void GeneralRules()
        {
            this.RuleFor(r => r.PayFrequency)
                .MustAsync(this.ValidPayFrequency)
                .DependentRules(() =>
                {
                    this.HaveAnyWorkdaysBeenSelectedRules();

                    this.RuleFor(r => r.CurrencyId)
                        .Must(r => r.HasValue && r != default(long))
                        .When(m => this.ShouldValidate(nameof(m.CurrencyId), m))
                        .WhenAsync(this.CurrencySettingActive)
                        .WithMessage(this.Localizer.GetString("lblCurrencyRequired"));

                    this.RuleFor(r => r.Package2)
                        .Empty()
                        .When(m => this.ShouldValidate(nameof(m.Package2), m))
                        .WhenAsync(this.SecondPackageSettingNotActive)
                        .WithMessage(this.Localizer.GetString("lblSecondPackageInvalid"));

                    this.RuleFor(r => r.Package)
                        .Must(_ => _ >= default(decimal))
                        .WhenAsync(this.SecondPackageSettingNotActive)
                        .WithMessage(this.Localizer.GetString("lblPackageInvalid"));

                    this.RuleFor(r => r.ThirteenCheque)
                        .Must(NoThirteenthCheque)
                        .When(_ => _.IsAnnual != true)
                        .WithMessage(this.Localizer.GetString("lblthirteenChequeInvalid"));

                    this.RuleFor(r => r.IsAnnual)
                        .NotEqual(true)
                        .When(_ => _.PayFrequency != PayFrequency.Monthly)
                        .WithMessage(this.Localizer.GetString("lblIsAnnualInvalid"));

                    this.RuleFor(r => r.HaveAnyWorkdaysBeenSelected)
                        .Empty()
                        .When(m => this.ShouldValidate(nameof(m.HaveAnyWorkdaysBeenSelected), m))
                        .WhenAsync(this.DisallowWorkingDays)
                        .WithMessage(this.Localizer.GetString("lblWorkingDaysInvalid"));

                    this.RuleFor(r => r.CompanyFrequencyId)
                        .MustAsync((m, v, c) => this.FrequencyNotChanged(m, c))
                        .WithMessage(this.Localizer.GetString("lblCompanyFrequencyInvalid"));

                    this.RuleFor(r => r.IncreaseReasonId)
                        .Must(_ => _ >= default(long))
                        .When(m => this.ShouldValidate(nameof(m.IncreaseReasonId), m))
                        .WhenAsync(this.RequireReason)
                        .WithMessage(this.Localizer.GetString("lblWorkReasonRequired"));

                    this.RuleFor(r => r.PayFrequency)
                        .NotEmpty()
                        .WithMessage(this.Localizer.GetString("lblPayFrequencyRequired"));

                    this.RuleFor(r => r.PercentageOfPreviousPackage)
                        .Empty()
                        .UnlessAsync(this.NotEarliestPayRate)
                        .WithMessage(this.Localizer.GetString("lblPercentageIncreaseInvalid"));

                    this.RuleFor(r => r.CategoryCode)
                        .Empty()
                        .UnlessAsync(this.PayRateCategoryValidForEmployeeAsync)
                        .When(m => this.ShouldValidate(nameof(m.CategoryCode), m))
                        .DependentRules(() =>
                        {
                            this.RuleFor(r => r.HoursPerDay)
                                .NotEmpty()
                                .UnlessAsync(this.OrganizationCategoryHasDefaultsAsync)
                                .DependentRules(() =>
                                {
                                    this.RuleFor(r => r.HoursPerDay)
                                        .Must(r => r is > default(double) and <= 24)
                                        .UnlessAsync(this.OrganizationCategoryHasDefaultsAsync)
                                        .WithMessage(this.Localizer.GetString("lblHoursPerDayInvalid"));
                                })
                                .WithMessage(this.Localizer.GetString("lblHoursPerDayRequired"));

                            this.DaysPerPeriodRules();
                        })
                        .WithMessage(this.Localizer.GetString("lblCategoryInvalidForEmployee"));

                    this.RuleFor(r => r.BureauTaxabilityOptionId)
                        .NotEmpty()
                        .WhenAsync(this.IsCanadaAsync)
                        .WithMessage(this.Localizer.GetString("lblProvinceOfEmploymentRequired"));
                })
                .WithMessage(this.Localizer.GetString("lblPayFrequencyInvalid"));
        }

        protected virtual void HaveAnyWorkdaysBeenSelectedRules()
        {
            this.RuleFor(r => r.HaveAnyWorkdaysBeenSelected)
                .Must(r => r)
                .When(m => this.ShouldValidate(nameof(m.HaveAnyWorkdaysBeenSelected), m))
                .WhenAsync(this.WorkDaySelectionSettingActive)
                .WithMessage(this.Localizer.GetString("lblNoWorkDaysSelected"));
        }

        protected virtual void DaysPerPeriodRules()
        {
            this.RuleFor(r => r.DaysPerPeriod)
                .GreaterThan(default(long))
                .UnlessAsync(this.OrganizationCategoryHasDefaultsAsync)
                .DependentRules(() =>
                {
                    this.RuleFor(r => r.DaysPerPeriod)
                        .Must(r => r is > 0 and <= 7)
                        .When(r => r.PayFrequency == PayFrequency.Weekly)
                        .UnlessAsync(this.OrganizationCategoryHasDefaultsAsync)
                        .WithMessage(this.Localizer.GetString("lblDaysPerPeriodInvalid", 7));

                    this.RuleFor(r => r.DaysPerPeriod)
                        .Must(r => r is > default(double) and <= 14)
                        .When(r => r.PayFrequency == PayFrequency.Fortnight)
                        .UnlessAsync(this.OrganizationCategoryHasDefaultsAsync)
                        .WithMessage(this.Localizer.GetString("lblDaysPerPeriodInvalid", 14));

                    this.RuleFor(r => r.DaysPerPeriod)
                        .Must(r => r is > default(double) and <= 31)
                        .When(r => r.PayFrequency == PayFrequency.Monthly)
                        .UnlessAsync(this.OrganizationCategoryHasDefaultsAsync)
                        .WithMessage(this.Localizer.GetString("lblDaysPerPeriodInvalid", 31));
                })
                .WithMessage(this.Localizer.GetString("lblDaysPerPeriodRequired"));
        }

        /// <inheritdoc/>
        protected override void UpdateExtraRules()
        {
            this.EffectiveDateRules();

            base.UpdateExtraRules();
        }

        private async Task<bool> OrganizationCategoryHasDefaultsAsync(EmployeePayRate model, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(model.CategoryCode))
            {
                return false;
            }

            var companyId = this.tenantProvider.GetCompanyId().Value;
            var category = await this.scopedCache.GetOrCreateAsync(
                $"EmployeePayRateValidator:OrganizationCategoryHasDefaults:{companyId}:{model.CategoryCode}:{model.EffectiveDate.ToShortDateString()}",
                () => this.organizationCategoryService.GetCategoriesByCompanyId(companyId)
                    .OrderByDescending(_ => _.EffectiveDate)
                    .FirstOrDefaultAsync(_ => _.EffectiveDate <= model.EffectiveDate && _.Code == model.CategoryCode, cancellationToken));

            if (category == null)
            {
                return false;
            }

            // We need to make sure that the default values will still be able to pass validation
            if (category.PayRateDaysPerPeriod <= 0 || (category.PayRateHoursPerDay <= 0 || category.PayRateHoursPerDay > 24))
            {
                return false;
            }

            return model.PayFrequency switch
            {
                PayFrequency.Weekly => category.PayRateDaysPerPeriod <= 7,
                PayFrequency.Fortnight => category.PayRateDaysPerPeriod <= 14,
                _ => category.PayRateDaysPerPeriod <= 31
            };
        }

        private void AdditionalCreationRules()
        {
            this.RuleFor(r => r.EffectiveDate)
                .GreaterThan(DateTime.MinValue)
                .DependentRules(() =>
                {
                    this.EffectiveDateRules();
                    this.CreateExtraRules();
                })
                .WithMessage(this.Localizer.GetString("lblEffectiveDateRequired"));
        }

        private void EffectiveDateRules()
        {
            this.RuleFor(_ => _.EffectiveDate)
                .MustAsync(this.HaveRunsForEmploymentPeriod)
                .WhenAsync(this.EarliestPayRateAsync)
                .WithMessage(this.Localizer.GetString("lblNoRunsForEmploymentPeriod"))
                .DependentRules(() =>
                {
                    this.RuleFor(r => r.EffectiveDate)
                        .MustAsync(this.NotAfterPeriodEndDate)
                        .WhenAsync(this.EarliestPayRateAsync)
                        .WithMessage(this.Localizer.GetString("lblEffectiveDateCannotBeAfterCurrentRunCloses"));
                });

            this.RuleFor(r => r.EffectiveDate)
                .Empty()
                .WhenAsync(this.NotBeDuplicatePayRateAsync)
                .WithMessage(this.Localizer.GetString("lblEffectiveDateMustBeUnique"));

            this.RuleFor(r => r.EffectiveDate)
                .MustAsync(this.NotBeforeGroupJoinDate)
                .WithMessage(this.Localizer.GetString("lblEffectiveDateCannotBeBeforeGroupJoinDate"));

            this.RuleFor(r => r.EffectiveDate)
                .MustAsync(this.IsNotBeforeEmploymentDate)
                .WithMessage(this.Localizer.GetString("lblEffectiveDateCannotBeBeforeEmploymentDate"));
        }

        private async Task<bool> PayRateCategoryValidForEmployeeAsync(EmployeePayRate model, CancellationToken cancellationToken)
        {
            if (!string.IsNullOrWhiteSpace(model.CategoryCode))
            {
                var categories = await this.organizationCategoryService.GetOrganizationCategoriesForEmployee(this.tenantProvider.GetCompanyId().Value, model.EmployeeId, model.EffectiveDate);
                return categories.Any(_ => _.Code == model.CategoryCode);
            }

            return true;
        }

        private async Task<bool> IsPayRateRequiredAsync(EmployeePayRate model, ValidationContext<EmployeePayRate> context, CancellationToken cancellationToken)
        {
            if (model.EmployeeId == 0)
            {
                // new employee Uplaod
                var frequencyFromSheet = context.GetEmployeeFrequencyId();
                return frequencyFromSheet is null or 0;
            }

            var isFirstPayRate = await this.EarliestPayRateAsync(model, cancellationToken);
            if (!isFirstPayRate || model.CompanyFrequencyId != default)
            {
                return isFirstPayRate;
            }

            var frequencyFromEmployee = await this.companySettingService.IsActiveAsync(this.tenantProvider.GetCompanyId()!.Value, CompanySettingCode.BasicProfile.CompanyFrequency);
            return !frequencyFromEmployee && isFirstPayRate;
        }

        private Task<bool> IsFrequencyActiveAsync(EmployeePayRate payRate, long companyFrequencyId, CancellationToken cancellationToken)
        {
            return companyFrequencyId == 0 ? Task.FromResult(true) : this.companyFrequencyService.IsCompanyFrequencyActiveAsync(this.tenantProvider.GetCompanyId()!.Value, companyFrequencyId, payRate.EffectiveDate);
        }

        private Task<bool> EarliestPayRateAsync(EmployeePayRate model, CancellationToken cancellationToken)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"EarliestPayRate:{model.EmployeeId}:{model.PayRateId}",
                async () =>
                {
                    if (model.EmployeeId == 0)
                    {
                        return false; // new employee
                    }

                    return !await this.payRateService.GetPayRatesByEmployeeId(model.EmployeeId).AnyAsync(_ => _.PayRateId != model.PayRateId, cancellationToken);
                });
        }

        private static bool NoThirteenthCheque(bool? thirteenthCheque) => thirteenthCheque != true;

        private async Task<bool> NotBeDuplicatePayRateAsync(EmployeePayRate model, CancellationToken cancellationToken)
        {
            if (model.EmployeeId == 0)
            {
                return false; // new employee
            }

            return await this.payRateService.GetPayRatesByEmployeeId(model.EmployeeId)
                .AnyAsync(_ => _.PayRateId != model.PayRateId && _.EffectiveDate.Date == model.EffectiveDate.Date, cancellationToken);
        }

        private async Task<bool> WorkDaySelectionSettingActive(EmployeePayRate model, CancellationToken cancellationToken)
        {
            return await this.companySettingService.HasWorkDaySettingSelectedAsync(this.tenantProvider.GetCompanyId()!.Value);
        }

        private async Task<bool> RequireReason(EmployeePayRate model, CancellationToken cancellationToken)
        {
            return await this.companySettingService.IsActiveAsync(this.tenantProvider.GetCompanyId().Value, CompanySettingCode.PayRate.Reason) &&
                   await this.increaseReasonService.CompanyHasIncreaseReasonsAsync(this.tenantProvider.GetCompanyId().Value);
        }

        private async Task<bool> CurrencySettingActive(EmployeePayRate model, CancellationToken cancellationToken)
        {
            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(this.tenantProvider.GetCompanyId().Value);
            return await this.companySettingService.IsActiveAsync(this.tenantProvider.GetCompanyId().Value, CompanySettingCode.PayRate.Currency) &&
                await this.companyGroupExchangeRateService.CompanyGroupHasCurrenciesAsync(companyGroupId);
        }

        private async Task<bool> SecondPackageSettingNotActive(EmployeePayRate model, CancellationToken cancellationToken)
        {
            return !await this.companySettingService.IsActiveAsync(this.tenantProvider.GetCompanyId().Value, CompanySettingCode.PayRate.SecondPayRate);
        }

        private Task<bool> NotEarliestPayRate(EmployeePayRate model, CancellationToken cancellationToken)
        {
            return model.EmployeeId == 0 ? Task.FromResult(true) : // New employee
                this.payRateService.GetPayRatesByEmployeeId(model.EmployeeId).AnyAsync(_ => _.EffectiveDate < model.EffectiveDate, cancellationToken);
        }

        private async Task<bool> FrequencyNotChanged(EmployeePayRate model, CancellationToken cancellationToken)
        {
            if (model.EmployeeId == 0)
            {
                return true; // New employee, there is no position
            }

            var frequencyId = await this.employeeService.GetEmployeeFrequencyIdAsync(model.EmployeeId);
            return (!frequencyId.HasValue && model.CompanyFrequencyId != default) || (frequencyId.HasValue && model.CompanyFrequencyId == default) || frequencyId == model.CompanyFrequencyId;
        }

        private async Task<bool> DisallowWorkingDays(EmployeePayRate model, CancellationToken cancellationToken)
        {
            return !await this.companySettingService.HasWorkDaySettingSelectedAsync(this.tenantProvider.GetCompanyId()!.Value);
        }

        private Task<bool> HasEmploymentStatus(EmployeePayRate model, CancellationToken cancellationToken)
        {
            return model.EmployeeId == 0 ? Task.FromResult(true) : // New employee upload, there will be a EmploymentStatus
                this.employmentStatusService.GetEmploymentStatusesByEmployeeId(model.EmployeeId).AnyAsync(cancellationToken);
        }

        private async Task<bool> IsNotBeforeEmploymentDate(EmployeePayRate model, DateTime effectiveDate, ValidationContext<EmployeePayRate> context, CancellationToken cancellationToken)
        {
            var employmentDate = model.EmployeeId == 0
                ? context.GetEmploymentStatusEmploymentDate()
                : await this.employmentStatusService.GetEmploymentDateAsync(model.EmployeeId);

            if (employmentDate == null)
            {
                return false;
            }

            return employmentDate <= model.EffectiveDate;
        }

        private async Task<bool> NotBeforeGroupJoinDate(EmployeePayRate model, DateTime effectiveDate, ValidationContext<EmployeePayRate> context, CancellationToken cancellationToken)
        {
            var groupJoinDate = model.EmployeeId == 0
                ? context.GetEmploymentStatusGroupJoinDate()
                : await this.employmentStatusService.GetGroupJoinDateAsync(model.EmployeeId);

            if (groupJoinDate == null)
            {
                return false;
            }

            return groupJoinDate.Value.Date <= model.EffectiveDate.Date;
        }

        private async Task<bool> NotAfterPeriodEndDate(EmployeePayRate model, DateTime effectiveDate, ValidationContext<EmployeePayRate> context, CancellationToken cancellationToken)
        {
            var employmentDate = model.EmployeeId == 0
                ? context.GetEmploymentStatusEmploymentDate()
                : await this.employmentStatusService.GetEmploymentDateAsync(model.EmployeeId);

            if (employmentDate == null)
            {
                return false;
            }

            var frequencyId = model.CompanyFrequencyId != default
                ? model.CompanyFrequencyId
                : model.EmployeeId == 0 ? context.GetEmployeeFrequencyId() : await this.employeeService.GetEmployeeFrequencyIdAsync(model.EmployeeId);

            if (await this.CurrentRunAfterEmploymentDateAsync(frequencyId!.Value, employmentDate!.Value))
            {
                // Disregard this check if creating a record in a closed run
                return true;
            }

            return await this.companyRunService.GetCompanyRuns(frequencyId.Value, employmentDate.Value)
                .AnyAsync(_ => _.PeriodEndDate >= model.EffectiveDate, cancellationToken);
        }

        private Task<bool> CurrentRunAfterEmploymentDateAsync(long frequencyId, DateTime employmentDate)
        {
            return this.scopedCache.GetOrCreateAsync($"EmployeePayRateValidator:Frequency:{frequencyId}:EffectiveDate:{employmentDate}",
                async () =>
                {
                    var currentRunId = await this.GetCurrentRunIdAsync(frequencyId);
                    if (currentRunId == default)
                    {
                        return false;
                    }

                    var currentRun = await this.companyRunService.GetRunPeriodAsync(currentRunId);
                    return currentRun?.PeriodStartDate > employmentDate;
                });
        }

        private Task<long> GetCurrentRunIdAsync(long companyFrequencyId)
        {
            return this.companyRunService.GetCompanyRuns(companyFrequencyId, RunStatus.Open)
                            .OrderByDescending(_ => _.PeriodEndDate)
                                .ThenBy(_ => _.OrderNumber)
                            .Select(_ => _.RunId)
                            .FirstOrDefaultAsync();
        }

        private async Task<bool> ValidPayFrequency(EmployeePayRate model, PayFrequency payFrequency, ValidationContext<EmployeePayRate> context, CancellationToken cancellationToken)
        {
            var frequencyId = model.CompanyFrequencyId != default
                ? model.CompanyFrequencyId
                : model.EmployeeId == 0 ? context.GetEmployeeFrequencyId() : await this.employeeService.GetEmployeeFrequencyIdAsync(model.EmployeeId);

            var runFrequency = await this.companyFrequencyService.GetRunFrequencyAsync(frequencyId.Value);

            return model.PayFrequency.ToString() == Enum.GetName(runFrequency) ||
                model.PayFrequency == PayFrequency.Daily ||
                model.PayFrequency == PayFrequency.Hourly;
        }

        private async Task<bool> ValidateMismatchedFrequencyAsync(EmployeePayRate model, long companyFrequencyId, ValidationContext<EmployeePayRate> context, CancellationToken cancellationToken)
        {
            var employeeFrequencyId = await this.employeeService.GetEmployeeFrequencyIdAsync(model.EmployeeId);
            if (employeeFrequencyId != null && companyFrequencyId > 0 && employeeFrequencyId != companyFrequencyId)
            {
                var frequencyName = await this.companyFrequencyService.GetNameAsync(employeeFrequencyId.Value);
                context.MessageFormatter.AppendArgument("ValidationMessage", this.Localizer.GetString("errFrequencyNameMismatch", frequencyName));

                return false;
            }

            return true;
        }

        private bool ShouldValidate(string propertyName, EmployeePayRate entity)
        {
            // Special case for HaveAnyWorkdaysBeenSelected - check if any working day properties are modified
            if (propertyName == "HaveAnyWorkdaysBeenSelected")
            {
                var workingDayProperties = typeof(EmployeePayRate)
                    .GetProperties()
                    .Where(prop => prop.Name.Contains("WorkingDay"))
                    .Select(prop => prop.Name);
                return workingDayProperties.Any(prop => this.applicationContext.IsFieldModified(entity, prop));
            }

            // For other properties, check if the specific property is modified
            // IsFieldModified handles the logic for create vs update scenarios internally
            return this.applicationContext.IsFieldModified(entity, propertyName);
        }

        private async Task<bool> HaveRunsForEmploymentPeriod(EmployeePayRate model, DateTime effectiveDate, ValidationContext<EmployeePayRate> context, CancellationToken cancellationToken)
        {
            var employmentDate = model.EmployeeId == default
                ? context.GetEmploymentStatusEmploymentDate()
                : await this.employmentStatusService.GetEmploymentDateAsync(model.EmployeeId);

            if (employmentDate == null)
            {
                return false;
            }

            var frequencyId = model.CompanyFrequencyId != default
                ? model.CompanyFrequencyId
                : model.EmployeeId == 0
                    ? context.GetEmployeeFrequencyId()
                    : await this.employeeService.GetEmployeeFrequencyIdAsync(model.EmployeeId);

            if (await this.CurrentRunAfterEmploymentDateAsync(frequencyId!.Value, employmentDate!.Value))
            {
                // Disregard this check if creating a record in a closed run
                return true;
            }

            return await this.companyRunService.GetCompanyRuns(frequencyId.Value, employmentDate).AnyAsync(cancellationToken);
        }

        private async Task<bool> IsCanadaAsync(EmployeePayRate model, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId();

            if (!companyId.HasValue)
            {
                return false;
            }

            return await this.companyService.GetTaxCountryIdAsync(companyId.Value) == (int)TaxCountry.Canada;
        }
    }
}