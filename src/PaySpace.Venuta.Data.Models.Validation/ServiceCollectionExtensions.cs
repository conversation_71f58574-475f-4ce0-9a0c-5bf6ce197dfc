namespace PaySpace.Venuta.Data.Models.Validation
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using FluentValidation;

    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Tax;
    using PaySpace.Venuta.Data.Models.Validation.Company;
    using PaySpace.Venuta.Data.Models.Validation.CustomFields;
    using PaySpace.Venuta.Data.Models.Validation.Employees;
    using PaySpace.Venuta.Data.Models.Validation.Employees.CA;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions.Models;
    using PaySpace.Venuta.Validation.Annotations.Abstractions;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddValidators(this IServiceCollection services)
        {
            services.AddCountryServices(typeof(ICustomFieldListValidator<>), ServiceLifetime.Transient);
            services.AddCountryServices(typeof(ICustomFormFieldValidator<>), ServiceLifetime.Transient);
            services.AddCountryServices<ICustomFieldListValidator<TableBuilderCustomFieldValue>>(ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<TableBuilder>>(typeof(CompanyTableBuilderValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeeCustomForm>>(typeof(EmployeeCustomFormValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeeDependant>>(typeof(EmployeeDependantValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeeBankDetail>>(typeof(EmployeeBankDetailValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeeBankHeader>>(typeof(EmployeeBankHeaderValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<Employee>>(typeof(EmployeeValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeeEmploymentStatus>>(typeof(EmploymentStatusValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeePosition>>(typeof(EmployeePositionValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeeSuspension>>(typeof(EmployeeSuspensionValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<Models.Company.Company>>(typeof(CompanyValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<Models.Company.Company>>(typeof(AddressValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeeLumpSumResult>>(typeof(EmployeeLumpSumResultValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<CompanyPublicHoliday>>(typeof(CompanyPublicHolidayValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<BulkCaptureCode>>(typeof(BulkCaptureCodeValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeePayRate>>(typeof(EmployeePayRateValidator).Assembly, ServiceLifetime.Transient);

            services.AddCountryServices<IValidator<EmployeeTakeOn>>(typeof(EmployeeTakeOnValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<EmployeeLumpSumResult>>(typeof(EmployeeLumpSumResultValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<ManualIrp5Number>>(typeof(CanadaManualIrp5NumberValidator).Assembly, ServiceLifetime.Transient);
            services.AddCountryServices<IValidator<CompanyRun>>(typeof(CompanyRunValidator).Assembly, ServiceLifetime.Transient);

            services.AddScoped<ICustomFieldValidatorHelper, CustomFieldValidatorHelper>();
            services.AddSingleton<IRequiredValidationService, RequiredValidationService>();

            foreach (var type in GetValidatorTypes())
            {
                foreach (var interfaceType in type.GetInterfaces())
                {
                    services.TryAddTransient(interfaceType, type);
                }
            }

            return services;
        }

        private static IEnumerable<Type> GetValidatorTypes()
        {
            return typeof(ServiceCollectionExtensions).Assembly.GetTypes()
                .Where(t => !t.IsAbstract && t.IsPublic && typeof(IValidator).IsAssignableFrom(t) && t.ContainsGenericParameters == false)

                // these should not be registered
                .Where(t => !typeof(OrganizationGroupAddressValidator).IsAssignableFrom(t))
                .Where(t => !typeof(CompanyTableBuilderValidator).IsAssignableFrom(t))
                .Where(t => !typeof(EmployeeCustomFormValidator).IsAssignableFrom(t));
        }
    }
}