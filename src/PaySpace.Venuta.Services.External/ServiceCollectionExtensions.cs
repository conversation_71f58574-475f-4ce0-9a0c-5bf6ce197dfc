namespace PaySpace.Venuta.Services.External
{
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;
    using PaySpace.Calculation.Cache.WebApi.Client;
    using PaySpace.Calculation.Cache.WebApi.Client.Interfaces;
    using PaySpace.Calculation.Common.Http;
    using PaySpace.Calculation.WebApi.Client;
    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.RunGeneration.WebApi.Client;
    using PaySpace.RunGeneration.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Abstractions;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddExternalServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.TryAddSingleton<ICalculationWebApiClient, DefaultCalculationWebApiClient>();
            services.TryAddSingleton<ICalculationCacheWebApiClient, DefaultCalculationCacheWebApiClient>();
            services.TryAddSingleton<IRunGenerationApiClient, DefaultRunGenerationApiClient>();

            // Config.
            services.Configure<ClientSettings>(configuration.GetSection("ClientSettings"));
            services.Configure<SsrsSettings>(configuration.GetSection("SsrsSettings"));
            services.Configure<BankAccountValidatorClientSettings>(configuration.GetSection("BankAccountValidatorClientSettings"));
            services.Configure<ZohoSettings>(configuration.GetSection("ZohoAuthentication"));
            services.Configure<CognativeServicesSettings>(configuration.GetSection("CognativeServicesSettings"));

            // Clients.
            services.AddSingleton<IEmailClient, EmailClient>();
            services.AddSingleton<IEmailService, EmailService>();
            services.AddSingleton<IZohoService, ZohoService>();
            services.AddSingleton<IWordConversionService, WordConversionService>();

            services.AddTransient<ISsrsClient, SsrsClient>();
            services.AddTransient<IBankAccountValidatorClient, BankAccountValidatorClient>();

            return services;
        }

        public static void AddCalculationServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton(new CalculationWebApiClient(
                                      new HttpClientService(configuration["ClientSettings:CalcWebApiBaseUrl"]),
                                      new ScheduledRecalcSqlService(configuration.GetConnectionString("DefaultConnection")),
                                      Sources.Base.Venuta));

            services.AddSingleton(new CalculationCacheWebApiClient(
                new HttpClientService(configuration["ClientSettings:CalcCacheWebApiBaseUrl"])));

            services.AddSingleton<ICalculationWebApiClient>(sp => sp.GetRequiredService<CalculationWebApiClient>());
            services.AddSingleton<ICalculationCacheWebApiClient>(sp => sp.GetRequiredService<CalculationCacheWebApiClient>());

            services.AddSingleton<IRunGenerationApiClient>(new RunGenerationApiClient(new HttpClientService(configuration["ClientSettings:RunGenerationWebApiBaseUrl"])));
        }
    }
}