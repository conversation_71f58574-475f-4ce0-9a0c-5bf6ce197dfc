namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Company
{
    using AutoMapper;
    using AutoMapper.EquivalencyExpression;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;
    using PaySpace.Venuta.Data.Models.Enums;

    public class CompanyLeaveServiceLengthProfile : Profile
    {
        public CompanyLeaveServiceLengthProfile()
        {
            this.CreateMap<CompanyLeaveServiceLength, CompanyLeaveServiceLengthDto>()
                .ForMember(dest => dest.AccrualPeriod, opt => opt.MapFrom(_ => (LeaveAccrualPeriod)_.AccrualPeriodId))
                .ForMember(dest => dest.LeaveAccrualValue, opt => opt.MapFrom(_ => (LeaveAccrualValue)_.LeaveAccrualValueId));

            this.CreateMap<CompanyLeaveServiceLengthDto, CompanyLeaveServiceLength>()
                .ForMember(dest => dest.CompanyId, opt => opt.MapFrom<CompanyIdConverter>())
                .ForMember(dest => dest.AccrualPeriodId, opt => opt.MapFrom(_ => (int)_.AccrualPeriod))
                .ForMember(dest => dest.LeaveAccrualValueId, opt => opt.MapFrom(_ => (int)_.LeaveAccrualValue))
                .ForMember(dest => dest.CompanyLeaveDetail, opt => opt.Ignore())
                .ForMember(dest => dest.LeaveAccrualValue, opt => opt.Ignore())
                .ForMember(dest => dest.AccrualPeriod, opt => opt.Ignore())
                .EqualityComparison((src, dest) => src.ServiceLengthId == dest.ServiceLengthId);
        }
    }
}