namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Company
{
    using AutoMapper;
    using AutoMapper.EquivalencyExpression;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Dto.Mappers.Converters.Proxies;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;
    using PaySpace.Venuta.Data.Models.Enums;

    public class CompanyLeaveSetupProfile : Profile
    {
        public CompanyLeaveSetupProfile()
        {
            this.CreateMap<CompanyLeaveSetupDto, CompanyLeaveDetail>()
                .ForMember(dest => dest.CompanyId, opt => opt.MapFrom<CompanyIdConverter>())
                .ForMember(dest => dest.DropOffMonthId, opt => opt.MapFrom<MonthOfYearNormalizerConverterProxy<CompanyLeaveSetupDto, CompanyLeaveDetail>, string>(_ => _.DropOffMonth))
                .ForMember(dest => dest.AccrualOptionId, opt => opt.MapFrom(_ => (int)_.AccrualOption))
                .ForMember(dest => dest.AccrualPeriodId, opt => opt.MapFrom(_ => (int)_.AccrualPeriod))
                .ForMember(dest => dest.EncashComponentId, opt => opt.LookupFrom(_ => _.EncashComponentId))
                .ForMember(dest => dest.CompanyLeaveSchemeId, opt => opt.LookupFrom(_ => _.CompanyLeaveScheme))
                .ForMember(dest => dest.LeaveAccrualValueId, opt => opt.MapFrom(_ => (int?)_.LeaveAccrualValue))
                .ForMember(dest => dest.LiabilityComponentId, opt => opt.LookupFrom(_ => _.LiabilityComponentId))
                .ForMember(dest => dest.LeaveForfeitPeriodId, opt => opt.MapFrom(_ => (int?)_.LeaveForfeitPeriod))
                .ForMember(dest => dest.ForfeitCompanyLeaveSetupId, opt => opt.LookupFrom(_ => _.ForfeitCompanyLeaveSetupId))
                .ForMember(dest => dest.DropOffMonth, opt => opt.Ignore())
                .ForMember(dest => dest.AccrualOption, opt => opt.Ignore())
                .ForMember(dest => dest.AccrualPeriod, opt => opt.Ignore())
                .ForMember(dest => dest.LeaveAccrualValue, opt => opt.Ignore())
                .ForMember(dest => dest.LeaveForfeitPeriod, opt => opt.Ignore());

            this.CreateMap<CompanyLeaveSetupDto, CompanyLeaveSetup>()
                .ForMember(dest => dest.CompanyId, opt => opt.MapFrom<CompanyIdConverter>())
                .ForMember(dest => dest.LeaveType, opt => opt.MapFrom(_ => _.LeaveType))
                .ForMember(dest => dest.OrderNumber, opt => opt.MapFrom(_ => _.OrderNumber))
                .ForMember(dest => dest.LeaveDescription, opt => opt.MapFrom(_ => _.LeaveDescription))
                .ForMember(dest => dest.CompanyLeaveSchemeId, opt => opt.LookupFrom(_ => _.CompanyLeaveScheme))
                .EqualityComparison((src, dest) => src.CompanyLeaveSetupId == dest.CompanyLeaveSetupId)
                .ForAllOtherMembers(_ => _.Ignore());

            this.CreateMap<CompanyLeaveDetail, CompanyLeaveSetupDto>()
                .ForMember(dest => dest.LeaveType, opt => opt.MapFrom(_ => _.CompanyLeaveSetup.LeaveType))
                .ForMember(dest => dest.OrderNumber, opt => opt.MapFrom(_ => _.CompanyLeaveSetup.OrderNumber))
                .ForMember(dest => dest.LeaveDescription, opt => opt.MapFrom(_ => _.CompanyLeaveSetup.LeaveDescription))
                .ForMember(dest => dest.CompanyLeaveSchemeId, opt => opt.MapFrom(_ => _.CompanyLeaveSetup.CompanyLeaveSchemeId))
                .ForMember(dest => dest.CompanyLeaveScheme, opt => opt.MapFrom(_ => _.CompanyLeaveSetup.CompanyLeaveScheme.SchemeName))

                // Enums / Custom Lookups
                .ForMember(dest => dest.DropOffMonth, opt => opt.MapFrom(_ => _.DropOffMonthId))
                .ForMember(dest => dest.EncashComponentId, opt => opt.MapFrom(_ => _.EncashComponentId))
                .ForMember(dest => dest.LiabilityComponentId, opt => opt.MapFrom(_ => _.LiabilityComponentId))
                .ForMember(dest => dest.AccrualPeriod, opt => opt.MapFrom(_ => (LeaveAccrualPeriod)_.AccrualPeriodId))
                .ForMember(dest => dest.AccrualOption, opt => opt.MapFrom(_ => (LeaveAccrualOption)_.AccrualOptionId))
                .ForMember(dest => dest.ForfeitCompanyLeaveSetupId, opt => opt.MapFrom(_ => _.ForfeitCompanyLeaveSetupId))
                .ForMember(dest => dest.CompanyLeaveServiceLengths, opt => opt.MapFrom(_ => _.CompanyLeaveServiceLengths))
                .ForMember(dest => dest.LeaveAccrualValue, opt => opt.MapFrom(_ => (LeaveAccrualValue?)_.LeaveAccrualValueId))
                .ForMember(dest => dest.LeaveForfeitPeriod, opt => opt.MapFrom(_ => (LeaveForfeitPeriod?)_.LeaveForfeitPeriodId));
        }
    }
}