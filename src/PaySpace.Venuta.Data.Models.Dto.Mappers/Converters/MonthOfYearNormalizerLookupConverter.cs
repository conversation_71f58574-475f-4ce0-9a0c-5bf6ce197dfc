namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Converters
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.Mappers;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;

    public class MonthOfYearNormalizerLookupConverter<TSource, TDestination> : IMemberValueResolver<TSource, TDestination, string, int?>
    {
        private readonly LookupValueConverter<TSource, TDestination, int?> valueConverter;

        public MonthOfYearNormalizerLookupConverter(LookupValueConverter<TSource, TDestination, int?> valueConverter)
        {
            this.valueConverter = valueConverter;
        }

        public int? Resolve(TSource source, TDestination destination, string sourceMember, int? destMember, ResolutionContext context)
        {
            if (string.IsNullOrWhiteSpace(sourceMember))
            {
                return null;
            }

            // Normalize the month string by removing leading zeros
            var normalizedValue = NormalizeMonthString(sourceMember);
            if (normalizedValue != null)
            {
                // Delegate to the LookupValueConverter with the normalized value
                return this.valueConverter.Resolve(
                    source,
                    normalizedValue,
                    "DropOffMonth",
                    "MonthsOfYear",
                    context.GetRunId(),
                    context.GetVersion(),
                    context
                );
            }

            return null;
        }

        private static string NormalizeMonthString(string monthValue)
        {
            if (string.IsNullOrWhiteSpace(monthValue))
            {
                return null;
            }

            // Try to parse as integer to handle both "01" and "1" formats
            if (!int.TryParse(monthValue, out var monthNumber))
            {
                return null;
            }

            // Validate month range (1-12) and return as a string without leading zeros
            return monthNumber is >= 1 and <= 12
                ? monthNumber.ToString()
                : null;
        }
    }
}
