namespace PaySpace.Venuta.Data.Models.Dto.Mapping.Converters
{
    using System;

    using AutoMapper;

    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Dto.Mappers;
    using PaySpace.Venuta.Excel.Abstractions.Exceptions;
    using PaySpace.Venuta.Excel.Abstractions.Lookup;
    using PaySpace.Venuta.Services.Abstractions;

    public class MonthStringNormalizerConverter<TSource, TDestination> : IMemberValueResolver<TSource, TDestination, string, int?>
    {
        private readonly ITenantProvider tenantProvider;
        private readonly ILookupStrategyFactory strategyFactory;
        private readonly IEmployeeService employeeService;
        private readonly IModelMetadataProvider metadataProvider;
        private readonly IScopedCache scopedCache;

        public MonthStringNormalizerConverter(
            ITenantProvider tenantProvider,
            ILookupStrategyFactory strategyFactory,
            IEmployeeService employeeService,
            IModelMetadataProvider metadataProvider,
            IScopedCache scopedCache)
        {
            this.tenantProvider = tenantProvider;
            this.strategyFactory = strategyFactory;
            this.employeeService = employeeService;
            this.metadataProvider = metadataProvider;
            this.scopedCache = scopedCache;
        }

        public int? Resolve(TSource source, TDestination destination, string sourceMember, int? destMember, ResolutionContext context)
        {
            if (string.IsNullOrWhiteSpace(sourceMember))
            {
                return null;
            }

            // Normalize the month string by removing leading zeros and converting to integer
            var normalizedValue = this.NormalizeMonthString(sourceMember);
            if (normalizedValue == null)
            {
                return null;
            }

            // Use the MonthsOfYear lookup strategy with the normalized value
            var companyId = this.tenantProvider.GetCompanyId() ?? 0;
            var frequencyId = context.GetFrequencyId() ?? this.tenantProvider.GetFrequencyId();

            frequencyId ??= source is IEmployeeNumberEntity employeeEntity
                    ? this.employeeService.GetFrequencyId(employeeEntity.EmployeeId)
                    : null;

            var strategy = this.strategyFactory.GetStrategy("MonthsOfYear", context.GetVersion());
            if (strategy is ILookupStrategy lookupStrategy)
            {
                var key = $"MonthLookup:{source.GetType()}:MonthsOfYear:{normalizedValue}:{companyId}:{frequencyId}";
                var result = this.scopedCache.GetOrCreate(key, () => lookupStrategy.GetValueId(normalizedValue, companyId, frequencyId));

                if (result != null)
                {
                    return (int)result;
                }
            }

            throw new InvalidLookupValueException($"Invalid month value: {sourceMember}");
        }

        private string NormalizeMonthString(string monthValue)
        {
            if (string.IsNullOrWhiteSpace(monthValue))
            {
                return null;
            }

            // Try to parse as integer to handle both "01" and "1" formats
            if (int.TryParse(monthValue, out int monthNumber))
            {
                // Validate month range (1-12)
                if (monthNumber >= 1 && monthNumber <= 12)
                {
                    // Return as string without leading zeros
                    return monthNumber.ToString();
                }
            }

            return null;
        }
    }
}
