namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Converters
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.Mappers;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;

    public class MonthofYearLookupConverter<TSource, TDestination> : IMemberValueResolver<TSource, TDestination, string, int?>
    {
        private readonly LookupValueConverter<TSource, TDestination, int?> valueConverter;

        public MonthofYearLookupConverter(LookupValueConverter<TSource, TDestination, int?> valueConverter)
        {
            this.valueConverter = valueConverter;
        }

        public int? Resolve(TSource source, TDestination destination, string sourceMember, int? destMember, ResolutionContext context)
        {
            if (string.IsNullOrWhiteSpace(sourceMember))
            {
                return default;
            }

            // Normalize the month string by removing leading zeros
            var normalizedValue = this.NormalizeMonthString(sourceMember);
            if (normalizedValue == null)
            {
                return default;
            }

            // Delegate to the LookupValueConverter with the normalized value
            return this.valueConverter.Resolve(source, normalizedValue, "DropOffMonth", "MonthsOfYear", context.GetRunId(), context.GetVersion(), context);
        }

        private string NormalizeMonthString(string monthValue)
        {
            if (string.IsNullOrWhiteSpace(monthValue))
            {
                return null;
            }

            // Try to parse as integer to handle both "01" and "1" formats
            if (int.TryParse(monthValue, out int monthNumber))
            {
                // Validate month range (1-12)
                if (monthNumber >= 1 && monthNumber <= 12)
                {
                    // Return as string without leading zeros
                    return monthNumber.ToString();
                }
            }

            return null;
        }
    }
}
