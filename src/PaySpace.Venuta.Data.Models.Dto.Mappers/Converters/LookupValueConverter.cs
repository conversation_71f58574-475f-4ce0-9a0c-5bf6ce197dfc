namespace PaySpace.Venuta.Data.Models.Dto.Mapping.Converters
{
    using System;
    using System.Collections.Concurrent;
    using System.Linq;
    using System.Reflection;

    using AutoMapper;

    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Dto.Mappers;
    using PaySpace.Venuta.Excel.Abstractions.Exceptions;
    using PaySpace.Venuta.Excel.Abstractions.Lookup;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Validation.Annotations;

    internal static class LookupValueCache
    {
        public static readonly ConcurrentDictionary<Type, PropertyInfo[]> PropertyCache = new();
    }

    public class LookupValueConverter<TSource, TDestination, TDestMember> : IMemberValueResolver<TSource, TDestination, string, TDestMember>
    {
        private readonly ITenantProvider tenantProvider;
        private readonly ILookupStrategyFactory strategyFactory;
        private readonly IEmployeeService employeeService;
        private readonly IModelMetadataProvider metadataProvider;
        private readonly IScopedCache scopedCache;

        public LookupValueConverter(
            ITenantProvider tenantProvider,
            ILookupStrategyFactory strategyFactory,
            IEmployeeService employeeService,
            IModelMetadataProvider metadataProvider,
            IScopedCache scopedCache)
        {
            this.tenantProvider = tenantProvider;
            this.strategyFactory = strategyFactory;
            this.employeeService = employeeService;
            this.metadataProvider = metadataProvider;
            this.scopedCache = scopedCache;
        }

        public TDestMember Resolve(TSource source, TDestination destination, string sourceMember, TDestMember destMember, ResolutionContext context)
        {
            throw new NotImplementedException();
        }

        public TDestMember Resolve(TSource source, string sourceMember, string propertyName, string lookupName, long? runId, Version version, ResolutionContext context)
        {
            var sourceMemberValue = sourceMember;
            var companyId = this.tenantProvider.GetCompanyId() ?? 0;
            var taxCountryId = context.GetItem<int?>("TaxCountryId");
            var frequencyId = context.GetFrequencyId() ?? this.tenantProvider.GetFrequencyId();

            frequencyId ??= source is IEmployeeNumberEntity employeeEntity
                    ? this.employeeService.GetFrequencyId(employeeEntity.EmployeeId)
                    : null;

            var id = this.GetId(source, propertyName, lookupName, runId, sourceMemberValue, taxCountryId, companyId, frequencyId, version)
                    ?? throw new InvalidLookupValueException(this.GetDisplayName(propertyName));
            return (TDestMember)id;
        }

        private object GetId(TSource source, string propertyName, string lookupName, long? runId, string sourceMemberValue, int? taxCountryId, long? companyId, long? frequencyId, Version version)
        {
            if (companyId is null or 0 && taxCountryId > 0)
            {
                var countryStrategy = this.strategyFactory.GetCountryStrategy(lookupName);
                if (countryStrategy != null)
                {
                    var key = $"CountryLookup:{source.GetType()}:{lookupName}:{sourceMemberValue}:{taxCountryId}:{frequencyId}:{runId}";
                    return this.scopedCache.GetOrCreate(key, () => this.GetCountryValueId(countryStrategy, sourceMemberValue, taxCountryId));
                }
            }

            var strategy = this.strategyFactory.GetStrategy(lookupName, version) ?? throw new NotImplementedException($"Unknown Lookup Strategy '{lookupName}'.");
            if (strategy is ICascadingLookupStrategy cascadingStrategy)
            {
                return this.GetCascadingValue(cascadingStrategy, source, propertyName, sourceMemberValue, companyId ?? 0, frequencyId, runId);
            }

            if (strategy is ILookupStrategy lookupStrategy)
            {
                var key = $"Lookup:{source.GetType()}:{lookupName}:{sourceMemberValue}:{companyId}:{frequencyId}:{runId}";
                return this.scopedCache.GetOrCreate(key, () => lookupStrategy.GetValueId(sourceMemberValue, companyId ?? 0, frequencyId, runId));
            }

            throw new NotImplementedException($"Unknown Lookup Strategy '{lookupName}'.");
        }

        private object GetCountryValueId(ICountryLookupStrategy countryStrategy, string sourceMemberValue, int? taxCountryId)
        {
            return countryStrategy.GetCountryValueId(sourceMemberValue, taxCountryId!.Value);
        }

        private object GetCascadingValue(ICascadingLookupStrategy strategy, TSource source, string propertyName, string sourceMemberValue, long companyId, long? frequencyId, long? runId)
        {
            var relatedProperty = this.GetRelatedProperty(propertyName);
            var relatedValue = relatedProperty?.GetValue(source) as string;

            if (string.IsNullOrEmpty(relatedValue) && !strategy.RelatedCanBeNull)
            {
                throw new InvalidLookupValueException(this.GetDisplayName(relatedProperty?.Name ?? propertyName));
            }

            var key = CacheKeys.CascadingLookup(source.GetType(), propertyName, sourceMemberValue, relatedValue, companyId, frequencyId, runId);
            return this.scopedCache.GetOrCreate(key, () => strategy.GetValueId(sourceMemberValue, relatedValue, companyId, frequencyId, runId));
        }

        private PropertyInfo GetRelatedProperty(string propertyName)
        {
            var properties = LookupValueCache.PropertyCache.GetOrAdd(typeof(TSource), key => typeof(TSource).GetProperties());

            var attribute = properties.FirstOrDefault(p => p.Name == propertyName)?.GetCustomAttribute<BulkUploadLookupAttribute>(false);
            if (attribute == null || string.IsNullOrEmpty(attribute.RelatedProperty))
            {
                throw new InvalidLookupValueException(this.GetDisplayName(propertyName));
            }

            return properties.FirstOrDefault(_ => _.Name == attribute.RelatedProperty);
        }

        private string GetDisplayName(string propertyName)
        {
            var containerMetadata = this.metadataProvider.GetMetadataForType(typeof(TSource));

            var propertyMetadata = containerMetadata.Properties[propertyName];
            if (propertyMetadata == null)
            {
                return propertyName;
            }

            return propertyMetadata.DisplayName;
        }
    }
}