namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Converters.Proxies
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.Mappers.Converters;

    public class MonthOfYearNormalizerConverterProxy<TSource, TDestination> : IMemberValueResolver<TSource, TDestination, string, int?>
    {
        private readonly MonthOfYearNormalizerLookupConverter<TSource, TDestination> valueConverter;

        public MonthOfYearNormalizerConverterProxy(MonthOfYearNormalizerLookupConverter<TSource, TDestination> valueConverter)
        {
            this.valueConverter = valueConverter;
        }

        public int? Resolve(TSource source, TDestination destination, string sourceMember, int? destMember, ResolutionContext context)
        {
            return this.valueConverter.Resolve(source, destination, sourceMember, destMember, context);
        }
    }
}