namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Employees
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;
    using PaySpace.Venuta.Data.Models.Employees;

    public class EmployeeIR8AProfile : Profile
    {
        public EmployeeIR8AProfile()
        {
            this.CreateMap<EmployeeIR8A, EmployeeIR8ADto>()
                .ForMember(dest => dest.OverseasPostingReason, opt => opt.MapFrom(src => src.OverseasPostingReason.OverseasPostingCode))
                .ForMember(dest => dest.RemissionExemptIncomeReason, opt => opt.MapFrom(src => src.RemissionExemptIncomeReason.ExemptIncomeReasonCode))
                .ForMember(dest => dest.IncomeTaxOption, opt => opt.MapFrom(src => src.IncomeTaxOption.TaxOptionCode))
                .ForMember(dest => dest.TaxYear, opt => opt.MapFrom(src => src.TaxYear.YearStartDate.Year.ToString()))
                .ForMember(dest => dest.EmployeeNumber, opt => opt.MapFrom(src => src.Employee.EmployeeNumber));

            this.CreateMap<EmployeeIR8ADto, EmployeeIR8A>()
                .ForMember(dest => dest.EmployerIncomeTaxAmount, opt => opt.MapFrom(src => src.EmployerIncomeTaxAmount ?? 0))
                .ForMember(dest => dest.EmployeeLiabilityAmount, opt => opt.MapFrom(src => src.EmployeeLiabilityAmount ?? 0))
                .ForMember(dest => dest.RemissionIncomeAmount, opt => opt.MapFrom(src => src.RemissionIncomeAmount ?? 0))
                .ForMember(dest => dest.RemissionExemptIncomeAmount, opt => opt.MapFrom(src => src.RemissionExemptIncomeAmount ?? 0))
                .ForMember(dest => dest.EmployerCpfRefundClaimed, opt => opt.MapFrom(src => src.EmployerCpfRefundClaimed ?? 0))
                .ForMember(dest => dest.EmployeeCpfRefundClaimed, opt => opt.MapFrom(src => src.EmployeeCpfRefundClaimed ?? 0))
                .ForMember(dest => dest.EmployerRefundInterest, opt => opt.MapFrom(src => src.EmployerRefundInterest ?? 0))
                .ForMember(dest => dest.EmployeeRefundInterest, opt => opt.MapFrom(src => src.EmployeeRefundInterest ?? 0))
                .ForMember(dest => dest.AmountAccruedFrom1993, opt => opt.MapFrom(src => src.AmountAccruedFrom1993 ?? 0))

                .ForMember(dest => dest.TaxYearId, opt => opt.LookupFrom(src => src.TaxYear))
                .ForMember(dest => dest.TaxYear, opt => opt.Ignore())

                .ForMember(dest => dest.RemissionExemptIncomeReasonId, opt => opt.LookupFrom(src => src.RemissionExemptIncomeReason))
                .ForMember(dest => dest.RemissionExemptIncomeReason, opt => opt.Ignore())

                .ForMember(dest => dest.IncomeTaxOptionId, opt => opt.LookupFrom(src => src.IncomeTaxOption))
                .ForMember(dest => dest.IncomeTaxOption, opt => opt.Ignore())

                .ForMember(dest => dest.OverseasPostingReasonId, opt => opt.LookupFrom(src => src.OverseasPostingReason))
                .ForMember(dest => dest.OverseasPostingReason, opt => opt.Ignore());
        }
    }
}
