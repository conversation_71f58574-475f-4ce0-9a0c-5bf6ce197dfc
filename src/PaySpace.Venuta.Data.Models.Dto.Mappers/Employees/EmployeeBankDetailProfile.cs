namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Employees
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;

    public class EmployeeBankDetailProfile : Profile
    {
        public EmployeeBankDetailProfile()
        {
            // [dto] to [entity:EmployeeBankDetail]
            this.CreateMap<EmployeeBankDetailDto, EmployeeBankDetail>()
                .ForMember(dest => dest.EmployeeBankHeaderId, opt => opt.MapFrom<EmployeeBankDetailConverter>())
                .ForMember(dest => dest.EmployeeBankHeader, opt => opt.Ignore())
                .ForMember(dest => dest.BankName, opt => opt.NullSubstitute(string.Empty))
                .ForMember(dest => dest.BankBranchNo, opt => opt.NullSubstitute(string.Empty))
                .ForMember(dest => dest.AccountType, opt => opt.MapFrom(src => src.AccountType))
                .ForMember(dest => dest.BankAccountNo, opt => opt.NullSubstitute(string.Empty))
                .ForMember(dest => dest.Reference, opt => opt.NullSubstitute(string.Empty))
                .ForMember(dest => dest.BankAccountOwner, opt => opt.MapFrom(src => src.BankAccountOwner))

                .ForMember(dest => dest.CurrencyId, opt => opt.LookupFrom(src => src.Currency))
                .ForMember(dest => dest.Currency, opt => opt.Ignore())

                .ForMember(dest => dest.ComponentCompanyId, opt => opt.LookupFrom(src => src.CompanyComponent))
                .ForMember(dest => dest.ComponentCompany, opt => opt.Ignore())

                .ForMember(dest => dest.EbdIndicatorId, opt => opt.LookupFrom(src => src.CompanyEdbIndicator))
                .ForMember(dest => dest.EbdIndicator, opt => opt.Ignore())

                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Amount == null && src.SplitType == null ? 100 : src.Amount ?? 0))
                .ForMember(dest => dest.EmployeeId, opt => opt.Ignore());

            // [dto] to [entity:EmployeeBankHeader]
            this.CreateMap<EmployeeBankDetailDto, EmployeeBankHeader>()
                .ForMember(dest => dest.PaymentMethod, opt => opt.LookupFrom(src => src.PaymentMethod))
                .ForMember(dest => dest.SplitType, opt => opt.MapFrom(src => src.SplitType == null ? BankDetailSplitType.Percentage : src.SplitType));

            // [entity:EmployeeBankDetail] to [dto]
            this.CreateMap<EmployeeBankDetail, EmployeeBankDetailDto>()
                .ForMember(dest => dest.EmployeeId, opt => opt.MapFrom(src => src.EmployeeBankHeader.EmployeeId))
                .ForMember(dest => dest.EmployeeNumber, opt => opt.MapFrom(src => src.EmployeeBankHeader.Employee.EmployeeNumber))
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.EmployeeBankHeader.Employee.FullName))
                .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => src.EmployeeBankHeader.PaymentMethod))
                .ForMember(dest => dest.SplitType, opt => opt.MapFrom(src => src.EmployeeBankHeader.SplitType))
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency.CurrencyCode))
                .ForMember(dest => dest.CompanyComponent, opt => opt.MapFrom(src => src.ComponentCompany.AliasDescription))
                .ForMember(dest => dest.CompanyEdbIndicator, opt => opt.MapFrom(src => src.EbdIndicator.EbdIndicator));

            // [entity:EmployeeBankHeader] to [dto]
            this.CreateMap<EmployeeBankHeader, EmployeeBankDetailDto>();

            // [entity:EmployeeBankHeader] to [entity:EmployeeBankHeader]
            this.CreateMap<EmployeeBankHeader, EmployeeBankHeader>()
                .ForMember(dest => dest.EmployeeBankDetails, opt => opt.Ignore());

            // [entity:EmployeeBankDetail] to [entity:EmployeeBankDetail]
            this.CreateMap<EmployeeBankDetail, EmployeeBankDetail>()
                .ForMember(_ => _.BankDetailId, opts => opts.Ignore())
                .ForMember(_ => _.EmployeeBankHeaderId, opts => opts.Ignore())
                .ForMember(_ => _.EmployeeBankHeader, opts => opts.Ignore());
        }
    }
}