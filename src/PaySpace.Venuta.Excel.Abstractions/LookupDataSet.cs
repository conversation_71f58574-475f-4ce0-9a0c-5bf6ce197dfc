namespace PaySpace.Venuta.Excel.Abstractions
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Helpers;
    using PaySpace.Venuta.Validation.Annotations;

    public class LookupDataSet
    {
        [Key]
        [MessagePack.Key(0)]
        public string Value { get; set; }

        [MessagePack.Key(1)]
        public string Description { get; set; }

        [ClientDisplay(Client.NextGen)]
        [ClientDisplay(Client.ApiAdmin)]
        [MessagePack.Key(2)]
        public virtual long Id { get; set; }

        public bool IsMatch(string value)
        {
            return value == this.ToString() || value == this.Value || value == this.Description;
        }

        public override string ToString()
        {
            return this.Value == this.Description ? this.Value : $"{this.Value} ({this.Description})";
        }

        // Gets all the columns that needs to show in the lookup sheet, 1st value is the lookup value, rest are for display only.
        public virtual string[] GetValuesForLookupSheet()
        {
            return new[] { this.ToString() };
        }
    }

    public class CascadingLookupDataSet
    {
        public LookupDataSet LookupDataSet { get; set; }

        public LookupDataSet RelatedProperty { get; set; }

        public override string ToString()
        {
            return $"{this.LookupDataSet?.Description} ({this.LookupDataSet?.Value}) - {this.RelatedProperty?.Description} ({this.RelatedProperty?.Value})";
        }
    }

    public class OrganizationGroupLookupDataSet : LookupDataSet
    {
        public string OrganizationGroupDescription { get; set; }

        public long OrganizationGroupId { get; set; }
    }

    public class OrganizationLevelLookupDataSet : LookupDataSet
    {
        public int Level { get; set; }
    }

    public class OrganizationRegionLookupDataSet : LookupDataSet
    {
        public long RegionId { get; set; }
    }

    public class CompanyComponentLookupDataSet : LookupDataSet
    {
        public long ComponentId { get; set; }
    }

    public class ExchangeRateCurrencyLookupDataSet : LookupDataSet
    {
        public long CurrencyId { get; set; }
    }

    public class OrganizationGroupUploadCodeLookupDataSet : LookupDataSet
    {
        public override string ToString()
        {
            return this.Value == this.Description ? this.Value : $"{this.Description} ({this.Value})";
        }
    }

    public class ValueOnlyLookupDataSet : LookupDataSet
    {
        public override string ToString()
        {
            return this.Value;
        }
    }

    public class EmployeeNumberLookupDataSet : ValueOnlyLookupDataSet
    {
        public override string[] GetValuesForLookupSheet()
        {
            return new[] { this.ToString(), this.Description };
        }
    }

    public class PurchasedCarOptionLookupDataSet : LookupDataSet
    {
        public int OrderNumber { get; set; }

        public string Group { get; set; }
    }

    public class DescriptionOnlyLookupDataSet : LookupDataSet
    {
        public override string ToString()
        {
            return this.Description;
        }
    }

    public class ComponentCodeLookupDataSet : DescriptionOnlyLookupDataSet
    {
        [ScaffoldColumn(false)]
        public long ComponentCompanyId { get; set; }

        public string ComponentCode { get; set; }

        public string AliasDescription { get; set; }

        public override string[] GetValuesForLookupSheet()
        {
            return new[]
            {
                string.IsNullOrEmpty(this.ComponentCode) ? this.AliasDescription : this.ComponentCode,
                this.AliasDescription
            };
        }
    }

    public class CompanyRunLookupDataSet : LookupDataSet
    {
        [DataType(DataType.Date)]
        public DateTime PeriodStartDate { get; set; }

        [DataType(DataType.Date)]
        public DateTime PeriodEndDate { get; set; }

        [DataType(DataType.Date)]
        public DateTime? PayDate { get; set; }

        public RunStatus RunStatus { get; set; }

        public int OrderNumber { get; set; }

        public bool IsMainRun { get; set; }

        public RunType RunType { get; set; }

        [DataType(DataType.Date)]
        public DateTime? CutOffDate { get; set; }

        [ClientDisplay(Client.PowerBi)]
        public long FrequencyId { get; set; }

        public long RunId { get; set; }
    }

    public class CompanyBudgetPeriodLookupDataSet : LookupDataSet
    {
        public DateTime? PeriodStartDate { get; set; }

        public DateTime? PeriodEndDate { get; set; }
    }

    public class CountryLookupDataSet : DescriptionOnlyLookupDataSet
    {
        public string ISO2DigitCode { get; set; }

        public string ISO3DigitCode { get; set; }
    }

    public class UploadCodeLookupDataSet : LookupDataSet
    {
        public string UploadCode { get; set; }
    }

    public class EnumLookupDataSet : LookupDataSet
    {
        public int EnumValue { get; set; }
    }

    public class OrganizationCategoryLookupDataSet : ValueOnlyLookupDataSet
    {
        public double? PayRateHoursPerDay { get; set; }

        public double? PayRateDaysPerPeriod { get; set; }
    }

    public class TableBuilderLookupDataSet : LookupDataSet
    {
        // TableBuilderLookupDataSet needs its own CustomFieldDetail type, there is a bug that causes the metadata to show that all LookupDataSet gets a navigation property Fields
        // TODO: check if this is still needed in v8
        public IList<LookupCustomFieldDetail> Fields { get; set; }

        public bool IsEmployeeLevelOnly { get; set; }
    }

    public class LookupCustomFieldDetail
    {
        public string Code { get; set; }

        public string Label { get; set; }

        public string Type { get; set; }

        public bool IsRequired { get; set; }

        [ScaffoldColumn(false)]
        public string EditorOptions { get; set; }

        public LookupCustomFieldEditorOption[] Options => this.GetOptions();

        private LookupCustomFieldEditorOption[] GetOptions()
        {
            return EditorOptionsHelper.GetFormattedOptions(this.EditorOptions)
                .Select(_ => new LookupCustomFieldEditorOption(_.ValueCode, _.FieldValue))
                .ToArray();
        }
    }

    public class LookupCustomFieldEditorOption
    {
        public LookupCustomFieldEditorOption(string code, string description)
        {
            this.Code = code;
            this.Description = description;
        }

        public string Code { get; set; }

        public string Description { get; set; }
    }

    public class LeaveLookupDataSet : LookupDataSet
    {
        public LeaveType LeaveType { get; set; }

        public string LeaveBucket { get; set; }

        public string CompanyLeaveScheme { get; set; }

        public bool IsEmployeeDefined { get; set; }
    }

    public class ForfeitLeaveLookupDataSet : LookupDataSet
    {
        public int OrderNumber { get; set; }

        public long CompanyLeaveSetupId { get; set; }

        public long CompanyLeaveSchemeId { get; set; }

        public LeaveType LeaveType { get; set; }
    }

    public class ProvinceLookupDataSet : LookupDataSet
    {
        public int? CountryId { get; set; }

        // The bulk upload cascading logic requires the ToString method to only return the Description.
        public override string ToString()
        {
            return this.Description;
        }
    }

    public class BureauTaxabilityOptionLookupDataSet : LookupDataSet
    {
        public int? CountryId { get; set; }
    }

    public class TableBuilderCodeDataSet : LookupDataSet
    {
        public IList<CustomFieldDefaultValue> DefaultValues { get; set; }
    }

    public class CustomFieldDefaultValue
    {
        public string Code { get; set; }

        public string Value { get; set; }
    }

    public class CompanyGradeFieldValueLookDataSet : LookupDataSet
    {
        public int? OrderNumber { get; set; }

        public string? GradeFieldValue { get; set; }
    }

    public class TaxYearLookupDataSet : LookupDataSet
    {
        [DataType(DataType.Date)]
        public DateTime StartDate { get; set; }

        [DataType(DataType.Date)]
        public DateTime EndDate { get; set; }

        // We need to display this to clients, the Id property in base is hidden
        public int TaxYearId { get; set; }
    }

    public class TaxCountryLookupDataset : LookupDataSet
    {
        public int CountryId { get; set; }
    }

    public class CompanyGroupDataset : LookupDataSet
    {
        public long CompanyGroupId { get; set; }
    }

    public class CompanyPensionFundLinkLookupDataSet : DescriptionOnlyLookupDataSet
    {
        public decimal? EmployerPercent { get; set; }

        public decimal? EmployerFixedAmount { get; set; }

        public decimal? EmployeePercent { get; set; }

        public decimal? EmployeeFixedAmount { get; set; }

        public DateTime? EffectiveDate { get; set; }
    }

    public class ShiftTypeDataset : DescriptionOnlyLookupDataSet
    {
        public decimal? MondayHours { get; set; }

        public decimal? TuesdayHours { get; set; }

        public decimal? WednesdayHours { get; set; }

        public decimal? ThursdayHours { get; set; }

        public decimal? FridayHours { get; set; }

        public decimal? SaturdayHours { get; set; }

        public decimal? SundayHours { get; set; }

        public decimal? FullDayHours { get; set; }

        public decimal? HoursPerWeek { get; set; }
    }

    public class RosterDataset : LookupDataSet
    {
        public RosterType? RosterType { get; set; }
    }

    public class OccupationalLevelFieldValueLookDataSet : LookupDataSet
    {
        public int? OrderNumber { get; set; }
    }
}