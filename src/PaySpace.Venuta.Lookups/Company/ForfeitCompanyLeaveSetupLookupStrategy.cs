namespace PaySpace.Venuta.Lookups.Company
{
    using System;
    using System.Linq;

    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Excel.Abstractions.Lookup;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models;

    // Note, this is used in the Company Leave Scheme Parameters screen / API
    public class ForfeitCompanyLeaveSetupLookupStrategy : LookupStrategy, ILookupStrategy<ForfeitLeaveLookupDataSet>
    {
        private readonly ICompanyLeaveSchemeParameterService leaveSchemeParameterService;

        public ForfeitCompanyLeaveSetupLookupStrategy(ICompanyLeaveSchemeParameterService leaveSchemeParameterService)
        {
            this.leaveSchemeParameterService = leaveSchemeParameterService;
        }

        public override string Name => "ForfeitCompanyLeaveSetup";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData(companyId)
                // Information needed to sort the allowed carried forward buckets for specific LeaveTypes in a LeaveScheme
                .Select(_ => new ForfeitLeaveLookupDataSet
                {
                    Id = _.Id,
                    Value = _.Value,
                    OrderNumber = _.Order,
                    LeaveType = _.LeaveType,
                    Description = _.Description, // Format: "(OrderNumber) - LeaveDescription"
                    CompanyLeaveSetupId = _.CompanyLeaveSetupId,
                    CompanyLeaveSchemeId = _.CompanyLeaveSchemeId
                })
                .OrderBy(_ => _.LeaveType)
                .ThenBy(_ => _.OrderNumber);
        }

        public override object? GetValueId(string value, long companyId, long? frequencyId)
        {
            var data = this.GetData(companyId);

            if (long.TryParse(value, out var companyLeaveSetupId))
            {
                return data.FirstOrDefault(_ => _.CompanyLeaveSetupId == companyLeaveSetupId)?.Id ?? 0;
            }

            var encashment = data.FirstOrDefault(_ => _.Value.Equals(value, StringComparison.InvariantCultureIgnoreCase) ||
                                                      _.Description.Equals(value, StringComparison.InvariantCultureIgnoreCase));

            return encashment?.Id ?? 0;
        }

        private IQueryable<ForfeitCompanyLeaveSetupResult> GetData(long companyId)
        {
            return this.leaveSchemeParameterService.GetForfeitCompanyLeaveSetups(companyId);
        }
    }
}