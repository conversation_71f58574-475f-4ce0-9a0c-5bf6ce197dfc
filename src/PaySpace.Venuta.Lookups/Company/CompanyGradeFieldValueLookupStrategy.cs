namespace PaySpace.Venuta.Lookups.Company
{
    using System.Linq;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Excel.Abstractions.Lookup;

    public class CompanyGradeFieldValueLookupStrategy : LookupStrategy, ILookupStrategy<CompanyGradeFieldValueLookDataSet>
    {
        private readonly ReadOnlyContext context;

        public CompanyGradeFieldValueLookupStrategy(ReadOnlyContext context)
        {
            this.context = context;
        }

        public override string Name => "CompanyGradeFieldValue";

        public override IQueryable<CompanyGradeFieldValueLookDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData(companyId);
        }

        public override object? GetValueId(string value, long companyId, long? frequencyId)
        {
            return this.GetData(companyId)
                .Where(_ => _.Description == value)
                .Select(_ => _.Id)
                .FirstOrDefault();
        }

        // Includes the GradeFieldValue for usage in Leave Scheme Parameters
        private IQueryable<CompanyGradeFieldValueLookDataSet> GetData(long companyId)
        {
            // left-joined to ensure all CompanyGradeField values are returned
            return from cgf in this.context.Set<CompanyGradeField>()
                join cgfv in this.context.Set<CompanyGradeFieldValue>()
                    on cgf.GradeFieldId equals cgfv.CompanyGradeFieldId into cgvGroup
                from cgfv in cgvGroup.DefaultIfEmpty()
                where cgf.CompanyId == companyId
                select new CompanyGradeFieldValueLookDataSet
                {
                    Id = cgf.GradeFieldId,
                    Value = cgf.Description,
                    Description = cgf.Description,
                    OrderNumber = cgf.OrderNumber,
                    GradeFieldValue = cgfv.FieldValue ?? string.Empty
                };
        }
    }
}