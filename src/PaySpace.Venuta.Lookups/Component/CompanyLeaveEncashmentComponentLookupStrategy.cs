namespace PaySpace.Venuta.Lookups.Component
{
    using System;
    using System.Linq;

    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models;

    // Note, this is used in the Company Leave Scheme Parameters screen / API
    public class CompanyLeaveEncashmentComponentLookupStrategy : LookupStrategy
    {
        private readonly ICompanyLeaveSchemeParameterService leaveSchemeParameterService;

        public override string Name => "LeaveEncashmentComponent";

        public CompanyLeaveEncashmentComponentLookupStrategy(ICompanyLeaveSchemeParameterService leaveSchemeParameterService)
        {
            this.leaveSchemeParameterService = leaveSchemeParameterService;
        }

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData(companyId)
                .Select(_ => new LookupDataSet
                {
                    Id = _.ComponentId,
                    Value = _.AliasDescription,
                    Description = _.EncashmentDescription
                })
                .OrderBy(_ => _.Id);
        }

        public override object? GetValueId(string value, long companyId, long? frequencyId)
        {
            var data = this.GetData(companyId);

            if (long.TryParse(value, out var componentCompanyId))
            {
                return data.FirstOrDefault(_ => _.ComponentId == componentCompanyId)?.ComponentId ?? 0;
            }

            var encashment = data.FirstOrDefault(_ => _.AliasDescription.Equals(value, StringComparison.InvariantCultureIgnoreCase) ||
                                                      _.EncashmentDescription.Equals(value, StringComparison.InvariantCultureIgnoreCase));

            return encashment?.ComponentId ?? 0;
        }

        private IQueryable<LeaveEncashmentComponentResult> GetData(long companyId)
        {
            return this.leaveSchemeParameterService.GetEncashmentComponents(companyId);
        }
    }
}