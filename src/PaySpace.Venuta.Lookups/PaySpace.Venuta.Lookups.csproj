<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<CodeAnalysisRuleSet>..\..\PaySpace.Venuta.ruleset</CodeAnalysisRuleSet>
	</PropertyGroup>

	<PropertyGroup>
		<EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
	</PropertyGroup>

	<ItemGroup>
    		<EmbeddedResource Include="Sql\*.sql" />
  	</ItemGroup>

	<ItemGroup>
	  <PackageReference Include="Roslynator.Formatting.Analyzers">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PaySpace.Venuta.Data.Models\PaySpace.Venuta.Data.Models.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Data\PaySpace.Venuta.Data.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Excel.Abstractions\PaySpace.Venuta.Excel.Abstractions.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions\PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Modules.CustomForms.Abstractions\PaySpace.Venuta.Modules.CustomForms.Abstractions.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Modules.EmployeeTakeOns.Abstractions\PaySpace.Venuta.Modules.EmployeeTakeOns.Abstractions.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Modules.PublicHolidays\PaySpace.Venuta.Modules.PublicHolidays.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Services\PaySpace.Venuta.Services.csproj" />
	</ItemGroup>

</Project>