namespace PaySpace.Venuta.Lookups.Enums
{
    using System;
    using System.Globalization;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    public class LeaveAccrualOptionLookupStrategy : TranslatedEnumLookupStrategy
    {
        private readonly IEnumService enumService;

        public LeaveAccrualOptionLookupStrategy(IEnumService enumService)
        {
            this.enumService = enumService;
        }

        public override string Name => "LeaveAccrualOption";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData()
                .OrderBy(_ => _.AccrualOptionDescription)
                .Select(_ => new LookupDataSet
                {
                    Value = _.AccrualOptionCode,
                    Description = _.AccrualOptionDescription,
                    Id = _.AccrualOptionId
                });
        }

        protected override object? GetEnumValueId(string value, long companyId, long? frequencyId, CultureInfo? culture)
        {
            var id = this.GetData(culture)
                .Where(_ => _.AccrualOptionDescription.Equals(value, StringComparison.InvariantCultureIgnoreCase) ||
                            _.AccrualOptionCode.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.AccrualOptionId)
                .FirstOrDefault();

            return id != default ? id : null;
        }

        private IQueryable<EnumLeaveAccrualOption> GetData(CultureInfo? culture = null)
        {
            return this.enumService.GetLeaveAccrualOptions(culture).AsQueryable();
        }
    }
}