namespace PaySpace.Venuta.Lookups.Enums
{
    using System;
    using System.Globalization;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    public class LeaveAccrualPeriodLookupStrategy : TranslatedEnumLookupStrategy
    {
        private readonly IEnumService enumService;

        public LeaveAccrualPeriodLookupStrategy(IEnumService enumService)
        {
            this.enumService = enumService;
        }

        public override string Name => "LeaveAccrualPeriod";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData()
                .OrderBy(_ => _.AccrualPeriodDescription)
                .Select(_ => new LookupDataSet
                {
                    Value = _.AccrualPeriodCode,
                    Description = _.AccrualPeriodDescription,
                    Id = _.AccrualPeriodId
                });
        }

        protected override object? GetEnumValueId(string value, long companyId, long? frequencyId, CultureInfo? culture)
        {
            var id = this.GetData(culture)
                .Where(_ => _.AccrualPeriodDescription.Equals(value, StringComparison.InvariantCultureIgnoreCase) ||
                            _.AccrualPeriodCode.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.AccrualPeriodId)
                .FirstOrDefault();

            return id != default ? id : null;
        }

        private IQueryable<EnumLeaveAccrualPeriod> GetData(CultureInfo? culture = null)
        {
            return this.enumService.GetLeaveAccrualPeriods(culture).AsQueryable();
        }
    }
}