namespace PaySpace.Venuta.Lookups.Enums
{
    using System;
    using System.Globalization;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    public class MonthsOfYearLookupStrategy : TranslatedEnumLookupStrategy
    {
        private readonly IEnumService enumService;

        public MonthsOfYearLookupStrategy(IEnumService enumService)
        {
            this.enumService = enumService;
        }

        public override string Name => "MonthsOfYear";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData()
                .Select(x => new LookupDataSet { Value = x.MonthCode, Description = x.MonthDescription, Id = x.MonthId })
                .OrderBy(x => x.Value);
        }

        protected override object? GetEnumValueId(string value, long companyId, long? frequencyId, CultureInfo? culture)
        {
            // Cater for cases where the MonthCode does not include the trailing zero
            // Normalizes single-digit input (e.g., "1" to "01") for exact string comparison
            var normalizedValue = value.Length == 1 && char.IsDigit(value[0])
                ? value.PadLeft(2, '0')
                : value;

            return this.GetData(culture)
                .Where(_ => _.MonthCode.Equals(normalizedValue, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.MonthId)
                .FirstOrDefault();
        }

        private IQueryable<EnumMonthsOfYear> GetData(CultureInfo? culture = null)
        {
            return this.enumService.GetMonthsOfYear(culture).AsQueryable();
        }
    }
}