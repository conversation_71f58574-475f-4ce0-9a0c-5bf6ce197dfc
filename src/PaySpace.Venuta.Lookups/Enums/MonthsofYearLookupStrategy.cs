namespace PaySpace.Venuta.Lookups.Enums
{
    using System;
    using System.Globalization;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    public class MonthsOfYearLookupStrategy : TranslatedEnumLookupStrategy
    {
        private readonly IEnumService enumService;

        public MonthsOfYearLookupStrategy(IEnumService enumService)
        {
            this.enumService = enumService;
        }

        public override string Name => "MonthsOfYear";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData()
                .Select(x => new LookupDataSet { Value = x.MonthCode, Description = x.MonthDescription, Id = x.MonthId })
                .OrderBy(x => x.Value);
        }

        protected override object? GetEnumValueId(string value, long companyId, long? frequencyId, CultureInfo? culture)
        {
            if (int.TryParse(value, out var monthNumber) && monthNumber >= 1 && monthNumber <= 12)
            {
                return this.GetData(culture)
                    .Where(_ => _.MonthCode.Equals(monthNumber.ToString(), StringComparison.InvariantCultureIgnoreCase))
                    .Select(_ => _.MonthId)
                    .FirstOrDefault();
            }

            return this.GetData(culture)
                .Where(_ => _.MonthCode.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.MonthId)
                .FirstOrDefault();
        }

        private IQueryable<EnumMonthsOfYear> GetData(CultureInfo? culture = null)
        {
            return this.enumService.GetMonthsOfYear(culture).AsQueryable();
        }
    }
}