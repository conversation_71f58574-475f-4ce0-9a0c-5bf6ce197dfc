namespace PaySpace.Venuta.Lookups.Enums
{
    using System;
    using System.Globalization;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    public class MonthsOfYearLookupStrategy : TranslatedEnumLookupStrategy
    {
        private readonly IEnumService enumService;

        public MonthsOfYearLookupStrategy(IEnumService enumService)
        {
            this.enumService = enumService;
        }

        public override string Name => "MonthsOfYear";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData()
                .Select(x => new LookupDataSet { Value = x.MonthCode, Description = x.MonthDescription, Id = x.MonthId })
                .OrderBy(x => x.Value);
        }

        protected override object? GetEnumValueId(string value, long companyId, long? frequencyId, CultureInfo? culture)
        {
            if (string.IsNullOrEmpty(value))
                return null;

            var data = this.GetData(culture);

            // First try exact match
            var exactMatch = data
                .Where(_ => _.MonthCode.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.MonthId)
                .FirstOrDefault();

            if (exactMatch != 0)
                return exactMatch;

            // If no exact match and value can be parsed as integer, try matching by month number
            if (int.TryParse(value, out int monthNumber) && monthNumber >= 1 && monthNumber <= 12)
            {
                // Try to find by MonthId (assuming MonthId corresponds to month number)
                var monthMatch = data
                    .Where(_ => _.MonthId == monthNumber)
                    .Select(_ => _.MonthId)
                    .FirstOrDefault();

                if (monthMatch != 0)
                    return monthMatch;

                // Also try matching MonthCode as string representation of the number
                var monthCodeMatch = data
                    .Where(_ => _.MonthCode.Equals(monthNumber.ToString(), StringComparison.InvariantCultureIgnoreCase))
                    .Select(_ => _.MonthId)
                    .FirstOrDefault();

                if (monthCodeMatch != 0)
                    return monthCodeMatch;
            }

            return null;
        }

        private IQueryable<EnumMonthsOfYear> GetData(CultureInfo? culture = null)
        {
            return this.enumService.GetMonthsOfYear(culture).AsQueryable();
        }
    }
}