namespace PaySpace.Venuta.Lookups.Enums
{
    using System;
    using System.Globalization;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    public class LeaveForfeitPeriodLookupStrategy : TranslatedEnumLookupStrategy
    {
        private readonly IEnumService enumService;

        public LeaveForfeitPeriodLookupStrategy(IEnumService enumService)
        {
            this.enumService = enumService;
        }

        public override string Name => "LeaveForfeitPeriod";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData()
                .OrderBy(_ => _.ForfeitPeriodDescription)
                .Select(_ => new LookupDataSet
                {
                    Value = _.ForfeitPeriodCode,
                    Description = _.ForfeitPeriodDescription,
                    Id = _.ForfeitPeriodId
                });
        }

        protected override object? GetEnumValueId(string value, long companyId, long? frequencyId, CultureInfo? culture)
        {
            var id = this.GetData(culture)
                .Where(_ => _.ForfeitPeriodDescription.Equals(value, StringComparison.InvariantCultureIgnoreCase) ||
                            _.ForfeitPeriodCode.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.ForfeitPeriodId)
                .FirstOrDefault();

            return id != default ? id : null;
        }

        private IQueryable<EnumLeaveForfeitPeriod> GetData(CultureInfo? culture = null)
        {
            return this.enumService.GetLeaveForfeitPeriods(culture).AsQueryable();
        }
    }
}