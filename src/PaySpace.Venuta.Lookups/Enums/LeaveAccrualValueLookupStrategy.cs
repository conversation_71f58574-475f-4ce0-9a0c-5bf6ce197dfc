namespace PaySpace.Venuta.Lookups.Enums
{
    using System;
    using System.Globalization;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    public class LeaveAccrualValueLookupStrategy : TranslatedEnumLookupStrategy
    {
        private readonly IEnumService enumService;

        public LeaveAccrualValueLookupStrategy(IEnumService enumService)
        {
            this.enumService = enumService;
        }

        public override string Name => "LeaveAccrualValue";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData()
                .OrderBy(_ => _.AccrualValue)
                .Select(_ => new LookupDataSet
                {
                    Value = _.AccrualValueCode,
                    Description = _.AccrualValue,
                    Id = _.AccrualValueId
                });
        }

        protected override object? GetEnumValueId(string value, long companyId, long? frequencyId, CultureInfo? culture)
        {
            var id = this.GetData(culture)
                .Where(_ => _.AccrualValue.Equals(value, StringComparison.InvariantCultureIgnoreCase) ||
                            _.AccrualValueCode.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.AccrualValueId)
                .FirstOrDefault();

            return id != default ? id : null;
        }

        private IQueryable<EnumLeaveAccrualValue> GetData(CultureInfo? culture = null)
        {
            return this.enumService.GetLeaveAccrualValues(culture).AsQueryable();
        }
    }
}