namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Employees
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class EmployeeIR8AInfoBuilder : EmployeeAuditEntityInfoBuilder<EmployeeIR8A>
    {
        private readonly LoggerContext loggerContext;

        public EmployeeIR8AInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override async Task<object> GetAuditingValuesAsync(EmployeeIR8A entity)
        {
            return new
            {
                entity.IsIR21Submitted,
                entity.IsSection45Applicable,
                entity.IsIncomeTaxBorneByEmployer,
                entity.IsExemptRemissionIncomeApplicable,
                entity.EmployeeLiabilityAmount,
                entity.AmountAccruedFrom1993,
                entity.EmployerIncomeTaxAmount,
                entity.IsDeclarationByAgent,
                entity.DirectorsFeesApprovalDate,
                entity.DesignatedFundName,
                entity.PensionOrProvidentFundName,
                entity.BonusDeclarationDate,
                BasisYearId = await this.GetBasisYearDescriptionAsync(entity),
                IncomeTaxOptionId = await this.GetIncomeTaxOptionDescriptionAsync(entity),
                OverseasPostingReasonId = await this.GetOverseasPostingDescriptionAsync(entity),
                RemissionExemptIncomeReasonId = await this.GetRemissionExemptIncomeReasonnDescriptionAsync(entity)
            };
        }

        protected override long? GetAlternativeId(EmployeeIR8A entity)
        {
            return entity.EmployeeIR8AId;
        }

        private Task<string?> GetIncomeTaxOptionDescriptionAsync(EmployeeIR8A entity)
        {
            return this.loggerContext.Set<EnumIncomeTaxOption>()
                .Where(_ => _.TaxOptionId == entity.IncomeTaxOptionId)
                .Select(_ => _.TaxOptionDescription)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetOverseasPostingDescriptionAsync(EmployeeIR8A entity)
        {
            return this.loggerContext.Set<EnumOverseasPosting>()
                .Where(_ => _.OverseasPostingId == entity.OverseasPostingReasonId)
                .Select(_ => _.OverseasPostingDescription)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetRemissionExemptIncomeReasonnDescriptionAsync(EmployeeIR8A entity)
        {
            return this.loggerContext.Set<EnumRemissionExemptIncomeReason>()
                .Where(_ => _.ExemptIncomeReasonId == entity.RemissionExemptIncomeReasonId)
                .Select(_ => _.ExemptIncomeReasonDescription)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetBasisYearDescriptionAsync(EmployeeIR8A entity)
        {
            return this.loggerContext.Set<CountryTaxYear>()
                .Where(_ => _.TaxYearId == entity.TaxYearId)
                .Select(_ => _.YearStartDate.Year.ToString())
                .FirstOrDefaultAsync();
        }
    }
}
