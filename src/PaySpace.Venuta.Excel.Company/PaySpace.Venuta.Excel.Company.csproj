<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<CodeAnalysisRuleSet>..\..\PaySpace.Venuta.ruleset</CodeAnalysisRuleSet>
	</PropertyGroup>

	<PropertyGroup>
		<EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
	</PropertyGroup>

	<ItemGroup>
	  <PackageReference Include="Roslynator.Formatting.Analyzers">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PaySpace.Venuta.Excel\PaySpace.Venuta.Excel.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Lookups\PaySpace.Venuta.Lookups.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Modules.CompanyShiftPatterns.Abstractions\PaySpace.Venuta.Modules.CompanyShiftPatterns.Abstractions.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions\PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.csproj" />
		<ProjectReference Include="..\PaySpace.Venuta.Workflow.Activities\PaySpace.Venuta.Workflow.Activities.csproj" />
	</ItemGroup>

</Project>