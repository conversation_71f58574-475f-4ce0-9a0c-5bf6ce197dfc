namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Storage;

    public class CompanyLeaveSetupMutator : EntityMutator<CompanyLeaveSetupDto, CompanyLeaveDetail, long>
    {
        private readonly IMapper mapper;
        private readonly ICompanyLeaveSetupService leaveSetupService;
        private readonly ICompanyLeaveDetailService leaveDetailService;
        private readonly IStringLocalizer<CompanyLeaveSetup> localizer;

        public CompanyLeaveSetupMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<CompanyLeaveSetupDto, CompanyLeaveDetail> entityValidator,
            IAttachmentStorageService attachmentService,
            ICompanyLeaveSetupService leaveSetupService,
            ICompanyLeaveDetailService leaveDetailService,
            IStringLocalizer<CompanyLeaveSetup> localizer)
                : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.mapper = mapper;
            this.leaveSetupService = leaveSetupService;
            this.leaveDetailService = leaveDetailService;
            this.localizer = localizer;
        }

        protected override async Task<CompanyLeaveDetail?> FindAsync(MutatorContext context, long key)
        {
            // Ensure we have access to these navigation entities, for deletion and audit trail
            return await this.Set
                .Include(_ => _.CompanyLeaveSetup)
                    .ThenInclude(_ => _.CompanyLeaveScheme)
                .Include(_ => _.CompanyLeaveServiceLengths)
                .FirstOrDefaultAsync(_ => _.CompanyLeaveSetup.CompanyLeaveScheme.CompanyId == context.CompanyId &&
                                          _.CompanyLeaveDetailId == key);
        }

        protected override async Task<EntityValidationResult> ValidateAsync(MutatorContext context, CompanyLeaveSetupDto dto, CompanyLeaveDetail entity, string ruleSet, CancellationToken cancellationToken = default)
        {
            // Ensure there is no duplicate CompanyLeaveSetup Description per LeaveType
            var duplicateSetupDescriptionExists = await this.leaveSetupService.GetCompanyLeaveSetupsByLeaveTypeAsync(entity.CompanyLeaveSchemeId, dto.LeaveType)
                .ContinueWith(_ => _.Result
                    .Any(_ => _.LeaveDescription == entity.LeaveDescription && _.CompanyLeaveSetupId != entity.CompanyLeaveSetupId), cancellationToken);

            if (!duplicateSetupDescriptionExists)
            {
                return await base.ValidateAsync(context, dto, entity, ruleSet, cancellationToken);
            }

            var errorMessage = this.localizer.GetString(ErrorCodes.Leave.DuplicateSetupDescriptionsNotAllowed);
            return new EntityValidationResult(false, [new(errorMessage, ErrorCodes.Leave.DuplicateSetupDescriptionsNotAllowed, nameof(CompanyLeaveSetup))]);
        }

        // Handles both creating/updating CompanyLeaveSetups with order number re-sequencing if conflicts exist
        protected override async Task AfterMapAsync(MutatorContext context, CompanyLeaveSetupDto dto, CompanyLeaveDetail entity)
        {
            // On Create, the entity.CompanyLeaveSetup is null as it needs to be created
            var companyLeaveSetup = new CompanyLeaveSetup();
            if (entity.CompanyLeaveSetup != null)
            {
                // Update the existing CompanyLeaveSetup
                companyLeaveSetup = await this.leaveSetupService.GetCompanyLeaveSetupByDescriptionAsync(entity.CompanyLeaveSchemeId, entity.LeaveType!.Value, entity.CompanyLeaveSetup.LeaveDescription);
                if (companyLeaveSetup != null)
                {
                    entity.CompanyLeaveSetupId = companyLeaveSetup.CompanyLeaveSetupId;

                    // Rare case: clean incorrect order numbers for Special leave
                    if (dto.LeaveType == LeaveType.Special && dto.OrderNumber != 1)
                    {
                        dto.OrderNumber = 1;
                    }

                    if (dto.OrderNumber != entity.CompanyLeaveSetup.OrderNumber && dto.LeaveType != LeaveType.Special)
                    {
                        var totalSetupsPerLeaveType = await this.leaveSetupService.GetTotalLeaveSetupByLeaveTypeAsync(entity.CompanyLeaveSchemeId, dto.LeaveType);
                        // API Edge case: The order number they trying to update this record to, far exceeds the order numbers that exist
                        if (totalSetupsPerLeaveType != 0 && dto.OrderNumber > totalSetupsPerLeaveType)
                        {
                            dto.OrderNumber = totalSetupsPerLeaveType;
                        }

                        context["HasOrderNumberConflict"] = true;
                    }
                }
            }
            // Otherwise, create a new CompanyLeaveSetup
            else
            {
                // Re-sequence order numbers if the newly created Leave Setups order number is NOT greater than the current total (will cause a duplicate)
                var totalSetupsPerLeaveType = await this.leaveSetupService.GetTotalLeaveSetupByLeaveTypeAsync(entity.CompanyLeaveSchemeId, dto.LeaveType);
                if (totalSetupsPerLeaveType != 0 && dto.OrderNumber <= totalSetupsPerLeaveType)
                {
                    context["HasOrderNumberConflict"] = true;
                }
            }

            this.mapper.Map(dto, companyLeaveSetup);
            entity.CompanyLeaveSetup = companyLeaveSetup;

            // Ensure newly created ServiceLengths are linked to the correct CompanyLeaveDetail record
            await this.leaveDetailService.ProcessServiceLengthRecordsAsync(entity, dto);

            // Clear fields for disabled checkboxes on Add/Update
            this.leaveDetailService.SetDefaultValues(entity);

            await base.AfterMapAsync(context, dto, entity);
        }

        protected override async Task AfterUpdateAsync(MutatorContext context, CompanyLeaveDetail entity, CompanyLeaveSetupDto dto)
        {
            // Note the updates need to be done here as the validation and transaction for the updated records will have been completed
            await this.HandleOrderNumberConflictAsync(context, entity);
            await base.AfterUpdateAsync(context, entity, dto);
        }

        protected override async Task AfterAddAsync(MutatorContext context, CompanyLeaveDetail entity, CompanyLeaveSetupDto dto)
        {
            // Note the updates need to be done here as the validation and transaction for the created records will have been completed
            await this.HandleOrderNumberConflictAsync(context, entity);
            await base.AfterAddAsync(context, entity, dto);
        }

        protected override async Task DeleteAsync(MutatorContext context, long? employeeId, CompanyLeaveDetail entity)
        {
            if (entity.CompanyLeaveSetup?.CompanyLeaveScheme is null)
            {
                await base.DeleteAsync(context, employeeId, entity);
                return;
            }

            // Store relevant info for Audit Trail (non-mapped properties)
            entity.LeaveDescription = entity.CompanyLeaveSetup.LeaveDescription ?? string.Empty;
            entity.SchemeName = entity.CompanyLeaveSetup.CompanyLeaveScheme.SchemeName;
            entity.CompanyId = entity.CompanyLeaveSetup.CompanyLeaveScheme.CompanyId;
            entity.OrderNumber = entity.CompanyLeaveSetup.OrderNumber;
            entity.LeaveType = entity.CompanyLeaveSetup.LeaveType;

            // Special-Leave can only have an order number value of 1
            if (entity.CompanyLeaveSetup!.LeaveType == LeaveType.Special)
            {
                await base.DeleteAsync(context, employeeId, entity);
                return;
            }

            // Store relevant information for re-sequencing the order numbers after deletion
            context["NeedsOrderResequencing"] = true;
            context["DeletedCompanyLeaveSchemeId"] = entity.CompanyLeaveSetup.CompanyLeaveSchemeId;

            await base.DeleteAsync(context, employeeId, entity);
        }

        protected override async Task AfterDeleteAsync(MutatorContext context, CompanyLeaveDetail entity)
        {
            // Ensure the related CompanyLeaveSetup is also deleted
            if (entity.CompanyLeaveSetupId != 0)
            {
                var companyLeaveSetup = await this.leaveSetupService.FindByIdAsync(entity.CompanyLeaveSetupId);
                await this.leaveSetupService.DeleteAsync(companyLeaveSetup);
            }

            // Skip resequencing for Special-Leave types as they can only have an order number of 1
            if (entity.LeaveType != LeaveType.Special &&
                context.TryGetValue("NeedsOrderResequencing", out var _) &&
                context.TryGetValue("DeletedCompanyLeaveSchemeId", out var deletedCompanyLeaveSchemeId))
            {
                // Get all leave setups with the same type in the same scheme
                var deletedSchemeId = Convert.ToInt64(deletedCompanyLeaveSchemeId);
                var companyLeaveSetups = await this.leaveSetupService.GetCompanyLeaveSetupsByLeaveTypeAsync(deletedSchemeId, entity.LeaveType!.Value);

                // Shift down all order numbers greater than the deleted one
                var companyLeaveSetupsToUpdate = companyLeaveSetups.Where(_ => _.OrderNumber > entity.OrderNumber);
                foreach (var companyLeaveSetup in companyLeaveSetupsToUpdate)
                {
                    companyLeaveSetup.OrderNumber--;
                    await this.leaveSetupService.UpdateAsync(companyLeaveSetup);
                }
            }

            await base.AfterDeleteAsync(context, entity);
        }

        protected override Task<EntityValidationResult> ValidatePropertyPermissionsAsync(MutatorContext context, CompanyLeaveSetupDto originalDto, CompanyLeaveSetupDto dto)
        {
            // During update the time portion of the date gets modified and NotEditable gets triggered unintentionally
            dto.EffectiveDate = originalDto.EffectiveDate;

            return base.ValidatePropertyPermissionsAsync(context, originalDto, dto);
        }

        // Note, this handles both cases where an order number needs to be shifted upwards or downwards depending on available order number slots
        private async Task HandleOrderNumberConflictAsync(MutatorContext context, CompanyLeaveDetail entity)
        {
            // Return from this if the newly created/updated CompanyLeaveSetup does not have a conflicting order number
            if (!context.TryGetValue("HasOrderNumberConflict", out var _) || entity.CompanyLeaveSetup.LeaveType == LeaveType.Special)
            {
                return;
            }

            var companyLeaveSetups = await this.leaveSetupService.GetCompanyLeaveSetupsByLeaveTypeAsync(entity.CompanyLeaveSchemeId, entity.CompanyLeaveSetup.LeaveType);
            if (companyLeaveSetups != null)
            {
                var targetOrderNumber = entity.CompanyLeaveSetup.OrderNumber; // order number for this record which has already been updated
                var otherSetups = companyLeaveSetups.Where(_ => _.CompanyLeaveSetupId != entity.CompanyLeaveSetupId).ToList();

                // Find any leave setups with the same order number as the updated entity
                var conflictingLeaveSetups = otherSetups.Where(_ => _.OrderNumber == targetOrderNumber).ToList();
                if (conflictingLeaveSetups.Count == 0)
                {
                    return;
                }

                // For each duplicate, we need to find the first available lower order number
                foreach (var conflictingSetup in conflictingLeaveSetups)
                {
                    var usedOrderNumbers = companyLeaveSetups.Where(_ => _.CompanyLeaveSetupId != conflictingSetup.CompanyLeaveSetupId).Select(_ => _.OrderNumber).ToHashSet();

                    // start from the duplicate's current position and work downwards
                    var newOrder = conflictingSetup.OrderNumber - 1;
                    while (newOrder > 0 && usedOrderNumbers.Contains(newOrder))
                    {
                        newOrder--;
                    }

                    // If we found an available slot, use it
                    if (newOrder > 0)
                    {
                        conflictingSetup.OrderNumber = newOrder;
                    }
                    else
                    {
                        // If no slots available below, find first available slot above
                        newOrder = conflictingSetup.OrderNumber + 1;
                        while (usedOrderNumbers.Contains(newOrder))
                        {
                            newOrder++;
                        }

                        conflictingSetup.OrderNumber = newOrder;
                    }

                    // Update each entity individually
                    await this.leaveSetupService.UpdateAsync(conflictingSetup);
                }
            }
        }
    }
}