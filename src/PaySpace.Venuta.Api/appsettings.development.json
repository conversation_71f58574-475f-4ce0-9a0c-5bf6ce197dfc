{"Identity": {"Authority": "https://localhost:44392", "ClientId": "nextgen", "ClientSecret": "f7442f2a-bd04-4bf1-8b31-f1f1f2182e02"}, "ClientSettings": {"CalcWebApiBaseUrl": "https://payspaceuat-calc-webapi.azurewebsites.net/api", "CalcCacheWebApiBaseUrl": "https://payspaceuat-calc-cacheapi.azurewebsites.net/api", "RunGenerationWebApiBaseUrl": "https://payspaceuat-rungeneration-webapi.azurewebsites.net/api", "ApiUrl": "https://localhost:44393", "DynamicFormBuilderApiUrl": "https://localhost:7002"}, "SsrsSettings": {"Url": "http://**********/reportserver/ReportExecution2005.asmx", "Username": "reports", "Password": "tB*54eB,J?;@h^D"}, "BankAccountValidatorClientSettings": {"ServiceKey": "6b896a6a-663b-45e3-b2e6-ab10e431b1e8"}, "RedisSettings": {"Instance": "NextGen_Dev:", "DefaultConnection": "localhost:6379"}, "AzureConnections": {"ServiceBusConnection": "", "StorageConnection": "DefaultEndpointsProtocol=https;AccountName=websiteattachmentsdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "CosmosConnection": "AccountEndpoint=https://localhost:8081/;AccountKey=****************************************************************************************;"}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}}