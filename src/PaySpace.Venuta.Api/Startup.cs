namespace PaySpace.Venuta.Api
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using Duende.IdentityModel;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.AspNetCore.Authentication.JwtBearer;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Http.Features;
    using Microsoft.AspNetCore.Localization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.AspNetCore.OData;
    using Microsoft.AspNetCore.OData.NewtonsoftJson;
    using Microsoft.AspNetCore.OData.Query;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Microsoft.OData.Edm;
    using Microsoft.OData.UriParser;

    using PaySpace.Cache.Distributed;
    using PaySpace.Integrations.Webhooks;
    using PaySpace.Modules.Company.Directory;
    using PaySpace.Venuta.Api.Authorization;
    using PaySpace.Venuta.Api.Filters;
    using PaySpace.Venuta.Api.Infrastructure.Conventions;
    using PaySpace.Venuta.Api.Middleware;
    using PaySpace.Venuta.Api.Results;
    using PaySpace.Venuta.Api.Settings;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Mappers;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Excel;
    using PaySpace.Venuta.Excel.Bureau;
    using PaySpace.Venuta.Excel.Company;
    using PaySpace.Venuta.Excel.Components;
    using PaySpace.Venuta.Excel.CustomForms;
    using PaySpace.Venuta.Excel.Employees;
    using PaySpace.Venuta.Health;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Localization;
    using PaySpace.Venuta.Logging;
    using PaySpace.Venuta.Lookups;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes;
    using PaySpace.Venuta.Modules.CompanySettings;
    using PaySpace.Venuta.Modules.CompanyShiftPatterns;
    using PaySpace.Venuta.Modules.Components;
    using PaySpace.Venuta.Modules.Components.Employee;
    using PaySpace.Venuta.Modules.CustomForms;
    using PaySpace.Venuta.Modules.DynamicFormBuilder;
    using PaySpace.Venuta.Modules.EFTOutbox;
    using PaySpace.Venuta.Modules.Employee.Claims;
    using PaySpace.Venuta.Modules.Employee.Inbox;
    using PaySpace.Venuta.Modules.Employee.Positions;
    using PaySpace.Venuta.Modules.Employee.SuspensionSnapshot;
    using PaySpace.Venuta.Modules.EmployeeEtiTakeOns;
    using PaySpace.Venuta.Modules.EmployeeHistory;
    using PaySpace.Venuta.Modules.EmployeeTakeOns;
    using PaySpace.Venuta.Modules.EmploymentStability;
    using PaySpace.Venuta.Modules.GeneralLedger;
    using PaySpace.Venuta.Modules.HmrcPaymentRecord;
    using PaySpace.Venuta.Modules.Leave;
    using PaySpace.Venuta.Modules.Organization;
    using PaySpace.Venuta.Modules.OrgChart;
    using PaySpace.Venuta.Modules.PayRate;
    using PaySpace.Venuta.Modules.Payslips;
    using PaySpace.Venuta.Modules.PensionEnrolment;
    using PaySpace.Venuta.Modules.PublicHolidays;
    using PaySpace.Venuta.Modules.Retro;
    using PaySpace.Venuta.Modules.SecurityRoles;
    using PaySpace.Venuta.Modules.UserOrgPermissions;
    using PaySpace.Venuta.Modules.Workday;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authentication;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.External;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.Telemetry;
    using PaySpace.Venuta.Workflow;
    using PaySpace.Venuta.Workflow.Activities;

    public class Startup
    {
        private readonly IWebHostEnvironment env;

        public Startup(IConfiguration configuration, IWebHostEnvironment env)
        {
            this.Configuration = configuration;
            this.env = env;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddApplicationInsightsTelemetry(opt => opt.EnableAdaptiveSampling = true);
            services.AddApplicationInsightsTelemetryProcessor<SuppressTelemetryFilter>();

            if (this.env.IsDevelopment())
            {
                services.AddLogging(loggingBuilder =>
                {
                    loggingBuilder.AddSeq();
                });
            }

            services.ConfigureOptions<MvcOptions>();
            services.ConfigureOptions<JsonOptions>();

            // Add config.
            var identitySettings = this.Configuration.GetSection("Identity").Get<IdentitySettings>();
            services.AddSingleton(Options.Create(identitySettings));

            var endpointTimeout = this.Configuration.GetSection("EndpointTimeout").Get<EndpointTimeout>();
            services.AddSingleton(Options.Create(endpointTimeout));

            // Add framework services
            services.AddMemoryCache();
            services.AddLocalization();
            services.AddResponseCaching();
            services.AddResponseCompression(options => options.EnableForHttps = true);
            services.AddRouting(routing => routing.LowercaseUrls = true);
            services.AddDateOnlyTimeOnlyStringConverters();

            var mvc = services.AddControllers()
                .AddNewtonsoftJson()
                .AddDataAnnotationsLocalization()
                .AddFluentValidation()
                .ConfigureApplicationPartManager(setup => setup.FeatureProviders.Add(new GenericControllerFeatureProvider()))
                .ConfigureApiBehaviorOptions(options =>
                {
                    options.InvalidModelStateResponseFactory = context =>
                    {
                        var type = context.HttpContext.GetDtoType(false);
                        var entitySet = type != null ? context.HttpContext.Request.GetEntitySet(context.HttpContext.GetDtoType()) : null;
                        if (entitySet != null)
                        {
                            return new ODataBadRequestObjectResult(context, entitySet, context.ModelState)
                            {
                                ContentTypes = { "application/problem+json" }
                            };
                        }

                        var messages = context.ModelState.Values.SelectMany(_ => _.Errors.Select(error => error.ErrorMessage));
                        return new BadRequestObjectResult(new ErrorResponseModel(messages, context.HttpContext.User.IsNextGen()));
                    };
                });

            services.AddApiVersioning(options =>
            {
                options.DefaultApiVersion = ApiVersions.Default;
                options.AssumeDefaultVersionWhenUnspecified = true;
                options.ReportApiVersions = true;

                options.Conventions.Add(new ApiControllerVersionConvention());
            });

            mvc.AddOData(options =>
            {
                options.EnableQueryFeatures(this.Configuration.GetValue<int>("ODataQuerySettings:MaxTop"));

                // Create a default Edm Model for the service container to get set on the ODataFeature.
                options.AddRouteComponents(string.Empty, new EdmModel(), s =>
                {
                    s.AddSingleton<ODataUriResolver>(new StringAsEnumResolver { EnableCaseInsensitive = true });
                });
            }).AddODataNewtonsoftJson();

            services.AddODataQueryFilter(new CustomEnableQueryAttribute
            {
                MaxTop = this.Configuration.GetValue<int>("ODataQuerySettings:MaxTop"),
                PageSize = this.Configuration.GetValue<int>("ODataQuerySettings:PageSize"),
                MaxNodeCount = 500,
                HandleNullPropagation = HandleNullPropagationOption.False
            });

            // Security.
            services.AddSecurity(this.Configuration);
            services.AddApiSecurity();

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = identitySettings.Authority;
                    options.Audience = ApiResources.Api;
                    options.TokenValidationParameters.NameClaimType = JwtClaimTypes.Name;
                    options.TokenValidationParameters.RoleClaimType = JwtClaimTypes.Role;

                    options.Events = new JwtBearerEvents
                    {
                        OnMessageReceived = e =>
                        {
                            e.Token = TokenRetrieval.FromQueryStringOrHeader(e.Request);
                            return Task.CompletedTask;
                        },
                        OnTokenValidated = e =>
                        {
                            if (this.env.IsDevelopment())
                            {
                                // Push out the expires for when expire time is requested from the authentication ticket. Otherwise old tokens used for dev will throw an exception.
                                e.Properties.ExpiresUtc = DateTimeOffset.UtcNow.AddDays(14);
                            }

                            var expiresIn = e.Properties.ExpiresUtc.Value.Subtract(DateTimeOffset.Now) + e.Options.TokenValidationParameters.ClockSkew;
                            e.HttpContext.Items.Add("ExpiresIn", expiresIn);

                            return Task.CompletedTask;
                        }
                    };

                    if (this.env.IsDevelopment())
                    {
                        options.TokenValidationParameters.ValidateLifetime = false;
                    }

                    options.TokenHandlers.Clear();
                    options.TokenHandlers.Add(new CustomJwtSecurityTokenHandler
                    {
                        InboundClaimTypeMap = new Dictionary<string, string>
                        {
                            // Client claims.
                            { "client_id", JwtClaimTypes.Name },
                            { "client_agencyid", PaySpaceClaimTypes.AgencyId },
                            { "client_groupsid", PaySpaceClaimTypes.CompanyGroupId },
                            { "client_dev_account", PaySpaceClaimTypes.DeveloperAccount },
                            { "client_nameidentifier", JwtClaimTypes.Subject },
                            { "client_ehr", PaySpaceClaimTypes.EditHistoricalRecordsPermission },
                            { "client_role", JwtClaimTypes.Role }
                        }
                    });
                });

            services.AddAuthorizationBuilder()
                .AddFallbackPolicy("FallbackPolicy", builder => builder.RequireAuthenticatedUser())
                .AddPolicy("agency-user", policy => policy.RequireAuthenticatedUser().RequireRole(new string[] { UserTypeCodes.Bureau, UserTypeCodes.Agency }))
                .AddPolicy("full_access", policy => policy.RequireAuthenticatedUser().RequireScope("api.full_access"))
                .AddPolicy("bureau-user", policy => policy.RequireAuthenticatedUser().RequireRole(UserTypeCodes.Bureau))
                .AddPolicy("create", policy => policy.RequireAuthenticatedUser().RequireScope("api.full_access", "api.create"))
                .AddPolicy("update", policy => policy.RequireAuthenticatedUser().RequireScope("api.full_access", "api.update"))
                .AddPolicy("delete", policy => policy.RequireAuthenticatedUser().RequireScope("api.full_access", "api.delete"))
                .AddPolicy("metadata", policy => policy.RequireAuthenticatedUser().AddRequirements(new MetadataRequirement()))
                .AddPolicy("company", policy => policy.RequireAuthenticatedUser().AddRequirements(new CompanyRequirement()))
                .AddPolicy("company-group", policy => policy.RequireAuthenticatedUser().AddRequirements(new CompanyGroupRequirement()))
                .AddPolicy("employee", policy => policy.RequireAuthenticatedUser().AddRequirements(new EmployeeRequirement()))
                .AddPolicy("workday", policy => policy.RequireAuthenticatedUser().RequireScope("api.workday"))
                .AddPolicy("company-directory", policy => policy.RequireAuthenticatedUser().RequireScope("api.company_directory"))
                .AddPolicy("payspace-invoice", policy => policy.RequireAuthenticatedUser().RequireScope("api.invoice"));

            // Add application services.
            services.AddValidators();

            services.AddCustomFieldDataServices(this.Configuration.GetConnectionString("DefaultConnection"));
            services.AddLoggingServices(this.Configuration.GetConnectionString("DefaultConnection"))
                .AddPublishers();

            services.AddApplicationServices();
            services.AddLocalizationServices()
                .AddMetadataProviders();

            services.AddExcelServices()
                .AddExcelDownloadServices()
                .AddEmployeeExcelServices()
                .AddCompanyExcelServices()
                .AddBureauExcelServices()
                .AddComponentExcelServices()
                .AddCustomFormExcelServices()
                .AddLookupServices()
                .AddDataExtractionServices();
            services.AddDtoMaps();

            services.AddExternalServices(this.Configuration);
            services.AddCalculationServices(this.Configuration);

            // Add infrastructure services.
            services.AddDistributedCache(this.Configuration.GetSection("RedisSettings"))
                .AddRedisClient();
            services.AddStorage(this.Configuration, this.env.IsDevelopment());
            services.AddMessageBus(this.Configuration, this.env.IsDevelopment());

            // Modules.
            services.AddComponentModules();
            services.AddComponentExtractionServices();
            services.AddPayslipModules();
            services.AddLeaveModules();
            services.AddPayRateModules();
            services.AddEFTOutboxModules();
            services.AddEmployeePositionsModules();
            services.AddJobManagementPositionModules();
            services.AddEmployeeEtiTakeOnModules();
            services.AddEmployeeTakeOnModules();
            services.AddGeneralLedgerModules();
            services.AddWorkflow().AddApiWorkflow();
            services.AddRetroModules();
            services.AddCustomFormModules();
            services.AddOrgChartServices();
            services.AddClaimModules();
            services.AddWorkdayModule(this.Configuration["AzureConnections:WorkdayStorageAccount"]);
            services.AddWorkflowActivities();
            services.AddEmployeeHistoryModules();
            services.AddWebhookErrorServices();
            services.AddPublicHolidayModules();
            services.AddSecurityRoleModules();
            services.AddCompanySettingsModules();
            services.AddCompanyDirectory();
            services.AddStabilityModule();
            services.AddSuspensionSnapshotModule();
            services.AddOrganizationModules();
            services.AddInboxModules();
            services.AddDynamicFormBuilderModuleServices(this.Configuration);
            services.AddPensionEnrolmentModules();
            services.AddUserOrgPermissionsModules();
            services.AddHmrcModules();
            services.AddShiftPatternModules();
            services.AddLeaveSchemeParameterModules();

            // Health monitoring. Used with azure to gracefully restart
            services.AddCoreCustomHealthChecks()
                .AddRedisServiceHealthChecks()
                .AddDbContextHealthChecks<AuditDbContext>()
                .AddDbContextHealthChecks<ReadOnlyDbContext>();

            // Add API specific implementations.
            services.AddApiServices(this.Configuration.GetValue<bool?>("FeatureManagement:IsReportsApi"));

            // Add Report Services.
            services.AddReportServices();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IModelMetadataProvider metadataProvider, TelemetryConfiguration telemetryConfiguration)
        {
            this.ConfigureFluentValidationLocalization(metadataProvider);

            if (this.env.IsDevelopment())
            {
                // Disable Application Insights Telemetry in output window.
                telemetryConfiguration.DisableTelemetry = true;
            }
            else
            {
                telemetryConfiguration.EnableCustomAdaptiveSampling();

                // add the Strict-Transport-Security Header, force that all communication is done using HTTPS
                app.UseHsts(hsts => hsts.MaxAge(365).IncludeSubdomains().Preload());
            }

            app.UseForwardedHeaders();
            app.UseResponseCompression();
            app.UseHttpMethodOverride();
            app.UseXssQueryFilterMiddleware();

            app.UseCors();

            // Health should go before authentication to allow anonymous access.
            app.UseCustomHealthChecks();

            // Request should be authenticated before routing kicks in.
            app.UseAuthentication();

            app.UseRouting();

            app.Use((context, next) =>
            {
                context.Response.OnStarting(() =>
                {
                    context.Response.Headers["OData-Version"] = "4.0";

                    var authError = context.GetAuthenticationError();
                    if (!string.IsNullOrEmpty(authError))
                    {
                        context.Features.Get<IHttpResponseFeature>().ReasonPhrase = authError;
                    }

                    return Task.CompletedTask;
                });

                return next();
            });

            app.UseRateLimiting(this.env);
            app.UseTenant();
            app.UseCompanyRegionValidation();
            app.UseCountryCode();
            app.UseIpValidation();
            app.UseEmployeeNumberFilter();
            app.UseCompanySecurityProfile();
            app.UseSecurity();
            app.UseTenantTelemetry();
            app.UseRun().UseRunPeriod();

            app.UseRequestLocalization(this.GetLocalizationOptions());

            app.UseMetadata();
            app.UseAuthorization();

            app.UseEmptyResponse();

            app.UseFilterTranslator();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute("BasicMetadata", "odata/v{version}/$metadata", new { controller = "Metadata", action = "BasicMetadata" })
                    .RequireAuthorization("company-group");
                endpoints.MapControllerRoute("CompanyMetadata", "odata/v{version}/{company}/$metadata", new { controller = "Metadata", action = "CompanyMetadata" })
                    .RequireAuthorization("company");
                endpoints.MapControllers()
                    .Add(builder =>
                    {
                        var dto = EndpointHelper.GetDtoType(new EndpointMetadataCollection(builder.Metadata));
                        if (dto != null)
                        {
                            builder.Metadata.Add(new DtoType(dto));
                        }
                    });
            });
        }

        private void ConfigureFluentValidationLocalization(IModelMetadataProvider metadataProvider)
        {
            ValidatorOptions.Global.DisplayNameResolver = (type, memberInfo, expression) =>
            {
                if (memberInfo == null)
                {
                    return null;
                }

                var property = metadataProvider.GetMetadataForType(type).Properties[memberInfo.Name];
                if (property == null)
                {
                    return null;
                }

                return property.DisplayName;
            };
        }

        private RequestLocalizationOptions GetLocalizationOptions()
        {
            var options = new RequestLocalizationOptions
            {
                SupportedCultures = CultureData.SupportedCultures,
                SupportedUICultures = CultureData.SupportedCultures,
                DefaultRequestCulture = new RequestCulture(CultureData.DefaultCulture)
            };

            options.RequestCultureProviders.Insert(0, new QueryStringRequestCultureProvider());
            options.RequestCultureProviders.Insert(1, new AuthenticatedCultureProvider());
            return options;
        }
    }
}