namespace PaySpace.Venuta.Api.Pipeline.ModelBuilders
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Globalization;
    using System.Linq;

    using Maddalena;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Configuration;
    using Microsoft.OData.Edm;
    using Microsoft.OData.Edm.Vocabularies;
    using Microsoft.OData.ModelBuilder;

    using PaySpace.Venuta.Api.Infrastructure;
    using PaySpace.Venuta.Data.Models.Components.Results;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Bureau;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Dto.Components;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Employees.IncidentTypes;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Lookups;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Validation;
    using PaySpace.Venuta.Validation.Annotations;

    public class NextGenModelBuilder : CompanyModelBuilder
    {
        private static readonly string[] RetroCountries =
            [
                nameof(CountryCode.BR),
                nameof(CountryCode.IN),
                nameof(CountryCode.AU),
                nameof(CountryCode.GB),
                nameof(CountryCode.ZA),
                nameof(CountryCode.FR),
                nameof(CountryCode.DE)
            ];

        private readonly IModelMetadataProvider metadataProvider;
        private readonly ICompanyPaymentModuleService companyPaymentModuleService;
        private readonly ICompanyService companyService;
        private readonly PaymentModule paymentModule;
        private readonly ISecurityProfile securityProfile;
        private readonly Version version;

        public NextGenModelBuilder(
            IConfiguration configuration,
            IModelMetadataProvider metadataProvider,
            IStrategyRegistry strategyRegistry,
            ICompanyPaymentModuleService companyPaymentModuleService,
            ICompanyService companyService,
            Version version,
            PaymentModule paymentModule,
            IList<CachedMenu> userMenus,
            ISecurityProfile securityProfile)
            : base(configuration, metadataProvider, strategyRegistry, version, paymentModule, userMenus, securityProfile)
        {
            this.metadataProvider = metadataProvider;
            this.companyPaymentModuleService = companyPaymentModuleService;
            this.companyService = companyService;
            this.paymentModule = paymentModule;
            this.securityProfile = securityProfile;
            this.version = version;
        }

        public override IEdmModel GetEdmModel()
        {
            var edmModel = base.GetEdmModel();

            edmModel.SetAnnotationValue(edmModel, new NextgenAnnotation(true));

            return edmModel;
        }

        protected override void AnnotateEntity(IEdmModel edmModel, IEdmType entityType, ClrTypeAnnotation declaringType)
        {
            base.AnnotateEntity(edmModel, entityType, declaringType);

            var metadata = this.metadataProvider.GetMetadataForType(declaringType.ClrType);
            if (metadata.TryGetAttribute<BulkUploadEntityAttribute>(out var bulkUploadAttribute))
            {
                var controllerName = declaringType.ClrType.GetNavigationControllerNames().FirstOrDefault() ?? NameHelper.GetDisplayName(declaringType.ClrType.Name);

                edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "ControllerName", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), controllerName));
                edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "IsBulkUpload", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(true), true));
                edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "Area", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), bulkUploadAttribute.ScreenType.ToString()));

                if (bulkUploadAttribute.Lookups.Count > 0)
                {
                    edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "Lookups", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), string.Join(',', bulkUploadAttribute.Lookups)));
                }

                if (!bulkUploadAttribute.ShowByDefault)
                {
                    edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "ShowByDefault", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(false), bulkUploadAttribute.ShowByDefault));
                }

                if (!string.IsNullOrEmpty(bulkUploadAttribute.GroupedWith))
                {
                    edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "GroupedWith", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), bulkUploadAttribute.GroupedWith));
                    edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "ShowByDefault", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(true), bulkUploadAttribute.ShowByDefault));
                }
            }

            if (metadata.TryGetAttribute<CategoryAttribute>(out var categoryAttribute))
            {
                edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "Category", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), categoryAttribute.Category));
            }
        }

        protected override void AnnotateProperty(IEdmModel edmModel, IEdmStructuralProperty structuralProperty, ModelMetadata entityMetadata)
        {
            var taxCountryCode = this.securityProfile?.TaxCountryCode;

            var modelHelper = NextGenPropertyEdmModelHelper.Create(edmModel, structuralProperty, entityMetadata, taxCountryCode, this.version);

            modelHelper?.Annotate();
        }

        protected override void RegisterEntities(ODataConventionModelBuilder builder)
        {
            base.RegisterEntities(builder);

            this.RegisterEntity<AppealIncidentDto>(builder);
            this.RegisterEntity<ArbitrationIncidentDto>(builder);
            this.RegisterEntity<CompanyExternalQuickLinkDto>(builder);
            this.RegisterEntity<CompanyLeaveServiceLengthDto>(builder);
            this.RegisterEntity<CompanyRosterDto>(builder);
            this.RegisterEntity<CompanyRosterHistoryDto>(builder);
            this.RegisterEntity<CompanyRosterScheduleDto>(builder);
            this.RegisterEntity<CompanyWorkflowRoleDto>(builder);
            this.RegisterEntity<ConciliationIncidentDto>(builder);
            this.RegisterEntity<CourtIncidentDto>(builder);
            this.RegisterEntity<DisciplinaryIncidentDto>(builder);
            this.RegisterEntity<DiscussionIncidentDto>(builder);
            this.RegisterEntity<DiscussionIncidentDto>(builder);
            this.RegisterEntity<EmployeePensionAssessmentDto>(builder);
            this.RegisterEntity<EmployeePensionEnrolmentDto>(builder);
            this.RegisterEntity<EmployeePensionLetterDto>(builder);
            this.RegisterEntity<EmployeeReviewDefaultRatersDto>(builder);
            this.RegisterEntity<EmployeeTerminationDto>(builder);
            this.RegisterEntity<GrievanceIncidentDto>(builder);
            this.RegisterEntity<OtherIncidentDto>(builder);
            this.RegisterEntity<ReviewIncidentDto>(builder);
            this.RegisterEntity<ShiftPatternDto>(builder);
            this.RegisterEntity<ShiftDetailsDto>(builder);

            if (this.version >= PaySpaceApiVersions.V2_0)
            {
                this.RegisterEntity<CompanyLeaveDetailDto>(builder);
                this.RegisterEntity<CompanyLeaveSchemeDto>(builder);
                this.RegisterEntity<CompanyLeaveSetupDto>(builder);
            }

            this.RegisterComplexType<LegislationOverrideDto>(builder);

            if (this.paymentModule is PaymentModule.Master or PaymentModule.NewMaster)
            {
                // Dummy types used on nextgen bulk actions page. These types redirect to classic when selected
                this.RegisterEntity<EmployeeActionTypeDto>(builder);
            }

            if (this.securityProfile.TaxCountryCode != nameof(CountryCode.ZA) || this.paymentModule is PaymentModule.Master or PaymentModule.NewMaster)
            {
                this.RegisterRetro(builder);
            }

            if (this.HasAccessToPacey())
            {
                this.RegisterComplexType<PaceyMessageTemplateDto>(builder);
            }

            if (this.companyService.GetCompanyFrequencyCount(this.securityProfile.CompanyId) > 1)
            {
                this.RegisterEntity<EditPayslipAdvancedDto>(builder);
            }

            if (this.HasFullAccessToEmployeeOnboarding())
            {
                if (PaySpaceConstants.EmployeeHistoryCountries.Contains(this.securityProfile.TaxCountryCode))
                {
                    var employeeOnboarding = this.RegisterComplexType<EmployeeHistoryOnboarding>(builder);
                    if (employeeOnboarding != null)
                    {
                        employeeOnboarding.Name = nameof(EmployeeOnboarding);
                    }
                }
                else
                {
                    this.RegisterComplexType<EmployeeOnboarding>(builder);
                }
            }

            if (!PaySpaceConstants.EmployeeHistoryCountries.Contains(this.securityProfile.TaxCountryCode))
            {
                if (this.securityProfile.IsFullAccess(SystemAreas.Profile.Basic, SystemAreas.Profile.Basic))
                {
                    this.RegisterComplexType<EmployeeUpdate>(builder);
                }
            }

            if (this.securityProfile.TaxCountryCode != CountryCode.BR.ToString())
            {
                this.RegisterComplexType<EmployeeDependantQuickAddDto>(builder);
            }
        }

        protected override void RegisterBureauEntities(ODataConventionModelBuilder builder)
        {
            base.RegisterBureauEntities(builder);
            // TODO: Register Bureau Entities here that is only visible for nextgen

            this.RegisterEntity<BureauPublicHolidayDto>(builder);

            if (this.version >= PaySpaceApiVersions.V2_0)
            {
                this.RegisterEntity<BureauCompanyRunDto>(builder);
            }
        }

        protected override void RegisterComponents(ODataConventionModelBuilder builder)
        {
            base.RegisterComponents(builder);

            this.RegisterEntity<EmployeeAlimonyResult>(builder);
            this.RegisterEntity<EmployeeFinancialHousePaymentsDto>(builder);
            this.RegisterEntity<EmployeeHousePaymentDetailResult>(builder);
            this.RegisterEntity<EmployeeMultiContractWorkResult>(builder);
            this.RegisterEntity<OnceOffEmployeeComponentDto>(builder);
        }

        protected override bool ShouldIgnoreProperty(ModelMetadata modelProperty)
        {
            var clientIds = modelProperty.GetAttributes<ClientDisplayAttribute>().Select(_ => _.ClientId).ToList();
            if (clientIds.Contains(ApiResources.NextGen, StringComparer.OrdinalIgnoreCase))
            {
                return false;
            }

            return base.ShouldIgnoreProperty(modelProperty);
        }

        protected override void DisableCustomFieldSelect<TEntity>(EntitySetConfiguration<TEntity> config)
        {
            // In order to enable the selection of custom fields for nextgen requests, we must override the default method.
        }

        protected override void RemoveEmployeeNavigationProperties(EntitySetConfiguration<EmployeeDto> employee)
        {
            // Don't remove
        }

        private bool HasFullAccessToEmployeeOnboarding()
        {
            return this.securityProfile.IsFullAccess(SystemAreas.Profile.BasicCreate, SystemAreas.Profile.BasicCreate)
                   && this.securityProfile.IsFullAccess(SystemAreas.BankDetail.Area, SystemAreas.BankDetail.Area)
                   && this.securityProfile.IsFullAccess(SystemAreas.EmploymentStatus.Area, SystemAreas.EmploymentStatus.Area)
                   && this.securityProfile.IsFullAccess(SystemAreas.Payroll.PayRates.Area, SystemAreas.Payroll.PayRates.Keys.Package);
        }

        private bool HasAccessToPacey()
        {
            return (this.securityProfile.IsFullAccess(SystemAreas.PaceyMessageTemplate.Area, SystemAreas.PaceyMessageTemplate.Area)
                    || this.securityProfile.IsReadOnly(SystemAreas.PaceyMessageTemplate.Area, SystemAreas.PaceyMessageTemplate.Area))
                && this.companyPaymentModuleService.HasPaceyModule(this.securityProfile.CompanyId);
        }

        private void RegisterRetro(ODataConventionModelBuilder builder)
        {
            if (RetroCountries.Contains(this.securityProfile.TaxCountryCode)
                    && (this.securityProfile.IsFullAccess(SystemAreas.Payroll.Retro.Trigger.Area, SystemAreas.Payroll.Retro.Trigger.Area)
                        || this.securityProfile.IsReadOnly(SystemAreas.Payroll.Retro.Trigger.Area, SystemAreas.Payroll.Retro.Trigger.Area)))
            {
                this.RegisterEntity<EmployeeRetroTriggerDto>(builder);
            }
        }
    }
}