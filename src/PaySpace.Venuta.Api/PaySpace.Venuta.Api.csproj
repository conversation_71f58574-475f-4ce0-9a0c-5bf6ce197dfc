<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
	<TargetFramework>net9.0</TargetFramework>
	<!--<Nullable>enable</Nullable>-->
	<NeutralLanguage>en-ZA</NeutralLanguage>
	<ApplicationInsightsResourceId>/subscriptions/8d9221e8-f3d9-4773-a334-79c9085522bc/resourcegroups/VenutaApplicationInsights/providers/microsoft.insights/components/Api</ApplicationInsightsResourceId>
	<CodeAnalysisRuleSet>..\..\PaySpace.Venuta.ruleset</CodeAnalysisRuleSet>
	<UserSecretsId>c84c4213-646d-4a09-8ea7-cc32278332de</UserSecretsId>
  </PropertyGroup>

  <PropertyGroup>
	<EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
	<EmbeddedResource Include="Sql\**\*" />
  </ItemGroup>

  <ItemGroup>
	<ProjectReference Include="..\PaySpace.Venuta.Modules.DynamicFormBuilder\PaySpace.Venuta.Modules.DynamicFormBuilder.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.HmrcPaymentRecord\PaySpace.Venuta.Modules.HmrcPaymentRecord.csproj" />
	<ProjectReference Include="..\PaySpace.Configuration\PaySpace.Configuration.csproj" />
	<ProjectReference Include="..\PaySpace.Modules.Company.Directory\PaySpace.Modules.Company.Directory.csproj" />
	<ProjectReference Include="..\PaySpace.Integrations.Webhooks\PaySpace.Integrations.Webhooks.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Data.Models.Dto.Mappers\PaySpace.Venuta.Data.Models.Dto.Mappers.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Data.Models.Dto\PaySpace.Venuta.Data.Models.Dto.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Data.Models.Validation\PaySpace.Venuta.Data.Models.Validation.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Data\PaySpace.Venuta.Data.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Excel.Bureau\PaySpace.Venuta.Excel.Bureau.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Excel.Company\PaySpace.Venuta.Excel.Company.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Excel.Components\PaySpace.Venuta.Excel.Components.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Excel.CustomForms\PaySpace.Venuta.Excel.CustomForms.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Excel.Employees\PaySpace.Venuta.Excel.Employees.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Health\PaySpace.Venuta.Health.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Infrastructure\PaySpace.Venuta.Infrastructure.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Localization\PaySpace.Venuta.Localization.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Logging\PaySpace.Venuta.Logging.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Lookups\PaySpace.Venuta.Lookups.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.CompanyLeaveSchemes\PaySpace.Venuta.Modules.CompanyLeaveSchemes.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.CompanySettings\PaySpace.Venuta.Modules.CompanySettings.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.CompanyShiftPatterns\PaySpace.Venuta.Modules.CompanyShiftPatterns.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Components\PaySpace.Venuta.Modules.Components.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.CustomForms\PaySpace.Venuta.Modules.CustomForms.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.EFTOutbox\PaySpace.Venuta.Modules.EFTOutbox.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Employee.Positions\PaySpace.Venuta.Modules.Employee.Positions.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Employee.SuspensionSnapshot\PaySpace.Venuta.Modules.Employee.SuspensionSnapshot.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.EmployeeEtiTakeOns\PaySpace.Venuta.Modules.EmployeeEtiTakeOns.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.EmployeeHistory\PaySpace.Venuta.Modules.EmployeeHistory.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.EmployeeTakeOns\PaySpace.Venuta.Modules.EmployeeTakeOns.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.EmploymentStability\PaySpace.Venuta.Modules.EmploymentStability.csproj" />
	<ProjectReference Include="..\Payspace.Venuta.Modules.GeneralLedger\PaySpace.Venuta.Modules.GeneralLedger.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Leave\PaySpace.Venuta.Modules.Leave.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Organization\PaySpace.Venuta.Modules.Organization.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.OrgChart\PaySpace.Venuta.Modules.OrgChart.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.PayRate\PaySpace.Venuta.Modules.PayRate.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Payslips\PaySpace.Venuta.Modules.Payslips.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.PensionEnrolment\PaySpace.Venuta.Modules.PensionEnrolment.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Retro\PaySpace.Venuta.Modules.Retro.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.UserOrgPermissions\PaySpace.Venuta.Modules.UserOrgPermissions.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.Workday\PaySpace.Venuta.Modules.Workday.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Modules.SecurityRoles\PaySpace.Venuta.Modules.SecurityRoles.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Security.Authentication\PaySpace.Venuta.Security.Authentication.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Security.Authorization\PaySpace.Venuta.Security.Authorization.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Security\PaySpace.Venuta.Security.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Services.Reports\PaySpace.Venuta.Services.Reports.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Storage\PaySpace.Venuta.Storage.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Telemetry\PaySpace.Venuta.Telemetry.csproj" />
	<ProjectReference Include="..\PaySpace.Venuta.Workflow.Activities\PaySpace.Venuta.Workflow.Activities.csproj" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="PaySpace.Cache" />
	<PackageReference Include="PaySpace.Venuta.Messaging" />
	<PackageReference Include="AutoMapper.AspNetCore.OData.EFCore" />
	<PackageReference Include="AutoMapper.Collection" />
	<PackageReference Include="AutoMapper" />
	<PackageReference Include="DateOnlyTimeOnly.AspNet" />
	<PackageReference Include="FluentValidation.AspNetCore" />
	<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
	<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
	<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
	<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" />
	<PackageReference Include="Microsoft.AspNetCore.OData" />
	<PackageReference Include="Microsoft.AspNetCore.OData.NewtonsoftJson" />
	<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" />
	<PackageReference Include="Azure.Security.KeyVault.Secrets" />
	<PackageReference Include="NWebsec.AspNetCore.Middleware" />
	<PackageReference Include="Roslynator.Formatting.Analyzers">
	  <PrivateAssets>all</PrivateAssets>
	  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	</PackageReference>
	<PackageReference Include="Seq.Extensions.Logging" />
	<PackageReference Include="System.Linq.Dynamic.Core" />
  </ItemGroup>

  <ItemGroup>
	<!-- Temp solution to enable unix/linux support. Epplus uses System.Drawing.Common. -->
	<!-- From v6 it throws an error, From 7 unix/linux support is removed. -->
	<RuntimeHostConfigurationOption Include="System.Drawing.EnableUnixSupport" Value="true" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\..\.dockerignore">
      <Link>.dockerignore</Link>
    </Content>
  </ItemGroup>

</Project>