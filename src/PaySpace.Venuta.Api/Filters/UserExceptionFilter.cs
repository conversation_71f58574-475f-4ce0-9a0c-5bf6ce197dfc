namespace PaySpace.Venuta.Api.Filters
{
    using System;
    using System.ComponentModel;
    using System.Security.Claims;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Filters;
    using Microsoft.Extensions.Logging;
    using Microsoft.OData.Edm;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Api.Infrastructure;
    using PaySpace.Venuta.Api.Results;
    using PaySpace.Venuta.Excel.Abstractions.Exceptions;

    public class UserExceptionFilter : ExceptionFilterAttribute
    {
        private readonly ILogger logger;

        public UserExceptionFilter(ILogger<UserExceptionFilter> logger)
        {
            this.logger = logger;
        }

        public override void OnException(ExceptionContext context)
        {
            if (context.Exception is UnauthorizedAccessException)
            {
                this.logger.LogWarning(context.Exception, context.Exception.Message);
                context.Result = new ForbidResult();

                if (context.Exception is MutatorUnauthorizedAccessException mutatorException)
                {
                    context.HttpContext.AddAuthenticationError(mutatorException.Message);
                }

                return;
            }

            if (context.Exception is NotImplementedException
                or NotSupportedException
                or NotFoundException)
            {
                context.Result = new NotFoundResult();
                return;
            }

            if (context.Exception is JsonSerializationException
                or JsonReaderException
                or InvalidEnumArgumentException
                or ArgumentException
                or ArgumentNullException
                or InvalidOperationException and not InvalidEmployeeNumberException)
            {
                this.logger.LogWarning(context.Exception, context.Exception.Message);
                context.Result = this.CreateErrorResult(context);
                return;
            }

            if ((context.Exception is MutatorUnauthorizedAccessException && context.HttpContext.User.IsNextGen()) || context.Exception is InvalidEmployeeNumberException)
            {
                context.Result = this.CreateErrorResult(context);
            }
        }

        private BadRequestObjectResult CreateErrorResult(ExceptionContext context)
        {
            var type = context.HttpContext.GetDtoType(throwNotFound: false);
            var entitySet = type != null ? context.HttpContext.Request.GetEntitySet(context.HttpContext.GetDtoType()) : null;
            if (entitySet != null)
            {
                return new ODataBadRequestObjectResult(context, entitySet, context.Exception.Message);
            }

            if (context.Exception is ApiException apiException)
            {
                return new BadRequestObjectResult(new ErrorResponseModel(apiException.Errors, context.HttpContext.User.IsNextGen()));
            }

            return new BadRequestObjectResult(new ErrorResponseModel(context.Exception.Message, context.HttpContext.User.IsNextGen()));
        }
    }
}