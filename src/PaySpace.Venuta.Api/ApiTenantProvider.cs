namespace PaySpace.Venuta.Api
{
    using System;
    using System.Security.Claims;

    using Duende.IdentityModel;

    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;

    public class ApiTenantProvider : ITenantProvider, IClaimsTenantProvider
    {
        private readonly IHttpContextAccessor contextAccessor;

        public ApiTenantProvider(IHttpContextAccessor contextAccessor)
        {
            this.contextAccessor = contextAccessor;
        }

        private HttpContext HttpContext => this.contextAccessor.HttpContext;

        public long GetUserId(ClaimsPrincipal user)
        {
            return user.GetUserId();
        }

        public long GetUserId()
        {
            return this.GetUserId(this.HttpContext.User);
        }

        public UserType GetUserType()
        {
            if (this.HttpContext == null)
            {
                return UserType.Employee;
            }

            return Enum.Parse<UserType>(this.HttpContext.User.GetUserType());
        }

        public long GetAgencyId(ClaimsPrincipal user)
        {
            throw new NotImplementedException();
        }

        public long GetAgencyId()
        {
            throw new NotImplementedException();
        }

        public long? GetCompanyGroupId(ClaimsPrincipal user)
        {
            throw new NotImplementedException();
        }

        public long? GetCompanyGroupId()
        {
            throw new NotImplementedException();
        }

        public long? GetCompanyId(ClaimsPrincipal user)
        {
            if (this.HttpContext != null && this.HttpContext.Items.TryGetValue(PaySpaceConstants.CompanyId, out var companyId))
            {
                return Convert.ToInt64(companyId);
            }

            return null;
        }

        public long? GetCompanyId()
        {
            if (this.HttpContext?.User == null)
            {
                return null;
            }

            return this.GetCompanyId(this.HttpContext.User);
        }

        public long? GetEmployeeId(ClaimsPrincipal user)
        {
            if (this.HttpContext.Request.Headers.TryGetValue("X-EmployeeId", out var value))
            {
                if (long.TryParse(value, out var employeeId))
                {
                    return employeeId;
                }
            }

            if (user.HasClaim(JwtClaimTypes.Scope, "api.admin"))
            {
                return null;
            }

            if (user.IsInRole(UserTypeCodes.Employee))
            {
                return user.GetEmployeeId();
            }

            return null;
        }

        public long? GetEmployeeId()
        {
            return this.GetEmployeeId(this.HttpContext.User);
        }

        public long? GetFrequencyId(ClaimsPrincipal user)
        {
            return this.HttpContext.GetFrequencyId();
        }

        public long? GetFrequencyId()
        {
            return this.GetFrequencyId(this.HttpContext.User);
        }

        public string? GetTaxCountryCode(ClaimsPrincipal user)
        {
            if (this.HttpContext.Items.TryGetValue("Country", out var taxCountryCode))
            {
                return Convert.ToString(taxCountryCode);
            }

            return null;
        }

        public string? GetTaxCountryCode()
        {
            if (this.HttpContext == null)
            {
                return null;
            }

            return this.GetTaxCountryCode(this.HttpContext.User);
        }

        public int GetDecimalPlaces(ClaimsPrincipal user)
        {
            return user.GetDecimalPlaces();
        }

        public int GetDecimalPlaces()
        {
            return this.GetDecimalPlaces(this.HttpContext.User);
        }

        public bool CanEditHistoricalRecords(ClaimsPrincipal user)
        {
            return user.CanEditHistoricalRecords();
        }

        public bool CanEditHistoricalRecords()
        {
            return this.CanEditHistoricalRecords(this.HttpContext.User);
        }

        public int? GetTaxCountryId()
        {
            throw new NotImplementedException();
        }
    }
}