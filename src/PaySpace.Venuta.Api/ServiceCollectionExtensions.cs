namespace PaySpace.Venuta.Api
{
    using System;

    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Routing;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Api.Authorization;
    using PaySpace.Venuta.Api.Converters;
    using PaySpace.Venuta.Api.Infrastructure;
    using PaySpace.Venuta.Api.Infrastructure.MatcherPolicies;
    using PaySpace.Venuta.Api.MessageHandlers;
    using PaySpace.Venuta.Api.Pipeline;
    using PaySpace.Venuta.Api.Pipeline.ModelBuilders;
    using PaySpace.Venuta.Api.Services.Reports;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components.Results;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Dto.Components;
    using PaySpace.Venuta.Data.Models.Dto.Converters;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Telemetry.TelemetryInitializers;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddApiSecurity(this IServiceCollection services)
        {
            services.AddSingleton<IMenuUrlBuilder, ApiMenuUrlBuilder>();

            // https://andrewlock.net/how-to-register-a-service-with-multiple-interfaces-for-in-asp-net-core-di.
            services.AddSingleton<ApiTenantProvider>();
            services.AddSingleton<ITenantProvider>(sp => sp.GetRequiredService<ApiTenantProvider>());
            services.AddSingleton<IClaimsTenantProvider>(sp => sp.GetRequiredService<ApiTenantProvider>());

            // Authorization.
            services.AddSingleton<IAuthorizationHandler, CompanyAuthorizationHandler>();
            services.AddSingleton<IAuthorizationHandler, MetadataAuthorizationHandler>();
            services.AddSingleton<IAuthorizationHandler, CompanyGroupAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, EmployeeAuthorizationHandler>();

            return services;
        }

        public static IServiceCollection AddApiServices(this IServiceCollection services, bool? isReportsApiEnabled)
        {
            services.AddSingleton<ITelemetryInitializer, AuthenticatedTelemetryInitializer>();
            services.AddSingleton<ITelemetryInitializer, ApiTelemetryInitializer>();

            services.AddScoped<IModelBuilderFactory, ModelBuilderFactory>();
            services.AddScoped<IEdmModelFactory, EdmModelFactory>();

            // Converters.
            services.AddSingleton<JsonConverter, DescriptionStringEnumConverter>();
            services.AddSingleton<JsonConverter, AttachmentUrlConverter>();
            services.AddSingleton<JsonConverter, CompanyCategoryFieldValueConverter>();
            services.AddSingleton<JsonConverter, EmployeeNumberEntityConverter>();
            services.AddSingleton<JsonConverter, EmployeeCustomFormConverter>();
            services.AddSingleton<JsonConverter, CompanyCustomFormConverter>();
            services.AddSingleton<JsonConverter, CustomFieldConverter>();

            services.AddSingleton<JsonConverter, MergeListConverter<EmployeeAddressDto>>();
            services.AddSingleton<JsonConverter, MergeListConverter<CompanyAddressDto>>();
            services.AddSingleton<JsonConverter, MergeListConverter<EmployeeComponentValueResult>>();
            services.AddSingleton<JsonConverter, MergeListConverter<EmployeeFinancialHousePaymentLineDto>>((p) => new MergeListConverter<EmployeeFinancialHousePaymentLineDto>(true));
            services.AddSingleton<JsonConverter, MergeListConverter<EmployeeRecurringCostingDetailDto>>((p) => new MergeListConverter<EmployeeRecurringCostingDetailDto>(true));
            services.AddSingleton<JsonConverter, MergeListConverter<CompanyLeaveDetailDto>>();
            services.AddSingleton<JsonConverter, MergeListConverter<CompanyLeaveServiceLengthDto>>((_) => new MergeListConverter<CompanyLeaveServiceLengthDto>(true));

            services.AddSingleton<JsonConverter, DecimalConverter>();
            services.AddSingleton<JsonConverter, DefaultDoubleDecimalPlacesConverter>();
            services.AddSingleton<JsonConverter, BatchRequestItemConverter>();
            services.AddSingleton<JsonConverter, WebhookDataConverter>();
            services.AddSingleton<JsonConverter, CompanyGradeFieldValueConverter>();
            services.AddSingleton<JsonConverter, StringCollectionConverter>();
            services.AddSingleton<JsonConverter, ReportRequestDtoConverter>();
            services.AddSingleton<JsonConverter, ParameterDefinitionTypeConverter>();

            services.AddScoped(typeof(ICustomFieldConverterService<>), typeof(CustomFieldConverterService<>));
            services.AddScoped<ICustomFieldConverterService<TableBuilder>, TableBuilderCustomFieldConverterService>();
            services.AddScoped<ICustomFieldConverterService<EmployeeCustomForm>, CustomFormConverterService<EmployeeCustomForm>>();
            services.AddScoped<ICustomFieldConverterService<CompanyCustomForm>, CustomFormConverterService<CompanyCustomForm>>();
            services.AddScoped<IBulkUploadService, BulkUploadService>();

            // Message handlers.
            if (isReportsApiEnabled == true)
            {
                services.AddMessageHandler<ReportApiUserSigningInMessageHandler>();
                services.AddMessageHandler<ReportApiUserSigningOutMessageHandler>();
            }
            else
            {
                services.AddMessageHandler<ApiUserSigningInMessageHandler>();
                services.AddMessageHandler<ApiUserSigningOutMessageHandler>();
            }

            services.AddSingleton<ODataFilterParser>();

            services.TryAddEnumerable(ServiceDescriptor.Singleton<MatcherPolicy, PowerBiEndpointMatcherPolicy>());

            services.AddSingleton<MatcherPolicy, PowerBiEndpointMatcherPolicy>();
            services.AddSingleton<MatcherPolicy>(_ => CreateMatcherPolicy<DependantHistoryAttribute>(PaySpaceConstants.DependantHistoryCountries));
            services.AddSingleton<MatcherPolicy>(_ => CreateMatcherPolicy<EmployeeHistoryAttribute>(PaySpaceConstants.EmployeeHistoryCountries));

            services.AddScoped<IRegionCompanyExclusionService, RegionCompanyExclusionService>();

            services.AddScoped<IApiReportService, ApiReportService>();

            return services;
        }

        private static HistoryEndpointMatcherPolicy<T> CreateMatcherPolicy<T>(string[] countryCodes)
            where T : Attribute
        {
            return new HistoryEndpointMatcherPolicy<T>(countryCodes);
        }
    }
}