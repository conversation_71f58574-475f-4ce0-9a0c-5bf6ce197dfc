namespace PaySpace.Venuta.Api.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.OData.Query;

    using PaySpace.Venuta.Api.Infrastructure.QueryBuilders;
    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees.Results;

    [Route(ApiConstants.ControllerRoute)]
    public class EmployeeLeaveAdjustmentController : BulkController<EmployeeLeaveAdjustmentDto, Data.Models.Employees.EmployeeLeaveAdjustment, long>
    {
        private const string StoredProcName = "nextgen_api_leaveadjustments_get";

        public EmployeeLeaveAdjustmentController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
        }

        [HttpGet("{year}/{month}")]
        public IActionResult GetByPeriodCode(int year, int month)
        {
            return this.GetBuilder(periodCode: $"{year}{month}").ExecuteResult<EmployeeLeaveAdjustmentResult>();
        }

        [HttpGet("disabled-we-are-using-stored-proc")]
        public override IQueryable<EmployeeLeaveAdjustmentDto> GetCollection()
        {
            throw new NotImplementedException();
        }

        [HttpGet]
        public IActionResult Get()
        {
            return this.GetBuilder().ExecuteResult<EmployeeLeaveAdjustmentResult>();
        }

        protected override IQueryable<EmployeeLeaveAdjustmentDto> CreateSingleResultQueryable(long key)
        {
            return this.GetBuilder(key: key).ExecuteSingle<EmployeeLeaveAdjustmentResult>();
        }

        protected override Task<List<EmployeeLeaveAdjustmentDto>> GetDownloadData(ODataQueryOptions<EmployeeLeaveAdjustmentDto> options, bool nodata, bool all)
        {
            if (!all && (nodata || options.Filter == null))
            {
                return Task.FromResult(new List<EmployeeLeaveAdjustmentDto>(0));
            }

            return this.GetBuilder().ExecuteListAsync<EmployeeLeaveAdjustmentResult>();
        }

        private StoredProcQueryBuilder<EmployeeLeaveAdjustmentDto> GetBuilder(string? periodCode = null, long? key = null)
        {
            return new EmployeeLeaveAdjustmentQueryBuilder(StoredProcName)
                .For(this)
                .SetSqlTimeout(120)
                .SetParameter("leaveAdjustmentId", key)
                .SetParameter("periodCode", periodCode);
        }
    }
}