namespace PaySpace.Venuta.Api.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.OData.Query;

    using PaySpace.Venuta.Api.Infrastructure.QueryBuilders;
    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees.Results;
    using PaySpace.Venuta.Excel.Abstractions.Models;

    using EmployeeLeaveAdjustment = PaySpace.Venuta.Data.Models.Employees.EmployeeLeaveAdjustment;

    public class EmployeeLeaveApplicationController : BulkController<EmployeeLeaveApplicationDto, EmployeeLeaveAdjustment, long>
    {
        private const string StoredProcName = "nextgen_api_leaveadjustments_get";

        public EmployeeLeaveApplicationController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
        }

        [HttpGet]
        public IActionResult Get()
        {
            return this.GetBuilder().ExecuteResult<EmployeeLeaveAdjustmentResult>();
        }

        [HttpGet("{year}/{month}")]
        public IActionResult GetByPeriodCode(int year, int month)
        {
            return this.GetBuilder(periodCode: $"{year}{month}").ExecuteResult<EmployeeLeaveAdjustmentResult>();
        }

        [HttpGet("disabled-we-are-using-stored-proc")]
        public override IQueryable<EmployeeLeaveApplicationDto> GetCollection()
        {
            throw new NotImplementedException();
        }

        protected override IQueryable<EmployeeLeaveApplicationDto> CreateSingleResultQueryable(long key)
        {
            return this.GetBuilder(key: key).ExecuteSingle<EmployeeLeaveAdjustmentResult>();
        }

        protected override Task<List<EmployeeLeaveApplicationDto>> GetDownloadData(ODataQueryOptions<EmployeeLeaveApplicationDto> options, bool nodata, bool all)
        {
            if (!all && (nodata || options.Filter == null))
            {
                return Task.FromResult(new List<EmployeeLeaveApplicationDto>(0));
            }

            return this.GetBuilder().ExecuteListAsync<EmployeeLeaveAdjustmentResult>();
        }

        protected override MutatorContext GetMutatorContext()
        {
            var context = base.GetMutatorContext();
            if (this.Request.Query.TryGetValue("allowWorkflow", out var allowWorkflow)
                && bool.TryParse(allowWorkflow, out var hasWorkflow) && hasWorkflow)
            {
                context.Add("EnableWorkflow", hasWorkflow);
                context.Add("EnableAttachment", true);
                context.Add("PathBase", this.Request.PathBase);
            }
            else
            {
                hasWorkflow = false;
            }

            if (!hasWorkflow
                && this.Request.Query.TryGetValue("validateAttachments", out var validateAttachments)
                && bool.TryParse(validateAttachments, out var enableAttachment))
            {
                context.Add("EnableAttachment", enableAttachment);
            }

            return context;
        }

        private StoredProcQueryBuilder<EmployeeLeaveApplicationDto> GetBuilder(string? periodCode = null, long? key = null)
        {
            return new EmployeeLeaveApplicationQueryBuilder(StoredProcName)
                .For(this)
                .MapCustomFields(this.MapCustomFields)
                .SetParameter("leaveAdjustmentId", key)
                .SetParameter("periodCode", periodCode);
        }

        private List<EmployeeLeaveApplicationDto> MapCustomFields(List<EmployeeLeaveApplicationDto> data, IList<CustomFieldDto> customFields)
        {
            if (customFields.Count == 0)
            {
                return data;
            }

            foreach (var leaveApplication in data)
            {
                leaveApplication.CustomFields = customFields.Where(_ => _.RelatedId == leaveApplication.LeaveAdjustmentId).ToList();
            }

            return data;
        }
    }
}