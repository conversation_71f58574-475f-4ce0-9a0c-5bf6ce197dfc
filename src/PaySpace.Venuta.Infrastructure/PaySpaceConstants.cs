namespace PaySpace.Venuta.Infrastructure
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;

    public static class PaySpaceConstants
    {
        public static readonly TimeSpan AbsoluteSessionTimeout = TimeSpan.FromHours(10);
        public static readonly TimeSpan SessionTimeout = TimeSpan.FromMinutes(20);
        public static readonly TimeSpan SessionWarning = TimeSpan.FromMinutes(2);

        public static readonly long[] CompaniesWithAdditionalFrequencyTab = new[] { 13343L };

        public static readonly string[] SdlExemptionCountries = { "BJ", "KE", "NA", "SN", "TG", "TZ", "ZM", "ZW" };

        public static readonly string[] EmployeeHistoryCountries = { "BR", "FR" };

        public static readonly string[] DependantHistoryCountries = { "BR" };

        public static class Idp
        {
            public const string Replicate = "replicate";

            public static bool IsLocal(string idp)
            {
                return idp is "local" or Replicate;
            }
        }

        public const long PaySpaceAgencyId = 1013;
        public const long PaySpaceBureauId = 1;
        public const string PaySpaceAgencyUrl = "secure.payspace.com";
        public const string PaySpaceHost = "payspace.com";
        public const string DefaultAgencyName = "PaySpace";
        public const string DefaultAgencyTheme = "PaySpaceTheme";
        public const long AgencyBankDetailNavigationLinkId = 166;

        public const int DecimalPrecision = 8;
        public const string DecimalPlaces = "DecimalPlaces";

        public const string UserId = "UserId";
        public const string CompanyId = "CompanyId";
        public const string CountryId = "CountryId";
        public const string FrequencyId = "FrequencyId";
        public const string FrequencyIds = "FrequencyIds";
        public const string CategoryId = "CategoryId";
        public const string CountryCustomFormId = "CountryCustomFormId";
        public const string CompanyCustomFormId = "CompanyCustomFormId";
        public const string RunId = "RunId";
        public const string ComponentCompanyId = "ComponentCompanyId";
        public const string Version = "Version";
        public const string CompanyLeaveSchemeId = "CompanyLeaveSchemeId";

        public const string RelatedPrimaryKey = "RelatedPrimaryKey";
        public const string ValidationInfo = "ValidationInfo";
        public const string UseInMemoryValidationData = "UseInMemoryValidationData";
        public const string SkipAttachmentValidation = "SkipAttachmentValidation";
        public const string AllowEmployeeNumberChange = "AllowEmployeeNumberChange";
        public const string AllowCustomFieldAdd = "AllowCustomFieldAdd";
        public const string SecurityProfile = "Profile";
        public const string CompanyFrequencyId = "CompanyFrequencyId";
        public const string EmployeeEffectiveDate = "EmployeeEffectiveDate";
        public const string EmployeeCitizenshipId = "EmployeeCitizenshipId";
        public const string EmployeeGenderId = "EmployeeGenderId";
        public const string EmployeeBirthday = "EmployeeBirthday";
        public const string EmploymentStatusGroupJoinDate = "EmploymentStatusGroupJoinDate";
        public const string EmploymentStatusEmploymentDate = "EmploymentStatusEmploymentDate";
        public const string CustomFieldEffectiveDate = "CustomFieldEffectiveDate";
        public const string BulkCaptureAction = "BulkCaptureAction";
        public const string EnableRealTimeCalculations = "EnableRealTimeCalculations";
        public const string EmployeeCustomFormController = "DynamicHistoricalInformation";
        public const string CompanyCustomFormController = "CompanyCustomForms";
        public const string YearToDateTakeOnController = "YearToDateTakeOn";
        public const string EmployeeEtiTakeOnController = "EmployeeEtiTakeOn";
        public const string PayslipsController = "Payslips";
        public const string PayslipEditController = "PayslipEdit";
        public const string CompanySecurityRoleController = "CompanySecurityRole";
        public const string BureauUserProfileController = "BureauUserProfile";
        public const string OrganizationGradeController = "OrganizationGrade";
        public const string OrganizationGroupController = "OrganizationGroup";
        public const string JobManagementController = "JobManagement";
        public const string CompanyUserProfileController = "CompanyUserProfile";
        public const string BureauSecurityRoleController = "BureauSecurityRole";
        public const string UserActivateController = "UserActivate";
        public const string CompanyGroupLinkController = "CompanyGroupLink";
        public const string CopyConfigurationController = "CopyConfiguration";
        public const string IsNextGen = "IsNextGen";
        public const string DisableAttachmentCustomFieldValidation = "DisableAttachmentCustomFieldValidation";
        public const string IsBureauRunOperation = "IsBureauRunOperation";
        public const string BureauRunManagementController = "BureauRunManagement";
        public const string CompanyRunController = "CompanyRun";
        public const string AverageTaxMethodCode = "AverageTaxMethod";

        public const int CompanyWorkflowId = 7;
        public const int EmployeeWorkflowId = 6;
        public const int PayslipDatesPageId = 73;
        public const int AgencyNavLinkId = 60641;
        public const int BureauNavLinkId = 5;
        public const int ReportsNavLinkId = 129;
        public const int EmployeePayslipNavLinkId = 144;

        public const double MaxAgeAllowed = 365.25 * 150;

        public const string ProvinceFieldCode = "ADDUFC";

        public const string SystemUser = "<EMAIL>";

        public const string EstTimeZone = "E. South America Standard Time";
        public const string EstTimeZoneId = "America/Sao_Paulo";

        public static readonly Dictionary<string, long> ControllerLinkIds = new()
        {
            { "CompanyExternalQuickLink", 60640 },
            { "CompanyGLDetail", 101 },
            { "CompanyGroupExchangeRate", 272 },
            { "CompanyJobManagement", 60454 },
            { "CompanyQualification", 220 },
            { "CompanyReviewProcess", 203 },
            { "CompanySkill", 177 },
            { "CompanySkillCategory", 219 },
            { "CompanyTrainingCourse", 174 },
            { "CompanyWorkflowRole", 261 },
            { "CostingProjectActivity", 185 },
            { "EmployeeInbox", 290 },
            { "EmployeeQualification", 222 },
            { "EmployeeRecurringCostingSplit", 60584 },
            { "EmployeeReviewHeader", 70899 },
            { "EmployeeReviewTemplate", 70899 },
            { "EmployeeSkill", 178 },
            { "JobManagementBudgetCost", 60454 },
            { "LeaveAdjustment", 118 },
            { "OrganizationGroup", 106 },
            { "OrganizationPosition", 107 },
            { "PayRateCategory", 277 },
            { "TrainingRecord", 175 },
            { "ComponentCompany", 26 },
            { "BureauPublicHoliday", 90 },
            { "CompanyLeaveScheme", 116 },
            { "CompanyLeaveSchemeParameter", 117 },
            { "BureauCompanyRun", 60421 },
            { "CompanyRun", 73 },
            { "EmployeeEvaluationDefaults", 30304}
        };

        public const char CanadaTempSinPrefix = '9';
    }

    public static class RecurringComponentConstants
    {
        public const string ComponentPercentageCosting = "Component Percentage Costing";
        public const string PercentageSplit = "Percentage Split";
        public const long FreeOrCheapAccommodationFringeBenefitBureauComponentId = 267;

        public static class Garnishee
        {
            public const string CapitalNA = "NA";
        }
    }

    public static class ComponentTaxCodeConstants
    {
        public const string TravelAllowance = "3701";
        public const string CompanyCarBenefit = "3802";
        public const string CompanyCarRentalBenefit = "3816";
        public const string PensionEmployerContribution = "4472";
        public const string PensionFringeBenefit = "3817";
        public const string PensionFringeBenefitForeign = "3867";
        public const string ProvidentFringeBenefit = "3825";
        public const string ProvidentFringeBenefitForeign = "3875";
        public const string ProvidentContribution = "4473";
        public const string RetirementAnnuityDeduction = "4475";
        public const string RetirementAnnuityEmployer = "3828";
        public const string RetirementAnnuityEmployerForeign = "3878";
        public const string LumpDeathTaxCode = "3922";
        public const string CarNote = "CAR";
        public const string BargainingCouncilEmployerContribution = "4584";
        public const string BargainingCouncilEmployerContributionFringeBenefit = "3833";
        public const string MedicalAidFringeBenefit = "3810";
        public const string MedicalAidEmployerContribution = "4474";
        public const string MedicalAidDeemedDeductions = "4005";
        public const string MedicalExpensesTaxCredit = "4116";
        public const string AdditionalMedicalExpensesTaxCredit = "4120";
        public const string EtiCode = "4118";
        public const long EtiDummyId = 4118;
        public const int SpecialComponentIdMedicalExpensesTaxCredit = 5430;
        public const int SpecialComponentIdAdditionalMedicalExpensesTaxCredit = 5431;
        public const string SavingsWithdrawalBenefitTaxCode = "3926";
    }

    public static class CountryTaxYearDetailRateCodeConstants
    {
        public const string EtiClaimMax = "ETIClaimMax";
        public const string EtiClaimBulkUpload = "ETIClaimBulkUpload";
        public const string EtiRangeLimitThree = "ETIRangeLimitThree";
        public const string LumpDeathRateCode = "LUMPDEATH";
    }

    public static class SpecialComponentCodes
    {
        public const string BasicSalary = "BasSal";
        public const string NetPay = "NetPay";
        public const string LeaveEncashment = "LeaveEnca";
        public const string Maternity = "Maternity";
        public const string Pension = "Pension";
        public const string Provident = "Provident";
        public const string LumpSum = "LumpSumA";
        public const string LumpSumNT = "LumpSumNT";
        public const string TableBuilder = "TBLEBLDER";
        public const string GrossUp = "GrossUp";
        public const string GrossUpAw = "GrossUpAw";

        public const int PayRateMinusCurrentInPackageContributionsComponentId = 0;
    }

    public static class InputTypeDescription
    {
        public const string Default = "Total amount";
    }

    public static class Roles
    {
        public const string ESS = "ESS";
        public const string MSS = "MSS";
        public const string CSS = "CSS";
        public const string ASS = "ASS";

        public const string Admin = "Admin";
        public const string TopLevel = "TopLevel";
        public const string Insights = "Insights";

        public static IReadOnlyDictionary<string, string[]> RootPermissions { get; } = new ReadOnlyDictionary<string, string[]>(
            new Dictionary<string, string[]>
            {
                { "Bureau", new[] { "Bureau", "Company", "Employee" } },
                { "Agency", new[] { "Company", "Employee" } },
                { "Company", new[] { "Company", "Employee" } },
                { "Employee", new[] { "Employee" } }
            });
    }

    public static class UserTypeCodes
    {
        /// <summary>
        /// Combine Company, Agency and Bureau user types.
        /// </summary>
        public const string CAB = "Company,Agency,Bureau";

        public const string Bureau = "Bureau";
        public const string Company = "Company";
        public const string Agency = "Agency";
        public const string Employee = "Employee";
    }

    public static class ContentTypes
    {
        public const string Zip = "application/x-zip-compressed";
    }

    public static class ExternalProviders
    {
        public const string Azure = "AzureAD";
        public const string Google = "Google";
        public const string OneLogin = "OneLogin";
        public const string Okta = "Okta";
        public const string OktaAxioPay = "OktaAxioPay";
        public const string OktaCompassionInternational = "OktaCompassionInternational";
        public const string OktaV2 = "OktaV2";
        public const string QuickBooks = "QuickBooks";
        public const string Xero = "Xero";
        public const string SCB = "SCB";
        public const string Acumatica = "Acumatica";
        public const string PingFed = "PingFed";
    }

    public static class Cookies
    {
        public const string Culture = ".AspNetCore.Culture";
        public const string ManagerSelfService = "ManagerSelfService";
        public const string UserSessionSettings = "UserSession";
    }

    public static class ProductBoardClaimTypes
    {
        public const string Email = "email";
        public const string Id = "id";
        public const string Name = "name";
        public const string CompanyName = "company_name";
        public const string CompanyDomain = "company_domain";
    }

    public static class PathConstants
    {
        public const string PaymentModuleUpsell = "/PaymentModuleUpsell";
    }

    public static class ValidationInfoConstants
    {
        public const string TakeOnComponentUpdatedValues = "TakeOnComponentUpdatedValues";
        public const string TakeOnCompanyComponentId = "TakeOnCompanyComponentId";
        public const string ComponentHeader = "ComponentHeader";
    }

    public static class PaySpaceApiVersions
    {
        public static readonly Version[] SupportedVersions = { new("1.0"), new("1.1"), new("2.0") };

        public static readonly Version V1_0 = new("1.0");

        public static readonly Version V1_1 = new("1.1");

        public static readonly Version V2_0 = new("2.0");

        public static readonly Version Default = V2_0;
    }

    public static class PaySpaceDynamicFormBuilderApiVersions
    {
        public static readonly Version V1_0 = new("1.0");

        public static readonly Version Default = V1_0;
    }

    public static class BrazilConstants
    {
        public const string BrazilTradeUnionCode = "UNION";
        public const int BrazilTaxCountryId = 76;
        public const string BrazilTaxCountryCode = "BR";
        public const int BrazilAddressCountryId = 78;
        public const string ThirteenthChequeTaxCode = "5504";

        public const string AcquisitionLeaveBucket = "Férias Período Aquisitivo";

        public const string BrazilISO3DigitCode = "BRA";
        public const string MunicipalityFieldCode = "ADDMC";

        public static class BureauConfigCodes
        {
            public const string ThirdVac = "THIRDVAC";
        }

        public static class CustomFieldCodes
        {
            public const string VacationSell = "VACSELLA";
        }
    }

    public static class SpainConstants
    {
        public const int TaxCountryId = 157;
        public const string MunicipalityFieldCode = "MUNCODE";
    }

    public static class UKConstants
    {
        public const int AddressCountryId = 7;
        public const int TaxCountryId = 92;
        public const string TaxCountryCode = "GB";
        public const string PensionTableBuilderCategoryCode = "PEN";
        public const string PensionPensionQualifiedSchemeFieldCode = "QUALSch";
    }

    public static class ClaimFormType
    {
        public const string SimpleCarForm = "S";
        public const string DetailedCarForm = "D";
        public const string AmountForm = "A";
    }

    public static class ClaimWorkflowConstants
    {
        public const string Initialized = "Initialized";
        public const string Approved = "Approved";
        public const string Rejected = "Rejected";

        public static class Sources
        {
            public const string Api = "API";
        }
    }

    public static class JobManagementConstants
    {
        public const string ActiveEmployee = "A";
        public const string VacantEmployee = "V";
    }

    public static class RegionConstants
    {
        public const string OrganizationRegion = "OrganizationRegion";
    }

    public static class CustomFieldFormIdConstants
    {
        public const int EmployeeLeaveAdjustment = 18;
    }

    public static class BureauCustomFieldConstants
    {
        // From CustomFieldForms Table
        public const int OrganizationRegionEntity = 10;
    }

    public static class PublicHolidayConstants
    {
        public const int ProvincialLevel = 2;
        public const int CategoryLevel = 3;
        public const int MunicipalityLevel = 4;
    }

    public static class CompanySettingConstants
    {
        public const string ScreenName = "Company Settings";

        public const string CompanySettingController = "CompanySettings";

        public const string CompanyLeaveSettingsController = "LeaveSettings";
        public const string CompanyLeaveSettingsScreen = "Company.Setting.Leave";

        public const string CompanyGeneralSettingsController = "GeneralSettings";
        public const string CompanyGeneralSettingsScreen = "Company.Setting.General";

        public const string CompanySecuritySettingController = "SecuritySettings";
        public const string CompanySecuritySettingScreen = "Company.Setting.Security";

        public const string CompanyPerformanceManagementSettingsController = "PerformanceManagementSettings";
        public const string CompanyPerformanceManagementSettingsScreen = "Company.Setting.Performance.Management";

        public const string CompanyWorkforcePlanningSettingsController = "WorkforcePlanningSettings";
        public const string CompanyWorkforcePlanningSettingsScreen = "Company.Setting.WorkforcePlanning";

        public const string CompanyCostingSettingsController = "CostingSettings";
        public const string CompanyCostingSettingsScreen = "Company.Setting.Costing";

        public const string CompanyClaimSettingsController = "ClaimSettings";
        public const string CompanyClaimSettingsScreen = "Company.Setting.Claim";

        public const string CompanyCalculationSettingsController = "CalculationSettings";
        public const string CompanyCalculationSettingsScreen = "Company.Setting.Calc";

        public const string PositionSettingController = "PositionSettings";
        public const string PositionSettingScreen = "Company.Setting.Position";

        public static ReadOnlyDictionary<string, string> SettingScreens => new(new Dictionary<string, string>
        {
            { CompanyClaimSettingsController, CompanyClaimSettingsScreen },
            { CompanyCalculationSettingsController, CompanyCalculationSettingsScreen },
            { CompanyGeneralSettingsController, CompanyGeneralSettingsScreen },
            { CompanyCostingSettingsController, CompanyCostingSettingsScreen },
            { CompanyLeaveSettingsController, CompanyLeaveSettingsScreen },
            { CompanyPerformanceManagementSettingsController, CompanyPerformanceManagementSettingsScreen },
            { PositionSettingController, PositionSettingScreen },
            { CompanySecuritySettingController, CompanySecuritySettingScreen },
            { CompanyWorkforcePlanningSettingsController, CompanyWorkforcePlanningSettingsScreen }
        });

        public static ReadOnlyDictionary<string, List<string>> SettingsSecurableKeys => new(new Dictionary<string, List<string>>()
        {
            { CompanyGeneralSettingsController, [SystemAreas.CompanyGeneralSetting.SecurableKeys.TaxabilityOptions] }
        });
    }

    public static class TrainingCompanyConstants
    {
        public static readonly Dictionary<int, string> CompanyNameLookup = new()
        {
            { 14621, "NextGen Advanced Case Studies" },
            { 8407, "Payroll Master Essentials" },
            { 8181, "Payroll Master Advanced" },
            { 13837, "NextGen Essentials" },
            { 24642, "NextGen E-Learning" },
            { 13844, "NextGen Advanced" },
            { 20441, "NextGen Advanced" },
            { 21012, "NextGen Advanced" },
            { 21152, "NextGen Advanced" },
            { 28687, "NextGen HR Essentials" },
            { 39006, "Payroll Essentials" }
        };
    }

    public static class CompanyCalculationSettingConstants
    {
        public const string ProrateLookupCode = "PROU";
        public const string GROSSAFTER = "GROSSAFTER";
        public const string PRORATEPER = "PRORATEPER";
        public const string BLKAUTOPAY = "BLKAUTOPAY";

        // UK/IE-specific Settings
        public const string WorkingDaysOption = "WORKDAYPERIOD";
        public const string CalendarDaysOption = "CALENDARPERIOD";

        // Settings hidden for UK
        public const string WWPRO1 = "WWPRO1";
        public const string PRDAYSIN = "PRDAYSIN";

        // ZA-specific Setting
        public const string GROSSPLACE = "GROSSPLACE";

        public static class ProrateLookupValues
        {
            public const string DoNotProrate = "1";
            public const string CalenderDaysPerPeriod = "2";
            public const string FixedCalenderDays = "3";
            public const string ActualWorkingDays = "4";
            public const string DaysPerPeriodWorkingDays = "5";
            public const string ActualRosterDaysPerPeriodWorkingDays = "6";
            public const string WorkingDaysNoProration = "7";
        }
    }

    public static class TaxCountryCodes
    {
        public const string UnitedKingdom = "GB";
    }

    public static class BureauConfigSettings
    {
        public const string NewNonCumulativeTaxMethod = "NEWNONC";
    }

    public static class BureauCustomFormCategoryCodes
    {
        public static class Singapore
        {
            public const string StatutoryInformation = "STATINFO";
        }
    }

    public static class PositionConstants
    {
        public const string EmployeePosition = "EmployeePosition";
        public const int FormId = 7;

        public static class BrazilCustomFieldCodes
        {
            public const string SubContractType = "SUBCON";
            public const string ContractDays = "DIACON";
            public const string ContractEndDate = "DATFCON";
            public const string ExtendContract = "EXTEND";
            public const string ExtensionDays = "DIAPRO";
            public const string ExperienceContractStartDate = "DATPRO";
            public const string ExperienceContractEndDate = "DATPROF";
            public const string EmploymentContractType = "TPCON";
        }
    }

    public static class SingaporeConstants
    {
        public static class BureauCustomFormFieldCodes
        {
            public static class StatutoryInformation
            {
                public const string ResidentStatus = "RESSTAT";
                public const string ResidentEffectiveDate = "RESEFFDATE";
                public const string CurrentResidentStatus = "CURRESSTAT";
                public const string PermanentResidentFirstYearDate = "PR1DTE";
                public const string PermanentResidentSecondYearDate = "PR2DTE";
                public const string PermanentResidentThirdYearDate = "PR3DTE";
            }
        }
    }

    public static class CanadaConstants
    {
        public static class SuspensionCustomFieldCodes
        {
            public const string PayPeriodEndDate = "FPED";
            public const string OverrideFirstDateWorked = "OVR1STDAY";
        }
    }

    public static class LeaveSchemeConstants
    {
        public const string StandardLeaveDescription = "Standard Leave";
        public static readonly DateTime MinimumEffectiveDate = new(1950, 1, 1);

        public static class EffectiveDateForfeit
        {
            public const string GroupJoinDate = "G";
            public const string EmploymentDate = "D";
            public const string ThisMonthEveryYear = "M";
            public const string DateEmployeeLinkedToScheme = "E";
        }

        public static class Description
        {
            public const string SickLeave = "Sick Leave";
            public const string AnnualLeave = "Annual Leave";
            public const string SpecialLeave = "Special Leave";
            public const string FamilyResponsibilityLeave = "Family Responsibility Leave";
        }
    }

    public static class GenderConstants
    {
        public static class Codes
        {
            public const string Male = "M";
            public const string Female = "F";
            public const string Unclassified = "U";

            public const string Any = "U";
            public const string Unknown = "U";
            public const string NonBinary = "X";
        }

        public static class Descriptions
        {
            public const string Male = "Male";
            public const string Female = "Female";
            public const string Unclassified = "Unclassified";
        }
    }

    public static class EmployeeProfileConstants
    {
        public static readonly ReadOnlyDictionary<string, string> SdlExemptionCompanyToEmployeeCodePairs = new(new Dictionary<string, string>() {
            { "EPP", "01" },
            { "Learner", "02" },
            { "MCE", "03" },
            { "None", "04" },
            { "PSG", "05" },
            { "RPB", "06" },
            { "SELB", "07" },
            { "NE", "08" }
        });
    }

    public static class SenegalConstants
    {
        public static class SuspensionCustomFieldCodes
        {
            public const string Anntax = "ANNTAX";
        }
    }
}