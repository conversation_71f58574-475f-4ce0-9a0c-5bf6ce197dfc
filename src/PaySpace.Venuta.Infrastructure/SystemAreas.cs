namespace PaySpace.Venuta.Infrastructure
{
    public static class SystemAreas
    {
        public static class MultiSearch
        {
            public const string Area = "System.MultiSearch";
        }

        public static class Widgets
        {
            public static class Keys
            {
                public const string Leave = "leaveWidgetContainer";
                public const string Payslips = "payslipWidgetContainer";
                public const string Claims = "claimsWidgetContainer";
                public const string Inbox = "inboxWidgetContainer";
                public const string Teams = "teamsWidgetContainer";
                public const string Documents = "documentWidgetContainer";
                public const string Evaluations = "evalHistoryWidgetContainer";
                public const string Statistics = "StatisticsWidgetContainer";
                public const string Movement = "EmployeeMovementWidgetContainer";
            }
        }

        public static class Audit
        {
            public const string AreaPrefix = "Audit.";
        }

        public static class Notification
        {
            public const string Area = "System.Notification";

            public static class Keys
            {
                public const string CreateFailure = "Create.Failure";
                public const string CreateSuccess = "Create.Success";
                public const string DeleteFailure = "Delete.Failure";
                public const string DeleteSuccess = "Delete.Success";
                public const string GenericFailure = "Generic.Failure";
                public const string GenericSuccess = "Generic.Success";
                public const string GenericProcessing = "Generic.Processing";
                public const string UpdateFailure = "Update.Failure";
                public const string UpdateSuccess = "Update.Success";
                public const string CompanyRunUnavailable = "Company.Run.Unavailable";
                public const string CompanyFrequencyRequired = "Company.Frequency.Required";
                public const string PayRateRequired = "PayRate.Required";
                public const string UserTypeSwitchTitle = "User.Type.Change.Title";
                public const string UserTypeSwitchEss = "User.Type.Change.Ess";
                public const string UserTypeSwitchCss = "User.Type.Change.Css";
            }
        }

        public static class Account
        {
            public const string Login = "Login.Process";
            public const string Register = "Login.Register";
            public const string Reset = "Forgotten.Password";
            public const string ChangePassword = "Change.Password";
            public const string Access = "Access.Denied";
            public const string PaymentModule = "PaymentModule";
            public const string OptIn = "OptIn";
        }

        public static class Mfa
        {
            public const string Otp = "Mfa.Otp";
            public const string Setup = "Mfa.Setup";

            public static class Keys
            {
                public const string SmsOtp = "SmsOtp";
                public const string EmailHasBeenSent = "EmailOtpSent";
                public const string SmsHasBeenSent = "SmsOtpSent";
                public const string GoogleAuthShouldBeUsed = "UseGoogleOtp";
                public const string IncorrectOtp = "IncorrectOtp";
            }
        }

        public static class Leave
        {
            public const string Application = "Leave.Application";
            public const string Adjustment = "Leave.Adjustment";
            public const string Balances = "Leave.Balances";
            public const string Setup = "Leave.Setup";

            public const string LeaveExpiryEmail = "Leave.Expiry.Email";
            public const string LeaveNotificationEmail = "Leave.Notification.Email";

            public static class Keys
            {
                public const string LeavePriority = "0008";
                public const string Expire = "Expire";
                public const string ExpireHours = "ExpireHours";
                public const string Move = "Move";
                public const string LeaveApplicationPage = "LeaveApplicationPage";
                public const string LeaveSetupPage = "LeaveSetupPage";
                public const string LeaveValue = "HeaderTextAnnual";
                public const string LeaveValueCalculation = "HeaderText1";
                public const string LeaveAdjustmentAttachments = "lblAttachment";
                public const string LeaveCancellation = "leaveCancellation";
                public const string LeaveDays = "lblDays";
                public const string LeaveHours = "lblHours";

                public const string LeaveType = "lblLeaveType";
                public const string Description = "lblDescription";
                public const string ExpiryDate = "lblExpiryDate";
                public const string NumberOfDays = "lblNumbOfDays";
            }
        }

        public static class Payslip
        {
            public const string Area = "Payslips";
            public const string View = "Payslip.View";
            public const string Edit = "Payslip.Edit";
            public const string Compare = "Payslip.Compare";
            public const string AdvancedEdit = "Payslip.EditAdvanced";

            public static class Keys
            {
                public const string Email = "Email.PasswordBirthday";
            }
        }

        public static class SalaryPaymentFiles
        {
            public const string Area = "SalaryPaymentFiles";
        }

        public static class TaxCertificates
        {
            public const string View = "Tax.Certificates";
        }

        public static class EmployeeDashboard
        {
            public const string Area = "Dashboard";
        }

        public static class CompanyDashboard
        {
            public const string Area = "CompanyDashboard";
        }

        public static class GeneralLedger
        {
            public const string Area = "Reports.GeneralLedger";
        }

        public static class Calculation
        {
            public const string Area = "Payroll.Calculation";
        }

        public static class Subordinate
        {
            public const string Area = "Subordinate";
        }

        public static class Navigation
        {
            public const string Area = "Navigation";
            public const string ElasticSearch = "Navigation.ElasticSearch";
        }

        public static class Workflow
        {
            public const string Area = "Workflow";
            public const string Status = "Workflow.Status";

            public static class Keys
            {
                public const string Email = "Workflow.Email";
                public const string NoApproversNotFound = "0002";
            }
        }

        public static class WorkflowStepActivity
        {
            public const string Area = "Workflow.StepActivity";

            public static class Keys
            {
                public const string Previous = "Previous";
                public const string Next = "Next";
                public const string Finish = "Finish";

                public const string EmployeeProfileRequired = "0001";
                public const string EmploymentStatusRequired = "0002";
                public const string EmployeePayRateRequired = "0003";
                public const string EmployeeBankingDetailsRequired = "0004";
                public const string YearToDateRequired = "0005";
                public const string DependantsRequired = "0006";
                public const string PositionsRequired = "0007";
                public const string LeaveSetupRequired = "0008";
                public const string ProjectRequired = "0009";
                public const string AttachmentsRequired = "0010";
                public const string NotesRequired = "0011";
                public const string DynamicHistoricalInformationRequired = "0012";
                public const string RecurringComponentRequired = "0013";
                public const string CustomFormsRequired = "0014";

                public static class PageInfo
                {
                    public const string Page = "PageInfo.Page";
                    public const string Of = "PageInfo.Of";
                }

                public static class NewEngagement
                {
                    public const string Subject = "NewEngagement.Subject";
                    public const string Message = "NewEngagement.Message";
                    public const string NoGuidOrCodeCombinationFound = "0001";
                }
            }
        }

        public static class Profile
        {
            public const string Basic = "Profile.Basic";
            public const string BasicCreate = "Profile.Basic.Create";
            public const string Address = "Profile.Address";
            public const string Dependants = "Profile.Dependants";
            public const string Attachments = "Profile.Attachments";
            public const string Positions = "Profile.Positions";
            public const string Notifications = "Profile.Notification";
            public const string Notes = "Profile.Notes";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string OutOfOffice = "outOfOfficeTab";
                public const string OnBehalfOf = "onBehalfOfTab";
                public const string AttachmentPage = "attachmentPage";
                public const string DeleteAttachment = "deleteAttachment";
                public const string DependantsCheckBox = "EmployeeDependantsCheckBox";
                public const string DeleteDependant = "DeleteDependant";
                public const string Image = "txtimage";
                public const string BankDetailsPage = "EmployeeBankingDetailsPage";
                public const string CreateEmployee = "CreateEmployee";
                public const string CustomFields = "CustomFields";
            }
        }

        public static class OnBehalfOf
        {
            public const string Area = "OnBehalfOf";
        }

        public static class OutOfOffice
        {
            public const string Area = "OutOfOffice";
        }

        public static class Termination
        {
            public const string Suspension = "Termination.Suspension";

            public static class Keys
            {
                public const string EmployeeSuspensionPage = "EmployeeSuspensionPage";
                public const string DeleteSuspension = "DeleteSuspension";
            }
        }

        public static class OrganizationRegions
        {
            public const string Area = "Regions";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string Description = "lblDescription";

                public const string EffectiveDate = "lblEffectiveDate";
                public const string RegionCode = "lblRegionCode";
                public const string RegionCodeMaxLengthError = "lblRegionCodeMaxLength";
                public const string RegionCodeRequiredError = "lblRegionCodeRequired";
                public const string RegionCodeExistError = "lblRegionCodeExist";
                public const string EffectiveDateGreaterThanInactiveDateError = "lblEffectiveDateGreaterThanInactiveDateError";
                public const string DescriptionRequiredError = "err.DescriptionRequired";
                public const string DescriptionMaxLengthError = "err.DescriptionMaxLength";
                public const string PositionsExistError = "err.PositionsExist";
                public const string RegionsExistError = "err.RegionsExist";
                public const string JobsExistError = "err.JobsExist";
                public const string RegionHistoryExistError = "lblRegionHistoryExist";
            }
        }

        public static class CompanyTableBuilder
        {
            public const string Area = "CompanyTableBuilder";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string Description = "lblDescription";
                public const string Code = "lblCode";
                public const string DescriptionRequiredError = "DescriptionRequiredError";
                public const string CodeExitsError = "lblCodeExitsError";
                public const string NameExitsError = "lblNameExitsError";
                public const string DeleteError = "lblDeleteError";
                public const string StabilityTooltip = "lblStabilityTooltip";
                public const string IsDefault = "lblDefault";
                public const string SetAsDefault = "lblSetDefault";
                public const string DuplicateDefaultError = "lblDuplicateDefaultError";
                public const string OnlyQualifyingSchemeCanDefault = "lblQualifyingSchemeDefault";
            }
        }

        public static class CompanyRoster
        {
            public const string Area = "Roster";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string Name = "lblName";
                public const string EffectiveDate = "lblEffectiveDate";

                public const string NameRequiredError = "err.NameRequiredError";
                public const string NameMaxLengthError = "err.NameMaxLength";
                public const string PositionsExistError = "err.PositionsExist";
                public const string NameExistError = "err.NameExist";
                public const string InvalidCharactersError = "err.NameInvalid";
            }
        }

        public static class CompanyGroupLink
        {
            public const string Area = "CompanyGroupLink";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string Name = "lblName";
                public const string MoveToGroup = "lblMoveToGroup";
                public const string RemoveFromGroup = "lblRemoveFromGroup";
                public const string NewGroup = "lblNewGroup";
                public const string SearchGroup = "lblSearchGroup";
                public const string SelectUsers = "lblSelectUsers";
                public const string Action = "lblAction";
                public const string Submit = "btnSubmit";
                public const string Confirm = "btnConfirm";
                public const string Cancel = "btnCancel";
                public const string ConfirmationText = "ConfirmationText";
                public const string ConfirmationTextWarning = "ConfirmationTextWarning";
            }
        }

        public static class CompanyWorkflowRole
        {
            public const string Area = "Company.Workflow.Role";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string NewCompanyWorkflowRoleButton = "btnNewCompanyWorkflowRole";

                public const string Code = "lblCode";
                public const string Description = "lblDescription";

                public const string CodeDuplicateError = "errCodeDuplicateExists";
                public const string CodeLengthError = "errCodeLength";
                public const string DescriptionDuplicateError = "errDescriptionDuplicateExists";
                public const string DescriptionLengthError = "errDescriptionLength";

                public const string EmployeePositionExistsError = "errEmployeePositionExists";
            }
        }

        public static class CompanyExternalQuickLink
        {
            public const string Area = "CompanyExternalQuickLink";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string NewCompanyExternalQuickLinkButton = "btnNewCompanyExternalQuickLink";

                public const string ExternalUrl = "lblExternalUrl";
                public const string DisplayText = "lblDisplayText";

                public const string DisplayTextDuplicateError = "errDisplayTextDuplicateExists";
                public const string ExternalUrlDuplicateError = "errExternalUrlDuplicateExists";
                public const string DuplicateEntryError = "errDuplicateEntry";

                public const string ExternalUrlInvalidError = "errExternalUrlInvalidUrl";
                public const string ExternalUrlInvalid = "errExternalUrlInvalid";
            }
        }

        public static class CustomFields
        {
            public const string Area = "CustomFields";
            public const string BureauArea = "Bureau.CustomFields";

            public static class CustomFieldEntity
            {
                public const int TableBuilder = 14;
                public const int CustomForm = 19;
            }

            public static class Keys
            {
                public const string TabHeader = "lblTabHeader";
                public const string GroupTabHeader = "lblGroupTabHeader";
                public const string Field = "customFields";
                public const string NoEntitySelected = "0001";
                public const string FieldLabelRequired = "0003";
                public const string FieldCodeRequired = "0004";
                public const string UniqueOptions = "0005";
                public const string NotAllowedToDelete = "0006";
                public const string UniqueSystemFieldLabels = "0014";
                public const string DescriptionForEditorOptionRequired = "lblDescriptionForEditorOptionRequired";
                public const string FieldCodeMaxLength = "lblFieldCodeMaxLength";
                public const string FieldLabelMaxLength = "lblFieldLabelMaxLength";
                public const string ValidationExprMaxLength = "lblValidationExprMaxLength";
                public const string ValidationRuleMaxLength = "lblValidationRuleMaxLength";
                public const string IsEmployeeOrIsCompanyRequired = "lblIsEmployeeOrIsCompanyRequired";
                public const string FieldInUseDeleteError = "lblFieldInUseDeleteError";
                public const string CustomFormFieldUsedInCalc = "lblCustomFormFieldUsedInCalc";
                public const string CannotDeleteFieldWithDependencies = "lblCannotDeleteFieldWithDependencies";
                public const string InvalidFieldCode = "lblFieldCodeErrorMsg";
                public const string FieldTooltipMaxLength = "FieldTooltipMaxLength";

                public const string FieldLabelAlreadyExist = "errFieldLabelExists";
                public const string FieldCodeAlreadyExist = "errFieldCodeExists";

                public const string ReservedFieldCodeInUse = "errReservedFieldCodeInUse";

                public const string CountryLegislativeFields = "lblCountryLegislativeFields";
                public const string DuplicateFieldLabel = "errDuplicateFieldLabel";
                public const string DuplicateFieldCode = "errDuplicateFieldCode";
                public const string DuplicateFieldGroupName = "errDuplicateFieldGroupName";
                public const string CannotDeleteFieldGroupWithDependencies = "errCannotDeleteFieldGroupWithDependencies";
                public const string CustomFields = "lblCustomFields";
                public const string ChildLookupOptionsEmpty = "errChildLookupOptionsEmpty";
                public const string LookupOptionsEmpty = "errLookupOptionsEmpty";
                public const string ParentHasMultipleChildren = "lblParentHasMultipleChildren";
                public const string LookupHasChildren = "errLookupHasChildren";
                public const string ChildrenHaveMultipleOptions = "lblChildrenHaveMultipleOptions";
                public const string DataGridMultipleOptions = "lblDataGridMultipleOptions";
                public const string CustomFieldIncorrectFormat = "CustomFieldIncorrectFormat";
            }
        }

        public static class CustomForms
        {
            public static class Bureau
            {
                public const string Area = "CustomForms.Bureau";
                public const string PageHeader = "lblPageHeader";
            }

            public static class CompanyConfig
            {
                public const string Area = "CustomForms.Company.Config";
                public const string PageHeader = "lblPageHeader";
            }

            public static class Company
            {
                public const string Area = "CustomForms.Company";
                public const string PageHeader = "lblPageHeader";
            }

            public static class ConfigKeys
            {
                public const string CountryCustomFormsPage = "CountryCustomForms";
                public const string CustomFormsBreadcrumb = "CountryCustomBreadcrumb";
                public const string TabCountryCustomFormGroups = "tabFormGroups";
                public const string TabCountryCustomFormItems = "tabFormItems";

                public const string PageHeader = "lblPageHeader";
                public const string AddCustomFormRecord = "btnNewCustomForm";
                public const string AddCustomFieldRecord = "btnNewCustomField";

                public const string Country = "lblCountry";
                public const string CompanyLevel = "lblCompanyLevel";
                public const string EmployeeLevel = "lblEmployeeLevel";
                public const string CollectionName = "lblFormName";
                public const string CollectionCode = "lblCollectionCode";
                public const string AllowEss = "lblAllowEmpEss";
                public const string FormType = "lblFormType";
                public const string Required = "lblRequired";
                public const string ShouldCalculate = "lblShouldCalculate";
                public const string GroupTabHeader = "lblGroupTabHeader";
                public const string CustomFormScreenType = "lblCustomFormScreenType";
                public const string CustomFormEmployeeScreenType = "lblCustomFormEmployeeScreenType";
                public const string CustomFormCompanyScreenType = "lblCustomFormCompanyScreenType";
                public const string CustomFormHideFromSecurityRoles = "lblCustomFormHideFromSecurityRole";
                public const string HideEffectiveDate = "lblHideEffDate";
                public const string ShowPayrollCycle = "ShowPayrollCycle";

                public const string CountryRequiredError = "lblCountryRequired";
                public const string FormNameRequiredError = "lblFormNameRequired";
                public const string CodeRequiredError = "lblCodeRequired";
                public const string FormNameMaxLengthError = "lblFormNameMaxLengthError";
                public const string CodeMaxLengthError = "lblCodeMaxLengthError";
                public const string DuplicateFormsError = "errDuplicateForms";
                public const string CompanyRequired = "lblCompanyRequired";
                public const string FormNameExists = "errFormNameExists";
                public const string CodeExists = "errCodeExists";
                public const string EmployeeLevelRequired = "errEmployeeLevelRequiredError";
                public const string FormLevelIsRequired = "errLevelRequiredError";
                public const string FormTypeIsRequired = "errFormTypeRequired";
                public const string ScreenIsRequired = "errScreenRequired";
                public const string SpecialCharactersNotAllowedForCode = "lblSpecialCharactersNotAllowedForCode";
                public const string CodeExistsOnCompanyOrBureauCollection = "lblCodeExistsOnCompanyOrBureauCollection";
                public const string CodeExistsOnTableBuilder = "lblCodeExistsOnTableBuilder";
                public const string CustomFormDependencyError = "errCustomFormDependency";
                public const string CustomFormScreenTypeDependencyError = "errCustomFormScreenEditDependency";
                public const string CustomFormIncorrectFormat = "CustomFormIncorrectFormat";
            }

            public static class Keys
            {
                public const string Field = "customForms";
                public const string EffectiveDateRequired = "lblEffectiveDateRequired";
                public const string EffectiveDateDuplicated = "001";
                public const string CategoryNotValidForForm = "lblCategoryNotValidForForm";
                public const string LibraryNoEffectiveDate = "errLibNoEffDate";
                public const string InvalidProjectCode = "errInvalidProjectCode";
                public const string CodeRequired = "lblCodeRequired";
                public const string CodeMaxLength = "lblCodeMaxLength";
                public const string DescriptionRequired = "lblDescriptionRequired";
                public const string DescriptionMaxLength = "lblDescriptionMaxLength";
                public const string RepositoryDuplicated = "lblRepositoryDuplicated";
                public const string EffectiveDateBeforeEmploymentDateError = "errEffectiveDateBeforeEmploymentDate";
                public const string EffectiveDateMustMatchParent = "errEffectiveDateMustMatchParent";
                public const string IsOpenRun = "IsOpenRun";
                public const string CannotModifyValuesInClosedRun = "CannotModifyValuesInClosedRun";
                public const string CannotDeleteValueInClosedRun = "CannotDeleteValueInClosedRun";
                public const string CompanyRunRequired = "CompanyRunRequired";
            }
        }

        public static class TableBuilderCategory
        {
            public const string Area = "TableBuilderCategory";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string GroupTabHeader = "lblGroupTabHeader";
                public const string FieldsHeader = "lblCustomFieldsHeader";
                public const string CategoryHeader = "lblCategoryHeader";
                public const string Name = "lblName";
                public const string NameRequiredError = "lblNameRequiredError";
                public const string NameMaxLengthError = "lblNameMaxLengthError";
                public const string NameExistError = "lblNameExistError";
                public const string Country = "lblCountry";
                public const string CountryRequiredError = "lblCountryRequiredError";
                public const string CodeRequiredError = "lblCodeRequiredError";
                public const string CodeMaxLengthError = "lblCodeMaxLengthError";
                public const string CodeExistError = "lblCodeExistError";
                public const string CodeExistOnCompanyCollectionError = "lblCodeExistOnCompanyCollectionError";
                public const string CodeExistOnBureauCollectionError = "lblCodeExistOnBureauCollectionError";
                public const string CategoryInUseDeleteError = "lblCategoryInUseDeleteError";
            }
        }

        public static class Settings
        {
            public const string Area = "User.Settings";
        }

        public static class User
        {
            public const string Area = "User";
        }

        public static class Claims
        {
            public const string Capture = "Claims.Capture";
        }

        public static class Inbox
        {
            public const string Area = "Inbox";

            public static class Keys
            {
                public const string Subject = "Subject";
                public const string LeaveCancellation = "lblLeaveCancellation";
            }
        }

        public static class Calendar
        {
            public const string Area = "Calendar";
        }

        public static class Error
        {
            public const string Area = "Error";
        }

        public static class Contact
        {
            public const string Area = "Contact";

            public static class Keys
            {
                public const string Page = "employeePublicProfilePage";
                public const string BirthDateWidget = "aboutWidgetBirthDate";
            }
        }

        public static class DynamicHistoricalInformation
        {
            public const string Area = "DynamicHistoricalInformation";

            public static class Keys
            {
                public const string Page = "dynamicHistoricalInformation";
            }
        }

        public static class ActionType
        {
            public const string Area = "ActionType";
        }

        public static class AssetRegister
        {
            public const string Area = "AssetRegister";

            public static class Keys
            {
                public const string EmployeeAssetRegisterPage = "EmployeeAssetRegisterPage";
            }
        }

        public static class ChangeRequest
        {
            public const string Area = "ChangeRequest";
        }

        public static class EmployeeEtiTakeOn
        {
            public const string Area = "EmployeeEtiTakeOn";

            public static class Keys
            {
                public const string BulkUploadHeader = "lblBulkUploadHeader";
                public const string EmployeeNumber = "lblEmployeeNumber";
                public const string MonthlyRemuneration = "lblMonthlyRemuneration";
                public const string MonthlyMinimumWage = "lblMonthlyMinimumWage";
                public const string WagePaid = "lblWagePaid";
                public const string EtiHours = "lblEtiHours";
                public const string EtiValue = "lblEtiValue";
                public const string PeriodCode = "lblPeriodCode";
                public const string EtiPeriodCount = "lblEtiPeriodCount";

                public const string EmployeeIdRequiredError = "lblEmployeeIdRequiredError";
                public const string PeriodCodeRequiredError = "lblPeriodCodeRequiredError";
                public const string ComponentIdRequiredError = "lblComponentIdRequiredError";
                public const string MonthlyRemunerationNegativeValueError = "lblMonthlyRemunerationNegativeValueError";
                public const string MonthlyMinimumWageNegativeValueError = "lblMonthlyMinimumWageNegativeValueError";
                public const string WagePaidNegativeValueError = "lblWagePaidNegativeValueError";
                public const string EtiHoursNegativeValueError = "lblEtiHoursNegativeValueError";
                public const string EtiValueNegativeValueError = "lblEtiValueNegativeValueError";
                public const string NoEtiComponentFoundError = "lblNoEtiComponentFoundError";
                public const string EtiTakeOnPeriodCodeGroupJoinDateError = "lblEtiTakeOnPeriodCodeGroupJoinDateError";
                public const string EtiTakeOnPeriodCodeTerminationDateError = "lblEtiTakeOnPeriodCodeTerminationDateError";
                public const string EtiTakeOnExistsError = "lblEtiTakeOnExistsError";
                public const string EtiValueGreaterThanEtiClaimMaxError = "lblEtiValueGreaterThanEtiClaimMaxError";
                public const string MonthlyRemunerationGreaterThanEtiRangeLimitThreeError = "lblMonthlyRemGreaterThanEtiRangeLimitThreeError";
                public const string EtiPeriodCountMaxError = "lblEtiPeriodCountMaxError";
                public const string EmployeeNotEmployedYet = "lblEmployeeNotEmployedYet";
                public const string MtdValueNegativeError = "lblMtdValueNegativeError";
            }
        }

        public static class YearToDateTakeOn
        {
            public const string Area = "YearToDateTakeOn";

            public static class Keys
            {
                public const string YearToDateTakeOnPage = "YearToDateTakeOnPage";
                public const string PageHeader = "lblPageHeader";
                public const string EmployeeNumber = "lblEmployeeNumber";
                public const string ComponentDescription = "ComponentDescription";
                public const string ComponentCodeOrDescription = "lblComponentCodeOrDescription";
                public const string TaxCode = "TaxCode";
                public const string ComponentValue = "ComponentValue";

                public const string TerminationDateError = "lblTerminationDateError";
                public const string EmploymentStatusError = "lblEmploymentStatusError";
                public const string FutureEmploymentStatusError = "lblFutureEmploymentStatusError";
                public const string NoTakeOnRunFoundError = "lblNoTakeOnRunFoundError";
                public const string TakeOnRunClosedError = "lblTakeOnRunClosedError";
                public const string TaxYearStartDateError = "lblTaxYearStartDateError";
                public const string CarNoteRequired = "CarNoteRequired";
                public const string TaxCodeRequired = "TaxCodeRequired";
                public const string TaxCodeBothEnteredOneRequired = "TaxCodeBothEnteredOneRequired";
                public const string SpecialComponentRequired = "SpecialComponentRequired";
                public const string TaxCodeAndSpecialComponentRequired = "TaxCodeAndSpecialComponentRequired";
                public const string TaxCodeAndAdditionalTaxCodeRequired = "TaxCodeAndAdditionalTaxCodeRequired";
                public const string BargainingEmployerContributionRequired = "errBargainingEmployerContributionRequired";
                public const string BargainingFringeBenefitRequired = "errBargainingFringeBenefitRequired";
                public const string EmployeeNotEmployedYet = "lblEmployeeNotEmployedYet";
            }
        }

        public static class Performance
        {
            public const string Journal = "Performance.Journal";
            public const string Evaluation = "EmployeeEvaluation";
            public const string History = "EmployeeEvaluationHistory";
            public const string EmployeeKPIs = "Performance.EvaluationKPIs";
            public const string HistoryWidget = "Performance.History.Widget";

            public static class Keys
            {
                public const string Attachment = "lblAttachment";
                public const string EmployeeKPIsPage = "employeeEvaluationKPIs";
                public const string NotifyApprover = "lblNotifyApprover";
            }
        }

        public static class EmployeeReviewDefaultRaters
        {
            public const string Area = "EmployeeReviewDefaultRaters";

            public static class Keys
            {
                public const string EmployeeReviewDefaultRatersHeader = "lblEmployeeReviewDefaultRatersHeader";
            }
        }

        public static class Payroll
        {
            public const string Errors = "Payroll.Errors";

            public static class PayRates
            {
                public const string Area = "Payroll.PayRates";

                public static class Keys
                {
                    public const string Package = "lblPackage";
                }
            }

            public static class Retro
            {
                public static class Trigger
                {
                    public const string Area = "Payroll.Retro.Trigger";
                }
            }
        }

        public static class TrainingRecord
        {
            public const string Area = "TrainingRecord";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string AddNewTrainingRecord = "lblNewRecord";

                public const string EmployeeTrainingRecordPage = "EmployeeTrainingRecordPage";
                public const string EmployeeTrainingRecordEmployeeRateField = "EmployeeTrainingRecordEmployeeRateField";

                public const string EffectiveDate = "lblEffectiveDate";
                public const string EndDate = "lblEndDate";
                public const string Course = "lblCourse";
                public const string TrainingStatus = "lblTrainingStatus";
                public const string TrainingNotCompleteStatus = "lblTrainingNotCompleteStatus";
                public const string CourseResult = "lblCourseResult";
                public const string DirectCourseCost = "lblDirectCourseCost";
                public const string InvoiceNumber = "lblInvoiceNumber";
                public const string Facilitator = "lblFacilitator";
                public const string Assessor = "lblAssessor";
                public const string CertificateNo = "lblCertificateNo";
                public const string CertificateExpiryDate = "lblCertificateExpiryDate";
                public const string TravelCost = "lblTravelCost";
                public const string Accommodation = "lblAccommodation";
                public const string FacilitatorCost = "lblFacilitatorCost";
                public const string FoodBeverage = "lblFoodBeverage";
                public const string Additional5 = "lblAdditional5";
                public const string Additional6 = "lblAdditional6";
                public const string Notes = "lblNotes";
                public const string Attachment = "lblAttachment";
                public const string AttachmentName = "lblAttachmentName";
                public const string AddReminder = "lblAddReminder";

                public const string CourseRequiredError = "lblCourseRequired";
                public const string StatusRequiredError = "lblStatusRequired";
                public const string TrainingNotCompleteStatusRequiredError = "lblTrainingNotCompleteStatus";
                public const string TrainingNotCompleteStatusEmptyError = "lblTrainingNotCompleteStatusEmpty";
                public const string CertificateMaxLengthError = "lblCertificateMaxLength";
                public const string InvoiceNumberMaxLengthError = "lblInvoiceNumberMaxLengthError";
                public const string FacilitatorMaxLengthError = "lblFacilitatorMaxLengthError";
                public const string AssessorMaxLengthError = "lblAssessorMaxLengthError";
                public const string NotesMaxLengthError = "lblNotesMaxLength";
                public const string EffectiveDateRequiredError = "lblEffectiveDateRequired";
                public const string EndDateBeforeStartError = "lblEndDateBeforeStart";

                public const string AccomodationMaxLengthError = "lblAccomodationMaxLengthError";
                public const string Additional5MaxLengthError = "lblAdditional5MaxLengthError";
                public const string Additional6MaxLengthError = "lblAdditional6MaxLengthError";
                public const string CourseResultMaxLengthError = "lblCourseResultMaxLengthError";
                public const string DirectCourseCostMaxLengthError = "lblDirectCourseCostMaxLengthError";
                public const string EmployeeRateMaxLengthError = "lblEmployeeRateMaxLengthError";
                public const string FacilitatorCostMaxLengthError = "lblFacilitatorCostMaxLengthError";
                public const string FoodBeverageMaxLengthError = "lblFoodBeverageMaxLengthError";
                public const string TravelCostMaxLengthError = "lblTravelCostMaxLengthError";
                public const string CourseInactive = "lblCourseInactive";
            }
        }

        public static class Sidebar
        {
            public const string Area = "Sidebar";
        }

        public static class EmploymentStatus
        {
            public const string Area = "EmploymentStatus";
            public const string PageHeader = "lblPageHeader";

            public static class SecurableKeys
            {
                public const string Page = "EmploymentStatusPage";
            }

            public static class Constants
            {
                public static class NatureOfPersonCodes
                {
                    public const string Refugee = "R";
                    public const string RefugeeDescription = "R - Refugee";
                    public const string IndividualWithNIENnumber = "B - Individual with NIE number";
                    public const string IndividualWithDNINumber = "A - Individual with DNI number";
                }

                public static class IdentityCodes
                {
                    public const string Refugee = "Refugee";
                    public const string ID = "ID";
                    public const string NIE = "NIE";
                }
            }

            public static class LocaleStrings
            {
                public const string PageHeader = "lblPageHeader";
                public const string Detail = "lblDetail";
                public const string History = "lblHistory";

                public const string ActionsGroup = "lblActionsGroup";
                public const string TerminationGroup = "lblTerminationGroup";
                public const string IdentificationGroup = "lblIdentificationGroup";
                public const string TaxGroup = "lblTaxGroup";

                public const string Termination = "lblTermination";
                public const string ReInstateNewRecord = "lblReInstateNewRecord";
                public const string ReInstateOldRecord = "lblReInstateOldRecord";
                public const string Percent = "lblPercent";
                public const string Amount = "lblAmount";

                public const string Submit = "lblSubmit";

                public const string CompanyRun = "lblCompanyRun";
                public const string Edit = "lblEdit";

                public const string CustomDate1 = "lblCustomDate1";
                public const string CustomDate2 = "lblCustomDate2";

                public const string DeleteExistingPayslipsError = "lblDeleteExistingPayslipsError";
                public const string DeleteNotLatestError = "lblDeleteNotLatestError";
                public const string InvalidPercentageError = "lblInvalidPercentageError";

                public const string JobActiveWarning = "lblJobActiveWarning";

                public const string IR21ReminderTitle = "IR21ReminderTitle";
                public const string IR21ReminderWarningMessage = "IR21ReminderWarningMessage";

                public const string IdNumberExpiryDateRequired = "lblIdNumberExpiryDateRequired";
                public const string SocialSecurityNumberRequired = "SocialSecurityNumberRequired";
                public const string TaxReferenceNumberRequired = "TaxReferenceNumberRequired";
                public const string TaxReferenceNumberAssigned = "errTaxReferenceNumberAssigned";
            }
        }

        public static class Position
        {
            public const string OrganizationPositions = "OrganizationPositions";

            public static class SecurableKeys
            {
                public const string Page = "Profile.Positions";
            }
        }

        public static class OrganizationPosition
        {
            public const string Area = "Position.OrganizationPositions";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
            }
        }

        public static class Notes
        {
            public const string Area = "Profile.Notes";

            public static class SecurableKeys
            {
                public const string NotesPage = "NotesPage";
            }
        }

        public static class BankDetail
        {
            public const string Area = "BankDetail";

            public static class Constants
            {
                public const string MainAccountOverLabel = "lblOverrideMainAccount";
                public const string AccountNoLabel = "lblBankAccountNo";

                public static class BankName
                {
                    public const string Other = "Other";
                }
            }
        }

        public static class Attachment
        {
            public const string Area = "General.Attachments";
        }

        public static class AdvancedSearch
        {
            public const string Area = "Advanced.Search";
        }

        public static class Dependants
        {
            public const string Area = "Profile.Dependants";

            public static class Keys
            {
                public const string DependantsPage = "DependantsPage";
                public const string DeleteDependant = "DeleteDependant";
                public const string DependantsCheckBox = "EmployeeDependantsCheckBox";

                public const string QuickAddPageHeader = "lblPageHeaderDependantsQuickAdd";
            }
        }

        public static class CompanyDto
        {
            public const string Area = "CompanyDto";
        }

        public static class Company
        {
            public const string Area = "Company";

            public const string AddressArea = "CompanyAddress";

            public static class Keys
            {
                public const string CustomFields = "customFields";
                public const string CalculationMethodOptions = "CalculationMethodOptions";
            }
        }

        public static class AddCompany
        {
            public const string Area = "AddCompany";

            public static class Keys
            {
                public const string AddNew = "AddNew";
            }
        }

        public static class CompanyRun
        {
            public const string Area = "CompanyRun";

            public static class Keys
            {
                public const string BureauPageHeader = "BureauPageHeader";
                public const string PageHeader = "PageHeader";
            }
        }

        public static class BureauCompanyRun
        {
            public const string Area = "BureauCompanyRun";
        }

        public static class Project
        {
            public const string Area = "Project";
        }

        public static class BulkUpload
        {
            public const string Area = "BulkUpload";

            public static class Keys
            {
                public const string DuplicateUploadError = "lblDuplicateUploadError";
                public const string DuplicateComponents = "DuplicateComponentsError";
                public const string TooManyErrors = "lblTooManyErrors";
                public const string BasicPayUploadNotAllowed = "lblBasicPayUploadNotAllowed";
                public const string StatutoryOrAutoComponentUploadNotAllowed = "lblStatutoryOrAutoComponentUploadNotAllowed";

                public const string True = "TRUE";
                public const string False = "FALSE";

                public const string ParentPrefix = nameof(ParentPrefix);
            }
        }

        public static class MenuItems
        {
            public const string Area = "Menu.Items";
        }

        public static class RecurringComponent
        {
            public const string Area = "Payroll.Components";

            public static class Tables
            {
                public const string Base = "Components.Base";
                public const string BonusProvision = "Components.BonusProvision";
                public const string CompanyCar = "Components.CompanyCar";
                public const string Disability = "Components.Disability";
                public const string EmployeeHousePaymentDetail = "Components.HousePayments";
                public const string EmployeeSaving = "Components.Saving";
                public const string FinancialHousePayment = "Components.FinancialHousePayments";
                public const string Garnishee = "Components.Garnishee";
                public const string GroupLife = "Components.GroupLife";
                public const string IncomeProtection = "Components.IncomeProtection";
                public const string MultiContractWork = "Components.MultiContractWork";
                public const string Loan = "Components.Loan";
                public const string Medical = "Components.Medical";
                public const string Pension = "Components.Pension";
                public const string RetirementAnnuity = "Components.RetirementAnnuity";
                public const string Travel = "Components.Travel";
                public const string Union = "Components.Union";
                public const string Alimony = "Components.Alimony";
                public const string TableBuilder = "Components.TableBuilder";
            }

            public static class Keys
            {
                public const string AccommodationDetailRequired = "lblAccomodationDetailRequired";
                public const string CustomField1Required = "lblCustomField1Required";
                public const string CustomField2Required = "lblCustomField2Required";
                public const string CustomField2SouthAfricaRequired = "lblCustomField2SouthAfricaRequired";
                public const string EmployeeDeductionRequired = "lblEmployeeDeductionRequired";
                public const string FloorAreaOrPropertyValueRequired = "lblFloorAreaOrPropertyValueRequired";
                public const string HotelAccommodationOptionRequired = "lblHotelAccommodationOptionRequired";
                public const string InvalidAccomodationDetailCombination = "lblInvalidAccomodationDetailCombination";
                public const string PropertyOwnershipRequired = "lblPropertyOwnershipRequired";
                public const string PropertyTypeRequired = "lblPropertyTypeRequired";
                public const string PropertyValueRequired = "PropertyValueRequired";
                public const string RatedRequired = "lblRatedRequired";
                public const string RemunerationProxyRequired = "lblRemunerationProxyRequired";
                public const string SwazilandAccommodationOptionAreaRequired = "lblSwazilandAccommodationOptionAreaRequired";
                public const string SwazilandAccommodationOptionRequired = "lblSwazilandAccommodationOptionRequired";
                public const string SwazilandPropertyValueRequired = "lblSwazilandPropertyValueRequired";
                public const string TableBuilderCodeRequired = "lblTableBuilderCodeRequired";
                public const string IncreaseDecreaseCannotHaveValueOnCreate = "errIncreaseDecreaseCannotHaveValueOnCreate";
                public const string IncreaseDecreaseGreaterThanZeroWhenBalanceIsZero = "errIncreaseDecreaseGreaterThanZeroWhenBalanceIsZero";
                public const string InitialBalanceRequired = "errInitialBalanceRequired";
            }
        }

        public static class Component
        {
            public const string Company = "CompanyComponent";
        }

        public static class BulkCaptureCode
        {
            public const string Area = "BulkCaptureCode";
        }

        public static class ExcelTips
        {
            public const string Area = "Excel.Tips";
        }

        public static class PayslipHeader
        {
            public const string Area = "PayslipHeader";
        }

        public static class EsocialDashboard
        {
            public const string Area = "Esocial.Dashboard";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string EsocialDashboardRedirectLink = "lblEsocialRedirectLink";
            }
        }

        public static class BusinessPartner
        {
            public const string Area = "BusinessPartner";

            public static class Keys
            {
                public const string BureauPage = "BureauPage";
                public const string AgencyTopLevelPage = "AgencyTopLevelPage";
            }
        }

        public static class Report
        {
            public const string Area = "ReportArea";

            public static class Keys
            {
                public const string Page = "ReportPage";
                public const string PayRollRegisterReport = "PayRollRegisterReport";
                public const string RunSelect = "lblRunSelect";
                public const string MonthSelect = "lblMonthSelect";
                public const string YtdSelect = "lblYtdSelect";
                public const string ActualPeriodSelect = "lblActualPeriodSelect";
                public const string ApprovedMonthSelect = "lblApprovedMonthSelect";
                public const string DifferenceSelect = "lblShowDifferenceDiff";
                public const string SumSelect = "lblShowSumDifferenceSum";
                public const string FormattedSelect = "lblDefaultFormatSelect";
                public const string NonFormattedSelect = "lblNonFormattedSelect";
                public const string BalancingToolFormatSelect = "lblBalancingToolFormatSelect";
                public const string FormatPerLineSelect = "lblFormatPerLine";
                public const string FormatPivotHorizontallySelect = "lblFormatPivotHorizontally";
                public const string AllEmployees = "lblAllEmployees";
                public const string AllLeaveTypes = "lblAllLeaveTypes";
                public const string AllLevels = "lblAllLevels";
                public const string AllPositions = "lblAllPositions";
                public const string AllRegions = "lblAllRegions";
                public const string AllPayslipActions = "lblAllPayslipActions";
                public const string AllComponents = "lblAllComponents";
                public const string AllFrequencies = "lblAllFrequencies";
                public const string AllCurrencies = "lblAllCurrencies";
                public const string EmployeeNumber = "lblEmployeeNumber";
                public const string LastName = "lblLastName";
                public const string Settings = "lblSettings";
                public const string Filters = "lblFilters";
                public const string Additional = "lblAdditional";
                public const string GenerationCompleted = "lblGenerationCompleted";
                public const string GenerationFailed = "lblGenerationFailed";
                public const string PreparingReport = "PreparingReport";
                public const string DownloadReady = "DownloadReady";
                public const string DateRange = "lblDateRange";
                public const string ReportCategoryRequired = "lblReportCategoryRequired";
                public const string TemplateDisplayNameRequired = "lblTemplateDisplayNameRequired";
                public const string DescriptionRequired = "lblDescriptionRequired";
                public const string ChangeDescriptionRequired = "lblChangeDescriptionRequired";
                public const string CustomReportNameExists = "lblCustomReportNameExists";
                public const string ValueRequired = "lblValueRequired";
                public const string CompanyGroup = "lblCompanyGroup";
                public const string Company = "lblCompany";
                public const string Frequency = "lblFrequency";
                public const string ReportViewType = "lblReportViewType";
                public const string ReportDesigner = "ReportDesigner";
                public const string CustomReports = "CustomReports";
                public const string ExtractFile = "ExtractFile";
                public const string BureauLevelReports = "BureauLevelReports";
            }
        }

        public static class ReportDataSourceBuilder
        {
            public const string Area = "Reports.DataSourceBuilder";

            public static class Keys
            {
                public const string ColumnsRequiredError = "err.ColumnsRequired";
                public const string EmployeeEntityRequiredError = "err.EmployeeEntityRequired";
                public const string TableNotConnectedError = "err.EntityNotConnected";
                public const string LikeNotSupportedError = "err.LikeNotSupported";
                public const string OneEntityAllowedError = "err.OneEntityAllowed";
                public const string ParameterNotInUse = "err.ParameterNotInUse";
                public const string ParameterDoesNotExist = "err.ParameterDoesNotExist";
                public const string DataSourceViewRequired = "err.DataSourceViewRequired";
                public const string FrequencyFilterTypeRequired = "err.FrequencyFilterTypeRequired";
                public const string SettingsPageHeader = "SettingsPageHeader";
                public const string ReportViewType = "ReportViewType";
                public const string CompanyGroupViewType = "IsCompanyViewAvailable";
                public const string CompanyViewType = "CompanyViewType";
                public const string FrequencyViewType = "CompanyViewType";
                public const string FrequencyFilterType = "FrequencyFilterType";
                public const string ShowRegions = "ShowRegionParameter";
                public const string ShowProjects = "ShowProjectsParameter";
                public const string ShowOrganisationPositions = "ShowOrganisationPositionParameter";
                public const string ShowOrganisationUnits = "ShowOrganisationUnitParameter";
                public const string ShowEmployeeNumber = "ShowEmployeeNumber";
                public const string ShowPayslipActions = "ShowPayslipActions";
                public const string ShowComponentCodes = "ShowComponentCodes";
                public const string EntityBasedParameters = "EntityBasedParametersHeader";
                public const string Save = "Save";
                public const string Cancel = "Cancel";
                public const string Proceed = "Proceed";
                public const string SelectParametersMessage = "SelectParametersMessage";
                public const string FrequencyParamNotSupported = "FrequencyParamNotSupported";
            }
        }

        public static class CustomReport
        {
            public const string Area = "Reports.CustomReport";

            public static class Keys
            {
                public const string NameRequiredError = "err.NameRequired";
                public const string NameExistsError = "err.NameExists";
            }
        }

        public static class OrganizationGroup
        {
            public const string Area = "OrganizationGroup";
        }

        public static class OrganizationUnit
        {
            public const string Area = "OrganizationUnit";

            public static class Keys
            {
                public const string InvalidOrganizationUnitDataDownloadError = "dlErr.InvalidOrganizationUnitData";
            }
        }

        public static class GeneralLedgerReport
        {
            public const string Area = "GeneralLedgerReport";
        }

        public static class Integrations
        {
            public const string Area = "Integrations";

            public static class Keys
            {
                public const string Page = "IntegrationsPage";
                public const string PageHeader = "lblPageHeader";

                public const string TabWebhooks = "tabWebhooks";
                public const string SecretKey = "lblSecretKey";
                public const string AddWebhookConfigurationButton = "btnAddWebhookConfiguration";
                public const string Name = "lblName";
                public const string MessageType = "lblMessageType";
                public const string ExposeToken = "lblExposeToken";
                public const string DestinationUrl = "lblDestinationUrl";
                public const string Enabled = "lblEnabled";
                public const string NameRequiredError = "err.NameRequired";
                public const string MessageTypeRequiredError = "err.MessageTypeRequired";
                public const string DestinationUrlRequiredError = "err.DestinationUrlRequired";
                public const string DuplicateConfigError = "err.DuplicateConfig";
                public const string DuplicateNameError = "err.DuplicateName";

                public const string ClientNameRequiredError = "err.ClientNameRequired";
                public const string ClientNameMaxLengthError = "err.ClientNameMaxLengthError";
                public const string DuplicateClientNameError = "err.DuplicateClientName";
                public const string ClientIdRequiredError = "err.ClientIdRequired";
                public const string ClientIdMaxLengthError = "err.ClientIdMaxLengthError";
                public const string DuplicateClientIdError = "err.DuplicateClientId";
                public const string DuplicateClientInvalidFormat = "err.ClientIdInvalidFormat";
                public const string UserRequiredError = "err.UserRequired";
                public const string InvalidUserError = "err.InvalidUser";

                public const string TabApi = "tabApi";
                public const string ClientId = "lblClientId";
                public const string ClientName = "lblClientName";
                public const string User = "lblUser";
                public const string ApiScopeType = "lblApiScopeType";
                public const string DevelopmentMode = "lblDevelopmentMode";
                public const string ClientSecret = "lblClientSecret";
                public const string ReadOnly = "lblGrantReadOnly";
                public const string FullAccess = "lblGrantFullAccess";

                public const string TabSingleSignOn = "tabSingleSignOn";
                public const string ExternalIdentityProvider = "lblExternalIdentityProvider";
            }
        }

        public static class EmployeeLumpSum
        {
            public const string Area = "EmployeeLumpSum";

            public static class Keys
            {
                public const string RecordCannotBeDeletedHasDependencies = "0001";
                public const string DirectiveAmountBillionValueError = "0002";
                public const string DirectiveTaxBillionValueError = "0003";
                public const string DuplicateLumpSumDirectiveForDirectiveNumber = "0004";
                public const string ValidateComponentExistOnPayslip = "0005";
                public const string ReferenceNumberOnlyNumeric = "0006";
                public const string TaxCodeRequired = "0007";
                public const string DirectiveAmountRequired = "0008";
                public const string DirectiveTaxRequired = "0009";
                public const string DirectiveNumberMaxLength = "0010";
                public const string PayslipDoesNotExist = "0011";
                public const string DirectiveIssuedDateRequired = "0012";
                public const string EmployeesCannotHaveMoreThanFiveDirectivesPerTaxYear = "0013";
                public const string DuplicateLumpSumDirectivesExistForIdentifier = "0014";
                public const string DirectiveNumberRequired = "0015";
                public const string ValidateDirectiveIssuedDateForCurrentTaxYear = "0016";
                public const string LumpSumDirectiveDoesNotExist = "0017";
                public const string ComponentDoesNotExistOnPayslip = "0018";
                public const string DirectiveAmountNegativeError = "0019";
                public const string DirectiveTaxNegativeError = "0020";
                public const string DirectiveReversalForPriorMonthError = "0021";
                public const string DirectiveReversalForPriorTaxYearError = "0022";
                public const string DirectiveNumberOnlyNumeric = "0023";
                public const string ComponentPriorToDirectiveIssuedDate = "0024";
                public const string EmployeesCannotHaveMoreThanFourDirectivesPerTaxYear = "0025";
                public const string SavingsWithdrawalBenefitCannotHaveMoreThanOneDirectivePerTaxYear = "0026";
                public const string SavingsWithdrawalBenefitMinimumAmountError = "0027";
                public const string TaxFreeDirectiveAmountRequired = "0028";
                public const string MissingDirectiveAmountForTaxCode = "errMissingDirectiveAmountForTaxCode";
                public const string ComponentExistOnReversalError = "errComponentExistOnPayslipForReversal";
                public const string AntedateSalaryPensionPriorToDirectiveIssuedDate = "errAntedateSalaryPensionPriorToDirectiveIssuedDate";
            }
        }

        public static class CompanyEmploymentEquity
        {
            public const string Area = "Company.EmploymentEquity";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";

                public const string Name = "lblName";
                public const string PlanStartDate = "lblPlanStartDate";
                public const string PlanEndDate = "lblPlanEndDate";
                public const string PlanLength = "lblPlanLength";

                public const string EquityValueExistsError = "errCompanyEmploymentEquityPlanValueExists";
                public const string EquityOverlappingError = "errCompanyEmploymentEquityOverlap";
            }
        }

        public static class CompanyEmploymentEquityValue
        {
            public const string Area = "Company.EmploymentEquityValue";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
            }
        }

        public static class CompanyQualification
        {
            public const string Area = "Company.Qualification";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string NewCompanyQualificationButton = "btnNewCompanyQualification";

                public const string Description = "lblDescription";
                public const string NqfLevel = "lblNqfLevel";

                public const string CompanyQualificationDuplicateError = "errCompanyQualificationDuplicateExists";

                public const string EmployeeQualificationExistsError = "errEmployeeQualificationExists";

                public const string DescriptionRequiredError = "errDescriptionRequired";
                public const string DescriptionLengthError = "errDescriptionLength";
            }
        }

        public static class EmployeeQualification
        {
            public const string Area = "Employee.Qualification";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string NewEmployeeQualificationButton = "btnNewEmployeeQualification";

                public const string EmployeeNumber = "lblEmployeeNumber";
                public const string DateCompleted = "lblDateCompleted";
                public const string CompanyQualification = "lblCompanyQualification";
                public const string EducationLevel = "lblEducationLevel";
                public const string Subjects = "lblSubjects";
                public const string Institution = "lblInstitution";
                public const string InstituteType = "lblInstituteType";
                public const string HighestQualification = "lblHighestQualification";

                public const string EmployeeQualificationDuplicateError = "errEmployeeQualificationDuplicateExists";
                public const string HighestQualificationDuplicateError = "errHighestQualificationDuplicateExists";
                public const string InstitutionLengthError = "errInstitutionLength";
                public const string SubjectsLengthError = "errSubjectsLength";
                public const string CompletionDateRequiredError = "errCompletionDateRequired";
                public const string QualificationRequiredError = "errQualificationRequired";
                public const string EducationalLevelRequired = "errEducationalLevelRequired";

                public const string CustomFields = "customFields";
            }
        }

        public static class CompanySkillCategory
        {
            public const string Area = "Company.Skill.Category";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string NewCompanySkillCategoryButton = "btnNewCompanySkillCategory";

                public const string CompanySkillCategoryCode = "lblCompanySkillCategoryCode";
                public const string Description = "lblDescription";

                public const string CompanySkillCategoryCodeDuplicateError = "errCompanySkillCategoryCodeDuplicateExists";
                public const string DescriptionDuplicateError = "errDescriptionDuplicateExists";

                public const string CodeLengthError = "errCodeLength";
                public const string CodeSpecialCharactersError = "errCodeSpecialCharacters";
                public const string DescriptionLengthError = "errDescriptionLength";

                public const string CompanySkillCategoryCodeExistsError = "errCompanySkillExists";

                public const string CompanySkillDescriptionRequiredError = "errCompanySkillDescriptionRequired";
                public const string CompanySkillDescriptionLengthError = "errCompanySkillDescriptionLength";
                public const string CompanySkillCategoryRequiredError = "errCompanySkillCategoryRequired";
                public const string CompanySkillOverviewLengthError = "errCompanySkillOverviewLength";
            }
        }

        public static class CompanyTrainingCourse
        {
            public const string Area = "Company.TrainingCourse";

            public static class Keys
            {
                public const string DescriptionDuplicateError = "errDescriptionDuplicateExists";
                public const string EmployeeTrainingCourseExistsError = "errEmployeeTrainingCourseExists";
            }
        }

        public static class CompanySkill
        {
            public const string Area = "Company.Skill";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string NewCompanySkillButton = "btnNewCompanySkill";

                public const string Description = "lblDescription";
                public const string CompanySkillCategory = "lblCompanySkillCategory";
                public const string Overview = "lblOverview";

                public const string DescriptionDuplicateError = "errDescriptionDuplicateExists";

                public const string EmployeeSkillExistsError = "errEmployeeSkillExists";
            }
        }

        public static class EmployeeSkill
        {
            public const string Area = "Employee.Skill";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string NewEmployeeSkillButton = "btnNewEmployeeSkill";

                public const string EmployeeNumber = "lblEmployeeNumber";
                public const string CompanySkillCategory = "lblCompanySkillCategory";
                public const string CompanySkill = "lblCompanySkill";
                public const string Competency = "lblCompetency";
                public const string Experience = "lblExperience";

                public const string CompanySkillExistsError = "errCompanySkillExists";

                public const string CustomFields = "customFields";
            }
        }

        public static class EmployeeUpdate
        {
            public static class Keys
            {
                public const string PageHeader = "lblUpdateEmployee";
            }
        }

        public static class ReleaseNotes
        {
            public const string Area = "ReleaseNotes";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string EnhancementsTab = "Enhancements.Tab";
                public const string LegislationTab = "Legislation.Tab";
                public const string LegendReleased = "Legend.Released";
                public const string LegendDevelopment = "Legend.Development";
                public const string MessagedRead = "Messaged.Read";
                public const string BtnDownload = "Button.Download";
                public const string BtnPdfDownload = "Button.PdfDownload";
                public const string BtnMarkAll = "Button.MarkAll";
                public const string BtnProductBoard = "Button.ProductBoard";
                public const string ReleaseNotesTab = "ReleaseNotes.Tab";
                public const string PreliminaryNotesTab = "PreliminaryNotes.Tab";
            }

            public static class GridColumCaption
            {
                public const string ReleaseNote = "Column.ReleaseNoteTitle";
                public const string IsActive = "Column.IsActive";
                public const string Ticket = "Column.Ticket";
                public const string PublishDate = "Column.PublishDate";
                public const string EstimatedReleaseDate = "Column.EstimatedReleaseDate";
                public const string DevelopmentDate = "Column.DevelopmentDate";
                public const string Country = "Column.Country";
                public const string VisibleDate = "Column.VisibleDate";
            }
        }

        public static class PowerBi
        {
            public const string Area = "PowerBi";
        }

        public static class OrgChart
        {
            public const string Area = "OrgChart";
        }

        public static class Mscoa
        {
            public const string Area = "Mscoa";
        }

        public static class Rti
        {
            public const string Area = "Rti";
        }

        public static class CloudRoom
        {
            public const string Area = "CloudRoom";

            public static class Keys
            {
                public const string Unassigned = "lblUnassignedUser";
            }
        }

        public static class CloudRoomComment
        {
            public const string Area = "CloudRoomComment";
        }

        public static class ReleaseNotesConfig
        {
            public const string Area = "ReleaseNotesConfig";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
            }
        }

        public static class CompanyGLDetail
        {
            public const string Area = "CompanyGLDetail";
        }

        public static class CompanyGroupExchangeRate
        {
            public const string Area = "CompanyGroupExchangeRates";

            public static class Keys
            {
                public const string Empty = "lblEmpty";
                public const string ExistingRecord = "lblExistingRecord";
                public const string Duplication = "lblDuplication";
                public const string ExchangeRateId = "lblExchangeRateId";
                public const string Country = "lblCountry";
                public const string EffectiveDateEmptyError = "lblEffectiveDateEmptyError";
                public const string DeleteDenied = "lblDeleteDenied";
                public const string PayRateLinked = "lblPayRateLinked";
                public const string OneDollarInCurrencyGreaterThan0Error = "lblOneDollarInCurrencyGreaterThanZeroError";
                public const string CurrencyIdEmptyError = "lblCurrencyIdEmptyError";
            }
        }

        public static class CostingProjectActivity
        {
            public const string Area = "Costing.Project.Activity";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string AddNewProjectCostingActivity = "btnNewProjectCostingActivity";

                public const string ProjectActivityDescription = "lblProjectActivityDescription";
                public const string ProjectActivityCode = "lblProjectActivityCode";
                public const string Glcode = "lblGlcode";
                public const string OrganizationUnit = "lblOrganizationUnit";
                public const string RegionId = "lblRegion";
                public const string KeyAccountManagerEmployeeNumber = "lblEmployeeId";
                public const string MonthlyStaffBudget = "lblMonthlyStaffBudget";
                public const string MonthlyBudgetedHours = "lblMonthlyBudgetedHours";
                public const string ActiveHourlyRate = "lblActiveHourlyRate";
                public const string DoNotShow = "lblDoNotShow";
                public const string TsdefaultHours = "lblTsdefaultHours";
                public const string MaxHours = "lblMaxHours";
                public const string CategoryCode = "lblCategoryCode";
                public const string InactiveDate = "lblInactiveDate";
                public const string HourlyCost = "lblHourlyCost";
                public const string Additional = "lblAdditional";
                public const string Submit = "lblSubmit";

                public const string ProjectActivityDescriptionRequiredError = "lblProjectActivityDescriptionRequiredError";
                public const string ProjectActivityDescriptionExistsError = "lblProjectActivityDescriptionExistsError";
                public const string ProjectDependenciesExistsError = "errProjectDependenciesExists";
                public const string CurrentFutureProjectDependenciesExistsError = "errCurrentFutureProjectDependenciesExists";

                public const string ProjectActivityCodeRequiredError = "lblProjectActivityCodeRequiredError";
                public const string ProjectActivityCodeExistsError = "lblProjectActivityCodeExistsError";

                public const string KeyAccountManagerForWorkflowExistsError = "lblKeyAccountManagerForWorkflowExistsError";
                public const string PayRateCategoryExistsError = "lblPayRateCategoryExistsError";
                public const string PercentageCostSplitExistsError = "lblPercentageCostSplitExistsError";
                public const string EmployeeProjectExistsError = "lblEmployeeProjectExistsError";

                public const string MonthlyStaffBudgetNegativeValueError = "lblMonthlyStaffBudgetNegativeValueError";
                public const string ActivityHourlyRateNegativeValueError = "lblActivityHourlyRateNegativeValueError";
                public const string MonthlyBudgetedHoursNegativeValueError = "lblMonthlyBudgetedHoursNegativeValueError";
                public const string TsdefaultHoursNegativeValueError = "lblTsdefaultHoursNegativeValueError";

                public const string ProjectActivityDescriptionLength = "lblProjectActivityDescriptionLength";
                public const string ProjectActivityCodeLength = "lblProjectActivityCodeLength";
                public const string GlCodeLength = "lblGlCodeLength";

                public const string MaximumCharactersAllowedError = "lblMaximumCharactersAllowedError";
                public const string InvalidHoursPerDay = "lblInvalidHoursPerDay";

                public const string MonthlyBudgetedHoursError = "lblMonthlyBudgetedHoursError";
                public const string MonthlyStaffBudgetError = "lblMonthlyStaffBudgetError";
                public const string ActivityHourlyRateError = "lblActivityHourlyRateError";
            }
        }

        public static class EmployeeInbox
        {
            public const string Area = "Employee.Inbox";
        }

        public static class Pacey
        {
            public const string Area = "Pacey";
        }

        public static class PaceyMessageTemplate
        {
            public const string Area = "Pacey.Message.Template";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";

                public const string MessageTemplate = "lblMessageTemplate";
                public const string EmployeeNumber = "lblEmpNumber";
            }
        }

        public static class OrganizationCategory
        {
            public const string Area = "Organization.Category";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";

                public const string ApplicableGrade = "lblApplicableGrade";
                public const string EffectiveDate = "lblEffectiveDate";
                public const string Code = "lblCategoryCode";
                public const string Description = "lblCategoryDescription";
                public const string PayRateDefault = "lblPayRateDefault";
                public const string PayRateHoursPerDay = "lblPayRateHoursPerDay";
                public const string PayRateDaysPerPeriod = "lblPayRateDaysPerPeriod";
                public const string IsPayRateRecordLinked = "lblIsPayRateRecordLinked";
                public const string IncreaseReason = "lblIncreaseReason";

                public const string EffectiveDateRequiredError = "lblEffectiveDateRequiredError";
                public const string CategoryCodeRequiredError = "lblCategoryCodeRequiredError";
                public const string CategoryCodeExistsError = "lblCategoryCodeExistsError";
                public const string CategoryDescriptionExistsError = "lblCategoryDescriptionExistsError";
                public const string CategoryDescriptionRequiredError = "lblDescriptionRequiredError";
                public const string PayRateDefaultRequiredError = "lblPayRateDefaultRequiredError";
                public const string PayRateCategoryExistsError = "lblPayRateCategoryExistsError";

                public const string PayRateDefaultNegativeValueError = "lblPayRateDefaultNegativeValueError";
                public const string PayRateHoursPerDayNegativeValueError = "lblPayRateHoursPerDayNegativeValueError";
                public const string PayRateDaysPerPeriodNegativeValueError = "lblPayRateDaysPerPeriodNegativeValueError";
                public const string InvalidPayRateHoursPerDayError = "lblInvalidPayRateHoursPerDayError";
                public const string InvalidPayRateDaysPerPeriodError = "lblInvalidPayRateDaysPerPeriodError";
                public const string IncreaseReasonRequiredError = "lblIncreaseReasonRequiredError";

                public const string AdditionalValueNotFoundError = "lblAdditionalValueNotFoundError";
                public const string AdditionalValueOrderNumberError = "lblAdditionalValueOrderNumberError";
                public const string AdditionalValueDescriptionLengthError = "lblAdditionalValueDescriptionLengthError";
                public const string AdditionalValueDescriptionRequiredError = "lblAdditionalValueDescriptionRequiredError";
                public const string AdditionalValueDescriptionDuplicateError = "lblAdditionalValueDescriptionDuplicateError";
            }
        }

        public static class EmployeeRecurringTemplate
        {
            public const string Area = "EmployeeRecurringTemplate";
        }

        public static class TaxCountryConfig
        {
            public const string Area = "EnumTaxCountries";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string CountryCodeExistsError = "errCountryCodeExists";
                public const string CountryDescriptionExistsError = "errCountryDescriptionExists";
                public const string CountryStatusIsNullError = "errCountryStatusIsNull";
            }
        }

        public static class EmployeeRecurringCostingSplit
        {
            public const string Area = "EmployeeRecurringCostingSplit";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";
            }
        }

        public static class StabilityRules
        {
            public const string Area = "StabilityRules";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string ErrorDescriptionLength = "errDescriptionLength";
                public const string ErrorDescriptionRequired = "errDescriptionRequired";
                public const string ErrorEffectiveDateRequired = "errEffectiveDateRequired";
                public const string ErrorStabilityRequired = "errStabilityRequired";
                public const string ErrorStabilityTypeRequired = "errStabilityTypeRequired";
                public const string ErrorLeaveTypeRequired = "errLeaveTypeRequired";
                public const string ErrorLeaveDescriptionLength = "errLeaveDescriptionLength";
                public const string ErrorSuspensionReasonRequired = "errSuspensionReasonRequired";
                public const string ErrorPositionDateFieldRequired = "errPositionDateFieldRequired";
                public const string ErrorInvalidDays = "errInvalidDays";
                public const string ErrorInvalidMonths = "errInvalidMonths";
                public const string ErrorDaysOrMonthRequired = "errDaysOrMonthRequired";
                public const string ErrorInvalidInactiveDate = "errInvalidInactiveDate";
                public const string ErrorRuleCantBeDeleted = "errRuleCantBeDeleted";
            }
        }

        public static class CompanyComponents
        {
            public const string Area = "Payroll.Components.Company";
        }

        public static class CompanyComponentSubCodes
        {
            public const string Area = "Payroll.Components.Company.Sub.Code";
        }

        public static class UserProfiles
        {
            public const string Area = "User.Profiles";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
            }
        }

        public static class UserActivate
        {
            public const string Area = "UserActivate";

            public const string TableName = "ActivateUsers";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
            }
        }

        public static class BureauProfiles
        {
            public const string Area = "Bureau.Profiles";
        }

        public static class CopyConfiguration
        {
            public const string Area = "Bureau.CopyConfiguration";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string CopyConfigBreadcrumb = "lblCopyConfigBreadcrumb";
            }
        }

        public static class CompanyJobManagement
        {
            public const string Area = "Company.JobManagement";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string CustomFields = "customFields";

                public const string JobNumberRequired = "lblJobNumberRequiredError";
                public const string JobTitleRequired = "lblJobTitleRequiredError";
                public const string EmploymentSubCategoryRequired = "lblEmploymentSubCategoryRequiredError";
                public const string PreferredGenderRequired = "lblPreferredGenderRequiredError";
                public const string PreferredRaceRequired = "lblPreferredRaceRequiredError";
                public const string PreferredLocalizationRequired = "lblPreferredLocalizationRequiredError";
                public const string OccupationalLevelRequired = "lblOccupationalLevelRequiredError";
                public const string BudgetGroupRequired = "lblBudgetGroupRequiredError";
                public const string RegionRequired = "lblRegionRequiredError";
                public const string ReportsToEffectiveDateRequired = "lblReportsToEffectiveDateRequiredfError";
                public const string ReportsToJobIdRequired = "lblReportsToJobIdRequiredError";
                public const string PositionRequired = "lblPositionRequiredError";
                public const string GradeRequired = "lblGradeRequiredError";
                public const string OrganizationUnitRequired = "lblOrgUnitRequiredError";
                public const string SingleStartEndDateRequired = "lblSingleStartEndDateRequiredError";
                public const string GlAccountRequired = "lblGLAccountRequiredError";
                public const string EmployeeStatusRequired = "lblEmployeeStatusRequiredError";
                public const string StartDateRequired = "StartDateRequiredError";

                public const string JobTitleMaxLength = "lblJobTitleMaxLengthError";
                public const string RegionNotAllowedPermission = "lblRegionNotAllowedPermissionError";
                public const string DuplicateJobNumberUponEdit = "lblDuplicateJobNumberUponEditError";
                public const string JobCannotReportToSelf = "lblJobCannotReportToSelfError";
                public const string JobCannotReportToJobsBelowReportingChain = "lblJobCannotReportToJobsBelowReportingChainError";
                public const string EmployeeNameOverwritten = "lblEmployeeNameOverwrittenError";
                public const string OrganizationUnitNotAllowedPermission = "lblOrgUnitNotAllowedPermissionError";
                public const string NoBaseCostConfigured = "lblNoBaseCostConfiguredError";
                public const string InvalidAbolishDate = "lblInvalidAbolishDateError";
                public const string CannotTransferJobToCostCentre = "lblCannotTransferJobToCostCentreError";
                public const string CopyJobToRegionNotAllowedPermission = "lblCopyJobToRegionNotAllowedPermissionError";
                public const string CannotTransferJobToSelectedOrganizationGroup = "lblCannotTransferJobToSelectedOrgGroupError";
                public const string DatesCannotOverlap = "lblDatesCannotOverlapError";
                public const string InvalidPositionEffectiveDate = "lblInvalidPositionEffectiveDateError";
                public const string EndDateShouldBeAfterStartDate = "lblEndDateShouldBeAfterStartDateError";
                public const string InvalidNumericValue = "lblInvalidNumericValueError";
                public const string CannotAbolishJobWhenActiveEmployeeAttached = "lblCannotAbolishJobWhenActiveEmployeeAttachedError";
                public const string CannotAbolishJobWhenEmployeeAttached = "lblCannotAbolishJobWhenEmployeeAttachedError";
                public const string OrganizationGroupInactive = "lblOrgGroupInactiveError";
                public const string OrganizationUnitNotSelected = "lblOrgUnitNotSelectedError";
                public const string TransferDateSelectedWithoutOrganizationUnit = "lblTransferDateSelectedWithoutOrgUnitError";
                public const string OrganizationUnitSelectedWithoutTransferDate = "lblOrgUnitSelectedWithoutTransferDateError";
                public const string InvalidTransferDate = "lblInvalidTransferDateError";
                public const string EffectiveDateShouldBeAfterLatestPosition = "lblEffectiveDateShouldBeAfterLatestPositionError";
                public const string DuplicatePositionEffectiveDate = "lblDuplicatePositionEffectiveDateError";
                public const string InvalidExternalParty = "lblExternalPartyError";
                public const string DuplicateComponents = "lblDuplicateComponentsError";
                public const string CannotDeleteActiveJob = "lblCannotDeleteActiveJobError";
                public const string LockdownModeEnabled = "lblCompanyJobManagementInLockDownMode";
                public const string JobNumberReadOnly = "lblJobNumberReadOnlyError";
                public const string CopyJobNumbers = "lblJobNumbers";
                public const string CopyJobProcessing = "lblCopyJobProcessing";
                public const string CopyJobSuccess = "lblCopyJobSuccess";
                public const string CreatedSuccessfully = "lblCopySuccess";
                public const string CopyJobLimit = "lblCopyJobLimit";
                public const string CopyCount = "lblCopies";
                public const string PositionFieldIsReadonly = "lblPositionFieldIsReadonlyError";
                public const string OrgUnitFieldIsReadonly = "lblOrgUnitFieldIsReadonlyError";
                public const string GradeFieldIsReadonly = "lblGradeFieldIsReadonlyError";
                public const string EmploymentSubCategoryFieldIsReadonly = "lblEmploymentSubCategoryFieldIsReadonlyError";
                public const string RegionFieldIsReadonly = "lblRegionFieldIsReadonlyError";
                public const string PositionTypeFieldIsReadonly = "lblPositionTypeFieldIsReadonlyError";
            }
        }

        public static class CompanyJobManagementMultiDate
        {
            public const string Area = "CompanyJobManagementMultiDate";
        }

        public static class CompanyJobManagementComponent
        {
            public const string Area = "CompanyJobManagementComponents";
        }

        public static class PublicHoliday
        {
            public const string Area = "PublicHoliday";
        }

        public static class BureauPublicHoliday
        {
            public const string Area = "PublicHoliday";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string Country = "Country";

                public const string HolidayDate = "HolidayDate";
                public const string HolidayDescription = "HolidayDescription";
                public const string PublicHolidayLevel = "PublicHolidayLevel";
                public const string Province = "Province";
            }
        }

        public static class BureauConfigSettings
        {
            public const string Area = "Bureau.ConfigSettings";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";
                public const string Country = "Country";

                public const string ConfigCodeMaxLength = "ConfigCodeMaxLength";
                public const string ConfigNameMaxLength = "ConfigNameMaxLength ";
                public const string ConfigValueMaxLength = "ConfigValueMaxLength ";
                public const string CountryRequired = "CountryRequired ";
                public const string TaxYearRequired = "TaxYearRequired";
                public const string ConfigDescriptionExists = "ConfigDescriptionExists";

            }
        }

        public static class EmailValidation
        {
            public const string Area = "EmailValidation";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
            }
        }

        public static class CompanyPublicHoliday
        {
            public const string Area = "Company.PublicHoliday";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string PublicHolidayDate = "HolidayDate";
                public const string HolidayDescription = "HolidayDescription";
                public const string PublicHolidayLevel = "PublicHolidayLevel";
                public const string PublicHolidayProvinces = "Province";
                public const string PublicHolidayCategories = "Category";
                public const string PublicHolidayMunicipalities = "Municipality";
            }
        }

        public static class PublicHolidayCategory
        {
            public const string Area = "PublicHolidayCategory";
        }

        public static class CompanyFrequencies
        {
            public const string Area = "Payroll.CompanyFrequencies";
        }

        public static class EmployeeIncident
        {
            public const string Area = "Employee.Incident";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";

                public const string IncidentTypeRequired = "lblIncidentTypeRequiredError";
                public const string IncidentDateRequired = "lblIncidentDateRequiredError";
                public const string OffenceRequired = "lblOffenceRequiredError";
                public const string OutcomeRequired = "lblOutcomeRequiredError";
                public const string AppealReasonRequired = "lblAppealReasonRequiredError";
                public const string LegalBodyRequired = "lblLegalBodyRequiredError";
                public const string OtherIncidentTypeRequired = "lblOtherIncidentTypeRequiredError";
                public const string OutcomeDateShouldBeAfterIncidentDate = "lblOutcomeDateShouldBeAfterIncidentDateError";
                public const string DateOfOutcomeShouldBeAfterIncidentDate = "lblDateOfOutcomeShouldBeAfterIncidentDateError";
                public const string PreparingAttachments = "lblPreparingAttachments";
                public const string Attachments = "lblAttachments";
                public const string CustomFields = "customFields";
            }
        }

        public static class CompanySetting
        {
            public const string Area = "Company.Setting";

            public static class Keys
            {
                public const string SettingValue = "lblSettingValue";
                public const string SettingIndicator = "lblSettingIndicator";
            }
        }

        public static class CompanyLeaveSetting
        {
            public const string Area = "Company.Setting.Leave";
        }

        public static class General
        {
            public const string Area = "General";
        }

        public static class CompanyGeneralSetting
        {
            public const string Area = "Company.Setting.General";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string ResetFailure = "lblResetError";
                public const string ResetSuccess = "lblResetSuccess";
                public const string ResetProcessing = "lblResetProcessing";
            }

            public static class SecurableKeys
            {
                public const string TaxabilityOptions = "General.Settings.TaxabilityOptions";
            }
        }

        public static class CompanyPositionSetting
        {
            public const string Area = "Company.Setting.Position";
        }

        public static class CompanySecuritySetting
        {
            public const string Area = "Company.Setting.Security";
        }

        public static class CompanyPerformanceManagementSetting
        {
            public const string Area = "Company.Setting.Performance.Management";
        }

        public static class CompanyWorkforcePlanningSetting
        {
            public const string Area = "Company.Setting.WorkforcePlanning";
        }

        public static class CompanyClaimSetting
        {
            public const string Area = "Company.Setting.Claim";
        }

        public static class CompanyCostingSetting
        {
            public const string Area = "Company.Setting.Costing";
        }

        public static class CompanyCalculationSetting
        {
            public const string Area = "Company.Setting.Calc";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";

                public const string ProrateDaysIsRequired = "lblPRDAYSINRequired";
            }
        }

        public static class DynamicFormBuilder
        {
            public const string Area = "DynamicFormBuilder";

            public const string CompanyArea = "DynamicFormBuilder.Company";
            public const string BureauArea = "DynamicFormBuilder.Bureau";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";

                public const string ModuleTypeAddNewEmployee = "lblAddNewEmployee";
                public const string ModuleTypeOther = "lblOther";

                public const string AddNewEmployeeDefaultInputGroupCaption = "lblAddNewEmployeeDefaultInputGroupCaption";
                public const string AddNewEmployeeDefaultInputEffectiveDate = "lblAddNewEmployeeDefaultInputEffectiveDate";

                public const string AuditChangedByUserName = "lblAuditChangedByUserName";
                public const string AuditActionType = "lblAuditActionType";
                public const string AuditDateOccurred = "lblAuditDateOccurred";
                public const string AuditModuleType = "lblAuditModuleType";
                public const string AuditFormName = "lblAuditFormName";
                public const string AuditDetails = "lblAuditDetails";

                public const string AuditTrailFormActivationDescription = "auditTrailFormActivationDescription";
                public const string AuditTrailFormDeactivationDescription = "auditTrailFormDeactivationDescription";

                public const string DatabaseSaveErrorMessage = "lblDatabaseSaveErrorMessage";
            }
        }

        public static class OrganizationLevel
        {
            public const string Area = "Company.OrgHierarchyLevel";
        }

        public static class TaxYearDetail
        {
            public const string Area = "Bureau.TaxYearDetail";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string Country = "lblCountry";

                public const string StartDateMustBeBeforeEndDate = "errStartDateMustBeBeforeEndDate";
                public const string StartDateMustNotOverlapPreviousYears = "errStartDateMustNotOverlapPreviousYears";
                public const string EndDateMustNotOverlapPreviousYears = "errEndDateMustNotOverlapPreviousYears";
                public const string BracketNameAndCodeMustBeUnique = "errBracketNameAndCodeMustBeUnique";
                public const string RateCodeAndEffectiveDateMustBeUnique = "errRateCodeAndEffectiveDateMustBeUnique";
            }
        }

        public static class SpecialComponent
        {
            public const string Area = "Bureau.SpecialComponent";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";

                public const string SpecialCodeMaxLength = "SpecialCodeMaxLength";
                public const string SpecialDescriptionMaxLength = "SpecialDescriptionMaxLength";
                public const string DuplicateSpecialCodeException = "errDuplicateSpecialCodeException";
            }
        }

        public static class CompanyPensionEnrolment
        {
            public const string Area = "Company.PensionEnrolment";

            public static class Keys
            {
                public const string Header = "lblPageHeader";
            }
        }

        public static class EmployeePensionEnrolment
        {
            public const string Area = "Employee.PensionEnrolment";

            public static class Keys
            {
                public const string Header = "lblPageHeader";
            }
        }

        public static class EmployeePensionLetter
        {
            public const string Area = "Employee.PensionLetter";

            public static class Keys
            {
                public const string Header = "lblPageHeader";
            }
        }

        public static class TaxDefinition
        {
            public const string Area = "Bureau.TaxDefinition";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";

                public const string IncomeBaseRequired = "IncomeBaseRequired";
                public const string SpecifyExceptionRule = "SpecifyExceptionRule";
                public const string DuplicateEffectiveDate = "DuplicateEffectiveDate";
                public const string DuplicateTaxCodeException = "DuplicateTaxCodeException";
                public const string DuplicateComponentException = "DuplicateComponentException";
            }
        }

        public static class SecurityRoles
        {
            public const string DisplayArea = "SecurityGroups";
            public const string CompanyArea = "Company.SecurityRoles";
            public const string BureauArea = "Bureau.SecurityRoles";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string BureauPageHeader = "lblBureauPageHeader";
                public const string AddPageHeader = "lblAddPageHeader";
                public const string AddBureauPageHeader = "lblAddBureauPageHeader";
                public const string EditPageHeader = "lblEditPageHeader";
                public const string CopyPageHeader = "lblCopyPageHeader";
            }
        }

        public static class BureauCategory
        {
            public const string Area = "Bureau.Category";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string CategoryDescriptionRequired = "lblCategoryDescriptionRequired";
                public const string CategoryDescriptionMaxLength = "lblCategoryDescriptionMaxLength";
                public const string CategoryDependenciesException = "lblCategoryDependenciesException";
            }
        }

        public static class Iras
        {
            public const string Area = "Iras";
        }

        public static class GovernmmentHub
        {
            public const string Area = "GovernmmentHub";
        }

        public static class CompanySecurityRoles
        {
            public const string Area = "Company.SecurityRoles";
        }

        public static class BureauSecurityRoles
        {
            public const string Area = "Bureau.SecurityRoles";
        }

        public static class UserOrgPermissions
        {
            public const string Area = "Organization.Unit.Permissions";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
            }
        }

        public static class OrganizationGrade
        {
            public const string Area = "OrganizationGrade";

            public static class Keys
            {
                public const string Header = "lblPageHeader";
            }
        }

        public static class RecordOfEmploymentAdjustment
        {
            public const string Area = "RecordOfEmploymentAdjustment";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";
                public const string DecimalPrecisionScaleError = "DecimalPrecisionScaleError";
                public const string DecimalHoursPrecisionScaleError = "DecimalHoursPrecisionScaleError";
            }
        }

        public static class BureauSuspension
        {
            public const string Area = "Bureau.Suspension";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";
                public const string SuspensionPage = "SuspensionPage";
                public const string DeleteSuspension = "DeleteSuspension";
                public const string PaymentAndComponentRequired = "PaymentAndComponentRequired";
                public const string HistoryRecordAlreadyExists = "HistoryRecordAlreadyExists";
            }
        }

        public static class HmrcPaymentRecord
        {
            public const string Area = "HMRC.Payment.Record";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";
            }

            public static class Quarters
            {
                public const string Q1 = "Quarter 1";
                public const string Q2 = "Quarter 2";
                public const string Q3 = "Quarter 3";
                public const string Q4 = "Quarter 4";
            }
        }

        public static class CompanyLeaveScheme
        {
            public const string Area = "Company.LeaveScheme";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";

                public const string SchemeNameRequiredError = "SchemeNameRequiredError";
                public const string SchemeNameLengthError = "SchemeNameLengthError";
                public const string SchemeNameExistError = "SchemeNameExistError";
                public const string SchemeCodeLengthError = "SchemeCodeLengthError";
                public const string LeaveSchemeHasDependencyError = "LeaveSchemeHasDependencyError";
                public const string MaxLeaveSchemesExceededError = "MaxLeaveSchemesExceededError";
            }
        }

        public static class PayCalendar
        {
            public const string Area = "PayCalendar";
        }

        public static class CompanyRosterSchedules
        {
            public const string Area = "Company.RosterSchedules";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";

                public const string Roster = "lblRoster";
                public const string ShiftType = "lblShiftType";
                public const string ScheduleDate = "lblScheduleDate";
                public const string ExpectedHours = "lblExpectedHours";

                public const string RosterRequired = "errRosterRequired";
                public const string ShiftTypeRequired = "errShiftTypeRequired";
                public const string ScheduleDateRequired = "errScheduleDateRequired";
                public const string ExpectedHoursRequired = "errExpectedHoursRequired";
                public const string ExpectedHoursRange = "errExpectedHoursRange";
                public const string DuplicateRosterSchedules = "errDuplicateRosterSchedules";
                public const string ScheduleDateInClosedRun = "errScheduleDateInClosedRun";
                public const string ShiftTypeNotLinkedToRoster = "errShiftTypeNotLinkedToRoster";
            }
        }

        public static class CompanyShiftTypes
        {
            public const string Area = "Company.ShiftTypes";

            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
            }
        }

        public static class TaxCode
        {
            public const string Area = "Bureau.TaxCode";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";

                public const string TaxCountryRequired = "TaxCountryRequired";
                public const string TaxCodeRequired = "TaxCodeRequired";
                public const string TaxCodeMaxLength = "TaxCodeMaxLength";
                public const string TaxCodeTypeRequired = "TaxCodeTypeRequired";
                public const string TaxCodeDescriptionRequired = "TaxCodeDescriptionRequired";
                public const string TaxCodeDescriptionMaxLength = "TaxCodeDescriptionMaxLength";
                public const string IRP5PayslipActionRequired = "IRP5PayslipActionRequired";
                public const string EffectiveDateRequired = "EffectiveDateRequired";
                public const string TaxCodeDependencies = "TaxCodeDependencies";
            }
        }

        public static class YearEndReporting
        {
            public const string Area = "YearEndReporting";
            public static class Keys
            {
                public const string PageHeader = "lblPageHeader";
                public const string IR8AHeader = "lblIR8A";
                public const string Appendix8BHeader = "lblAppendix8B";
                public const string IRASAmendmentGuide = "btnIRASAmendmentGuide";
                public const string BasisYear = "lblBasisYear";
                public const string IsDeclarationByAgent = "lblIsDeclarationByAgent";
                public const string IsSection45Applicable = "lblIsSection45Applicable";
                public const string IsIR21Submitted = "lblIsIR21Submitted";
                public const string IsIncomeTaxBorneByEmployer = "lblIsIncomeTaxBorneByEmployer";
                public const string IncomeTaxOption = "lblIncomeTaxOption";
                public const string EmployerIncomeTaxAmount = "lblEmployerIncomeTaxAmount";
                public const string EmployeeLiabilityAmount = "lblEmployeeLiabilityAmount";
                public const string RemissionIncomeAmount = "lblRemissionIncomeAmount";
                public const string IsExemptRemissionIncomeApplicable = "lblIsExemptRemissionIncomeApplicable";
                public const string RemissionExemptIncomeReason = "lblRemissionExemptIncomeReason";
                public const string RemissionExemptIncomeAmount = "lblRemissionExemptIncomeAmount";
                public const string OverseasPostingReason = "lblOverseasPostingReason";
                public const string DesignatedFundName = "lblDesignatedFundName";
                public const string LumpSumPaymentReason = "lblLumpSumPaymentReason";
                public const string LumpSumPaymentBasis = "lblLumpSumPaymentBasis";
                public const string PensionOrProvidentFundName = "lblPensionOrProvidentFundName";
                public const string AmountAccruedFrom1993 = "lblAmountAccruedFrom1993";
                public const string BonusDeclarationDate = "lblBonusDeclarationDate";
                public const string DirectorsFeesApprovalDate = "lblDirectorsFeesApprovalDate";
                public const string TenRecordsPerTaxYearError = "errTenRecordsPerTaxYear";
                public const string InvalidTaxYearError = "errInvalidTaxYear";
                public const string IncomeTaxoptionRequiredError = "errIncomeTaxoptionRequired";
                public const string RemissionExemptIncomeReasonRequiredError = "errRemissionExemptIncomeReasonRequired";
                public const string InvalidRemissionExemptIncomeAmountError = "errInvalidRemissionExemptIncomeAmount";
                public const string OverseasPostingReasonRequiredError = "errOverseasPostingReasonRequired";
                public const string DesignatedFundNameLengthError = "errDesignatedFundNameLength";
                public const string LumpSumPaymentReasonLengthError = "errLumpSumPaymentReasonLength";
                public const string LumpSumPaymentBasisRequiredError = "errLumpSumPaymentBasisRequired";
                public const string PensionOrProvidentFundNameLengthError = "errPensionOrProvidentFundNameLength";
                public const string InvalidAmountAccruedFrom1993Error = "errInvalidAmountAccruedFrom1993";
                public const string InvalidBonusDeclarationDateError = "errInvalidBonusDeclarationDate";
                public const string InvalidDirectorsFeesApprovalDateError = "errInvalidDirectorsFeesApprovalDate";
                public const string CompanyRegistrationNumberLimitError = "errCompanyRegistrationNumberLimit";
                public const string CompanyNameLimitError = "errCompanyNameLimit";
                public const string InvalidExercisePriceError = "errInvalidExercisePrice";
                public const string InvalidExerciseOpenMarketPricePerShareError = "errInvalidExerciseOpenMarketPricePerShare";
                public const string InvalidSharesAmountError = "errInvalidSharesAmount";
                public const string ExerciseDateRequiredError = "errExerciseDateRequired";
                public const string GrantDateRequiredError = "errGrantDateRequired";
                public const string InvalidDecimalAmount = "errInvalidDecimalAmount";
                public const string InvalidAmount = "errInvalidAmount";
                public const string DependentPrimitiveFieldError = "errDependentPrimitiveField";
                public const string DependentLookupFieldError = "errDependentLookupField";
                public const string DependentBooleanFieldError = "errDependentBooleanField";
                public const string EmployerCpfRefundClaimed = "lblEmployerCpfRefundClaimed";
                public const string EmployeeCpfRefundClaimed = "lblEmployeeCpfRefundClaimed";
                public const string EmployerRefundInterest = "lblEmployerRefundInterest";
                public const string EmployeeRefundInterest = "lblEmployeeRefundInterest";
                public const string AccommodationId = "lblAccommodationId";

                // EmployeeAppendix8AValidator
                public const string AddressLine1LengthError = "errAddressLine1Length";
                public const string AddressLine2LengthError = "errAddressLine2Length";
                public const string AddressLine3LengthError = "errAddressLine3Length";
                public const string OccupationPeriodStartDateRequiredError = "errOccupationPeriodStartDateRequired";
                public const string OccupationPeriodStartDateBasisYearError = "errOccupationPeriodStartDateBasisYear";
                public const string OccupationPeriodEndDateRequiredError = "errOccupationPeriodEndDateRequired";
                public const string OccupationPeriodEndDateBasisYearError = "errOccupationPeriodEndDateBasisYear";
                public const string OccupationPeriodEndDateBeforeStartError = "errOccupationPeriodEndDateBeforeStart";
                public const string NumberOfEmployeesSharingError = "errNumberOfEmployeesSharing";
                public const string AnnualValuePremisesError = "errAnnualValuePremises";
                public const string FurnitureFittingOptionRequiredError = "errFurnitureFittingOptionRequired";
                public const string ValueFurnitureFittingMismatchError = "errValueFurnitureFittingMismatch";
                public const string RentPaidToLandlordRequiredError = "errRentPaidToLandlordRequired";
                public const string TaxableValuePlaceOfResidenceError = "errTaxableValuePlaceOfResidence";
                public const string RentPaidByEmployeeError = "errRentPaidByEmployee";
                public const string TotalTaxableValuePlaceOfResidenceError = "errTotalTaxableValuePlaceOfResidence";
                public const string UtilitiesCostsError = "errUtilitiesCosts";
                public const string DriverCostsError = "errDriverCosts";
                public const string ServantCostsError = "errServantCosts";
                public const string TaxableValueUtilitiesHousekeepingError = "errTaxableValueUtilitiesHousekeeping";
                public const string CostHotelAccommodationError = "errCostHotelAccommodation";
                public const string HotelAmountPaidByEmployeeError = "errHotelAmountPaidByEmployee";
                public const string TaxableValueHotelAccommodationError = "errTaxableValueHotelAccommodation";
                public const string Max10RecordsPerYearError = "errMax10RecordsPerYear";
                public const string Only5TaxYearsAllowedError = "errOnly5TaxYearsAllowed";

                public const string EditAppendix8APageHeader = "lblEditAppendix8APageHeader";
                public const string AddressLine1 = "lblAddressLine1";
                public const string AddressLine2 = "lblAddressLine2";
                public const string AddressLine3 = "lblAddressLine3";
                public const string OccupationPeriodStartDate = "lblOccupationPeriodStartDate";
                public const string OccupationPeriodEndDate = "lblOccupationPeriodEndDate";
                public const string NumberOfEmployeesSharing = "lblNumberOfEmployeesSharing";
                public const string AnnualValuePremises = "lblAnnualValuePremises";
                public const string FurnitureFittingOption = "lblFurnitureFittingOption";
                public const string ValueFurnitureFitting = "lblValueFurnitureFitting";
                public const string RentPaidToLandlord = "lblRentPaidToLandlord";
                public const string TaxableValuePlaceOfResidence = "lblTaxableValuePlaceOfResidence";
                public const string RentPaidByEmployee = "lblRentPaidByEmployee";
                public const string TotalTaxableValuePlaceOfResidence = "lblTotalTaxableValuePlaceOfResidence";
                public const string UtilitiesCosts = "lblUtilitiesCosts";
                public const string DriverCosts = "lblDriverCosts";
                public const string ServantCosts = "lblServantCosts";
                public const string TaxableValueUtilitiesHousekeeping = "lblTaxableValueUtilitiesHousekeeping";
                public const string CostHotelAccommodation = "lblCostHotelAccommodation";
                public const string HotelAmountPaidByEmployee = "lblHotelAmountPaidByEmployee";
                public const string TaxableValueHotelAccommodation = "lblTaxableValueHotelAccommodation";
                public const string NumberOfDays = "lblNumberOfDays";
                public const string TotalAccommodationBenefit = "lblTotalAccommodationBenefit";
                public const string EmployeeNumber = "lblEmpNumber";
                public const string CompanyRegistrationNumber = "lblCompanyRegistrationNumber";
                public const string CompanyName = "lblCompanyName";
                public const string PlanType = "lblPlanType";
                public const string EmployeeEquityBasedRemuneration = "lblEmployeeEquityBasedRemuneration";
                public const string GrantDate = "lblGrantDate";
                public const string ExerciseDate = "lblExeciseDate";
                public const string ExercisePrice = "lblExercisePrice";
                public const string ExerciseOpenMarketPricePerShare = "lblExerciseOpenMarketPricePerShare";
                public const string SharesAmount = "lblSharesAmount";
                public const string GainsGrossAmount = "lblGainsGrossAmount";
                public const string TotalGainsGrossAmountESOPAndESOWAfter2003 = "lblTotalGainsGrossAmountESOPAndESOWAfter2003";
                public const string TotalGainsGrossAmountESOPBefore2003 = "lblTotalGainsGrossAmountESOPBefore2003";
                public const string TotalRecordCountExceeds30 = "lblTotalRecordCountExceeds30";
            }
        }

        public static class CboCode
        {
            public const string Area = "CBOCode";
        }

        public static class CompanyLeaveSchemeParameter
        {
            public const string Area = "Company.LeaveSchemeParameter";

            public static class Keys
            {
                public const string PageHeader = "PageHeader";

                public const string NextCycle = "lblNextCycle";
                public const string GradeBasedAccrualSettingCode = "GRADELVELOOKUP";
                public const string GradeBasedMaxBalanceSettingCode = "GRADELVMAXLKUP";
            }

            public static class ForfeiturePeriodOption
            {
                public const string EffectiveDateEmpLinked = "E";
                public const string EmploymentDate = "D";
                public const string GroupJoinDate = "G";
                public const string SpecifyMonth = "M";
            }
        }
    }
}