namespace PaySpace.Venuta.Infrastructure
{
    public static class ErrorCodes
    {
        public static class Login
        {
            public const string InvalidUser = "0001";
            public const string AccountInactive = "0009";
            public const string AccountLocked = "0010";
            public const string FinalAttempt = "0011";
            public const string PasswordExpired = "0012";
            public const string InvalidToken = "0015";
            public const string InvalidOtp = "0016";
            public const string CaptchaFailed = "0017";
            public const string NumberNotValid = "0018";
            public const string InvalidProductEdition = "0020";
            public const string InvalidIPAddress = "0021";
        }

        public static class Register
        {
            public const string InvalidProductEdition = "0002";
            public const string EmployeeNotFound = "0004";
            public const string UserExists = "0005";
            public const string InvalidToken = "0008";
        }

        public static class ChangePassword
        {
            public const string PasswordHistory = "0011";
        }

        public static class Employee
        {
            public const string EmailAlreadyExistsInThisCompany = "0007";
            public const string LegalWorkAgeFourteen = "0008";
            public const string LegalWorkAgeFifteen = "0009";
            public const string LegalWorkAgeSixteen = "0010";
            public const string ProvinceRequired = "0011";
            public const string CountryRequired = "0012";
            public const string AddressCodeRequired = "0013";
            public const string EmployeeNumberAlreadyExistsInThisCompany = "0014";
            public const string AddressLine1Required = "0015";
            public const string PhysicalAddressCantBeChanged = "0016";
            public const string PhysicalAddressRequired = "0017";
            public const string DuplicateAddressTypes = "0018";
            public const string InitialsRequired = "0019";
            public const string InvalidEmailFormat = "0020";
            public const string InvalidProvinceForCountry = "0021";
            public const string EmployeeNumberRequired = "0022";
            public const string InvalidWorkNumber = "0023";
            public const string InvalidHomeNumber = "lblInvalidHomeNumber";
            public const string InvalidCellNumber = "lblInvalidCellNumber";
            public const string InitialsMaxChar = "0024";
            public const string WorkExtensionMaxChar = "0025";
            public const string EmailAlreadyExists = "0026";
            public const string MaritalStatusRequired = "0027";
            public const string BirthdayRequired = "0028";
            public const string InvalidBirthDate = "InvalidBirthDate";
            public const string GenderRequired = "0029";
            public const string DateCreatedReadonly = "0030";
            public const string AddressLine3Required = "0031";
            public const string AddressCodeLengthRangeError = "0032";
            public const string AddressCodeLengthError = "errAddressCodeLength";
            public const string AddressCodeCanContainNumberOnly = "0033";
            public const string AddressCodeCannotContainOnlyZeros = "errAddressCodeZeros";
            public const string AddressCodeMaxLengthError = "errAddressCodeMaxLength";
            public const string UnitNumberLengthError = "0034";
            public const string ComplexLengthError = "0035";
            public const string AddressLine1LengthError = "0036";
            public const string SameAsPhysicalCantBeSet = "0037";
            public const string SpecialServicesMustBeEmpty = "0038";
            public const string IsCareofAddressCantBeSet = "0039";
            public const string CareOfIntermediaryCantBeSet = "0040";
            public const string CareOfIntermediaryRequred = "0041";
            public const string CitizenshipRequired = "0042";
            public const string AddressLine3MaxLength = "0043";
            public const string AddressLine2MaxLength = "0044";
            public const string ComplexMustBeEmptyPostalOrPrivateBag = "0045";
            public const string UnitNumberMustBeEmptyPostalOrPrivateBag = "0046";
            public const string InvalidEtiExempt = "0047";
            public const string InvalidOneNumberRequired = "0048";
            public const string CanNotDeleteEmployee = "0050";
            public const string EmployeeNumberMaxLength = "0051";
            public const string AddressCodeInvalidFormat = "0052";
            public const string FirstNameInvalidFormat = "0053";
            public const string MiddleNameInvalidFormat = "0054";
            public const string LastNameInvalidFormat = "0055";
            public const string InitialsInvalidFormat = "0056";
            public const string CompanyFrequencyRequired = "lblCompanyFrequencyRequired";
            public const string EmailLinkedToUser = "lblEmailLinkedToUser";
            public const string EmployeeNumberChangeNotAllowed = "lblEmployeeNumberChangeNotAllowed";
            public const string IdNumberNotCorrectLength = "lblIdNumberNotCorrectLength";
            public const string GenderNotMatchingIdNumber = "lblGenderNotMatchingIdNumber";
            public const string CitizanshipNotMatchingIdNumber = "lblCitizanshipNotMatchingIdNumber";
            public const string DateOfBirthNotMatchingIdNumber = "lblDateOfBirthNotMatchingIdNumber";
            public const string IdNumberInvalid = "lblIdNumberInvalid";
            public const string RaceRequired = "errRaceRequired";
            public const string ContactNumberLength = "0057";
            public const string EmailAddressSpecialCharactersNotAllowed = "0058";
            public const string InvalidSARSNumberFormat = "0059";
            public const string NoNonNumericOrWhiteSpacesAllowed = "0060";
            public const string InvalidCountryGenderEnum = "0061";
            public const string InvalidRequiredAddressesSA = "0062";
            public const string MunicipalityRequired = "errMunicipalityRequired";
            public const string AddressStreetTypeRequired = "errAddressStreetTypeRequired";
            public const string ErrorEffectiveDateRequired = "errEffectiveDateRequired";
            public const string ErrorEffectiveDateInvalid = "errEffectiveDateInvalid";
            public const string ErrorEmploymentDate = "errEmploymentDate";
            public const string ErrorGroupJoinDate = "errGroupJoinDate";
            public const string ErrorMaxEffectiveDate = "errMaxEffectiveDate";
            public const string FrequencyInactive = "errFrequencyInactive";
            public const string SDLExceptionRequired = "lblSDLExceptionRequired";
            public const string UIFExceptionRequired = "lblUIFExceptionRequired";
            public const string InvalidUKAddressCode = "errInvalidUKAddressCode";
            public const string FullNameRequired = "errFullNameRequired";
            public const string FullNameNameMaxLength = "errFullNameMaxLength";
            public const string PreferredNameMaxLength = "errPreferredNameMaxLength";
            public const string IndiaAddressCodeMaxLengthError = "IndiaAddressCodeMaxLengthError";
            public const string IndiaAddressCodeFormatError = "IndiaAddressCodeFormatError";
            public const string AustraliaAddressCodeLengthError = "AustraliaAddressCodeLengthError";
            public const string AustraliaAddressCodeFormatError = "AustraliaAddressCodeFormatError";
            public const string EmailAddressMaxLength = "errEmailAddressMaxLength";
            public const string InvalidCharacterInAddressFields = "errInvalidCharacterInAddressFields";
            public const string BlockMaxLengthError = nameof(BlockMaxLengthError);
            public const string EntranceMaxLengthError = nameof(EntranceMaxLengthError);
            public const string StaircaseMaxLengthError = nameof(StaircaseMaxLengthError);
            public const string FloorMaxLengthError = nameof(FloorMaxLengthError);
            public const string DoorMaxLengthError = nameof(DoorMaxLengthError);
            public const string InvalidCanadaAddressCode = "errInvalidCanadaAddressCode";
            public const string InvalidMinimumAge = "errBirthDateUnder14";
            public const string LanguageRequired = "errLanguageRequired";
            public const string AddressStreetNumberRequired = "errStreeNumberRequired";
            public const string InvalidFranceAddressCode = "errInvalidFranceAddressCode";
            public const string IrelandAddressCodeFormatError = "IrelandAddressCodeFormatError";
            public const string InvalidNamesLengthError = "InvalidNamesLengthError";
            public const string EmailAddressContainsSpace = "EmailAddressContainsSpace";
            public const string AddressLine2Required = "AddressLine2Required";
            public const string DobInFutureError = "errDateOfBirthInFuture";
            public const string MinimumAgeError = "errMinimumAgeRequired";
            public const string MaximumAgeError = "errMaximumAgeExceeded";
        }

        public static class Leave
        {
            public const string PeriodNotAvailable = "0002";
            public const string NotLinkedToLeaveScheme = "0006";
            public const string ZeroLeaveDays = "0009";
            public const string NoBucket = "0015";
            public const string LeaveTypeNotConfigured = "0018";
            public const string LeaveBalance = "0021";
            public const string RequireDocumentation = "0023";
            public const string EightWeekRequiredDocument = "0062";
            public const string BeforeWeekend = "0024";
            public const string AfterWeekend = "0025";
            public const string BeforeHoliday = "0026";
            public const string AfterHoliday = "0027";
            public const string NoReason = "0054";
            public const string Overlap = "0057";
            public const string TotalBiggerThanPeriod = "0058";
            public const string ProblemProcessingLeaveValue = "0059";
            public const string ProblemProcessingMaxDate = "0060";
            public const string CommentsMaxLength = "0061";
            public const string ChangeTransactionType = "lblLeaveTransactionTypeCannotBeChanged";
            public const string ApplicationAlreadyCancelled = "lblApplicationAlreadyCancelled";
            public const string ApplicationCannotBeCancelled = "lblCannotCancel";
            public const string OnlyApprovedApplicationsCanBeCancelled = "lblOnlyApprovedCanBeCancelled";
            public const string ApplicationCancellation = "LeaveCancellation";
            public const string LeaveStatusRequired = "lblStatusRequired";
            public const string LeaveEntryTypeRequired = "lblLeaveEntryTypeRequired";
            public const string BucketRequired = "lblLeaveBucketRequired";
            public const string ReferenceTooLong = "lblReferenceMaxLength";
            public const string InactiveLeaveScheme = "lblInactiveLeaveScheme";

            public const string ModificationsDisabled = "lblModificationsNotAllowedForClosedRun";
            public const string LeaveTypeRequired = "lblLeaveTypeRequired";
            public const string EmployeeFrequencyRequired = "lblEmployeeFrequencyRequired";
            public const string CompanyRunRequired = "lblCompanyRunRequired";
            public const string LeaveReasonInvalid = "lblLeaveReasonInvalid";
            public const string LeaveBucketNotInvalid = "lblLeaveBucketInvalid";
            public const string TakeOnRunNotAllowed = "lblTakeOnRunNotAllowed";
            public const string ClosedRunNotAllowed = "lblClosedRunNotAllowed";
            public const string RunModificationAccessDenied = "lblRunModificationAccessDenied";
            public const string TimeOfDayRequired = "lblTimeOfDayRequired";
            public const string TimeOfDayOnlyForPartial = "lblTimeOfDayOnlyForPartial";

            public const string OnlyAdminCanSkipWorkflow = "lblOnlyAdminCanSkipWorkflow";
            public const string OnlyAdminCanSelectRun = "lblOnlyAdminCanSelectRun";
            public const string OnlyAdminCanChangeStatus = "lblOnlyAdminCanChangeStatus";
            public const string OnlyAdminCanSkipValidation = "lblOnlyAdminCanSkipValidation";

            public const string StartDateInvalid = "errInvalidStartDate";
            public const string StartDateCannotBeChangedOnCancellation = "lblStartDateCancellation";
            public const string EndDateCannotBeChangedOnCancellation = "lblEndDateCancellation";
            public const string LeaveValueCannotBeChangedOnCancellation = "lblNoOfDaysCancellation";

            public const string OverwriteHours = "lblLeaveHoursCannotBeOverwritten";
            public const string LeaveValueTooLow = "lblLeaveValueLessThanMinimum";
            public const string LeaveValueTooHigh = "lblLeaveValueGreaterThanMaximum";

            public const string LeaveHoursTooLow = "lblLeaveLowerThanMinimumHours";
            public const string LeaveHoursTooHigh = "lblLeaveHigherThanMaximumHours";
            public const string LeaveDaysTooLow = "lblLeaveLowerThanMinimumDays";
            public const string LeaveDaysTooHigh = "lblLeaveHigherThanMiximumDays";
            public const string LeaveDaysHigherThanConsecutiveDays = "lblLeaveHigherThanConsecutiveDays";
            public const string ThirteenthChequeNotAllowed = "lblThirteenthChequeNotAllowed";

            public const string DuplicateOverrideLeaveApplications = "errDuplicateOverrideLeaveApplication";
            public const string DuplicatePriorityLeaveApplications = "errDuplicatePriorityLeaveApplication";

            public const string NoFutureRunExists = "errNoFutureRunExists";
            public const string NotEmployedForRun = "errNotEmployedForRun";
            public const string LeaveAcrossFinancialYear = "errWithinFinancialYear";
            public const string LeaveStopDateExceeded = "errLeaveStopDateExceeded";
            public const string CalcIsRunning = "errCalcIsRunning";
            public const string ConcessionOnlyForAnnual = "errConcessionOnlyForAnnual";
            public const string InvalidConcessionDate = "errInvalidConcessionDate";
            public const string CannotSellDays = "errCannotSellDays";
            public const string InvalidLeaveDaysToSell = "errInvalidLeaveDaysToSell";
            public const string InvalidLeaveEntryTypeToSell = "errInvalidLeaveEntryTypeToSell";
            public const string InvalidConcessionPeriod = "errInvalidConcessionPeriod";

            public const string EffectiveDateIsAfterRunEndDate = "errEffectiveDateIsAfterRunEndDate";
            public const string DuplicateSetupDescriptionsNotAllowed = "errDuplicateSetupDescriptionsNotAllowed";
        }

        public static class BankDetails
        {
            public const string InvalidBranchCode = "0001";
            public const string AccountNumberFailed = "0002";
            public const string InvalidAccountType = "0003";
            public const string AccountNumberNotInt = "0004";
            public const string ValidationUnavailable = "0005";
            public const string InvalidSplit = "0006";
            public const string AccountExistsFor = "0007";
            public const string DuplicateAccounts = "0008";
            public const string InvalidBranchCodeLength = "0009";

            public const string BankDetailIdRequired = "0010";
            public const string PaymentMethodRequired = "0011";
            public const string AccountTypeRequired = "0012";
            public const string BankNameRequired = "0013";
            public const string BankBranchNoRequired = "0014";
            public const string BankAccountNoRequired = "0015";
            public const string SplitPercentageRequired = "0016";
            public const string AmountRequired = "0017";
            public const string ComponentRequired = "0018";
            public const string BankingKeyTypeRequired = "BankingKeyTypeRequired";
            public const string CpfNumberRequired = "CpfNumberRequired";
            public const string CnpjNumberRequired = "CnpjNumberRequired";
            public const string CellphoneNumberRequired = "CellphoneNumberRequired";
            public const string EmailAddressRequired = "EmailAddressRequired";
            public const string RandomKeyRequired = "RandomKeyRequired";

            public const string MainAccountNotSet = "0019";
            public const string EftMainAccountRequired = "0020";
            public const string MultipleMainAccountsNotAllowed = "0021";
            public const string MultipleCashChequeNotAllowed = "0022";
            public const string SplitTypeRequired = "0023";
            public const string BankAccountOwnerRequired = "0024";
            public const string CannotDeleteMainAccount = "0025";
            public const string BankAccountOwnerTypeRequired = "0026";
            public const string AmountGreaterThanZero = "0027";
            public const string ComponentMustBeNullOnMain = "0048";
            public const string CurrencyMustBeNullOnMain = "0049";
            public const string CannotAddSameComponentMoreThanOnce = "0050";
            public const string OnlyNumbersAndLettersAllowed = "0051";
            public const string CantDeleteLastRecord = "lblCantDeleteLastRecord";
            public const string InvalidSplitTypeUpload = "InvalidSplitTypeUpload";
            public const string InvalidAccountNumber = "lblInvalidAccountNumber";
            public const string ExternalChangesMadeReloadScreen = "lblExternalChangesMadeReloadScreen";
            public const string ComponentIdNullWhenNotComponent = "lblComponentIdNullWhenNotComponent";
            public const string CurrencyNullWhenNotComponent = "lblCurrencyNullWhenNotComponent";
            public const string BankDetailRequired = "lblBankDetailId";
            public const string CannotChangePaymentMethod = "lblCannotChangePaymentMethod";
            public const string InvalidSwiftCodeLength = "lblInvalidSwiftCodeLength";
            public const string AgencyCheckDigitRequired = "lblAgencyCheckDigitRequired";
            public const string AccountCheckDigitDigitRequired = "lblAccountCheckDigitDigitRequired";
            public const string AgencyCheckDigitMustBeSingleAlphaNumericCharacter = "lblAgencyCheckDigitMustBeSingleAlphaNumericCharacter";
            public const string AccountCheckDigitDigitMustBeSingleAlphaNumericCharacter = "lblAccountCheckDigitMustBeSingleAlphaNumericCharacter";
            public const string InvalidCpfNumber = "InvalidCpfNumber";
            public const string InvalidCnpjNumber = "InvalidCnpjNumber";
            public const string InvalidCellNumber = "InvalidCellNumber";
            public const string InvalidContactEmail = "InvalidEmailAddress";
            public const string InvalidRandomKeyLength = "InvalidRandomKeyLength";
            public const string InvalidAccountNumberLength = "InvalidAccountNumberLength";
            public const string InvalidAccountNoLength = "InvalidAccountNoLength";
            public const string InvalidBranchNoLength = "InvalidBranchCodeLength";
            public const string InvalidIFSCCodeMaxLength = "InvalidIFSCCodeMaxLength";
            public const string InvalidIFSCCodeFormat = "InvalidICSCCodeFormat";

            public static class Venuta
            {
                public const string CannotHaveNegative = "0040";
                public const string BankAccountOwnerTypeRequired = "0041";
                public const string BankAccountOwnerRequired = "0042";
                public const string AccountTypeRequired = "0043";
                public const string BankNameRequired = "0044";
                public const string BankBranchNoRequired = "0045";
                public const string BankAccountNoRequired = "0046";
                public const string ComponentRequired = "0047";
            }
        }

        public static class EmploymentStatus
        {
            public const string CannotHaveTaxDirectiveNumberIfTaxStatusNotDirective = "0001";
            public const string CannotHavePercentageAmountIfNotRequired = "0002";
            public const string CannotHavePercentageIfPercentageAmountIsNotPercentage = "0003";
            public const string CannotHaveDeemedMonthlyRemunerationIfDeemed75IndicatorTrue = "0004";
            public const string CannotHaveDeemed75IndicatorIfTaxStatusNotDirector = "0005";
            public const string CannotHaveDeemedRecoveryMonthlyIfDeemed75IndicatorTrue = "0006";
            public const string CannotHaveIrp30IfTaxStatusNotLabour = "0007";
            public const string InvalidTaxStatus = "0008";
            public const string CompanyRunRequiredWhenTerminating = "0009";
            public const string TerminationDateRequiredWhenTerminating = "0010";
            public const string TerminationReasonRequiredWhenTerminating = "0011";
            public const string TerminationLeaveRequiredWhenTerminating = "0012";
            public const string CannotHaveTerminationCompanyRunWhenNotTerminating = "0013";
            public const string CannotHaveTerminationDateWhenNotTerminating = "0014";
            public const string CannotHaveTerminationReasonWhenNotTerminating = "0015";
            public const string CannotHaveTerminationLeaveWhenNotTerminating = "0016";
            public const string EmploymentStatusAlreadyExists = "0017";
            public const string TerminationDateMustBeAfterEmploymentDate = "0018";
            public const string EmploymentDateMustBeAfterGroupJoinDate = "0019";
            public const string EmploymentDateMustNotOverlapExistingEmploymentStatus = "0020";
            public const string GroupJoinDateCannotBeBeforeAnyExistingRecords = "0021";
            public const string ReinstatementWithBreakNotPossibleWhenPayslipsForOpenPeriodExistCapturedAgainstOldRecord = "0022";
            public const string TerminationDateMustBeAfterStartOfTaxYearAsThereClosedRunsSinceThen = "0023";
            public const string TerminationDateCannotBeBeforeTheLastDayOfThePreviousTaxYear = "0024";
            public const string AdditionalDateMustBeNullIfSettingOff = "0025";
            public const string AdditionalDate1MustBeNullIfSettingOff = "0026";
            public const string AdditionalDateRequired = "0027";
            public const string AdditionalDate1Required = "0028";
            public const string PassportCountryRequiredIfIdentityTypeNotID = "0029";
            public const string PassportCountryMustBeNullIfIdentityTypeID = "0030";
            public const string PassportOrIdNumberRequired = "0031";
            public const string TempRequiredIfSettingOn = "0032";
            public const string TempMustBeNullIfSettingOff = "0033";
            public const string TaxDirectiveNumberRequiredIfTaxStatusIsDirective = "0034";
            public const string PercentageAmountRequired = "0035";
            public const string PercentageRequiredIfPercentageAmountIsPercentage = "0036";
            public const string DeemedMonthlyRemunerationRequiredIfDeemed75IndicatorFalse = "0037";
            public const string Deemed75IndicatorRequiredIfTaxStatusIsDirector = "0038";
            public const string DeemedRecoveryMonthlyRequiredIfDeemed75IndicatorFalse = "0039";
            public const string Irp30RequiredIfTaxStatusIsLabour = "0040";
            public const string CannotHaveAmountIfPercentageAmountIsNotAmount = "0041";
            public const string AmountRequiredIfPercentageAmountIsAmount = "0042";
            public const string InvalidOldEmployeeId = "0043";
            public const string InvalidOldEmployeeIdGroupId = "0044";
            public const string TaxReferenceNumberMustBe10NumericCharactersLong = "0045";
            public const string PleaseAddEncashmentComponentBeforeYouCanEncach = "0046";
            public const string TerminationOnClosedRunNotAllowed = "0047";
            public const string CannotReinstateActiveTaxProfile = "0048";
            public const string TaxReferenceNumberRequired = "0049";
            public const string PassportIssueCountryRequired = "0051";
            public const string DuplicateEmployeeNumbers = "0052";
            public const string DateShouldNotBeOlderThanMaxAgeAllowed = "0053";
            public const string EmploymentActionMustBeNullOnCreation = "lblEmploymentActionMustBeNullOnCreation";
            public const string EmploymentDateCantBeBeforeAnyTerminationDate = "lblEmploymentDateCantBeBeforeAnyTerminationDate";
            public const string DeleteNotLatestError = "lblDeleteNotLatestError";
            public const string InvalidIdentityType = "lblInvalidIdentityType";
            public const string IdNumberInvalid = "lblIdNumberInvalid";
            public const string IdNumberMustBeNull = "lblIdNumberMustBeNull";
            public const string InvalidPassportNumber = "lblInvalidPassportNumber";
            public const string PassportExpiryBeforeIssueDate = "lblPassportExpiryBeforeIssueDate";
            public const string PermitExpiryBeforeIssueDate = "lblPermitExpiryBeforeIssueDate";
            public const string PassportExpiryDateRequired = "lblPassportExpiryDateRequired";
            public const string PermitExpiryDateRequired = "lblPermitExpiryDateRequired";
            public const string ExistingEmploymentDate = "lblExistingEmploymentDate";
            public const string CannotChangeEmploymentDateExistingRunsError = "lbCannotChangeEmploymentDateExistingRunsError";
            public const string MustReInstateNewRecord = "lblMustReInstateNewRecord";
            public const string NoVacantJob = "lblNoVacantJob";
            public const string IdNumberNotCorrectLength = "lblIdNumberNotCorrectLength";
            public const string FinalizeTaxInvalidReferenceError = "lblFinalizeTaxInvalidReferenceError";
            public const string InvalidTaxReferenceNumber = "lblInvalidTaxReferenceNumber";
            public const string InvalidTaxReferenceNumberLength = "InvalidTaxReferenceNumberLength";
            public const string DateOfBirthNotMatchingIdNumber = "lblDateOfBirthNotMatchingIdNumber";
            public const string CitizanshipNotMatchingIdNumber = "lblCitizanshipNotMatchingIdNumber";
            public const string GenderNotMatchingIdNumber = "lblGenderNotMatchingIdNumber";
            public const string DeleteExistingPayslipsError = "lblDeleteExistingPayslipsError";
            public const string IdNumberDuplicate = "lblIdNumberDuplicate";
            public const string HasPayslipBeforeEmploymentDate = "lblHasPayslipBeforeEmploymentDate";
            public const string DuplicateEmploymentDate = "lblDuplicateEmploymentDate";
            public const string DuplicateTerminationDate = "lblDuplicateTerminationDate";
            public const string ObsoleteTaxStatusId = "errObsoleteTaxStatusId";
            public const string DistinctEmployeeEmployementActions = "lblDistinctEmployeeEmployementActions";
            public const string EmployeeFrequencyRequired = "lblEmployeeFrequencyRequired";
            public const string EmploymentDateAfterPayRateOrPositionEffectiveDate = "0054";
            public const string IdNumbersOnly = "errIdNumbersOnly";
            public const string DeleteExistingPayratesError = "errLinkedToPayrate";
            public const string TaxDirectiveNumberRequiredOnSingleIncomeSource = "errTaxDirectiveNumberRequired";
            public const string EmploymentStatusNewNotAllowed = "errEmploymentStatusNewNotAllowed";
            public const string ErrorGroupJoinDate = "errGroupJoinDate";
            public const string ErrorEmploymentDate = "errEmploymentDate";
            public const string ErrorReinstateWithBreakNotAllowed = "errReinstateWithBreakNotAllowed";
            public const string AsylumIdNumberRequired = "errAsylumIdNumberRequired";
            public const string MustBeAfterSuspensions = "lblMustBeAfterSuspensions";
            public const string TaxStatusRequired = "lblTaxStatusRequired";
            public const string NatureOfPersonRequired = "lblNatureOfPersonRequired";
            public const string ErrorDuplicateIdNumber = "errDuplicateIdNumber";
            public const string ErrorMustHaveActiveFrequency = "errMustHaveActiveFrequency";
            public const string InvalidTerminationReason = "errInvalidTerminationReason";
            public const string InvalidSocialSecurityNumber = "errInvalidSocialSecurityNumber";
            public const string InvalidCanadianIdLength = "errInvalidCanadianIdLength";
            public const string IdNumberExpiryDateRequired = "lblIdNumberExpiryDateRequired";
            public const string InvalidIdNumberFormat = "lblInvalidIdNumberFormat";
            public const string ArrearsComponentsWarning = "lblArrearsComponentsWarning";
            public const string IdNumberMaxLengthExceeded = "lblIdNumberMaxLengthExceeded";
            public const string InvalidPassportMaximumNumber = "lblInvalidPassportMaximumNumber";
            public const string InvalidDniNieNumber = "lblInvalidDniNieNumber";
            public const string InvalidIndiaIdLength = "errInvalidIndiaIdLength";
            public const string InvalidAadharNumber = "errAadharNumberDigitsOnly";
            public const string InvalidNumericValues = "InvalidNumericValues";
            public const string InvalidNAF = "lblInvalidNAF";
            public const string EmploymentIdentifierRequired = "errEmploymentIdentifierRequired";
            public const string EmploymentIdentifierMaxLength = "errEmploymentIdentifierMaxLength";
            public const string IdNumberPPSDuplicate = "errIdNumberPPSDuplicate";
            public const string IdNumberAndIdentifierNotUnique = "errIdNumberAndIdentifierNotUnique";
            public const string SeveranceDaysRequired = "errSeveranceDaysRequired";
            public const string PaySeveranceRequired = "errPaySeveranceRequired";
        }

        public static class EmployeeTakeOn
        {
            public const string CppEmployeeDeductionRequired = "errCppEmployeeDeductionRequired";
            public const string CppPeriodicContributionRequired = "errCppPeriodicContributionRequired";
            public const string CppRegularContributionRequired = "errCppRegularContributionRequired";
            public const string CppIncomeRegularRequired = "errCppIncomeRegularRequired";
            public const string CppIncomePeriodicRequired = "errCppIncomePeriodicRequired";
            public const string CppTotalIncomeRequired = "errCppTotalIncomeRequired";

            public const string QppEmployeeDeductionRequired = "errQppEmployeeDeductionRequired";
            public const string QppIncomeRegularRequired = "errQppIncomeRegularRequired";
            public const string QppIncomePeriodicRequired = "errQppIncomePeriodicRequired";
            public const string QppTotalIncomeRequired = "errQppTotalIncomeRequired";
            public const string QppPeriodicContributionRequired = "errQppPeriodicContributionRequired";
            public const string QppRegularContributionRequired = "errQppRegularContributionRequired";

            public const string EiEmployeeDeductionRequired = "errEiEmployeeDeductionRequired";
            public const string EiPeriodicContributionRequired = "errEiPeriodicContributionRequired";
            public const string EiRegularContributionRequired = "errEiRegularContributionRequired";
            public const string EiIncomeRegularRequired = "errEiIncomeRegularRequired";
            public const string EiIncomePeriodicRequired = "errEiIncomePeriodicRequired";
            public const string EiTotalIncomeRequired = "errEiTotalIncomeRequired";

            public const string QpipEmployeeDeductionRequired = "errQpipEmployeeDeductionRequired";
            public const string QpipIncomePeriodicRequired = "errQpipIncomePeriodicRequired";
            public const string QpipIncomeRegularRequired = "errQpipIncomeRegularRequired";
            public const string QpipPeriodicContributionRequired = "errQpipPeriodicContributionRequired";
            public const string QpipRegularContributionRequired = "errQpipRegularContributionRequired";
            public const string QpipTotalIncomeRequired = "errQpipTotalIncomeRequired";

            public const string FedTaxDeductionRequired = "errFedTaxDeductionRequired";
            public const string FederalTaxRequired = "errFederalTaxRequired";
            public const string ProvincialTaxRequired = "errProvincialTaxRequired";
            public const string LumpSumTaxRequired = "errLumpSumTaxRequired";

            public const string BcEmployerTaxIncomeRequired = "errBcEmployerTaxIncomeRequired";
            public const string MbEmployerTaxIncomeRequired = "errMbEmployerTaxIncomeRequired";
            public const string NlEmployerTaxIncomeRequired = "errNlEmployerTaxIncomeRequired";
            public const string OnEmployerTaxIncomeRequired = "errOnEmployerTaxIncomeRequired";
            public const string QcEmployerTaxIncomeRequired = "errQcEmployerTaxIncomeRequired";
            public const string OnEmployerTaxRequired = "errOnEmployerTaxRequired";
            public const string BcEmployerTaxRequired = "errBcEmployerTaxRequired";
            public const string QcEmployerTaxRequired = "errQcEmployerTaxRequired";
            public const string MbEmployerTaxRequired = "errMbEmployerTaxRequired";
            public const string NlEmployerTaxRequired = "errNlEmployerTaxRequired";

            public const string WorkersCompensationContributionRequired = "errWorkersCompensationContributionRequired";
            public const string ProvincialWorkersCompEarningsRequired = "errProvincialWorkersCompEarningsRequired";
            public const string WorkersCompensationIncomeRequired = "errWorkersCompensationIncomeRequired";

            public const string WorkforceSkillsDevelopmentIncomeRequired = "errWorkforceSkillsDevelopmentIncomeRequired";
            public const string WorkforceSkillsDevelopmentEmployerRequired = "errWorkforceSkillsDevelopmentEmployerRequired";

            public const string RegularTaxableIncomeRequired = "errRegularTaxableIncomeRequired";
            public const string PeriodicTaxableIncomeRequired = "errPeriodicTaxableIncomeRequired";
            public const string TrueTaxableBonusIncomeRequired = "errTrueTaxableBonusIncomeRequired";
            public const string TrueRegularTaxableIncomeRequired = "errTrueRegularTaxableIncomeRequired";
            public const string QuebecTrueTaxableIncomeRequired = "errQuebecTrueTaxableIncomeRequired";
            public const string QuebecRegularTaxableIncomeRequired = "errQuebecRegularTaxableIncomeRequired";
            public const string NunavutTaxableIncomeRequired = "errNunavutTaxableIncomeRequired";
            public const string NorthwestTerritoryTaxableIncomeRequired = "errNorthwestTerritoryTaxableIncomeRequired";
            public const string NunavutTaxRequired = "errNunavutTaxRequired";
            public const string NorthwestTerritoryTaxRequired = "errNorthwestTerritoryTaxRequired";

            public const string RetirementAllowableRequired = "errRetirementAllowableRequired";
            public const string PeriodicRetirementAllowableRequired = "errPeriodicRetirementAllowableRequired";

            public const string QuebecPeriodicTaxableIncomeRequired = "errQuebecPeriodicTaxableIncomeRequired";
        }

        public static class LeaveSetup
        {
            public const string NoOpenOrFutureRuns = "0001";
            public const string EffectiveDatePrecedesCompanyLeaveSetupTypes = "0002";
            public const string SharesEffectiveDateWithPreviousSetup = "0003";
            public const string EmployeePreviouslyLinkedToSchemeMigrateBalancesManually = "0004";
            public const string RecordDeletedMigrateBalancesManually = "0005";
            public const string InactiveLeaveScheme = "lblInactiveScheme";
            public const string LeaveSchemeInUse = "errLeaveSchemeInUse";

            public const string EffectiveDateReadOnly = "errEffectiveDateReadOnly";
            public const string LeaveSchemeReadOnly = "errLeaveSchemeReadOnly";
            public const string HolidayCategoryReadOnly = "errHolidayCategoryReadOnly";
            public const string EffectiveDateBeforeEmploymentDate = "errEffectiveDateBeforeEmploymentDate";
        }

        public static class Workflow
        {
            public const string NoWorkflow = "0001";
            public const string NoApprovers = "0002";
            public const string ApproverNoEmail = "0003";
            public const string InvalidRun = "0004";
            public const string SelectedApproverRequired = "0005";
            public const string SelectedApproverInvalid = "0006";
            public const string SelectedApproverMustBeNull = "0007";
            public const string RunRequired = "0008";
            public const string RunMustBeNull = "0009";
            public const string WorkflowActionRequired = "0010";
            public const string CommentsRequiredOnReject = "CommentRequired";
            public const string InvalidAppover = "InvalidAppover";
            public const string ClaimAlreadyCompleted = "ClaimAlreadyCompleted";
        }

        public static class Payslip
        {
            public const string CannotCreatePayslipInThisTaxYear = "0003";
            public const string PayslipDeletedSuccess = "0002";
            public const string UnableToCreatePayslipDueToCompanySettings = "0004";
            public const string UnableToCreateDuetoPayRate = "errPayRateDateLaterThanPeriodEndDate";
            public const string PayslipExists = "errPayslipExists";
            public const string CalcIsRunning = "errCalcIsRunning";
            public const string CommentsMaxLength = "errCommentsMaxLength";
            public const string InvalidExternalPayslipRef = "errInvalidExternalPayslipRef";
        }

        public static class Position
        {
            public const string GLDisallowed = "0001";
            public const string RequireGL = "0002";
            public const string NoPayRate = "0003";
            public const string InvalidPositionEffectiveDate = "0004";
            public const string InvalidJob = "0005";
            public const string InvalidPayPoint = "0006";
            public const string InvalidRegion = "0007";
            public const string InvalidGrade = "0008";
            public const string InvalidSubCategory = "0009";
            public const string InvalidCategory = "0010";
            public const string InvalidWorkflowRole = "0011";
            public const string InvalidRoster = "0012";
            public const string JobNameNotAllowed = "0013";
            public const string TradeUnionNotAllowed = "0014";
            public const string OrganizationGroupNotAllowed = "0015";
            public const string PayPointNotAllowed = "0016";
            public const string OrganizationRegionNotAllowed = "0017";
            public const string GradeNotAllowed = "0018";
            public const string PositionTypeNotAllowed = "0019";
            public const string IsPromotionNotAllowed = "0020";
            public const string AdministratorNotAllowed = "0021";
            public const string CommentsNotAllowed = "0022";
            public const string InvalidEmployeeId = "0023";
            public const string InvalidOrganizationPosition = "0024";
            public const string InvalidOrganizationPositionExpired = "0025";
            public const string ReportysToPersonInvalid = "0026";
            public const string PositionEffectiveDateInFuture = "0027";
            public const string DirectlyReportMustBeInSameCompanyGroup = "0028";
            public const string DirectlyReportMustBeInSameCompany = "0029";
            public const string NotAllowedToReportTo = "0030";
            public const string DirectReportsTerminated = "0031";
            public const string NotLatestPosition = "0032";
            public const string AdministratorTerminated = "0033";
            public const string FrequencyRequired = "0034";
            public const string WorkforcePlanningDisabled = "0035";
            public const string FrequencyRequiredForJob = "0036";

            public const string DeleteNewerRecords = "lblDeleteNewerRecords";
            public const string InvalidEffectiveDate = "lblInvalidEffectiveDate";
            public const string OrganizationPositionRequired = "lblOrganizationPositionRequired";
            public const string OrganizationGroupRequired = "lblOrganizationGroupRequired";
            public const string ReportysToPersonRequired = "lblReportysToPersonRequired";
            public const string PayPointRequired = "lblPayPointRequired";
            public const string RegionRequired = "lblRegionRequired";
            public const string CategoryRequired = "lblCategoryRequired";
            public const string SubCategoryRequired = "lblSubCategoryRequired";
            public const string JobRequired = "lblJobRequired";
            public const string GradeRequired = "lblGradeRequired";
            public const string Lockdown = "lblLockdown";
            public const string DeleteTerminated = "lblDeleteTerminated";
            public const string EffectiveDateRequired = "lblEffectiveDateRequired";
            public const string OrganizationPositionReadOnly = "lblOrganizationPositionReadOnly";
            public const string EffectiveDateNotBeforeGroupJoinDate = "lblEffectiveDateCannotBeBeforeGroupJoinDate";
            public const string InactiveOrgUnitError = "lblInactiveOrgUnitError";
            public const string AbolishedJob = "lblAbolishedJob";
            public const string FilledJob = "lblFilledJob";
            public const string ActiveJob = "lblActiveJob";
            public const string JobOrgUnitMismatch = "lblJobOrgUnitMismatch";
            public const string JobOrgPosMismatch = "lblJobOrgPosMismatch";
            public const string ExcludedPayFrequency = "lblExcludedPayFrequency";
            public const string EffectiveDateNotAfterContractEndDate = "lblEffectiveDateCannotBeAfterContractEndDate";
        }

        public static class CustomField
        {
            public const string InvalidCustomField = "0007";
            public const string InvalidOption = "0008";
            public const string InvalidTextValue = "0009";
            public const string InvalidNumber = "0010";
            public const string CustomFieldRequired = "0011";
            public const string InvalidBoolean = "0012";
            public const string InvalidDate = "0013";
            public const string EffectiveRequired = "lblEffectiveRequired";
            public const string AccessDeniedCreate = "AccessDeniedCreate";

            //Brazil
            public const string InvalidCpfNumber = "InvalidCpfNumber";
            public const string InvalidCpfNumberLength = "InvalidCpfNumberLength";
            public const string InvalidCnpjNumber = "InvalidCnpjNumber";
            public const string InvalidCnpjNumberLength = "InvalidCnpjNumberLength";
            public const string DuplicateCpfNumber = "DuplicateCpfNumber";
            public const string TotalCannotExceed90Days = "TotalCannotExceed90Days";
            public const string SubContractTypeNotAllowed = "SubContractTypeNotAllowed";

            //Australia
            public const string InvalidTfnNumber = "InvalidTfnNumber";
            public const string InvalidTfnNumberLength = "InvalidTfnNumberLength";
            public const string InvalidAbnNumber = "InvalidAbnNumber";
            public const string InvalidAbnNumberLength = "InvalidAbnNumberLength";
            public const string InvalidActOptions = "InvalidActOptions";
            public const string InvalidPrsOptions = "InvalidPrsOptions";
            public const string InvalidSuperannuationFundSplit = "InvalidSuperannuationFundSplit";

            //Canada
            public const string InvalidProvincialPersonalTaxCredits = "InvalidProvincialPersonalTaxCredits";
            public const string InvalidFederalPersonalTaxCredits = "InvalidFederalPersonalTaxCredits";
            public const string ProvincialTaxCreditsMatchesStatutoryRates = "ProvincialTaxCreditsMatchesStatutoryRates";
            public const string FederalTaxCreditsMatchesStatutoryRates = "FederalTaxCreditsMatchesStatutoryRates";
        }

        public static class CustomFormField
        {
            public const string InvalidLength = "lblInvalidLength";
            public const string InvalidTaxCode = "lblInvalidTaxCode";
            public const string InvalidTaxRegime = "lblInvalidTaxRegime";

            public const string CaptureTaxCodeRequired = "lblCaptureTaxCodeRequired";
        }

        public static class EntityClosedRunValidator
        {
            public const string EntityUpdateClosedRun = "0001";
            public const string DependentUpdateClosedRun = "0002";
            public const string EntityDeleteClosedRun = "0003";
            public const string PayRateNotFound = "0004";
        }

        public static class Project
        {
            public const string ProjectPayRateMustBeEmpty = "0001";
            public const string HoursPerDayMustBeEmpty = "0002";
            public const string DaysPerMonthMustBeEmpty = "0003";
            public const string PayFrequencyMustBeEmpty = "0004";
            public const string InvalidEndDate = "0005";
            public const string InvalidStartDate = "0006";
            public const string InvalidEndDateMustBeAfterStartDate = "0007";
            public const string DuplicateEffectiveDate = "0008";
            public const string InvalidEndDateMustBeAfterCurrentDate = "0009";
            public const string EndDateRequired = "0010";
            public const string EffectiveDateRequired = "0011";
            public const string ProjectRequired = "0012";
            public const string ReminderOptionIsNonEditable = "0013";
            public const string EffectiveDateBeforeEmploymentDate = "0014";
            public const string InvalidHoursPerDay = "0015";
            public const string InvalidDaysPerMonth = "0016";
        }

        public static class RecurringComponents
        {
            public static class Tables
            {
                public static class Garnishee
                {
                    public const string CreditorNameRequired = "0001";
                    public const string CapitalBalanceRequired = "0002";
                    public const string DeductionAmountRequired = "0003";
                    public const string CapitalBalanceMustBeNumberOrNA = "0004";
                    public const string CapitalBalanceIncorrectFormat = "CapitalBalanceIncorrectFormat";
                }

                public static class Base
                {
                    public const string EmploymentDateNotFound = "errEmploymentDateNotFound";
                }
            }
        }

        public static class BulkCaptureCodes
        {
            public const string EmployeeIdRequired = "EmployeeIdRequired";
            public const string RunIdRequired = "RunIdRequired";
            public const string ComponentIdRequired = "ComponentIdRequired";
            public const string VariableDefIdRequired = "VariableDefIdRequired";
            public const string VariableAmountRequired = "VariableAmountRequired";
            public const string KeyCombinationAlreadyExists = "KeyCombinationAlreadyExists";
            public const string InvalidInputType = "InvalidInputType";
            public const string InvalidEmployeeForSpecifiedRun = "InvalidEmployeeForSpecifiedRun";
            public const string InvalidComponentForSpecifiedRun = "InvalidComponentForSpecifiedRun";
            public const string InvalidCostCentreForSpecifiedRun = "InvalidCostCentreForSpecifiedRun";
            public const string InvalidProjectForSpecifiedRun = "InvalidProjectForSpecifiedRun";
            public const string InvalidActivityForSpecifiedRun = "InvalidActivityForSpecifiedRun";
            public const string RunClosedEditNotAllowed = "RunClosedEditNotAllowed";
            public const string TakeOnRunEditNotAllowed = "TakeOnRunEditNotAllowed";
            public const string InvalidPayrateAndFrequency = "InvalidPayrateAndFrequency";
            public const string NonUniqueComponentDescription = "NonUniqueComponentDescription";
            public const string InvalidComponentCode = "InvalidComponentCode";
            public const string SettingForTerminatedEmployees = "SettingForTerminatedEmployees";
            public const string NoActiveStatus = "lblNoActiveStatus";
            public const string RequiresExistingRecurringRecord = "RequiresExistingRecurringRecord";
            public const string CommentsMaxLength = "CommentsMaxLength";
            public const string NegativeCostingNotAllowed = "lblNegativeCostingNotAllowed";
            public const string EmployeeNotEmployedYet = "lblEmployeeNotEmployedYet";
            public const string RecoveryAmountNotWithDeduction = "lblRecoveryAmountNotWithDeduction";
            public const string TotalValueMaxLength = "errTotalValueMaxLength";
            public const string VariableAmountMaxLength = "errVariableAmountMaxLength";
            public const string ComponentValueMaxLength = "errComponentValueMaxLength";
            public const string RunNotFound = "errRunNotFound";
            public const string DuplicateRuns = "errDuplicateRuns";
            public const string NoActivePayRateExists = "errNoActivePayRateExists";
            public const string SettingForTerminatedEmployeesWarning = "SettingForTerminatedEmployeesWarning";
            public const string InvalidPostRunByRunBeforeDateRun = "errInvalidPostRunByRunBeforeDateRun";
            public const string StatutoryOrAutoComponentUploadNotAllowed = "lblStatutoryOrAutoComponentUploadNotAllowed";
        }

        public static class OrganizationGroup
        {
            public const string CompanyRequired = "lblCompanyRequired";
            public const string HasDependencies = "lblHasDependencies";
            public const string CantLinkToSelf = "lblCantLinkToSelf";
            public const string ParentOrgUnitNotFound = "lblParentOrgUnitNotFound";
            public const string CircularUnitError = "lblCircularUnitError";
            public const string UploadCodeRequired = "lblUploadCodeRequired";
            public const string OrganizationLevelRequired = "lblOrganizationLevelRequired";
            public const string DuplicateUploadCode = "lblDuplicateUploadCode";
            public const string InvalidInactiveDate = "lblInvalidInactiveDate";
            public const string OrganizationInProgress = "lblOrganizationInProgress";
            public const string DuplicateDescription = "errDuplicateDescription";
            public const string NewParentUnitMustBeSameLevelAsOldParentUnit = "errParentUnitMustBeSameLevelAsOldParentUnit";
            public const string UnitOrSubUnitsContainActiveEmployees = "errUnitOrSubUnitsContainActiveEmployees";
        }

        public static class EvaluationDefaults
        {
            public const string DoesNotExistFor = "lblDoesNotExistFor";
            public const string SectionAlreadyExistsFor = "lblSectionAlreadyExistsFor";
            public const string WithEffectiveDate = "lblWithEffectiveDate";
        }

        public static class EmployeeReviewDefaultRaters
        {
            public const string EmployeeReviewDefaultRatersHeader = "lblEmployeeReviewDefaultRatersHeader";
            public const string DefRaterIdGreaterThanZero = "errDefRaterIdGreaterThanZero";
            public const string ProcessTypeRequired = "errProcessTypeRequired";
            public const string RaterTypeRequired = "errRaterTypeRequired";
            public const string TemplateIdRequired = "errTemplateIdRequired";
            public const string EmployeeIdRequired = "errEmployeeIdRequired";
            public const string WeightingValidationError = "errWeightingValidationError";
            public const string RaterTypeAlreadyLinked = "errRaterTypeAlreadyLinked";
            public const string RaterIdAlreadyLinked = "errRaterIdAlreadyLinked";
            public const string ProcessTypeMoreThanOneDefaultTemplate = "errProcessTypeMoreThanOneDefaultTemplate";
            public const string RaterEmpNumberRequired = "errRaterEmpNumberRequired";
            public const string RaterEmpNumberDoesNotMatchRaterTypeSelection = "errRaterEmpNumberDoesNotMatchRaterTypeSelection";
            public const string RaterWeightningsMust100 = "errRaterWeightningsMust100";
            public const string MoreThanOneRaterSelectedInheritRaterFromPosition = "errMoreThanOneRaterSelectedInheritRaterFromPosition";
            public const string RaterEmpNumberDoesNotMatchRaterTypeAndInheritSelection = "errRaterEmpNumberDoesNotMatchRaterTypeAndInheritSelection";
            public const string InvalidRaterTypeSelection = "errInvalidRaterTypeSelection";
        }

        public static class AssetRegister
        {
            public const string QuantityRequired = "lblQuantityRequired";
            public const string DescriptionRequired = "lblDescriptionRequired";
            public const string IssuedDateRequired = "lblIssuedDateRequired";
            public const string ReturnDateCannotBeAfterIssuedDate = "lblReturnDateCannotBeAfterIssuedDate";
        }

        public static class Dependant
        {
            public const string AdultDependantWithNoRelationship = "AdultDependantWithNoRelationship";
            public const string AdultRelationshipWhenDependantTypeNotAdult = "lblAdultRelationshipWhenDependantTypeNotAdult";
            public const string BreadWinnerWhenDependantTypeNotSpouse = "lblBreadWinnerWhenDependantTypeNotSpouse";
            public const string ChildMedicalAidCalculations = "ChildMedicalAidCalculations";
            public const string ChildWithoutBirthdate = "ChildWithoutBirthdate";
            public const string ChildBirthDateWhenDependantTypeNotChild = "lblChildBirthDateWhenDependantTypeNotChild";
            public const string DeleteAccessDenied = "lblDeleteAccessDenied";
            public const string EffectiveDateRequired = "lblEffectiveDateRequired";
            public const string HasClosedRun = "lblHasClosedRun";
            public const string InvalidBirthDate = "InvalidBirthDate";
            public const string InvalidFrequency = "InvalidFrequency";
            public const string InactiveDateCannotBeAdvisedOnPost = "lblInactiveDateCannotBeAdvisedOnPost";
            public const string InactiveDateCannotBeBeforeEffectiveDate = "lblInactiveDateCannotBeBeforeEffectiveDate";
            public const string MaritalDateRequiredForSpouseDependent = "lblMaritalDateRequiredForSpouseDependent";
            public const string MaritalDateWhenDependantTypeNotSpouse = "lblMaritalDateWhenDependantTypeNotSpouse";
            public const string MaritalStatusWhenDependantTypeNotSpouse = "lblMaritalStatusWhenDependantTypeNotSpouse";
            public const string OnlyChildDependantAllowed = "lblOnlyChildDependantAllowed";
            public const string OnlySpecificFieldsCanBeChangedWhenRunIsClosed = "lblOnlySpecificFieldsCanBeChangedWhenRunIsClosed";
            public const string RestrictEmployerContributionDependantTypeSpouse = "lblRestrictEmployerContributionDependantTypeSpouse";
            public const string WorkingSpouseWhenDependantTypeNotSpouse = "lblWorkingSpouseWhenDependantTypeNotSpouse";
            public const string CanEditHistoricalRecord = "lblCanEditHistoricalRecord";
            public const string EmployeeNumberRequired = "lblEmpRequired";
            public const string EmployeePermissions = "lblEmployeePermissions";

            public const string HasOneDependantPopulated = "lblHasOneDependantPopulated";
            public const string ChildrenMaximum = "lblChildrenMaximum";
            public const string AdultMaximum = "lblAdultMaximum";
            public const string WorkingSpouseMaximum = "lblWorkingSpouseMaximum";
            public const string NonWorkingSpouseMaximum = "lblNonWorkingSpouseMaximum";

            public const string HasDuplicateEffectiveDate = "lblDuplicateEffectiveDate";
        }

        public static class ReleaseNote
        {
            public const string DevTicketIdIsUnique = "lblDevTicketIdIsUnique";
            public const string CountryIdRequired = "lblCountryIdRequired";
        }

        public static class EmployeeAttachment
        {
            public const string AttachmentRequired = "lblAttachmentRequired";
            public const string ClassificationRequired = "lblClassificationRequired";
            public const string DescriptionMaxLength = "lblDescriptionMaxLength";
            public const string NameMaxLength = "lblNameMaxLength";
        }

        public static class GeneralAttachment
        {
            public const string UnsupportedFileType = "lblUnsupportedFileType";
            public const string ExtensionDoesNotMatchContent = "lblExtensionDoesNotMatchContent";
            public const string AttachmentTooLarge = "lblAttachmentTooLarge";
        }

        public static class Suspension
        {
            public const string RequiredSuspensionDate = "lblSuspensionDateRequired";
            public const string RequiredReturnDate = "lblReturnDateRequired";
            public const string RequiredSuspensionReason = "lblSuspensionReasonRequired";
            public const string RequiredPaymentMethod = "lblPaymentMethodRequired";
            public const string RequiredMinimunPeriodOwed = "lblMinimunPeriodOwedRequired";
            public const string RequiredPercentage = "lblPercentageRequired";
            public const string RequiredAmount = "lblAmountRequired";
            public const string PercentageNotRequired = "lblPercentageNotRequired";
            public const string AmountNotRequired = "lblAmountNotRequired";
            public const string InvalidMinimumPeriod = "lblMinimunPeriodInvalid";
            public const string InvalidMaternityGender = "lblMaternityGenderInvalid";
            public const string InvalidDate = "lblDateInvalid";
            public const string InvalidPercentageAmount = "lblInvalidPercentageAmount";
            public const string InvalidOverridingAmount = "lblInvalidOverridingAmount";
            public const string HasBeenSuspended = "lblHasBeenSuspended";
            public const string SuspensionNotAfterTermination = "lblSuspensionNotAfterTermination";
            public const string SuspensionNotBeforeStartDate = "lblSuspensionNotBeforeStartDate";
            public const string RequiredLastDateForWhichPaid = "LastDateForWhichPaidRequired";
            public const string CannotDeleteSuspensionWithSubmittedSnapshots = "errCannotDeleteSuspensionWithSubmittedSnapshots";
            public const string InvalidFirstDayWorked = "errInvalidFirstDayWorked";
            public const string InvalidOverrideFirstDayWorked = "errInvalidOverrideFirstDayWorked";
            public const string InvalidLastDayPaid = "errInvalidLastDayPaid";
            public const string InvalidFinalPeriodEndDate = "errInvalidFinalPeriodEndDate";
            public const string UkValidNumberOfWeeks = "UkValidNumberOfWeeks";
            public const string UkValidReturnDate = "UkValidReturnDate";
            public const string SenegalTaxYearDates = "SenegalTaxYearDates";
            public const string SenegalMaternalSuspensionInCurrentYear = "SenegalMaternalSuspensionInCurrentYear";
            public const string SenegalLastActivePayslipAfterSuspension = "SenegalLastActivePayslipAfterSuspension";
            public const string SenegalFirstActivePayslipBeforeDateOfReturn = "SenegalFirstActivePayslipBeforeDateOfReturn";
            public const string AverageTaxCalculations = "AverageTaxCalculations";
            public const string DatesInSameRun = "DatesInSameRun";
            public const string InvalidExpectedReturn = "InvalidExpectedReturn";
            public const string UkSNCPNumberOfWeeksCap = "lblSNCPNumberOfWeeksCap";
            public const string UkBabyBirthdateRequired = "lblBabyBirthdateRequired";
            public const string UkSNCPWithinSixtyEightWeekWarning = "lblSNCPWithinSixtyEightWeekWarning";
            public const string InvalidPaymentMethod = "lblPaymentMethodInvalid";
            public const string EsTransactionTypeRequired = "lblTransactionTypeRequired";
            public const string SuspensionReasonIsNotCorrectForType = "errInvalidSuspensionReasonForType";
            public const string GenderIsNotCorrectForStatutoryMaternityPay = "errGenderIsNotCorrectForStatutoryMaternityPay";
        }

        public static class OutOfOffice
        {
            public const string FromDateRequired = "0001";
            public const string ToDateRequired = "0002";
            public const string AltEmployeeRequired = "0003";
            public const string FromDateBeforeToday = "0004";
            public const string FromDateAfterToDate = "0005";
            public const string AltEmployeeSameAsEmployee = "0006";
            public const string EmployeHasOutOfOffice = "0007";
            public const string DuplicateEmployeeRecords = "0008";
        }

        public static class EmployeeJournal
        {
            public const string DescriptionMaxLength = "0001";
        }

        public static class CompanyGLDetail
        {
            public const string InvalidOperation = "0001";
        }

        public static class EmployeeRecurringTemplate
        {
            public const string DuplicateEmployeeRecords = "0001";
        }

        public static class EmployeeRecurringCostingSplit
        {
            public const string CostCentreRequired = "errCostCentreRequired";
            public const string ZeroPercentage = "errZeroPercentage";
            public const string SplitAlreadyExists = "errSplitAlreadyExists";
            public const string DuplicateOrganizationUnit = "errDuplicateOrganizationUnit";
            public const string InvalidTotalPercentage = "errInvalidTotalPercentage";
            public const string DuplicateProject = "errDuplicateProject";
            public const string ProjectRequired = "errProjectRequired";
            public const string BasedOnSplitOptionRequired = "errBasedOnSplitOptionRequired";
            public const string EffectiveDateRequired = "errEffectiveDateRequired";
        }

        public static class Claim
        {
            public const string AttachmentRequired = "0003";
            public const string DescriptionAboveMaxLength = "0005";
            public const string EnterValidAmount = "0006";
            public const string ReturnDateRequired = "0007";
            public const string PeriodClosed = "0008";
            public const string RegistrationNumberRequired = "0009";
            public const string DepartureODORequired = "0010";
            public const string ReturnODORequired = "0011";
            public const string DescriptionRequired = "0012";
            public const string DestinationRequired = "0013";
            public const string DepartureMustBeBeforeArrival = "0014";
            public const string ArrivalODOMustBeGreaterThanDeparture = "0015";
            public const string DepartureDateOverlapsWithExisting = "0016";
            public const string ArrivalDateOverlapsWithExisting = "0017";
            public const string DepartureODOOverlapsWithExisting = "0018";
            public const string ArrivalODOOverlapsWithExisting = "0019";
            public const string TravelDistanceRequired = "0032";
            public const string DateRequired = "0033";
            public const string ProjectRequired = "0036";
            public const string ActivityRequired = "0037";
            public const string AmountInRange = "0038";
            public const string CanOnlyAddToBatchIfDraft = "0039";
            public const string CanOnlyDeleteDrafts = "0040";
            public const string NoValidWorkFlow = "0041";
            public const string ClaimNotDraft = "0042";
            public const string ClaimBatchNotValidForEmployee = "0043";
            public const string CanOnlyUpdateClaimIfStatusDraft = "0044";
            public const string InactiveComponent = "0045";
        }

        public static class CompanyGroupLink
        {
            public const string NoUserSelected = "lblNoUserSelected";
            public const string NoGroupSelected = "lblNoGroupSelected";
            public const string NameOfGroupRequired = "nameOfGroupRequired";
            public const string AllUserSelected = "allUserSelected";
            public const string NoUsersAvailable = "noUsersAvailable";
            public const string GroupAlreadyRegistered = "groupAlreadyRegistered";
        }

        public static class BusinessPartner
        {
            public const string MaxLength8 = "lblMaxLength8";
            public const string MaxLength20 = "lblMaxLength20";
            public const string MaxLength50 = "lblMaxLength50";
            public const string MaxLength255 = "lblMaxLength255";
            public const string AccountNumberMinLength = "lblBankAccountNumberMinLength";
            public const string InvalidAccountNumber = "lblInvalidAccountNumber";
            public const string AllBankFieldsRequired = "lblAllBankFieldsRequired";
            public const string BankBranchCodeLength = "lblBankBranchCodeLength";
            public const string MaxDiscountPercentValue = "lblMaxNewDiscountPercentValue";
            public const string MaxOldDiscountPercentValue = "lblMaxOldDiscountPercentValue";
            public const string AgencyNameRequired = "lblAgencyNameRequired";
            public const string AgencyTypeRequired = "lblAgencyTypeRequired";
            public const string AgencyContactPersonFirstNameRequired = "lblAgencyContactPersonFirstNameRequired";
            public const string AgencyContactPersonLastNameRequired = "lblAgencyContactPersonLastNameRequired";
            public const string AgencyContactEmailRequired = "lblAgencyContactEmailRequired";
            public const string AgencyContactTelephoneRequired = "lblAgencyContactTelephoneRequired";
            public const string BillingSourceIdRequired = "lblBillingSourceIdRequired";
            public const string APayURLRequired = "lblAPayURLRequired";
            public const string APayNameRequired = "lblAPayNameRequired";
            public const string APayContactEmailRequired = "lblAPayContactEmailRequired";
            public const string AThemeRequired = "lblAThemeRequired";
            public const string APayLogoRequired = "lblAPayLogoRequired";
            public const string InvalidContactEmail = "lblInvalidContactEmail";
            public const string InvalidBrandContactEmail = "lblInvalidBrandContactEmail";
        }

        public static class Company
        {
            public const string DuplicateRegistrationNumber = "DuplicateRegistrationNumber";
            public const string InvalidPayeReferenceNumber = "InvalidPayeReferenceNumber";
            public const string InvalidCompanyRegistrationNo = "InvalidCompanyRegistrationNo";
            public const string DuplicatePayeRegistrationNumber = "DuplicatePayeRegistrationNumber";
            public const string InvalidCompanyEmpNoStart = "InvalidCompanyEmpNoStart";
            public const string InvalidAddressCode = "InvalidAddressCode";
            public const string DuplicateGroupName = "DuplicateGroupName";
            public const string CompanyNameRequired = "CompanyNameRequired";
            public const string CompanyTradingNameRequired = "CompanyTradingNameRequired";
            public const string InvalidGroupName = "InvalidGroupName";
            public const string CompanyRegistrationNumberRequired = "CompanyRegistrationNumberRequired";
            public const string CompanyTaxReferenceNumberRequired = "CompanyTaxReferenceNumberRequired";
            public const string CityOrTownRequired = "CityOrTownRequired";
            public const string CompanyPaymentModuleRequired = "CompanyPaymentModuleRequired";
            public const string TaxAuthorityRequired = "TaxAuthorityRequired";
            public const string EmployeeNumberGenerationLength = "EmployeeNumberGenerationLength";
            public const string PasswordExpiryPeriodRequired = "PasswordExpiryPeriodRequired";
            public const string AgencyRequired = "AgencyRequired";
            public const string SalesOwnerRequired = "SalesOwnerRequired";
            public const string CompanyContactNoRequired = "CompanyContactNoRequired";
            public const string CompanyMobileNumberRequired = "CompanyMobileNumberRequired";
            public const string CompanyAlternateContactNoRequired = "CompanyAlternateContactNoRequired";
            public const string PhysicalAddressRequired = "errPhysicalAddressRequired";
            public const string DuplicateAddressTypes = "errDuplicateAddressTypes";
            public const string InvalidMaxAlphaNumericLength = "InvalidMaxAlphaNumericLength";
            public const string CompanyContactPersonRequired = "CompanyContactPersonRequired";
            public const string InvalidIrelandRegistrationNumber = "InvalidIrelandRegistrationNumber";
            public const string DuplicateIrelandRegistrationNumber = "DuplicateIrelandRegistrationNumber";
        }

        public static class CompanyPublicHoliday
        {
            public const string DuplicateRecord = "errDuplicateRecord";
            public const string BureauRecord = "errBureauRecord";
            public const string CategoryRequired = "errCategoryRequired";
            public const string MunicipalityRequired = "errMunicipalityRequired";
        }

        public static class ChangeRequest
        {
            public const string RequestDescriptionMaxLength = "RequestDescriptionMaxLength";
            public const string RequestReferenceMaxLength = "RequestReferenceMaxLength";
        }

        public static class UserProfiles
        {
            public const string UserRequired = "lblUserRequired";
            public const string FirstNameExceeds = "lblFirstNameExceeds";
            public const string LastNameExceeds = "lblLastNameExceeds";
            public const string CompanyFrequencyAccess = "lblCompanyFrequencyAccess";
            public const string InvalidEmail = "lblInvalidEmail";
            public const string EmailSpecialCharactersNotAllowed = "lblEmailSpecialCharactersNotAllowed";
            public const string EmailExists = "lblEmailExists";

            public const string StatusSelected = "lblStatusSelected";
            public const string NoJobTitle = "lblNoJobTitle";
            public const string EmployeeAllocateContact = "lblEmployeeAllocateContact";
            public const string CompanySystemAccess = "lblCompanySystemAccess";
            public const string CompanyFrequencyLinkRequired = "lblCompanyFrequencyLinkRequired";
            public const string SecurityRoleRequired = "lblSecurityRoleRequired";
            public const string CompanyOrEmployeeContactTypes = "lblCompanyOrEmployeeContactTypes";
            public const string CompanyOrBpNewsletters = "lblCompanyOrBpNewsletters";
            public const string CompanyOrBpHistoricalRecords = "lblCompanyOrBpHistoricalRecords";
            public const string CompanyOrBpCloudRoom = "lblCompanyOrBpCloudRoom";
            public const string CompanyOrBpAnalytics = "lblCompanyOrBpAnalytics";
            public const string CompanyOrBpOrgChart = "lblCompanyOrBpOrgChart";
            public const string CompanyOrBpBudgetUser = "lblCompanyOrBpBudgetUser";
            public const string BudgetSetting = "lblBudgetSetting";
            public const string BpIsAgencyTopLevel = "lblBpIsAgencyTopLevel";
            public const string CompanyOrBpPowerBi = "lblCompanyOrBpPowerBi";
            public const string PowerBiModule = "lblPowerBiModule";
            public const string CompanyOverrideLockDown = "lblCompanyOverrideLockDown";
            public const string CompanyOverrideSetting = "lblCompanyOverrideSetting";
            public const string EmployeeExists = "lblEmployeeExists";
            public const string ContactNumberLength = "lblMobileNumberMinimumLength";
            public const string ContactNumberMaxLength = "lblMobileNumberMaximumLength";
            public const string InvalidContactNumber = "lblMobileNumberNumeric";
            public const string InvalidPasswordLength = "lblPasswordMinimumLength";
            public const string InvalidPasswordNumeric = "lblPasswordNumericValue";
            public const string InvalidPasswordLowerCase = "lblPasswordLowerCaseValue";
            public const string InvalidPasswordUpperCase = "lblPasswordUpperCaseValue";
            public const string InvalidPasswordContainsPassword = "lblNotContainPassword";
            public const string InvalidPasswordSpaces = "lblNotContainSpaces";
            public const string InvalidPasswordMatch = "lblPasswordMatch";
        }

        public static class PublicHoliday
        {
            public const string DuplicateRecord = "errDuplicateRecord";
            public const string DateRequired = "errDateRequired";
            public const string DescRequired = "errDescRequired";
            public const string DescriptionMaxLength = "errMaxLength50";
            public const string LevelRequired = "errLevelRequired";
            public const string ProvinceRequired = "errProvinceRequired";
        }

        public static class PublicHolidayCategory
        {
            public const string DuplicateDesc = "errDuplicateDesc";
            public const string DuplicateCode = "errDuplicateCode";
            public const string LinkedHoliday = "errLinkedHoliday";
            public const string IsValidCode = "errIsValidCode";
        }

        public static class CompanyFrequency
        {
            public const string DeleteDependencyError = "errDeleteDependencyError";
            public const string PayDateTaxYearError = "errPayDateTaxYearError";
            public const string Weekly7DayMultipleError = "errWeekly7DayMultipleError";
            public const string WorkingHoursPerDayError = "errWorkingHoursPerDayError";
            public const string DuplicateTaxCertificateError = "errDuplicateTaxCertificateError";
            public const string ValidRunFrequency = "errValidRunFrequency";
            public const string FrequencyNameRequired = "errFrequencyNameRequired";
            public const string DuplicateFrequencyNameError = "errDuplicateFrequencyNameError";
            public const string PaydayRequired = "errPaydayRequired";
            public const string PayBeforeWeekendOrHolidayRequired = "errPayBeforeWeekendOrHolidayRequired";
            public const string WeeklyTaxYearStartDateRequired = "errWeeklyTaxYearStartDateRequired";
            public const string TaxCertificatePrefixRequired = "errTaxCertificatePrefixRequired";
            public const string InvalidNamibiaTaxCertificatePrefix = "errInvalidNamibiaTaxCertificatePrefix";
            public const string InvalidSATaxCertificatePrefix = "errInvalidSATaxCertificatePrefix";
            public const string WeeklyDaysPerPeriodError = "errWeeklyDaysPerPeriodError";
            public const string MonthlyDaysPerPeriodError = "errMonthlyDaysPerPeriodError";
            public const string FortnightDaysPerPeriodError = "errFortnightDaysPerPeriodError";
            public const string InvalidInactiveDateError = "errInvalidInactiveDateError";
            public const string InvalidPaydayWeekDay = "errInvalidPaydayWeekDayError";

            public const string FirstCycleEndDayRequired = "errFirstCycleEndDayRequired";
            public const string FirstCyclePayDateRequired = "errFirstCyclePayDateRequired";
            public const string SecondCycleEndDayRequired = "errSecondCycleEndDayRequired";
            public const string SecondCyclePayDateRequired = "errSecondCyclePayDateRequired";
            public const string FirstProcessingPeriodEndDateRequired = "errFirstProcessingPeriodEndDateRequired";
            public const string PayFrequencyNotAvailableInCountry = "errPayFrequencyNotAvailableInCountry";
            public const string SemiMonthlyDaysPerPeriodError = "errSemiMonthlyDaysPerPeriodError";
            public const string FirstProcessingPeriodEndDateFallsOnFirstOrSecondPayDay = "errFirstProcessingPeriodEndDateFallsOnFirstOrSecondPayDay";
            public const string InvalidPostRunByRunBeforeDateWithClosedRuns = "errPostRunByRunBeforeDateWithClosedRuns";
            public const string InvalidPeriodStartDateForTaxYears = "errInvalidPeriodStartDateForTaxYears";
            public const string PeriodStartDateRequired = "errPeriodStartDateRequired";
        }

        public static class LeaveSetupEntitlement
        {
            public const string DisallowEmployeeDefinedAccrual = "DisallowEmployeeDefinedAccrual";
            public const string LeaveSetupInClosedRun = "LeaveSetupInClosedRun";
            public const string DuplicateExcelRecord = "DuplicateEntitlementExcelRecord";
            public const string DuplicateRecord = "DuplicateEntitlementRecords";
            public const string NegativeAccrual = "NegativeAccrual";
            public const string InvalidScheme = "InvalidScheme";
        }

        public static class CompanyPensionEnrolment
        {
            public const string InvalidContactFirstNameLength = "errInvalidContactFirstNameLength";
            public const string InvalidContactLastNameLength = "errInvalidContactLastNameLength";
            public const string InvalidContactNumberLength = "errInvalidContactNumberLength";
            public const string InvalidContactNumber = "errInvalidContactNumber";
            public const string InvalidContactEmailLength = "errInvalidContactEmailLength";
            public const string InvalidContactEmail = "errInvalidContactEmail";
            public const string DutiesStartRequired = "errDutiesStartRequired";
            public const string ExemptionReasonRequired = "errExemptionReasonRequired";
        }

        public static class EmployeeIncident
        {
            public const string SRConditionsMaxLengthError = "errSRConditionsLength";
            public const string IncidentNotesMaxLengthError = "errNotesLength";
            public const string EmployeeRepresentativeMaxLengthError = "errEmployeeRepresentativeLength";
            public const string CompanyRepresentativeMaxLengthError = "errCompanyRepresentativeLength";
            public const string OutcomeDetailsMaxLengthError = "errOutcomeDetailsLength";
            public const string ChairPersonMaxLengthError = "errChairpersonLength";
            public const string CompanyWittnessMaxLengthError = "errCompanyWitnessesLength";
            public const string EmployeeWittnessMaxLengthError = "errEmployeeWitnessesLength";
            public const string CaseRefNoMaxLengthError = "errCaseRefNoLength";
            public const string SrDescriptionMaxLengthError = "errSettlementDetailsDescriptionLength";
            public const string CommissionerNameMaxLengthError = "errCommissionerNameLength";
            public const string LegalRepresentativeMaxLengthError = "errLegalRepresentativeLength";
            public const string SettlementDetailsMaxLengthError = "errSettlementDetailsLength";
            public const string AccusedMaxLengthError = "errAccusedLength";
            public const string FacilitatorMaxLengthError = "errFacilitatorLength";
            public const string NatureOfGrievanceMaxLengthError = "errNatureofGrievanceLength";
            public const string GrievanceRecommendationMaxLengthError = "errGrievanceRecommendationLength";
            public const string OutcomeNotesMaxLengthError = "errOutcomeNotesLength";
            public const string ReportedByMaxLengthError = "errReportedByLength";
            public const string SettlementDetailsReasonForDiscussion = "errSettlementDetailsReasonForDiscussionLength";
        }

        public static class SecurityRoles
        {
            public const string DuplicateEssOrMss = "errDuplicateEssOrMss";
            public const string DuplicateRoleName = "errDuplicateRoleName";
            public const string DuplicateRoleCode = "errDuplicateRoleCode";
            public const string DuplicateDefaultRole = "errDuplicateDefaultRoles";
            public const string InvalidSuperUser = "errInvalidSuperUser";
            public const string HasAttachedUsers = "errHasAttachedUsers";
        }

        public static class ComponentCompany
        {
            public const string DuplicateDescriptionError = "errDuplicateDescription";
            public const string DuplicateComponentCodeError = "errDuplicateComponentCode";
        }

        public static class HmrcPaymentRecord
        {
            public const string DuplicateRecordEntry = "errDuplicateRecordEntry";
            public const string PeriodCodeOrQuarterRequired = "errPeriodCodeOrQuarterRequired";
            public const string ProvideOnlyPeriodCodeOrQuarter = "errProvideOnlyPeriodCodeOrQuarter";
            public const string InvalidTaxYear = "errInvalidTaxYear";
            public const string InvalidPeriodCode = "errInvalidPeriodCode";
            public const string InvalidTakeOnRunPeriodCode = "errInvalidTakeOnRunPeriodCode";
            public const string InvalidTakeOnRunQuarters = "errInvalidTakeOnRunQuarters";
            public const string PeriodCodeFormat = "errPeriodCodeFormat";
            public const string PeriodCodeChangeNotAllowed = "errPeriodCodeChangeNotAllowed";
            public const string QuarterChangeNotAllowed = "errQuarterChangeNotAllowed";
            public const string PaymentDateRequired = "errPaymentDateRequired";
            public const string PaymentDateUpdateRequired = "errPaymentDateUpdateRequired";
            public const string QuarterlyValueLessThanMonthlySum = "errQuarterlyValueLessThanMonthlySum";
            public const string QuarterlyTotalExceedsMonthlyPayments = "warnQuarterlyTotalExceedsMonthlyPayments";
        }

        public static class CompanyRun
        {
            public const string PayDateRequired = "errPayDateRequired";
            public const string CalculationInProgress = "errCalculationInProgress";
            public const string DuplicateRuns = "errDuplicateRuns";
            public const string InvalidPeriodCodeFormat = "errInvalidPeriodCodeFormat";
            public const string PayDateIsSunday = "errPayDateIsSunday";
            public const string PayDateBeforeStartDate = "errPayDateBeforeStartDate";
            public const string PayDateOnPublicHoliday = "errPayDateOnPublicHoliday";
            public const string EndDateBeforeStartDate = "errEndDateBeforeStartDate";
            public const string ClosedCannotBeFutureRun = "errClosedCannotBeFutureRun";
            public const string UnableToDeleteDueToDependancyError = "errUnableToDeleteDueToDependancy";
            public const string PayDateOutsideAllocatedPeriod = "errPayDateOutsideAllocatedPeriod";
            public const string PayDateOutsideAllocatedPeriodFormatted = "errPayDateOutsideAllocatedPeriodFormatted";
            public const string PayDateAfterLaterBeforePrevious = "errPayDateAfterLaterBeforePrevious";
            public const string PayDateBeforeAnotherClosedRunPayDate = "errPayDateBeforeAnotherClosedRunPayDate";
            public const string PayDateForTransferNetPayMustBeFutureDate = "errPayDateForTransferNetPayMustBeFutureDate";
            public const string OpenPreviousPeriods = "errOpenPreviousPeriods";
            public const string InvalidRunOrderCheckSequence = "errInvalidRunOrderCheckSequence";
            public const string InvalidRunOrderFutureBeforeOpen = "errInvalidRunOrderFutureBeforeOpen";
            public const string OpenToRecalculateBeforeClosing = "errOpenToRecalculateBeforeClosing";
            public const string CloseRunsBeforeCurrent = "errCloseRunsBeforeCurrent";
            public const string InterimRunOrderCannotBeChanged = "errInterimRunOrderCannotBeChanged";
            public const string InvalidPayDateRunOrder = "errInvalidPayDateRunOrder";
            public const string InterimRunAfterMainRun = "errInterimRunAfterMainRun";
            public const string OnlyInterimRunsAllowedDuringCreate = "errOnlyInterimRunsAllowedDuringCreate";
            public const string PayDateMustBeTodayOrFuture = "errPayDateMustBeTodayOrFuture";
            public const string FixErrorsBeforeClosingRun = "errFixErrorsBeforeClosingRun";
            public const string PayPeriodCannotBeLinked = "errPayPeriodCannotBeLinked";
            public const string NotAllowedToDeleteMainRunError = "errNotAllowedToDeleteMainRunError";
            public const string RunDescriptionRequired = "errRunDescriptionRequired";
            public const string OrderNumberRequired = "errOrderNumberRequired";
            public const string ApostropheNotAllowedForRunDescription = "errApostropheNotAllowedForRunDescription";
            public const string StatusRequired = "errStatusRequiredRequired";
            public const string OrderNumberInvalid = "errInvalidOrderNumber";
            public const string PayDateOnBankHoliday = "errPayDateOnBankHoliday";
            public const string FutureRunBeforeOpenRunNotAllowed = "errFutureRunBeforeOpenRunNotAllowed";
            public const string InterimRunMustBeLinkedToMain = "errInterimRunMustBeLinkedToMain";
            public const string PeriodStartDateReadOnly = "errPeriodStartDateReadOnly";
            public const string PeriodEndDateReadOnly = "errPeriodEndDateReadOnly";
            public const string PeriodCodeReadOnly = "errPeriodCodeReadOnly";
        }

        public static class CompanyLeaveSchemeParameter
        {
            public const string CannotDeleteRecord = "errCannotDeleteRecord";
            public const string EffectiveDateRequired = "errEffectiveDateRequired";
            public const string EffectiveDateNotEditable = "errEffectiveDateNotEditable";

            // Accrual specific
            public const string AccrualOptionIdRequired = "errAccrualOptionRequired";
            public const string UpfrontOptionNotAllowed = "errUpfrontOptionNotAllowed";
            public const string OptionNotAllowedWithHours = "errOptionNotAllowedWithHours";

            // Specific to EmployeeDefined when true
            public const string AccrualPeriodZero = "errAccrualPeriodZero";
            public const string AccrualPeriodRequired = "errAccrualPeriodRequired";
            public const string AccrualPeriodIdRequired = "errAccrualPeriodIdRequired";
            public const string LeaveAccrualValueRequired = "errLeaveAccrualValueRequired";
            public const string AccrualPeriodValueRequired = "errAccrualPeriodValueRequired";

            public const string ForfeitPeriodZero = "errForfeitPeriodZero";
            public const string DropOffMonthRequired = "errDropOffMonthRequired";
            public const string UpfrontAccrualPeriodRequired = "errUpfrontAccrualPeriodRequired";
            public const string UpfrontMonthlyAccrualRequired = "errUpfrontMonthlyAccrualRequired";

            // Brazil specific
            public const string Max2DigitParcelNumber = "errMaxParcelDigitsTwo";
            public const string ParcelValueGreaterThan30 = "errParcelValueGreaterThan30";
            public const string ParcelValueNotInteger = "errParcelValueNotInteger";

            // Specific to CompanyLeaveServiceLength
            public const string EndYearsRequired = "errEndYearsRequired";
            public const string StartYearsRequired = "errStartYearsRequired";
            public const string ServiceDescriptionRequired = "errServiceDescriptionRequired";
        }
    }
}