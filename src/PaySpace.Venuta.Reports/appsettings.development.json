{"ConnectionStrings": {"ESocial": "Uri=https://esocial-uat.azurewebsites.net;header:X-Functions-Key=41f4ce6249e84bdba7b85e2bd38218d1;header:Authorization=\"expression:'Bearer ' + ?AccessToken\";query:period=\"expression:PeriodCodeSeperator(?PeriodCode)\";path:company=\"expression:'company/' + ?CompanyId\";path:report=\"expression:'report/' + ?ReportNumber\""}, "Identity": {"Authority": "https://localhost:44392", "ClientId": "nextgen", "ClientSecret": "f7442f2a-bd04-4bf1-8b31-f1f1f2182e02"}, "ClientSettings": {"ApiUrl": "https://localhost:44393", "ReportApiUrl": "https://localhost:44362", "NotificationUrl": "https://localhost:44365", "PaceyUrl": "https://payspaceuat-pacey.azurewebsites.net"}, "SsrsSettings": {"Url": "http://**********/reportserver/ReportExecution2005.asmx", "Username": "azurevmsql", "Password": "_eZaCYrR&6G*wB)d"}, "ElasticSearch": {"Urls": ["http://localhost:9200"]}, "RedisSettings": {"Instance": "NextGen_Dev:", "DefaultConnection": "localhost:6379"}, "AzureConnections": {"ServiceBusConnection": "", "StorageConnection": "DefaultEndpointsProtocol=https;AccountName=websiteattachmentsdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "CosmosConnection": "AccountEndpoint=https://localhost:8081/;AccountKey=****************************************************************************************;", "TaxBreakdownConnection": "DefaultEndpointsProtocol=https;AccountName=taxcalcbreakdownuatsan;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}, "Pacey": {"StorageConnection": "DefaultEndpointsProtocol=https;AccountName=websiteattachmentsdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "FunctionsKey": "LFBadriElEzqM3PEilbqvahkY/nlmgmrogekKIEDRclJ8yQgJdQtNQ=="}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}}