namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;

    [DisplayName(SystemAreas.CompanyLeaveSchemeParameter.Area)]
    public class LeaveSchemeParameterResult
    {
        // Needed for the dx-tree-list to work with a tree structure
        public string NodeId { get; set; }

        public long CompanyLeaveDetailId { get; set; }

        public DateTime EffectiveDate { get; set; }

        public long CompanyLeaveSetupId { get; set; }

        public LeaveType LeaveType { get; set; }

        [Display(Name = "LeaveDescription")]
        public string LeaveTypeDescription { get; set; }

        [Display(Name = "LeaveOrder")]
        public int OrderNumber { get; set; }

        public DateTime? StopDate { get; set; }
    }
}