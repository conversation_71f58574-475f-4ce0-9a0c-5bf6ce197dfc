namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models
{
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;

    [DisplayName(SystemAreas.CompanyLeaveSchemeParameter.Area)]
    public class LeaveSchemeResult
    {
        // Allows the parent data to work in the dx-tree-list
        public string NodeId { get; set; }

        public long CompanyLeaveSchemeId { get; set; }

        public long CompanyId { get; set; }

        [Display(Name = "LeaveScheme")]
        public string SchemeName { get; set; }

        public virtual ICollection<LeaveSchemeParameterResult> LeaveSchemeParameterResults { get; set; }
    }
}