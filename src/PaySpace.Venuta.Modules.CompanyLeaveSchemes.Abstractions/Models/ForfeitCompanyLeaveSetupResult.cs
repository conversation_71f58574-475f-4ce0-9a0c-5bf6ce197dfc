namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models
{
    using PaySpace.Venuta.Data.Models.Enums;

    public class ForfeitCompanyLeaveSetupResult
    {
        public long CompanyLeaveSetupId { get; set; }

        public long CompanyLeaveSchemeId { get; set; }

        public LeaveType LeaveType { get; set; }

        public string Description { get; set; }

        public string Value { get; set; }

        public long Id { get; set; }

        public int Order { get; set; }
    }
}