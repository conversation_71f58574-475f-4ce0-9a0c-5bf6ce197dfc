<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <CodeAnalysisRuleSet>..\..\PaySpace.Venuta.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <PropertyGroup>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Roslynator.Formatting.Analyzers">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Payspace.Venuta.Data.Models.Dto\PaySpace.Venuta.Data.Models.Dto.csproj" />
    <ProjectReference Include="..\PaySpace.Venuta.Data.Models\PaySpace.Venuta.Data.Models.csproj" />
  </ItemGroup>

</Project>