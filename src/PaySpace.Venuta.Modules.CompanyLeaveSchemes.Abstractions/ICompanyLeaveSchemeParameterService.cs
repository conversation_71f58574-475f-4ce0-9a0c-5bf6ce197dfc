namespace PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models;

    public interface ICompanyLeaveSchemeParameterService
    {
        Task<List<LeaveSchemeResult>> GetTreeListStructureAsync(long companyId);

        IQueryable<object> GetOrderNumbersLookup(long companyLeaveSchemeId, LeaveType leaveTypeId);

        IQueryable<ForfeitCompanyLeaveSetupResult> GetForfeitCompanyLeaveSetups(long companyId);

        IQueryable<LeaveEncashmentComponentResult> GetEncashmentComponents(long companyId);

        Task<(bool isGradeBasedAccrualEnabled, bool isGradeBasedMaxBalanceEnabled)> GetRelevantGradeSettingsAsync(long companyId);
    }
}