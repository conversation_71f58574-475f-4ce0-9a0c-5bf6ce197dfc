namespace PaySpace.Venuta.Security
{
    using System.Security.Claims;

    public interface IClaimsTenantProvider
    {
        long GetUserId(ClaimsPrincipal user);

        long GetAgencyId(ClaimsPrincipal user);

        long? GetCompanyGroupId(ClaimsPrincipal user);

        long? GetCompanyId(ClaimsPrincipal user);

        long? GetEmployeeId(ClaimsPrincipal user);

        long? GetFrequencyId(ClaimsPrincipal user);

        string? GetTaxCountryCode(ClaimsPrincipal user);

        int GetDecimalPlaces(ClaimsPrincipal user);

        bool CanEditHistoricalRecords(ClaimsPrincipal user);

        int? GetTaxCountryId();
    }
}
