namespace PaySpace.Venuta.Modules.Employee.Positions.Abstractions
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Organization;

    public interface ICompanyGradeFieldService : IGenericService<CompanyGradeField>
    {
        Task<bool> GradeFieldExistsAsync(long gradeFieldId, CancellationToken cancellationToken);

        IQueryable<CompanyGradeField> GetCompanyGradesFields(long companyId);

        IList<CompanyGradeField> GetGradesFields(long companyId);

        IList<CompanyGradeFieldValue> GetCompanyGradeFieldValues(long organizationGradeId);

        IList<CompanyGradeFieldValue> GetCompanyGradeFieldValuesByCompanyId(long companyId);
    }
}