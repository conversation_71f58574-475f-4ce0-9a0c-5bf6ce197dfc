namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Employee, ShowByDefault = false)]
    [DisplayName(SystemAreas.YearEndReporting.Area)]
    [NavigationControllerName(SystemAreas.YearEndReporting.Area)]
    public class EmployeeIR8ADto : IEmployeeNumberEntity, ICloneable
    {
        [Key]
        public long EmployeeIR8AId { get; set; }

        [ODataNotEditable]
        [BulkUploadIgnore]
        [BulkUploadHidden]
        public long EmployeeId { get; set; }

        [ODataNotEditable]
        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployeeNumber, Order = 1)]
        [BulkUploadLookup(DisableInExcel = true, ShowLookupSheetInExcel = true)]
        public string EmployeeNumber { get; set; }

        [Required]
        [Display(Name = SystemAreas.YearEndReporting.Keys.BasisYear, Order = 2)]
        [BulkUploadLookup(EnumType = typeof(CountryTaxYear), OverrideLookup = "TaxYear")]
        public string TaxYear { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsDeclarationByAgent, Order = 3)]
        public bool IsDeclarationByAgent { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsSection45Applicable, Order = 4)]
        public bool IsSection45Applicable { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsIR21Submitted, Order = 5)]
        public bool IsIR21Submitted { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsIncomeTaxBorneByEmployer, Order = 6)]
        public bool IsIncomeTaxBorneByEmployer { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IncomeTaxOption, Order = 7)]
        [BulkUploadLookup(EnumType = typeof(EnumIncomeTaxOption))]
        public string? IncomeTaxOption { get; set; }

        [Range(0, *********)]
        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployerIncomeTaxAmount, Order = 8)]
        public int? EmployerIncomeTaxAmount { get; set; }

        [Range(0, *********)]
        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployeeLiabilityAmount, Order = 9)]
        public int? EmployeeLiabilityAmount { get; set; }

        [Range(0, *********)]
        [Display(Name = SystemAreas.YearEndReporting.Keys.RemissionIncomeAmount, Order = 10)]
        public int? RemissionIncomeAmount { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.IsExemptRemissionIncomeApplicable, Order = 11)]
        public bool IsExemptRemissionIncomeApplicable { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumRemissionExemptIncomeReason))]
        [Display(Name = SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeReason, Order = 12)]
        public string? RemissionExemptIncomeReason { get; set; }

        [Range(0, *********)]
        [Display(Name = SystemAreas.YearEndReporting.Keys.RemissionExemptIncomeAmount, Order = 13)]
        public int? RemissionExemptIncomeAmount { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumOverseasPosting))]
        [Display(Name = SystemAreas.YearEndReporting.Keys.OverseasPostingReason, Order = 14)]
        public string? OverseasPostingReason { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.DesignatedFundName, Order = 15)]
        public string? DesignatedFundName { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.LumpSumPaymentReason, Order = 16)]
        public string? LumpSumPaymentReason { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.LumpSumPaymentBasis, Order = 17)]
        public string? LumpSumPaymentBasis { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.PensionOrProvidentFundName, Order = 18)]
        public string? PensionOrProvidentFundName { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.AmountAccruedFrom1993, Order = 19)]
        public decimal? AmountAccruedFrom1993 { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.BonusDeclarationDate, Order = 20)]
        public DateTime BonusDeclarationDate { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.DirectorsFeesApprovalDate, Order = 21)]
        public DateTime DirectorsFeesApprovalDate { get; set; }

        [Range(0, *********)]
        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployerCpfRefundClaimed, Order = 22)]
        public int? EmployerCpfRefundClaimed { get; set; }

        [Range(0, *********)]
        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployeeCpfRefundClaimed, Order = 23)]
        public int? EmployeeCpfRefundClaimed { get; set; }

        [Range(0, *********)]
        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployerRefundInterest, Order = 24)]
        public int? EmployerRefundInterest { get; set; }

        [Range(0, *********)]
        [Display(Name = SystemAreas.YearEndReporting.Keys.EmployeeRefundInterest, Order = 25)]
        public int? EmployeeRefundInterest { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}