namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.CompanyLeaveScheme.Area)]
    [NavigationControllerName("CompanyLeaveSchemeParameter")]
    public class CompanyLeaveSchemeDto : ICloneable
    {
        [Key]
        public long CompanyLeaveSchemeId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "SchemeName")]
        public string SchemeName { get; set; }

        [StringLength(50)]
        [Display(Name = "SchemeCode")]
        public string SchemeCode { get; set; }

        [DataType(DataType.Date)]
        [Display(Name = "InactiveDate")]
        public DateTime? InactiveDate { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}