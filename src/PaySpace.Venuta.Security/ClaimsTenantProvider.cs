namespace PaySpace.Venuta.Security
{
    using System.Linq;
    using System.Security.Claims;

    using PaySpace.Venuta.Infrastructure;

    public class ClaimsTenantProvider : IClaimsTenantProvider
    {
        public virtual long GetUserId(ClaimsPrincipal user)
        {
            return user.GetUserId();
        }

        public virtual long GetAgencyId(ClaimsPrincipal user)
        {
            ExceptionHelper.ThrowIfNull(nameof(user), user);

            return user.GetAgencyId();
        }

        public virtual long? GetCompanyGroupId(ClaimsPrincipal user)
        {
            ExceptionHelper.ThrowIfNull(nameof(user), user);

            if (user.IsInRole(UserTypeCodes.Employee) || user.IsInRole(UserTypeCodes.Company))
            {
                return user.GetCompanyGroupId();
            }

            return null;
        }

        public virtual long? GetCompanyId(ClaimsPrincipal user)
        {
            ExceptionHelper.ThrowIfNull(nameof(user), user);

            if (user.IsInRole(UserTypeCodes.Employee) || user.IsInRole(Roles.ESS))
            {
                return user.GetCompanyId();
            }

            var sessionIdentity = user.Identities.SingleOrDefault(_ => _.AuthenticationType == "Session");
            if (sessionIdentity is not null)
            {
                return user.GetCompanyId();
            }

            return null;
        }

        public virtual long? GetEmployeeId(ClaimsPrincipal user)
        {
            ExceptionHelper.ThrowIfNull(nameof(user), user);

            if (user.IsInRole(UserTypeCodes.Employee) || user.IsInRole(Roles.ESS))
            {
                return user.GetEmployeeId();
            }

            return null;
        }

        public virtual long? GetFrequencyId(ClaimsPrincipal user)
        {
            ExceptionHelper.ThrowIfNull(nameof(user), user);

            if (user.IsInRole(UserTypeCodes.Employee) || user.IsInRole(Roles.ESS))
            {
                return user.GetFrequencyId();
            }

            return null;
        }

        public virtual string? GetTaxCountryCode(ClaimsPrincipal user)
        {
            ExceptionHelper.ThrowIfNull(nameof(user), user);

            if (user.IsInRole(UserTypeCodes.Employee) || user.IsInRole(Roles.ESS))
            {
                return user.GetTaxCountryCode();
            }

            var sessionIdentity = user.Identities.SingleOrDefault(_ => _.AuthenticationType == "Session");
            if (sessionIdentity is not null)
            {
                return user.GetTaxCountryCode();
            }

            return null;
        }

        public virtual int GetDecimalPlaces(ClaimsPrincipal user)
        {
            ExceptionHelper.ThrowIfNull(nameof(user), user);
            return user.GetDecimalPlaces();
        }

        public virtual bool CanEditHistoricalRecords(ClaimsPrincipal user)
        {
            ExceptionHelper.ThrowIfNull(nameof(user), user);
            return user.CanEditHistoricalRecords();
        }

        public virtual int? GetTaxCountryId()
        {
            return null;
        }
    }
}
